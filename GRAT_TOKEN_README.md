# Gratitude Protocol Token (GRAT)

## 概述

Gratitude Protocol Token (GRAT) 是一个基于 ERC20 标准的简单代币合约，专为 Gratitude Protocol 生态系统设计。

## 代币信息

- **代币名称**: Gratitude Protocol
- **代币符号**: GRAT
- **小数位数**: 18
- **总供应量**: 250,000,000 GRAT (2.5亿)
- **最大供应量**: 250,000,000 GRAT (固定供应量)

## 主要功能

### 1. 基本 ERC20 功能
- ✅ 标准的转账功能 (`transfer`, `transferFrom`)
- ✅ 授权机制 (`approve`, `allowance`)
- ✅ 余额查询 (`balanceOf`)
- ✅ 总供应量查询 (`totalSupply`)

### 2. 铸造管理
- ✅ 铸造者权限管理
- ✅ 受控的代币铸造（不能超过最大供应量）
- ✅ 只有授权的铸造者可以铸造新代币

### 3. 代币销毁
- ✅ 用户可以销毁自己的代币
- ✅ 授权销毁功能（需要授权）
- ✅ 销毁会减少总供应量

### 4. 批量操作
- ✅ 批量转账功能
- ✅ 支持一次性向多个地址转账
- ✅ Gas 优化的批量操作

### 5. 安全功能
- ✅ 暂停/恢复功能（紧急情况下使用）
- ✅ 重入攻击保护
- ✅ 所有者权限管理
- ✅ 紧急提取功能

## 合约架构

```
GratitudeToken
├── ERC20 (OpenZeppelin)
├── Ownable (OpenZeppelin)
├── ReentrancyGuard (OpenZeppelin)
└── Pausable (OpenZeppelin)
```

## 部署说明

### 1. 环境准备

确保您已安装以下依赖：
```bash
npm install --save-dev hardhat
npm install @openzeppelin/contracts
```

### 2. 部署合约

使用提供的部署脚本：
```bash
npx hardhat run scripts/deploy-gratitude-token.js --network <network-name>
```

### 3. 验证部署

部署完成后，脚本会自动验证以下内容：
- 代币基本信息
- 总供应量分配
- 所有者权限设置
- 铸造者权限设置

## 使用示例

### 基本转账
```javascript
// 转账 1000 GRAT 给某个地址
await gratitudeToken.transfer(recipientAddress, ethers.parseEther("1000"));
```

### 批量转账
```javascript
const recipients = [address1, address2, address3];
const amounts = [
    ethers.parseEther("1000"),
    ethers.parseEther("2000"),
    ethers.parseEther("3000")
];
await gratitudeToken.batchTransfer(recipients, amounts);
```

### 代币销毁
```javascript
// 销毁 500 GRAT
await gratitudeToken.burn(ethers.parseEther("500"));
```

### 铸造管理
```javascript
// 添加铸造者（仅所有者）
await gratitudeToken.addMinter(minterAddress);

// 铸造代币（仅铸造者）
await gratitudeToken.mint(recipientAddress, ethers.parseEther("1000"));
```

## 管理功能

### 所有者权限
- 添加/移除铸造者
- 暂停/恢复合约
- 紧急提取合约中的代币
- 转移所有权

### 铸造者权限
- 铸造新代币（不能超过最大供应量）

## 安全考虑

1. **重入攻击保护**: 使用 OpenZeppelin 的 ReentrancyGuard
2. **暂停机制**: 紧急情况下可以暂停所有转账
3. **权限控制**: 严格的所有者和铸造者权限管理
4. **供应量限制**: 不能铸造超过最大供应量的代币

## 事件日志

合约会发出以下事件：
- `Transfer`: 代币转账
- `Approval`: 授权变更
- `MinterAdded`: 添加铸造者
- `MinterRemoved`: 移除铸造者
- `TokensBurned`: 代币销毁
- `Paused`: 合约暂停
- `Unpaused`: 合约恢复

## 测试

运行测试套件：
```bash
npx hardhat test test/GratitudeToken.test.js
```

## 许可证

MIT License

## 联系信息

如有问题或建议，请联系开发团队。

---

**注意**: 这是一个简化版本的代币合约，专注于核心功能。在生产环境中部署前，请确保进行充分的安全审计。
