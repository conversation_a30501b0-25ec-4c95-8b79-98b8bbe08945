# BSC链DeFi生态系统智能合约

## 项目概述

这是一个完整的BSC链上DeFi生态系统，包含四个相互关联的智能合约：TokenA（母币）、TokenB（子币）、NFT合约和挖矿合约。系统实现了复杂的代币经济模型，包括税收机制、自动分红、挖矿奖励和邀请系统。

## 🏗️ 系统架构

```
┌─────────────┐    ┌─────────────┐    ┌─────────────┐
│   TokenA    │    │   TokenB    │    │  MinerNFT   │
│   (母币)    │    │   (子币)    │    │  (NFT资产)  │
│             │    │             │    │             │
│ • 税收机制  │    │ • 税收机制  │    │ • 内置代币  │
│ • 自动分红  │    │ • 可铸造    │    │ • 定时释放  │
│ • 销毁功能  │    │ • 销毁功能  │    │ • 批量操作  │
└──────┬──────┘    └──────┬──────┘    └──────┬──────┘
       │                  │                  │
       └──────────────────┼──────────────────┘
                          │
              ┌───────────▼────────────┐
              │    MiningContract      │
              │      (挖矿合约)        │
              │                       │
              │ • 三种挖矿机制         │
              │ • 邀请奖励系统         │
              │ • 价格预言机集成       │
              │ • 自动释放算法         │
              └───────────────────────┘
                          │
              ┌───────────▼────────────┐
              │     PriceOracle        │
              │     (价格预言机)       │
              │                       │
              │ • PancakeSwap集成      │
              │ • 价格操纵保护         │
              │ • 多代币价格查询       │
              └───────────────────────┘
```

## 📋 合约详情

### 1. TokenA合约（母币）

- **总供应量**：2.5亿枚
- **税收机制**：
  - 买入税率：3%
  - 卖出税率：3%
- **税收分配**：
  - 20% → 营销钱包
  - 50% → 销毁
  - 30% → NFT持有者分红
- **核心功能**：
  - 自动分红机制
  - PancakeSwap集成
  - 税收豁免列表

### 2. TokenB合约（子币）

- **总供应量**：2100亿枚
- **税收机制**：
  - 买入税率：3%
  - 卖出税率：3%
- **税收分配**：
  - 50% → 营销钱包
  - 50% → 销毁
- **核心功能**：
  - 铸造功能（挖矿奖励）
  - 销毁功能
  - 角色权限管理

### 3. MinerNFT合约

- **总量**：2100个
- **内置资产**：每个NFT包含10万枚TokenA
- **核心功能**：
  - 单个/批量铸造
  - 按时间释放TokenA
  - 批量释放操作
  - 持有者查询接口

### 4. MiningContract合约（挖矿系统）

#### 三种挖矿机制：

**机制一：销毁TokenA获得TokenB**
- 销毁TokenA后获得2倍价值的TokenB
- 按30%/月固定月化率释放
- 支持邀请奖励

**机制二：持有NFT并销毁TokenB**
- 需要持有至少1个NFT
- 销毁TokenB后获得2倍价值的TokenB
- 按30%/月固定月化率释放

**机制三：持有NFT释放TokenA**
- 固定时间间隔释放固定数量
- 支持批量提取
- 自动结束机制

#### 邀请奖励系统：
- 上级获得下级10%价值的加速释放额度
- 加速释放值可高于待释放值
- 优先消耗加速释放值

### 5. PriceOracle合约（价格预言机）

- **PancakeSwap集成**：实时价格获取
- **价格保护**：防操纵机制
- **缓存系统**：提高查询效率
- **多代币支持**：支持任意ERC20代币

## 🚀 快速开始

### 环境要求

- Node.js >= 16.0.0
- npm >= 8.0.0
- Git

### 安装依赖

```bash
# 克隆项目
git clone <repository-url>
cd miner-token

# 安装依赖
npm install
```

### 环境配置

```bash
# 复制环境变量模板
cp .env.example .env

# 编辑.env文件，填入以下信息：
# PRIVATE_KEY=你的私钥
# BSCSCAN_API_KEY=你的BSCScan API密钥
# MARKETING_WALLET=营销钱包地址
```

### 编译合约

```bash
npm run compile
```

### 运行测试

```bash
# 运行所有测试
npm test

# 运行测试覆盖率
npm run test:coverage
```

## 📦 部署指南

### 测试网部署

```bash
# 部署到BSC测试网
npm run deploy:testnet

# 验证合约
npm run verify:testnet
```

### 主网部署

```bash
# 部署到BSC主网
npm run deploy:mainnet

# 验证合约
npm run verify:mainnet
```

### 部署后配置

部署完成后，系统会自动：
1. 设置合约之间的关系
2. 配置税收豁免地址
3. 向NFT合约转入TokenA
4. 设置铸造权限

## 🔧 使用指南

### 管理工具

```bash
# 启动交互式管理工具
npx hardhat run scripts/interact.js --network <network>

# 查看合约状态
npx hardhat run scripts/interact.js --network <network> status

# 铸造NFT
npx hardhat run scripts/interact.js --network <network> mint 5

# 释放NFT代币
npx hardhat run scripts/interact.js --network <network> release

# 领取挖矿奖励
npx hardhat run scripts/interact.js --network <network> claim
```

### 核心操作流程

#### 1. 用户获取NFT
```javascript
// 管理员铸造NFT给用户
await minerNFT.mint(userAddress);
```

#### 2. 机制一：销毁TokenA挖矿
```javascript
// 用户授权并销毁TokenA
await tokenA.approve(miningContract.address, amount);
await miningContract.burnTokenAForTokenB(amount);

// 等待释放期后领取TokenB
await miningContract.claimTokenBFromMechanism1();
```

#### 3. 机制二：销毁TokenB挖矿（需要NFT）
```javascript
// 用户必须持有NFT
await tokenB.approve(miningContract.address, amount);
await miningContract.burnTokenBWithNFT(amount);

// 领取TokenB奖励
await miningContract.claimTokenBFromMechanism2();
```

#### 4. NFT释放TokenA
```javascript
// 单个NFT释放
await minerNFT.releaseTokens(tokenId);

// 批量释放所有NFT
await minerNFT.batchReleaseTokens();
```

#### 5. 邀请关系设置
```javascript
// 被邀请人设置邀请人
await miningContract.setInviter(inviterAddress);
```

## 🧪 测试

项目包含完整的测试套件：

- **单元测试**：每个合约的独立功能测试
- **集成测试**：合约间交互功能测试
- **测试覆盖率**：目标覆盖率90%+

测试文件结构：
```
test/
├── TokenA.test.js          # TokenA合约测试
├── Integration.test.js     # 集成测试
└── ...                     # 其他测试文件
```

## 🔒 安全性考虑

### 已实现的安全措施

1. **重入攻击防护**：使用ReentrancyGuard
2. **权限控制**：基于Ownable和AccessControl
3. **输入验证**：所有公共函数参数验证
4. **溢出保护**：Solidity 0.8+内置保护
5. **紧急暂停**：Pausable功能
6. **价格操纵防护**：价格预言机保护机制

### 风险提示

1. **智能合约风险**：代码可能存在未发现的漏洞
2. **价格波动风险**：代币价格可能剧烈波动
3. **流动性风险**：代币可能面临流动性不足
4. **监管风险**：可能面临监管政策变化

## 📊 Gas优化

项目采用多种Gas优化技术：

1. **unchecked块**：优化安全计算
2. **存储布局优化**：减少存储槽使用
3. **批处理操作**：减少交易次数
4. **事件记录**：替代昂贵的存储操作

## 🔗 合约地址

### BSC主网
```
TokenA:         待部署
TokenB:         待部署
MinerNFT:       待部署
MiningContract: 待部署
PriceOracle:    待部署
```

### BSC测试网
```
TokenA:         待部署
TokenB:         待部署
MinerNFT:       待部署
MiningContract: 待部署
PriceOracle:    待部署
```

## 📈 代币经济模型

### TokenA经济模型
- 初始供应：2.5亿枚
- 通缩机制：交易税收销毁
- 分红机制：NFT持有者获得分红
- 释放机制：NFT定时释放

### TokenB经济模型
- 初始供应：2100亿枚
- 通胀机制：挖矿奖励铸造
- 通缩机制：挖矿销毁
- 价值支撑：2倍价值挖矿奖励

## 📚 API文档

详细的合约API文档请查看：[API Documentation](./docs/API.md)

## 🤝 贡献指南

1. Fork项目
2. 创建功能分支
3. 提交更改
4. 推送到分支
5. 创建Pull Request

## 📄 许可证

本项目采用MIT许可证 - 查看[LICENSE](LICENSE)文件了解详情

## 🆘 支持

如有问题或建议，请：

1. 查看文档
2. 搜索已有Issues
3. 创建新Issue
4. 联系开发团队

## 🔄 更新日志

### v1.0.0 (2024-XX-XX)
- 初始版本发布
- 实现核心合约功能
- 完成测试套件
- 添加部署脚本

---

**⚠️ 风险提示：本项目仅供学习和研究使用，投资有风险，请谨慎决策。**