{"name": "miner-token", "version": "1.0.0", "main": "index.js", "scripts": {"compile": "hardhat compile", "test": "hardhat test test/Basic.test.js", "test:all": "hardhat test", "test:coverage": "hardhat coverage", "test:gas": "REPORT_GAS=true hardhat test", "test:fork": "FORK_ENABLED=true hardhat test", "test:fork:gas": "FORK_ENABLED=true REPORT_GAS=true hardhat test", "deploy:testnet": "hardhat run scripts/deploy.js --network bsc_testnet", "deploy:mainnet": "hardhat run scripts/deploy.js --network bsc_mainnet", "verify:testnet": "hardhat run scripts/verify.js --network bsc_testnet", "verify:mainnet": "hardhat run scripts/verify.js --network bsc_mainnet", "node": "hardhat node", "node:fork": "FORK_ENABLED=true hardhat node", "node:mainnet": "hardhat node --fork https://bsc-dataseed1.binance.org/", "clean": "hardhat clean", "console": "hardhat console", "console:fork": "FORK_ENABLED=true hardhat console"}, "keywords": [], "author": "", "license": "ISC", "description": "", "devDependencies": {"@nomicfoundation/hardhat-toolbox": "^6.0.0", "@nomicfoundation/hardhat-verify": "^2.0.14", "hardhat": "^2.25.0"}, "dependencies": {"@openzeppelin/contracts": "^5.3.0", "dotenv": "^16.5.0"}}