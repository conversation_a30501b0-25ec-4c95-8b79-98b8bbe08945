// SPDX-License-Identifier: MIT
pragma solidity ^0.8.20;

import "@openzeppelin/contracts/token/ERC721/ERC721.sol";
import "@openzeppelin/contracts/token/ERC721/extensions/ERC721Enumerable.sol";
import "@openzeppelin/contracts/access/Ownable.sol";
import "@openzeppelin/contracts/utils/ReentrancyGuard.sol";
import "@openzeppelin/contracts/utils/Pausable.sol";
import "@openzeppelin/contracts/token/ERC20/IERC20.sol";

// MinerNFT合约 - 包含内置TokenA释放机制
contract MinerNFT is
    ERC721,
    ERC721Enumerable,
    Ownable,
    ReentrancyGuard,
    Pausable
{
    // 常量定义
    uint256 public constant MAX_SUPPLY = 2100; // NFT总量
    uint256 public constant TOKENS_PER_NFT = 100_000 * 1e18; // 每个NFT内置10万枚TokenA

    // 状态变量
    address public tokenAAddress; // TokenA合约地址
    uint256 public nextTokenId = 1; // 下一个token ID
    string private _baseTokenURI; // 基础URI

    // 释放相关
    uint256 public releaseStartTime; // 释放开始时间
    uint256 public releaseInterval = 30 days; // 释放间隔 (30天)
    // 12个月的释放比例数组 (3,3,3,5,5,5,10,10,10,10,10,26)
    uint256[12] public releasePercentages = [
        3,
        3,
        3,
        5,
        5,
        5,
        10,
        10,
        10,
        10,
        10,
        26
    ];
    uint256 public constant TOTAL_RELEASE_PERIODS = 12; // 总释放期数
    uint256 public constant RATE_DIVISOR = 100; // 释放比例除数

    // NFT信息映射
    struct NFTInfo {
        uint256 totalTokens; // NFT内置总代币数
        uint256 releasedTokens; // 已释放代币数
        uint256 lastReleaseTime; // 上次释放时间
        uint256 releaseStartTime; // 该NFT的个性化开始释放时间
    }

    mapping(uint256 => NFTInfo) public nftInfo; // NFT信息映射
    mapping(address => uint256[]) public userNFTs; // 用户持有的NFT列表
    mapping(uint256 => bool) public minering;
    mapping(address => bool) public miners;

    // 事件定义
    event NFTMinted(address indexed to, uint256 indexed tokenId);
    event TokensReleased(
        uint256 indexed tokenId,
        address indexed owner,
        uint256 amount
    );
    event BatchTokensReleased(
        address indexed owner,
        uint256[] tokenIds,
        uint256 totalAmount
    );
    event ReleaseParametersUpdated(
        uint256 startTime,
        uint256 interval,
        uint256 rate
    );
    event BaseURIUpdated(string newBaseURI);
    event NFTReleaseTimeUpdated(uint256 indexed tokenId, uint256 newStartTime);
    event BatchNFTReleaseTimeUpdated(
        uint256[] tokenIds,
        uint256[] newStartTimes
    );

    constructor(
        string memory name,
        string memory symbol,
        address _tokenAAddress
    ) ERC721(name, symbol) Ownable(msg.sender) {
        require(_tokenAAddress != address(0), "TokenA address cannot be zero");
        tokenAAddress = _tokenAAddress;

        // 设置释放开始时间为当前时间 + 1天
        releaseStartTime = block.timestamp + 30 days;
    }

    // 设置TokenA合约地址
    function setTokenAAddress(address _tokenAAddress) external onlyOwner {
        require(_tokenAAddress != address(0), "TokenA address cannot be zero");
        tokenAAddress = _tokenAAddress;
    }

    // 设置基础URI
    function setBaseURI(string calldata newBaseURI) external onlyOwner {
        _baseTokenURI = newBaseURI;
        emit BaseURIUpdated(newBaseURI);
    }

    function setMiner(address _miner) external onlyOwner {
        require(_miner != address(0), "miner address is zero");
        miners[_miner] = true;
    }

    function setMinering(uint256 tokenId, bool _minering) external {
        require(miners[msg.sender], "Only miner can set mining status");
        minering[tokenId] = _minering;
    }

    // 获取NFT挖矿状态
    function isMinering(uint256 tokenId) external view returns (bool) {
        require(tokenId > 0 && tokenId < nextTokenId, "Token does not exist");
        return minering[tokenId];
    }

    // 设置释放参数
    function setReleaseParameters(
        uint256 _startTime,
        uint256 _interval
    ) external onlyOwner {
        require(_interval > 0, "Invalid release interval");

        releaseStartTime = _startTime;
        releaseInterval = _interval;

        emit ReleaseParametersUpdated(_startTime, _interval, 0);
    }

    // 设置释放比例数组 - 优化循环和检查
    function setReleasePercentages(
        uint256[12] calldata _percentages
    ) external onlyOwner {
        uint256 totalPercentage = 0;
        
        // 优化循环，先检查后设置
        for (uint256 i; i < TOTAL_RELEASE_PERIODS;) {
            require(_percentages[i] > 0, "Percentage must be greater than 0");
            totalPercentage += _percentages[i];
            unchecked { ++i; }
        }
        require(totalPercentage == 100, "Total percentage must equal 100");
        
        // 批量设置以减少存储操作
        for (uint256 i; i < TOTAL_RELEASE_PERIODS;) {
            releasePercentages[i] = _percentages[i];
            unchecked { ++i; }
        }
    }

    // 批量设置NFT的个性化开始释放时间 - 优化循环和检查
    function batchSetNFTReleaseStartTime(
        uint256[] calldata tokenIds,
        uint256[] calldata startTimes
    ) external onlyOwner {
        uint256 arraysLength = tokenIds.length;
        require(arraysLength == startTimes.length && arraysLength > 0, "Invalid arrays");
        require(arraysLength <= 100, "Too many tokens"); // 防止gas limit

        // 优化循环，先检查后执行
        for (uint256 i; i < arraysLength;) {
            uint256 tokenId = tokenIds[i];
            uint256 startTime = startTimes[i];

            require(
                tokenId > 0 && tokenId < nextTokenId && startTime > 0,
                "Invalid token or start time"
            );
            unchecked { ++i; }
        }
        
        // 批量更新
        for (uint256 i; i < arraysLength;) {
            uint256 tokenId = tokenIds[i];
            uint256 startTime = startTimes[i];
            
            nftInfo[tokenId].releaseStartTime = startTime;
            nftInfo[tokenId].lastReleaseTime = startTime;
            unchecked { ++i; }
        }

        emit BatchNFTReleaseTimeUpdated(tokenIds, startTimes);
    }

    // 设置单个NFT的个性化开始释放时间
    function setNFTReleaseStartTime(
        uint256 tokenId,
        uint256 startTime
    ) external onlyOwner {
        require(tokenId > 0 && tokenId < nextTokenId, "Token does not exist");
        require(startTime > 0, "Invalid start time");

        nftInfo[tokenId].releaseStartTime = startTime;
        nftInfo[tokenId].lastReleaseTime = startTime;

        emit NFTReleaseTimeUpdated(tokenId, startTime);
    }

    // 获取NFT的个性化开始释放时间
    function getNFTReleaseStartTime(
        uint256 tokenId
    ) external view returns (uint256) {
        require(tokenId > 0 && tokenId < nextTokenId, "Token does not exist");
        return nftInfo[tokenId].releaseStartTime;
    }

    // 铸造NFT (单个)
    function mint(address to) external onlyOwner whenNotPaused {
        require(to != address(0), "Cannot mint to zero address");
        require(nextTokenId <= MAX_SUPPLY, "Max supply reached");

        uint256 tokenId = nextTokenId;
        nextTokenId++;

        _safeMint(to, tokenId);

        // 初始化NFT信息
        nftInfo[tokenId] = NFTInfo({
            totalTokens: TOKENS_PER_NFT,
            releasedTokens: 0,
            lastReleaseTime: releaseStartTime,
            releaseStartTime: releaseStartTime
        });

        emit NFTMinted(to, tokenId);
    }

    // 批量铸造NFT - 优化循环和检查
    function batchMint(
        address[] calldata recipients
    ) external onlyOwner whenNotPaused {
        uint256 recipientsLength = recipients.length;
        require(recipientsLength > 0, "Empty recipients array");
        require(recipientsLength <= 50, "Too many recipients"); // 防止gas limit
        require(
            nextTokenId + recipientsLength <= MAX_SUPPLY + 1,
            "Exceeds max supply"
        );

        // 预检查所有接收者
        for (uint256 i; i < recipientsLength;) {
            require(recipients[i] != address(0), "Cannot mint to zero address");
            unchecked { ++i; }
        }
        
        // 批量铸造和初始化
        uint256 startTokenId = nextTokenId;
        for (uint256 i; i < recipientsLength;) {
            uint256 tokenId = startTokenId + i;
            
            _safeMint(recipients[i], tokenId);

            // 初始化NFT信息
            nftInfo[tokenId] = NFTInfo({
                totalTokens: TOKENS_PER_NFT,
                releasedTokens: 0,
                lastReleaseTime: releaseStartTime,
                releaseStartTime: releaseStartTime
            });

            emit NFTMinted(recipients[i], tokenId);
            unchecked { ++i; }
        }
        
        nextTokenId += recipientsLength;
    }

    // 批量铸造NFT - 优化循环
    function batchMintCount(
        address recipient,
        uint128 count
    ) external onlyOwner whenNotPaused {
        require(count > 0 && count <= 50, "Invalid count"); // 防止gas limit
        require(recipient != address(0), "Invalid address");
        require(nextTokenId + count <= MAX_SUPPLY + 1, "Exceeds max supply");

        uint256 startTokenId = nextTokenId;
        
        for (uint256 i; i < count;) {
            uint256 tokenId = startTokenId + i;

            _safeMint(recipient, tokenId);

            // 初始化NFT信息
            nftInfo[tokenId] = NFTInfo({
                totalTokens: TOKENS_PER_NFT,
                releasedTokens: 0,
                lastReleaseTime: releaseStartTime,
                releaseStartTime: releaseStartTime
            });

            emit NFTMinted(recipient, tokenId);
            unchecked { ++i; }
        }
        
        nextTokenId += count;
    }

    // 计算NFT可释放的代币数量
    function getReleasableAmount(
        uint256 tokenId
    ) public view returns (uint256) {
        require(tokenId > 0 && tokenId < nextTokenId, "Token does not exist");

        NFTInfo storage nft = nftInfo[tokenId];
        uint256 nftReleaseStartTime = nft.releaseStartTime;

        if (block.timestamp < nftReleaseStartTime) {
            return 0;
        }

        // 防止时间下溢
        if (block.timestamp < nft.lastReleaseTime) {
            return 0;
        }

        // 计算从该NFT开始释放时间到现在经过的总期数
        uint256 timeSinceStart = block.timestamp - nftReleaseStartTime;
        uint256 totalPeriodsElapsed = timeSinceStart / releaseInterval;

        // 限制在12个月内
        if (totalPeriodsElapsed >= TOTAL_RELEASE_PERIODS) {
            totalPeriodsElapsed = TOTAL_RELEASE_PERIODS;
        }

        if (totalPeriodsElapsed == 0) {
            return 0;
        }

        // 计算到当前期数应该释放的总代币数 - 优化循环
        uint256 shouldHaveReleasedTotal = 0;
        uint256 maxReleasableTokens = nft.totalTokens;

        for (uint256 i; i < totalPeriodsElapsed;) {
            shouldHaveReleasedTotal +=
                (maxReleasableTokens * releasePercentages[i]) /
                RATE_DIVISOR;
            unchecked { ++i; }
        }

        // 确保不超过总代币数
        if (shouldHaveReleasedTotal > maxReleasableTokens) {
            shouldHaveReleasedTotal = maxReleasableTokens;
        }

        // 减去已释放的代币数
        if (shouldHaveReleasedTotal > nft.releasedTokens) {
            return shouldHaveReleasedTotal - nft.releasedTokens;
        }

        return 0;
    }

    // 释放单个NFT的代币
    function releaseTokens(
        uint256 tokenId
    ) external nonReentrant whenNotPaused {
        require(tokenId > 0 && tokenId < nextTokenId, "Token does not exist");
        require(ownerOf(tokenId) == msg.sender, "Not token owner");

        uint256 releasableAmount = getReleasableAmount(tokenId);
        require(releasableAmount > 0, "No tokens to release");

        // 更新NFT信息
        NFTInfo storage nft = nftInfo[tokenId];
        nft.releasedTokens += releasableAmount;
        nft.lastReleaseTime = nft.releaseStartTime;

        // 转账代币给用户
        IERC20(tokenAAddress).transfer(msg.sender, releasableAmount);

        emit TokensReleased(tokenId, msg.sender, releasableAmount);
    }

    // 批量释放用户所有NFT的代币
    function batchReleaseTokens() external nonReentrant whenNotPaused {
        uint256[] memory userTokens = userNFTs[msg.sender];
        require(userTokens.length > 0, "No NFTs owned");

        uint256 totalReleasable = 0;
        uint256[] memory releasableTokenIds = new uint256[](userTokens.length);
        uint256 releasableCount = 0;

        // 计算总可释放数量 - 优化循环
        uint256 userTokensLength = userTokens.length;
        for (uint256 i; i < userTokensLength;) {
            uint256 tokenId = userTokens[i];

            // 验证用户仍然拥有该NFT
            if (ownerOf(tokenId) == msg.sender) {
                uint256 releasableAmount = getReleasableAmount(tokenId);

                if (releasableAmount > 0) {
                    totalReleasable += releasableAmount;
                    releasableTokenIds[releasableCount] = tokenId;
                    unchecked { ++releasableCount; }

                    // 更新NFT信息
                    NFTInfo storage nft = nftInfo[tokenId];
                    nft.releasedTokens += releasableAmount;
                    nft.lastReleaseTime = nft.releaseStartTime;
                }
            }
            unchecked { ++i; }
        }

        require(totalReleasable > 0, "No tokens to release");

        // 转账代币给用户
        IERC20(tokenAAddress).transfer(msg.sender, totalReleasable);

        // 创建实际释放的tokenId数组 - 优化循环
        uint256[] memory actualReleasedTokenIds = new uint256[](
            releasableCount
        );
        for (uint256 i; i < releasableCount;) {
            actualReleasedTokenIds[i] = releasableTokenIds[i];
            unchecked { ++i; }
        }

        emit BatchTokensReleased(
            msg.sender,
            actualReleasedTokenIds,
            totalReleasable
        );
    }

    // 获取用户持有的NFT数量
    function getUserNFTCount(address user) external view returns (uint256) {
        return userNFTs[user].length;
    }

    // 获取用户持有的NFT列表
    function getUserNFTs(
        address user
    ) external view returns (uint256[] memory) {
        return userNFTs[user];
    }

    // 获取用户总的可释放代币数量
    function getUserTotalReleasableAmount(
        address user
    ) external view returns (uint256) {
        uint256[] memory userTokens = userNFTs[user];
        uint256 totalReleasable = 0;

        uint256 userTokensLength = userTokens.length;
        for (uint256 i; i < userTokensLength;) {
            uint256 tokenId = userTokens[i];

            // 验证用户仍然拥有该NFT
            if (ownerOf(tokenId) == user) {
                totalReleasable += getReleasableAmount(tokenId);
            }
            unchecked { ++i; }
        }

        return totalReleasable;
    }

    // 获取释放计划详情
    function getReleaseSchedule()
        external
        view
        returns (
            uint256[12] memory percentages,
            uint256 startTime,
            uint256 interval,
            uint256 totalPeriods
        )
    {
        return (
            releasePercentages,
            releaseStartTime,
            releaseInterval,
            TOTAL_RELEASE_PERIODS
        );
    }

    // 获取NFT的释放详细信息
    function getNFTReleaseInfo(
        uint256 tokenId
    )
        external
        view
        returns (
            uint256 totalTokens,
            uint256 releasedTokens,
            uint256 releasableNow,
            uint256 currentPeriod,
            uint256 nextReleaseTime
        )
    {
        require(tokenId > 0 && tokenId < nextTokenId, "Token does not exist");

        NFTInfo storage nft = nftInfo[tokenId];
        totalTokens = nft.totalTokens;
        releasedTokens = nft.releasedTokens;
        releasableNow = getReleasableAmount(tokenId);

        uint256 nftReleaseStartTime = nft.releaseStartTime;
        if (block.timestamp >= nftReleaseStartTime) {
            uint256 timeSinceStart = block.timestamp - nftReleaseStartTime;
            currentPeriod = timeSinceStart / releaseInterval;
            if (currentPeriod >= TOTAL_RELEASE_PERIODS) {
                currentPeriod = TOTAL_RELEASE_PERIODS - 1;
            }
            nextReleaseTime =
                nftReleaseStartTime +
                ((currentPeriod + 1) * releaseInterval);
        } else {
            currentPeriod = 0;
            nextReleaseTime = nftReleaseStartTime;
        }
    }

    // 获取所有NFT持有者信息 (用于TokenA分红计算)
    function getAllHolders()
        external
        view
        returns (address[] memory holders, uint256[] memory balances)
    {
        uint256 totalHolders = 0;
        address[] memory tempHolders = new address[](MAX_SUPPLY);
        uint256[] memory tempBalances = new uint256[](MAX_SUPPLY);

        // 遍历所有地址，统计持有者 - 优化循环
        for (uint256 i = 1; i < nextTokenId;) {
            try this.ownerOf(i) returns (address holder) {
                // 查找是否已经在列表中
                bool found = false;
                for (uint256 j; j < totalHolders;) {
                    if (tempHolders[j] == holder) {
                        unchecked { ++tempBalances[j]; }
                        found = true;
                        break;
                    }
                    unchecked { ++j; }
                }

                // 如果不在列表中，添加新持有者
                if (!found) {
                    tempHolders[totalHolders] = holder;
                    tempBalances[totalHolders] = 1;
                    unchecked { ++totalHolders; }
                }
            } catch {
                // Token不存在，跳过
            }
            unchecked { ++i; }
        }

        // 创建正确大小的数组
        holders = new address[](totalHolders);
        balances = new uint256[](totalHolders);

        for (uint256 i; i < totalHolders;) {
            holders[i] = tempHolders[i];
            balances[i] = tempBalances[i];
            unchecked { ++i; }
        }
    }

    // 重写函数以支持ERC721Enumerable
    function _increaseBalance(
        address account,
        uint128 value
    ) internal override(ERC721, ERC721Enumerable) {
        super._increaseBalance(account, value);
    }

    function _update(
        address to,
        uint256 tokenId,
        address auth
    ) internal override(ERC721, ERC721Enumerable) returns (address) {
        require(
            !minering[tokenId],
            "NFT is currently mining and cannot be transferred"
        );
        address from = super._update(to, tokenId, auth);

        // NFT转移时自动触发分红领取
        if (from != address(0) && from != to) {
            _claimDividendOnTransfer(from, tokenId);
        }

        // 更新用户NFT列表
        if (from != address(0)) {
            _removeTokenFromUser(from, tokenId);
        }

        if (to != address(0)) {
            userNFTs[to].push(tokenId);
        }

        return from;
    }

    // 从用户NFT列表中移除token - 优化搜索
    function _removeTokenFromUser(address user, uint256 tokenId) private {
        uint256[] storage tokens = userNFTs[user];
        uint256 tokensLength = tokens.length;
        
        for (uint256 i; i < tokensLength;) {
            if (tokens[i] == tokenId) {
                // 用最后一个元素替换当前元素，然后删除最后一个
                unchecked {
                    tokens[i] = tokens[tokensLength - 1];
                }
                tokens.pop();
                break;
            }
            unchecked { ++i; }
        }
    }

    // 紧急暂停/恢复功能
    function pause() external onlyOwner {
        _pause();
    }

    function unpause() external onlyOwner {
        _unpause();
    }

    // 紧急提取合约中的代币 (仅owner)
    function emergencyWithdraw() external onlyOwner {
        uint256 balance = IERC20(tokenAAddress).balanceOf(address(this));
        require(balance > 0, "No balance to withdraw");
        
        // 安全转账
        require(IERC20(tokenAAddress).transfer(owner(), balance), "Transfer failed");
    }

    // 获取基础URI
    function _baseURI() internal view override returns (string memory) {
        return _baseTokenURI;
    }

    // NFT转移时自动领取分红
    function _claimDividendOnTransfer(
        address /* from */,
        uint256 tokenId
    ) private {
        // 只有TokenA地址设置了才能进行分红领取
        if (tokenAAddress == address(0)) return;

        try ITokenADividend(tokenAAddress).claimNFTDividend(tokenId) {
            // 分红领取成功
        } catch {
            // 忽略分红领取失败，继续转移
        }
    }

    // 手动触发NFT分红领取（用户可调用）
    function claimNFTDividend(uint256 tokenId) external {
        require(ownerOf(tokenId) == msg.sender, "Not the owner of this NFT");
        require(tokenAAddress != address(0), "TokenA address not set");

        ITokenADividend(tokenAAddress).claimNFTDividend(tokenId);
    }

    // 批量领取多个NFT的分红 - 优化循环
    function batchClaimNFTDividends(uint256[] calldata tokenIds) external {
        require(tokenAAddress != address(0), "TokenA address not set");
        require(tokenIds.length > 0 && tokenIds.length <= 50, "Invalid token array"); // 防止gas limit

        uint256 tokenIdsLength = tokenIds.length;
        for (uint256 i; i < tokenIdsLength;) {
            require(ownerOf(tokenIds[i]) == msg.sender, "Not the owner of NFT");

            try ITokenADividend(tokenAAddress).claimNFTDividend(tokenIds[i]) {
                // 分红领取成功
            } catch {
                // 忽略单个NFT的分红领取失败，继续处理下一个
            }
            unchecked { ++i; }
        }
    }

    // 重写supportsInterface
    function supportsInterface(
        bytes4 interfaceId
    ) public view override(ERC721, ERC721Enumerable) returns (bool) {
        return super.supportsInterface(interfaceId);
    }
}

// TokenA分红接口
interface ITokenADividend {
    function claimNFTDividend(uint256 tokenId) external;
}
