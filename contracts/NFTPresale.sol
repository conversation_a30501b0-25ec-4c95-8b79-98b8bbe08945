// SPDX-License-Identifier: MIT
pragma solidity ^0.8.20;

import "@openzeppelin/contracts/token/ERC20/IERC20.sol";
import "@openzeppelin/contracts/utils/ReentrancyGuard.sol";
import "@openzeppelin/contracts/access/Ownable.sol";
import "@openzeppelin/contracts/utils/Pausable.sol";

/**
 * @title NFTPresale
 * @dev Gas优化的NFT预售合约：
 * - 每个地址可以购买多个NFT
 * - 固定价格 1.5 USDT
 * - 邀请关系记录
 * - USDT自动转账到指定收款地址
 * - 无时间限制，简化逻辑
 */
contract NFTPresale is ReentrancyGuard, Ownable, Pausable {
    // 使用immutable节省gas
    IERC20 public immutable usdtToken;
    uint256 public constant NFT_PRICE = 15000 * 10 ** 18; // 1.5w USDT (18 decimals)
    uint256 public constant MAX_SUPPLY = 500; // 最大供应量

    // 状态变量打包以节省存储slot
    address public treasuryWallet; // 收款钱包地址
    bool public presaleActive; // 预售状态
    uint256 public totalSold; // 总销量
    uint256 public totalParticipants; // 总参与者数量

    // 用户数据映射
    mapping(address => uint256) public userPurchased; // 用户购买数量
    mapping(address => address) public userReferrer; // 用户推荐人
    mapping(address => uint256) public referrerCount; // 推荐人数量统计
    mapping(address => bool) private isParticipant; // 参与者标记

    // 推荐关系存储
    mapping(address => address[]) public referrerToReferees; // 推荐人 => 被推荐人列表

    // 精简事件
    event NFTPurchased(
        address indexed buyer,
        address indexed referrer,
        uint256 quantity,
        uint256 totalAmount
    );
    event ReferralRecorded(address indexed referrer, address indexed referee);
    event PresaleStatusChanged(bool active);
    event TreasuryWalletUpdated(
        address indexed oldWallet,
        address indexed newWallet
    );

    constructor(
        address _usdtToken,
        address _treasuryWallet
    ) Ownable(msg.sender) {
        require(_usdtToken != address(0), "Invalid USDT address");
        require(
            _treasuryWallet != address(0),
            "Invalid treasury wallet address"
        );
        usdtToken = IERC20(_usdtToken);
        treasuryWallet = _treasuryWallet;
        presaleActive = true; // 默认激活，无时间限制
    }

    /**
     * @dev 设置收款钱包地址
     */
    function setTreasuryWallet(address _treasuryWallet) external onlyOwner {
        require(
            _treasuryWallet != address(0),
            "Invalid treasury wallet address"
        );
        address oldWallet = treasuryWallet;
        treasuryWallet = _treasuryWallet;
        emit TreasuryWalletUpdated(oldWallet, _treasuryWallet);
    }

    /**
     * @dev 切换预售状态
     */
    function togglePresale() external onlyOwner {
        presaleActive = !presaleActive;
        emit PresaleStatusChanged(presaleActive);
    }

    /**
     * @dev 购买NFT - 支持购买多个
     * @param referrer 邀请人地址
     * @param quantity 购买数量
     */
    function buyNFT(
        address referrer,
        uint256 quantity
    ) external nonReentrant whenNotPaused {
        require(presaleActive, "Presale not active");
        require(quantity > 0, "Quantity must be greater than 0");

        // 检查供应量，使用unchecked节省gas
        uint256 newTotalSold;
        unchecked {
            newTotalSold = totalSold + quantity;
        }
        require(newTotalSold <= MAX_SUPPLY, "Exceeds max supply");

        // 记录邀请关系（只在首次购买时设置）
        if (
            referrer != address(0) &&
            referrer != msg.sender &&
            userReferrer[msg.sender] == address(0)
        ) {
            userReferrer[msg.sender] = referrer;
            referrerToReferees[referrer].push(msg.sender);
            unchecked {
                referrerCount[referrer]++;
            }
            emit ReferralRecorded(referrer, msg.sender);
        }

        // 计算总金额，使用unchecked节省gas
        uint256 totalAmount;
        unchecked {
            totalAmount = NFT_PRICE * quantity;
        }

        // 直接转账USDT到收款钱包
        require(
            usdtToken.transferFrom(msg.sender, treasuryWallet, totalAmount),
            "Transfer failed"
        );

        // 更新状态
        unchecked {
            userPurchased[msg.sender] += quantity;
            totalSold = newTotalSold;
        }

        // 添加到参与者列表（只在首次购买时添加）
        if (!isParticipant[msg.sender]) {
            isParticipant[msg.sender] = true;
            unchecked {
                totalParticipants++;
            }
        }

        emit NFTPurchased(
            msg.sender,
            userReferrer[msg.sender],
            quantity,
            totalAmount
        );

        // 检查是否售罄
        if (newTotalSold >= MAX_SUPPLY) {
            presaleActive = false;
            emit PresaleStatusChanged(false);
        }
    }

    /**
     * @dev 单个购买NFT（向后兼容）
     * @param referrer 邀请人地址
     */
    function buyNFT(address referrer) external nonReentrant whenNotPaused {
        require(presaleActive, "Presale not active");
        require(totalSold < MAX_SUPPLY, "Sold out");

        // 记录邀请关系（只在首次购买时设置）
        if (
            referrer != address(0) &&
            referrer != msg.sender &&
            userReferrer[msg.sender] == address(0)
        ) {
            userReferrer[msg.sender] = referrer;
            referrerToReferees[referrer].push(msg.sender);
            unchecked {
                referrerCount[referrer]++;
            }
            emit ReferralRecorded(referrer, msg.sender);
        }

        // 直接转账USDT到收款钱包
        require(
            usdtToken.transferFrom(msg.sender, treasuryWallet, NFT_PRICE),
            "Transfer failed"
        );

        // 更新状态
        unchecked {
            userPurchased[msg.sender]++;
            totalSold++;
        }

        // 添加到参与者列表（只在首次购买时添加）
        if (!isParticipant[msg.sender]) {
            isParticipant[msg.sender] = true;
            unchecked {
                totalParticipants++;
            }
        }

        emit NFTPurchased(msg.sender, userReferrer[msg.sender], 1, NFT_PRICE);

        // 检查是否售罄
        if (totalSold >= MAX_SUPPLY) {
            presaleActive = false;
            emit PresaleStatusChanged(false);
        }
    }

    /**
     * @dev 紧急提取资金（仅用于紧急情况）
     */
    function emergencyWithdraw() external onlyOwner nonReentrant {
        uint256 contractBalance = usdtToken.balanceOf(address(this));
        require(contractBalance > 0, "No funds to withdraw");
        require(
            usdtToken.transfer(owner(), contractBalance),
            "Transfer failed"
        );
    }

    /**
     * @dev 暂停/恢复合约
     */
    function setPaused(bool _paused) external onlyOwner {
        if (_paused) {
            _pause();
        } else {
            _unpause();
        }
    }

    // ==================== 查询函数 ====================

    /**
     * @dev 获取预售基本信息
     */
    function getPresaleInfo()
        external
        view
        returns (
            uint256 _totalSold,
            uint256 _totalParticipants,
            uint256 _maxSupply,
            uint256 _nftPrice,
            bool _active
        )
    {
        return (
            totalSold,
            totalParticipants,
            MAX_SUPPLY,
            NFT_PRICE,
            presaleActive
        );
    }

    /**
     * @dev 获取用户信息
     */
    function getUserInfo(
        address user
    )
        external
        view
        returns (uint256 purchased, address referrer, uint256 refereeCount)
    {
        return (userPurchased[user], userReferrer[user], referrerCount[user]);
    }

    /**
     * @dev 检查是否可以购买指定数量
     */
    function canPurchase(uint256 quantity) external view returns (bool) {
        return
            presaleActive && quantity > 0 && totalSold + quantity <= MAX_SUPPLY;
    }

    /**
     * @dev 获取剩余NFT数量
     */
    function getRemainingSupply() external view returns (uint256) {
        unchecked {
            return MAX_SUPPLY - totalSold;
        }
    }

    /**
     * @dev 获取收款钱包地址
     */
    function getTreasuryWallet() external view returns (address) {
        return treasuryWallet;
    }

    /**
     * @dev 检查用户是否已购买
     */
    function hasPurchased(address user) external view returns (bool) {
        return userPurchased[user] > 0;
    }

    /**
     * @dev 获取推荐人的被推荐人列表
     * @param referrer 推荐人地址
     */
    function getReferees(
        address referrer
    ) external view returns (address[] memory) {
        return referrerToReferees[referrer];
    }

    /**
     * @dev 获取推荐人的被推荐人列表（分页）
     * @param referrer 推荐人地址
     * @param start 开始索引
     * @param limit 返回数量限制
     */
    function getReferees(
        address referrer,
        uint256 start,
        uint256 limit
    ) external view returns (address[] memory) {
        address[] memory allReferees = referrerToReferees[referrer];

        if (start >= allReferees.length) {
            return new address[](0);
        }

        uint256 end = start + limit;
        if (end > allReferees.length) {
            end = allReferees.length;
        }

        address[] memory result = new address[](end - start);
        for (uint256 i = start; i < end; i++) {
            result[i - start] = allReferees[i];
        }

        return result;
    }

    /**
     * @dev 获取推荐人的推荐详情（包含被推荐人的购买信息）
     * @param referrer 推荐人地址
     */
    function getReferralDetails(
        address referrer
    )
        external
        view
        returns (
            address[] memory referees,
            uint256[] memory purchasedAmounts,
            uint256 totalReferees,
            uint256 totalPurchasedByReferees
        )
    {
        address[] memory allReferees = referrerToReferees[referrer];
        uint256[] memory amounts = new uint256[](allReferees.length);
        uint256 totalPurchased = 0;

        for (uint256 i = 0; i < allReferees.length; i++) {
            amounts[i] = userPurchased[allReferees[i]];
            unchecked {
                totalPurchased += amounts[i];
            }
        }

        return (allReferees, amounts, allReferees.length, totalPurchased);
    }
}
