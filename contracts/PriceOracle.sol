// SPDX-License-Identifier: MIT
pragma solidity ^0.8.20;

import "@openzeppelin/contracts/access/Ownable.sol";
import "@openzeppelin/contracts/utils/ReentrancyGuard.sol";
import "./interfaces/IPriceOracle.sol";

// PancakeSwap V2接口
interface IPancakeFactory {
    function getPair(
        address tokenA,
        address tokenB
    ) external view returns (address pair);
}

interface IPancakePair {
    function getReserves()
        external
        view
        returns (uint112 reserve0, uint112 reserve1, uint32 blockTimestampLast);

    function token0() external view returns (address);

    function token1() external view returns (address);
}

// PancakeSwap价格预言机合约
contract PriceOracle is IPriceOracle, Ownable, ReentrancyGuard {
    // 常量定义
    address public constant WBNB = 0xBb4CDb9Cbd36B01Bd1CBAabF5C4d96b92B0e0f5f; // BSC主网WBNB地址
    address public constant PANCAKE_FACTORY =
        0xcA143Ce32Fe78f1f7019d7d551a6402fC5350c73; // PancakeSwap V2 Factory

    // 价格缓存和保护
    struct PriceInfo {
        uint256 price;
        uint256 lastUpdate;
        uint256 cumulativePrice;
        uint32 blockTimestamp;
    }

    mapping(address => PriceInfo) public priceCache;

    // 价格保护参数
    uint256 public maxPriceDeviation = 5000; // 最大价格偏差 50% (5000/10000)
    uint256 public constant PRICE_DEVIATION_BASE = 10000;
    uint256 public minUpdateInterval = 300; // 最小更新间隔 5分钟
    uint256 public maxPriceAge = 3600; // 最大价格有效期 1小时

    // 价格操纵保护
    uint256 public constant MIN_LIQUIDITY = 100 * 1e18; // 最小流动性要求 (1000 BNB)

    // 事件定义
    event PriceUpdated(address indexed token, uint256 price, uint256 timestamp);
    event PriceParametersUpdated(
        uint256 maxDeviation,
        uint256 minInterval,
        uint256 maxAge
    );

    constructor() Ownable(msg.sender) {}

    // 获取Token相对于BNB的价格
    function getTokenPrice(
        address token
    ) external view override returns (uint256) {
        require(token != address(0), "Invalid token address");

        if (token == WBNB) {
            return 1e18; // WBNB价格为1
        }

        // 先尝试从缓存获取
        PriceInfo storage cached = priceCache[token];
        if (
            cached.price > 0 &&
            (block.timestamp - cached.lastUpdate) < maxPriceAge
        ) {
            return cached.price;
        }

        // 从PancakeSwap获取实时价格
        return _getTokenPriceFromPancakeSwap(token);
    }

    // 从PancakeSwap获取价格
    function _getTokenPriceFromPancakeSwap(
        address token
    ) private view returns (uint256) {
        address pair = IPancakeFactory(PANCAKE_FACTORY).getPair(token, WBNB);
        require(pair != address(0), "Pair does not exist");

        (uint112 reserve0, uint112 reserve1, ) = IPancakePair(pair)
            .getReserves();
        require(reserve0 > 0 && reserve1 > 0, "Insufficient liquidity");

        address token0 = IPancakePair(pair).token0();

        uint256 tokenReserve;
        uint256 wbnbReserve;

        if (token0 == token) {
            tokenReserve = uint256(reserve0);
            wbnbReserve = uint256(reserve1);
        } else {
            tokenReserve = uint256(reserve1);
            wbnbReserve = uint256(reserve0);
        }

        // 检查最小流动性
        require(
            wbnbReserve >= MIN_LIQUIDITY,
            "Insufficient liquidity for reliable pricing"
        );

        // 计算价格 (tokenPrice = wbnbReserve * 1e18 / tokenReserve)
        return (wbnbReserve * 1e18) / tokenReserve;
    }

    // 获取两个Token的价格比率 (tokenA/tokenB * 1e18)
    function getTokenPriceRatio(
        address tokenA,
        address tokenB
    ) external view override returns (uint256) {
        uint256 priceA = this.getTokenPrice(tokenA);
        uint256 priceB = this.getTokenPrice(tokenB);

        require(priceB > 0, "Invalid tokenB price");

        return (priceA * 1e18) / priceB;
    }

    // 更新价格缓存 (带价格操纵保护)
    function updatePrice(address token) external nonReentrant {
        require(token != address(0), "Invalid token address");
        require(token != WBNB, "Cannot update WBNB price");

        PriceInfo storage cached = priceCache[token];

        // 检查更新频率限制
        require(
            block.timestamp - cached.lastUpdate >= minUpdateInterval,
            "Update too frequent"
        );

        uint256 newPrice = _getTokenPriceFromPancakeSwap(token);

        // 价格偏差保护
        if (cached.price > 0) {
            uint256 deviation = newPrice > cached.price
                ? ((newPrice - cached.price) * PRICE_DEVIATION_BASE) /
                    cached.price
                : ((cached.price - newPrice) * PRICE_DEVIATION_BASE) /
                    cached.price;

            require(
                deviation <= maxPriceDeviation,
                "Price deviation too large"
            );
        }

        // 更新价格缓存
        cached.price = newPrice;
        cached.lastUpdate = block.timestamp;

        emit PriceUpdated(token, newPrice, block.timestamp);
    }

    // 批量更新价格
    function batchUpdatePrices(
        address[] calldata tokens
    ) external nonReentrant {
        require(tokens.length > 0, "Empty token array");
        require(tokens.length <= 10, "Too many tokens"); // 限制批量数量

        for (uint256 i = 0; i < tokens.length; i++) {
            address token = tokens[i];
            if (token == address(0) || token == WBNB) continue;

            PriceInfo storage cached = priceCache[token];

            // 检查更新频率限制
            if (block.timestamp - cached.lastUpdate < minUpdateInterval)
                continue;

            try this._updateSinglePrice(token) {
                // 更新成功
            } catch {
                // 忽略单个token的更新错误，继续处理下一个
                continue;
            }
        }
    }

    // 内部单个价格更新函数
    function _updateSinglePrice(address token) external {
        require(msg.sender == address(this), "Internal function");

        uint256 newPrice = _getTokenPriceFromPancakeSwap(token);
        PriceInfo storage cached = priceCache[token];

        // 价格偏差保护
        if (cached.price > 0) {
            uint256 deviation = newPrice > cached.price
                ? ((newPrice - cached.price) * PRICE_DEVIATION_BASE) /
                    cached.price
                : ((cached.price - newPrice) * PRICE_DEVIATION_BASE) /
                    cached.price;

            require(
                deviation <= maxPriceDeviation,
                "Price deviation too large"
            );
        }

        // 更新价格缓存
        cached.price = newPrice;
        cached.lastUpdate = block.timestamp;

        emit PriceUpdated(token, newPrice, block.timestamp);
    }

    // 获取缓存的价格信息
    function getCachedPrice(
        address token
    ) external view returns (uint256 price, uint256 lastUpdate, bool isValid) {
        PriceInfo storage cached = priceCache[token];
        bool valid = cached.price > 0 &&
            (block.timestamp - cached.lastUpdate) < maxPriceAge;

        return (cached.price, cached.lastUpdate, valid);
    }

    // 检查价格是否可靠
    function isPriceReliable(address token) external view returns (bool) {
        if (token == WBNB) return true;

        address pair = IPancakeFactory(PANCAKE_FACTORY).getPair(token, WBNB);
        if (pair == address(0)) return false;

        (uint112 reserve0, uint112 reserve1, ) = IPancakePair(pair)
            .getReserves();
        if (reserve0 == 0 || reserve1 == 0) return false;

        address token0 = IPancakePair(pair).token0();
        uint256 wbnbReserve = (token0 == token)
            ? uint256(reserve1)
            : uint256(reserve0);

        return wbnbReserve >= MIN_LIQUIDITY;
    }

    // 设置价格保护参数
    function setPriceParameters(
        uint256 _maxPriceDeviation,
        uint256 _minUpdateInterval,
        uint256 _maxPriceAge
    ) external onlyOwner {
        require(
            _maxPriceDeviation <= PRICE_DEVIATION_BASE,
            "Invalid max deviation"
        );
        require(_minUpdateInterval > 0, "Invalid min interval");
        require(_maxPriceAge > _minUpdateInterval, "Invalid max age");

        maxPriceDeviation = _maxPriceDeviation;
        minUpdateInterval = _minUpdateInterval;
        maxPriceAge = _maxPriceAge;

        emit PriceParametersUpdated(
            _maxPriceDeviation,
            _minUpdateInterval,
            _maxPriceAge
        );
    }

    // 手动设置价格 (紧急情况使用)
    function setManualPrice(address token, uint256 price) external onlyOwner {
        require(token != address(0) && token != WBNB, "Invalid token");
        require(price > 0, "Invalid price");

        priceCache[token].price = price;
        priceCache[token].lastUpdate = block.timestamp;

        emit PriceUpdated(token, price, block.timestamp);
    }

    // 清除价格缓存
    function clearPriceCache(address token) external onlyOwner {
        delete priceCache[token];
    }

    // 获取交易对地址
    function getPairAddress(
        address tokenA,
        address tokenB
    ) external view returns (address) {
        return IPancakeFactory(PANCAKE_FACTORY).getPair(tokenA, tokenB);
    }

    // 获取交易对储备量
    function getPairReserves(
        address tokenA,
        address tokenB
    )
        external
        view
        returns (uint256 reserveA, uint256 reserveB, uint32 blockTimestampLast)
    {
        address pair = IPancakeFactory(PANCAKE_FACTORY).getPair(tokenA, tokenB);
        require(pair != address(0), "Pair does not exist");

        (uint112 reserve0, uint112 reserve1, uint32 timestamp) = IPancakePair(
            pair
        ).getReserves();

        address token0 = IPancakePair(pair).token0();

        if (token0 == tokenA) {
            return (uint256(reserve0), uint256(reserve1), timestamp);
        } else {
            return (uint256(reserve1), uint256(reserve0), timestamp);
        }
    }
}
