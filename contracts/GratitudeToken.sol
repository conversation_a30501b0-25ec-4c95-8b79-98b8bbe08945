// SPDX-License-Identifier: MIT
pragma solidity ^0.8.20;

import "@openzeppelin/contracts/token/ERC20/ERC20.sol";
import "@openzeppelin/contracts/access/Ownable.sol";
import "@openzeppelin/contracts/utils/ReentrancyGuard.sol";
import "@openzeppelin/contracts/utils/Pausable.sol";

/**
 * @title Gratitude Protocol Token (GRAT)
 * @dev 简单的ERC20代币合约，包含基本的管理功能
 * 代币名称: Gratitude Protocol
 * 代币符号: GRAT
 * 总供应量: 250,000,000 (2.5亿)
 * 小数位数: 18
 */
contract GratitudeToken is ERC20, Ownable, ReentrancyGuard, Pausable {
    // 常量定义
    uint256 public constant TOTAL_SUPPLY = 250_000_000 * 1e18; // 2.5亿枚代币
    uint256 public constant MAX_SUPPLY = TOTAL_SUPPLY; // 最大供应量限制
    
    // 铸造权限控制
    mapping(address => bool) public minters;

    // 事件定义
    event MinterAdded(address indexed minter);
    event MinterRemoved(address indexed minter);
    event TokensBurned(address indexed from, uint256 amount);

    /**
     * @dev 构造函数
     * @param _initialOwner 初始所有者地址
     */
    constructor(address _initialOwner) 
        ERC20("Gratitude Protocol", "GRAT") 
        Ownable(_initialOwner) 
    {
        require(_initialOwner != address(0), "Initial owner cannot be zero address");
        
        // 将总供应量铸造给初始所有者
        _mint(_initialOwner, TOTAL_SUPPLY);
        
        // 设置初始所有者为铸造者
        minters[_initialOwner] = true;
        emit MinterAdded(_initialOwner);
    }

    /**
     * @dev 修饰符：只有铸造者可以调用
     */
    modifier onlyMinter() {
        require(minters[msg.sender], "Caller is not a minter");
        _;
    }

    /**
     * @dev 添加铸造者
     * @param _minter 要添加的铸造者地址
     */
    function addMinter(address _minter) external onlyOwner {
        require(_minter != address(0), "Minter cannot be zero address");
        require(!minters[_minter], "Address is already a minter");
        
        minters[_minter] = true;
        emit MinterAdded(_minter);
    }

    /**
     * @dev 移除铸造者
     * @param _minter 要移除的铸造者地址
     */
    function removeMinter(address _minter) external onlyOwner {
        require(minters[_minter], "Address is not a minter");
        require(_minter != owner(), "Cannot remove owner as minter");
        
        minters[_minter] = false;
        emit MinterRemoved(_minter);
    }

    /**
     * @dev 铸造代币（仅铸造者可调用）
     * @param to 接收地址
     * @param amount 铸造数量
     */
    function mint(address to, uint256 amount) external onlyMinter whenNotPaused {
        require(to != address(0), "Cannot mint to zero address");
        require(amount > 0, "Amount must be greater than zero");
        require(totalSupply() + amount <= MAX_SUPPLY, "Would exceed max supply");
        
        _mint(to, amount);
    }

    /**
     * @dev 销毁代币
     * @param amount 销毁数量
     */
    function burn(uint256 amount) external whenNotPaused {
        require(amount > 0, "Amount must be greater than zero");
        require(balanceOf(msg.sender) >= amount, "Insufficient balance to burn");
        
        _burn(msg.sender, amount);
        emit TokensBurned(msg.sender, amount);
    }

    /**
     * @dev 从指定地址销毁代币（需要授权）
     * @param from 销毁来源地址
     * @param amount 销毁数量
     */
    function burnFrom(address from, uint256 amount) external whenNotPaused {
        require(amount > 0, "Amount must be greater than zero");
        require(from != address(0), "Cannot burn from zero address");
        
        uint256 currentAllowance = allowance(from, msg.sender);
        require(currentAllowance >= amount, "Burn amount exceeds allowance");
        
        _approve(from, msg.sender, currentAllowance - amount);
        _burn(from, amount);
        emit TokensBurned(from, amount);
    }



    /**
     * @dev 暂停合约
     */
    function pause() external onlyOwner {
        _pause();
    }

    /**
     * @dev 恢复合约
     */
    function unpause() external onlyOwner {
        _unpause();
    }

    /**
     * @dev 重写转账函数，添加暂停检查
     */
    function _update(address from, address to, uint256 amount)
        internal
        override
        whenNotPaused
    {
        super._update(from, to, amount);
    }

    /**
     * @dev 紧急提取功能（仅所有者）
     * 用于紧急情况下提取合约中的代币
     */
    function emergencyWithdraw() external onlyOwner {
        uint256 balance = balanceOf(address(this));
        require(balance > 0, "No tokens to withdraw");
        
        _transfer(address(this), owner(), balance);
    }

    /**
     * @dev 批量转账功能
     * @param recipients 接收者地址数组
     * @param amounts 对应的转账金额数组
     */
    function batchTransfer(
        address[] calldata recipients, 
        uint256[] calldata amounts
    ) external whenNotPaused nonReentrant {
        require(recipients.length == amounts.length, "Arrays length mismatch");
        require(recipients.length > 0, "Empty arrays");
        require(recipients.length <= 100, "Too many recipients"); // 限制批量转账数量
        
        uint256 totalAmount = 0;
        
        // 计算总转账金额
        for (uint256 i = 0; i < amounts.length; i++) {
            require(recipients[i] != address(0), "Cannot transfer to zero address");
            require(amounts[i] > 0, "Amount must be greater than zero");
            totalAmount += amounts[i];
        }
        
        require(balanceOf(msg.sender) >= totalAmount, "Insufficient balance");
        
        // 执行批量转账
        for (uint256 i = 0; i < recipients.length; i++) {
            _transfer(msg.sender, recipients[i], amounts[i]);
        }
    }

    /**
     * @dev 获取合约基本信息
     */
    function getTokenInfo() external view returns (
        string memory name,
        string memory symbol,
        uint8 decimals,
        uint256 totalSupply,
        uint256 maxSupply
    ) {
        return (
            name(),
            symbol(),
            decimals(),
            totalSupply(),
            MAX_SUPPLY
        );
    }

    /**
     * @dev 检查地址是否为铸造者
     */
    function isMinter(address account) external view returns (bool) {
        return minters[account];
    }
}
