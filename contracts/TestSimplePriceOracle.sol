// SPDX-License-Identifier: MIT
pragma solidity ^0.8.20;

import "@openzeppelin/contracts/access/Ownable.sol";
import "./interfaces/IPriceOracle.sol";

// PancakeSwap V2接口
interface IPancakePair {
    function getReserves()
        external
        view
        returns (uint112 reserve0, uint112 reserve1, uint32 blockTimestampLast);

    function token0() external view returns (address);

    function token1() external view returns (address);
}

interface IPancakeFactory {
    function getPair(
        address tokenA,
        address tokenB
    ) external view returns (address pair);
}

// 测试用的简单价格预言机
contract TestSimplePriceOracle is IPriceOracle, Ownable {
    // 可配置的地址
    address public wbnb;
    address public pancakeFactory;

    // 价格记录结构
    struct PriceRecord {
        uint256 price;
        uint256 timestamp;
        uint256 weight;
    }

    // 代币配置
    struct TokenConfig {
        address pairAddress;
        bool isToken0;
        bool initialized;
    }

    // 状态变量
    mapping(address => TokenConfig) public tokenConfigs;
    mapping(address => PriceRecord[]) public priceHistory;
    mapping(address => bool) public authorizedUpdaters;

    // 参数设置
    uint256 public constant MAX_PRICE_DEVIATION = 2000; // 20%最大偏差
    uint256 public constant DAMPENING_FACTOR = 1000; // 10%阻尼系数
    uint256 public constant PRICE_DEVIATION_BASE = 10000;
    uint256 public constant HISTORY_SIZE = 5; // 保留最近5次价格记录
    uint256 public constant MIN_LIQUIDITY = 1 * 1e15; // 降低最小流动性要求用于测试

    // 事件
    event PriceRecorded(
        address indexed token,
        uint256 spotPrice,
        uint256 recordedPrice,
        bool isDampened
    );
    event TokenConfigured(address indexed token, address indexed pair);
    event UpdaterAuthorized(address indexed updater, bool authorized);

    constructor(address _wbnb, address _pancakeFactory) Ownable(msg.sender) {
        wbnb = _wbnb;
        pancakeFactory = _pancakeFactory;
    }

    // 配置代币
    function configureToken(address token) external onlyOwner {
        require(token != address(0) && token != wbnb, "Invalid token");

        address pair = IPancakeFactory(pancakeFactory).getPair(token, wbnb);
        require(pair != address(0), "Pair does not exist");

        bool isToken0 = IPancakePair(pair).token0() == token;

        tokenConfigs[token] = TokenConfig({
            pairAddress: pair,
            isToken0: isToken0,
            initialized: true
        });

        // 初始化价格历史
        uint256 initialPrice = _getSpotPrice(token);
        _addPriceRecord(token, initialPrice, 100); // 初始权重100

        emit TokenConfigured(token, pair);
    }

    // 记录价格 - 由代币转账时调用
    function recordPrice(address token) external {
        require(
            authorizedUpdaters[msg.sender] || msg.sender == owner(),
            "Not authorized"
        );
        require(tokenConfigs[token].initialized, "Token not configured");

        uint256 spotPrice = _getSpotPrice(token);

        // 检查流动性
        if (!_checkLiquidity(token)) {
            return; // 流动性不足，跳过记录
        }

        uint256 recordedPrice = spotPrice;
        bool isDampened = false;

        // 如果有历史记录，检查偏差
        if (priceHistory[token].length > 0) {
            uint256 lastPrice = priceHistory[token][
                priceHistory[token].length - 1
            ].price;

            if (_isPriceDeviationTooLarge(spotPrice, lastPrice)) {
                // 偏差过大，使用阻尼价格
                recordedPrice = _calculateDampenedPrice(lastPrice, spotPrice);
                isDampened = true;
            }
        }

        // 计算权重
        uint256 weight = 100; // 基础权重

        _addPriceRecord(token, recordedPrice, weight);

        emit PriceRecorded(token, spotPrice, recordedPrice, isDampened);
    }

    // 获取代币价格 - 返回加权平均价格
    function getTokenPrice(
        address token
    ) external view override returns (uint256) {
        if (token == wbnb) {
            return 1e18;
        }

        require(tokenConfigs[token].initialized, "Token not configured");
        require(priceHistory[token].length > 0, "No price history");

        return _calculateWeightedPrice(token);
    }

    // 计算加权平均价格
    function _calculateWeightedPrice(
        address token
    ) internal view returns (uint256) {
        PriceRecord[] storage history = priceHistory[token];
        uint256 length = history.length;

        if (length == 1) {
            return history[0].price;
        }

        uint256 totalWeight = 0;
        uint256 weightedSum = 0;
        uint256 currentTime = block.timestamp;

        // 使用时间衰减权重：越新的价格权重越高
        for (uint256 i = 0; i < length; i++) {
            PriceRecord storage record = history[i];

            // 时间权重：最新的权重最高
            uint256 positionWeight = (i + 1) * 20;

            // 时间衰减
            uint256 age = currentTime - record.timestamp;
            uint256 timeWeight = age > 3600
                ? positionWeight / 2
                : positionWeight;

            uint256 finalWeight = timeWeight;

            weightedSum += record.price * finalWeight;
            totalWeight += finalWeight;
        }

        return
            totalWeight > 0
                ? weightedSum / totalWeight
                : history[length - 1].price;
    }

    // 添加价格记录
    function _addPriceRecord(
        address token,
        uint256 price,
        uint256 weight
    ) internal {
        PriceRecord[] storage history = priceHistory[token];

        // 如果历史记录满了，移除最旧的记录
        if (history.length >= HISTORY_SIZE) {
            for (uint256 i = 0; i < history.length - 1; i++) {
                history[i] = history[i + 1];
            }
            history.pop();
        }

        history.push(
            PriceRecord({
                price: price,
                timestamp: block.timestamp,
                weight: weight
            })
        );
    }

    // 检查价格偏差是否过大
    function _isPriceDeviationTooLarge(
        uint256 newPrice,
        uint256 lastPrice
    ) internal pure returns (bool) {
        if (lastPrice == 0) return false;

        uint256 deviation = newPrice > lastPrice
            ? ((newPrice - lastPrice) * PRICE_DEVIATION_BASE) / lastPrice
            : ((lastPrice - newPrice) * PRICE_DEVIATION_BASE) / lastPrice;

        return deviation > MAX_PRICE_DEVIATION;
    }

    // 计算阻尼价格
    function _calculateDampenedPrice(
        uint256 lastPrice,
        uint256 spotPrice
    ) internal pure returns (uint256) {
        if (spotPrice > lastPrice) {
            uint256 increase = ((spotPrice - lastPrice) * DAMPENING_FACTOR) /
                PRICE_DEVIATION_BASE;
            return lastPrice + increase;
        } else {
            uint256 decrease = ((lastPrice - spotPrice) * DAMPENING_FACTOR) /
                PRICE_DEVIATION_BASE;
            return lastPrice - decrease;
        }
    }

    // 检查流动性
    function _checkLiquidity(address token) internal view returns (bool) {
        TokenConfig storage config = tokenConfigs[token];
        IPancakePair pair = IPancakePair(config.pairAddress);

        (uint112 reserve0, uint112 reserve1, ) = pair.getReserves();
        uint256 wbnbReserve = config.isToken0
            ? uint256(reserve1)
            : uint256(reserve0);

        return wbnbReserve >= MIN_LIQUIDITY;
    }

    // 获取即时价格
    function _getSpotPrice(address token) internal view returns (uint256) {
        TokenConfig storage config = tokenConfigs[token];
        IPancakePair pair = IPancakePair(config.pairAddress);

        (uint112 reserve0, uint112 reserve1, ) = pair.getReserves();

        uint256 tokenReserve = config.isToken0
            ? uint256(reserve0)
            : uint256(reserve1);
        uint256 wbnbReserve = config.isToken0
            ? uint256(reserve1)
            : uint256(reserve0);

        require(tokenReserve > 0, "Zero token reserve");
        return (wbnbReserve * 1e18) / tokenReserve;
    }

    // 获取两个代币的价格比率
    function getTokenPriceRatio(
        address tokenA,
        address tokenB
    ) external view override returns (uint256) {
        uint256 priceA = this.getTokenPrice(tokenA);
        uint256 priceB = this.getTokenPrice(tokenB);
        require(priceB > 0, "Invalid tokenB price");
        return (priceA * 1e18) / priceB;
    }

    // 管理功能
    function setAuthorizedUpdater(
        address updater,
        bool authorized
    ) external onlyOwner {
        authorizedUpdaters[updater] = authorized;
        emit UpdaterAuthorized(updater, authorized);
    }

    // 紧急价格设置
    function emergencySetPrice(
        address token,
        uint256 price
    ) external onlyOwner {
        require(tokenConfigs[token].initialized, "Token not configured");
        require(price > 0, "Invalid price");

        _addPriceRecord(token, price, 100);
        emit PriceRecorded(token, price, price, false);
    }

    // 手动记录价格
    function manualRecordPrice(address token) external {
        require(tokenConfigs[token].initialized, "Token not configured");

        uint256 spotPrice = _getSpotPrice(token);

        if (!_checkLiquidity(token)) {
            return;
        }

        uint256 recordedPrice = spotPrice;
        bool isDampened = false;

        if (priceHistory[token].length > 0) {
            uint256 lastPrice = priceHistory[token][
                priceHistory[token].length - 1
            ].price;

            if (_isPriceDeviationTooLarge(spotPrice, lastPrice)) {
                recordedPrice = _calculateDampenedPrice(lastPrice, spotPrice);
                isDampened = true;
            }
        }

        _addPriceRecord(token, recordedPrice, 50); // 手动记录权重较低

        emit PriceRecorded(token, spotPrice, recordedPrice, isDampened);
    }

    // 获取价格信息
    function getPriceInfo(
        address token
    )
        external
        view
        returns (
            uint256 weightedPrice,
            uint256 spotPrice,
            uint256 lastRecordedPrice,
            uint256 priceCount,
            uint256 deviation
        )
    {
        require(tokenConfigs[token].initialized, "Token not configured");

        weightedPrice = this.getTokenPrice(token);
        spotPrice = _getSpotPrice(token);

        PriceRecord[] storage history = priceHistory[token];
        priceCount = history.length;

        if (priceCount > 0) {
            lastRecordedPrice = history[priceCount - 1].price;
            deviation = spotPrice > weightedPrice
                ? ((spotPrice - weightedPrice) * PRICE_DEVIATION_BASE) /
                    weightedPrice
                : ((weightedPrice - spotPrice) * PRICE_DEVIATION_BASE) /
                    weightedPrice;
        }
    }
}