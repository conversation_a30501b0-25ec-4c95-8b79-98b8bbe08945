// SPDX-License-Identifier: MIT
pragma solidity ^0.8.20;

import "@openzeppelin/contracts/token/ERC20/ERC20.sol";
import "@openzeppelin/contracts/access/Ownable.sol";
import "@openzeppelin/contracts/utils/ReentrancyGuard.sol";
import "@openzeppelin/contracts/utils/Pausable.sol";

// PancakeSwap接口定义
interface IPancakeRouter {
    function factory() external pure returns (address);
}

interface IPancakeFactory {
    function createPair(
        address tokenA,
        address tokenB
    ) external returns (address pair);

    function getPair(
        address tokenA,
        address tokenB
    ) external view returns (address pair);
}

// TokenA合约 - 母币，包含税收和自动分红机制
contract TokenA is ERC20, Ownable, ReentrancyGuard, Pausable {
    // 常量定义
    uint256 public constant TOTAL_SUPPLY = 250_000_000 * 1e18; // 2.5亿枚
    uint256 public constant BUY_TAX_RATE = 3; // 买入税率3%
    uint256 public constant SELL_TAX_RATE = 3; // 卖出税率3%
    uint256 public constant TAX_DIVISOR = 100; // 税率除数

    // 税收分配比例
    uint256 public constant MARKETING_SHARE = 20; // 20%进入营销钱包
    uint256 public constant BURN_SHARE = 50; // 50%销毁
    uint256 public constant DIVIDEND_SHARE = 30; // 30%分红给NFT持有者
    uint256 public constant SHARE_DIVISOR = 100; // 分配比例除数

    // 地址定义
    address public marketingWallet; // 营销钱包地址
    address public nftContract; // NFT合约地址
    address public pancakeSwapRouter; // PancakeSwap路由地址
    address public wbnbAddress; // WBNB地址

    // 税收豁免地址
    mapping(address => bool) public taxExempt;

    // 交易对地址映射
    mapping(address => bool) public isPair;

    // 分红相关 - 股息模型
    uint256 public totalDividends; // 累计分红总额
    uint256 public cumulativeDividendPerShare; // 累积每股分红
    uint256 public lastDividendTime; // 上次分红时间

    // NFT分红记录
    struct NFTDividendInfo {
        uint256 lastCumulativeDividend; // NFT记录的最后累积分红
        uint256 claimedAmount; // 已领取分红总额
    }

    mapping(uint256 => NFTDividendInfo) public nftDividendInfo; // NFT分红信息
    mapping(address => uint256) public userClaimedDividends; // 用户已领取分红（兼容旧版本）

    // 自动分红控制 - 优化存储布局
    struct AutoDividendConfig {
        uint128 gasLimit; // 自动分红Gas限制 (足够存储500000)
        uint64 maxNFTs; // 每次自动分红处理的最大NFT数量
        uint32 lastProcessedIndex; // 上次处理的NFT索引
        bool enabled; // 自动分红开关
    }

    AutoDividendConfig public autoDividendConfig =
        AutoDividendConfig({
            gasLimit: 500000,
            maxNFTs: 50,
            lastProcessedIndex: 0,
            enabled: true
        });

    uint256 public autoDividendThreshold = 1e19; // 自动分红触发阈值（0.001 ETH worth）

    // 分红提取状态 - 防止重入攻击
    mapping(address => bool) private _dividendWithdrawing;
    mapping(uint256 => bool) private _nftDividendWithdrawing;

    // 事件定义
    event TaxCollected(
        uint256 marketingAmount,
        uint256 burnAmount,
        uint256 dividendAmount
    );
    event DividendDistributed(uint256 amount);
    event DividendClaimed(address indexed user, uint256 amount);
    event TaxExemptUpdated(address indexed account, bool exempt);
    event PairUpdated(address indexed pair, bool isPair);
    event AutoDividendProcessed(
        uint256 startIndex,
        uint256 endIndex,
        uint256 totalAmount
    );
    event AutoDividendConfigUpdated(
        uint256 gasLimit,
        uint256 maxNFTs,
        uint256 threshold
    );

    constructor(
        address _marketingWallet,
        address _pancakeSwapRouter,
        address _wbnbAddress
    ) ERC20("TokenA", "TKA") Ownable(msg.sender) {
        require(
            _marketingWallet != address(0),
            "Marketing wallet cannot be zero address"
        );
        require(
            _pancakeSwapRouter != address(0),
            "Router cannot be zero address"
        );
        require(_wbnbAddress != address(0), "WBNB cannot be zero address");

        marketingWallet = _marketingWallet;
        pancakeSwapRouter = _pancakeSwapRouter;
        wbnbAddress = _wbnbAddress;

        // 设置税收豁免地址
        taxExempt[msg.sender] = true;
        taxExempt[address(this)] = true;
        taxExempt[_marketingWallet] = true;

        _createAndSetPair();

        // 铸造总供应量给部署者
        _mint(msg.sender, TOTAL_SUPPLY);
    }

    // 设置NFT合约地址
    function setNftContract(address _nftContract) external onlyOwner {
        require(
            _nftContract != address(0),
            "NFT contract cannot be zero address"
        );
        nftContract = _nftContract;
        taxExempt[_nftContract] = true;
    }

    // 设置税收豁免地址
    function setTaxExempt(address account, bool exempt) external onlyOwner {
        taxExempt[account] = exempt;
        emit TaxExemptUpdated(account, exempt);
    }

    // 设置交易对地址
    function setPair(address pair, bool _isPair) external onlyOwner {
        isPair[pair] = _isPair;
        emit PairUpdated(pair, _isPair);
    }

    // 设置营销钱包地址
    function setMarketingWallet(address _marketingWallet) external onlyOwner {
        require(
            _marketingWallet != address(0),
            "Marketing wallet cannot be zero address"
        );
        marketingWallet = _marketingWallet;
    }

    // 重写transfer函数以包含税收机制
    function _update(
        address from,
        address to,
        uint256 amount
    ) internal override whenNotPaused {
        require(amount > 0, "Transfer amount must be greater than zero");

        // 如果是铸造或销毁，跳过税收逻辑
        if (from == address(0) || to == address(0)) {
            super._update(from, to, amount);
            return;
        }

        // 检查是否需要收税 - 优化布尔运算
        bool shouldTakeTax = !(taxExempt[from] || taxExempt[to]);

        if (shouldTakeTax) {
            uint256 taxAmount = 0;

            // 判断是买入还是卖出
            if (isPair[from]) {
                // 买入交易
                taxAmount = (amount * BUY_TAX_RATE) / TAX_DIVISOR;
            } else if (isPair[to]) {
                // 卖出交易
                taxAmount = (amount * SELL_TAX_RATE) / TAX_DIVISOR;
            }

            if (taxAmount > 0) {
                // 收取税收
                super._update(from, address(this), taxAmount);

                // 分配税收
                _distributeTax(taxAmount);

                // 减少实际转账金额
                unchecked {
                    amount -= taxAmount;
                }
            }
        }

        // 执行实际转账
        super._update(from, to, amount);
    }

    // 分配税收
    function _distributeTax(uint256 taxAmount) private {
        // 计算各部分金额
        uint256 marketingAmount = (taxAmount * MARKETING_SHARE) / SHARE_DIVISOR;
        uint256 burnAmount = (taxAmount * BURN_SHARE) / SHARE_DIVISOR;
        uint256 dividendAmount = taxAmount - marketingAmount - burnAmount;

        // 发送到营销钱包
        if (marketingAmount > 0) {
            super._update(address(this), marketingWallet, marketingAmount);
        }

        // 销毁代币
        if (burnAmount > 0) {
            _burn(address(this), burnAmount);
        }

        // 累计分红 - 更新累积每股分红
        if (dividendAmount > 0 && nftContract != address(0)) {
            totalDividends += dividendAmount;
            // 更新累积每股分红
            uint256 totalNfts = _getTotalNfts();
            if (totalNfts > 0) {
                cumulativeDividendPerShare +=
                    (dividendAmount * 1e18) /
                    totalNfts;
            }

            lastDividendTime = block.timestamp;
            emit DividendDistributed(dividendAmount);

            // 触发自动分红 - 使用合约余额作为门槛
            AutoDividendConfig memory config = autoDividendConfig;
            uint256 currentContractBalance = balanceOf(address(this));
            if (
                config.enabled &&
                currentContractBalance >= autoDividendThreshold &&
                gasleft() > config.gasLimit + 50000 // 确保有足够gas
            ) {
                _processAutoDividend();
            }
        }

        emit TaxCollected(marketingAmount, burnAmount, dividendAmount);
    }

    // 计算用户可领取的分红 - 优化循环和gas使用
    function calculateDividend(address user) external view returns (uint256) {
        if (nftContract == address(0)) return 0;

        try IERC721Enumerable(nftContract).getUserNFTs(user) returns (
            uint256[] memory userNFTs
        ) {
            uint256 userNFTsLength = userNFTs.length;
            if (userNFTsLength == 0) return 0;

            uint256 totalClaimable = 0;
            // 优化循环
            for (uint256 i; i < userNFTsLength; ) {
                totalClaimable += _calculateNFTDividend(userNFTs[i]);
                unchecked {
                    ++i;
                }
            }
            return totalClaimable;
        } catch {
            return 0;
        }
    }

    // 计算单个NFT的可领取分红
    function _calculateNFTDividend(
        uint256 tokenId
    ) private view returns (uint256) {
        NFTDividendInfo storage nftInfo = nftDividendInfo[tokenId];

        // 计算自上次记录以来的新分红
        uint256 newDividend = cumulativeDividendPerShare >
            nftInfo.lastCumulativeDividend
            ? (cumulativeDividendPerShare - nftInfo.lastCumulativeDividend) /
                1e18
            : 0;

        return newDividend;
    }

    // 计算特定NFT的分红信息
    function calculateNFTDividend(
        uint256 tokenId
    ) external view returns (uint256) {
        if (nftContract == address(0)) return 0;

        try IERC721(nftContract).ownerOf(tokenId) returns (address) {
            return _calculateNFTDividend(tokenId);
        } catch {
            return 0;
        }
    }

    // 领取分红 - 优化重入保护和Gas使用
    function claimDividend() external nonReentrant whenNotPaused {
        require(nftContract != address(0), "NFT contract not set");
        require(!_dividendWithdrawing[msg.sender], "Already withdrawing");

        _dividendWithdrawing[msg.sender] = true;

        uint256 totalDividend = 0;
        uint256[] memory claimedTokenIds;
        uint256 claimedCount = 0;

        // 使用getUserNFTs获取用户的所有NFT
        try IERC721Enumerable(nftContract).getUserNFTs(msg.sender) returns (
            uint256[] memory userNFTs
        ) {
            require(userNFTs.length > 0, "No NFT to claim dividends");

            claimedTokenIds = new uint256[](userNFTs.length);
            uint256 userNFTsLength = userNFTs.length;

            // 优化循环 - 先计算总额，再批量更新状态
            for (uint256 i; i < userNFTsLength; ) {
                uint256 tokenId = userNFTs[i];
                uint256 nftDividend = _calculateNFTDividend(tokenId);
                if (nftDividend > 0) {
                    totalDividend += nftDividend;
                    claimedTokenIds[claimedCount] = tokenId;
                    unchecked {
                        ++claimedCount;
                    }
                }
                unchecked {
                    ++i;
                }
            }

            require(totalDividend > 0, "No dividend to claim");

            // 检查合约余额是否足够
            require(
                balanceOf(address(this)) >= totalDividend,
                "Insufficient contract balance"
            );

            // 批量更新NFT分红记录 - 在转账前完成状态更新
            for (uint256 i; i < claimedCount; ) {
                uint256 tokenId = claimedTokenIds[i];
                uint256 nftDividend = _calculateNFTDividend(tokenId);

                nftDividendInfo[tokenId]
                    .lastCumulativeDividend = cumulativeDividendPerShare;
                nftDividendInfo[tokenId].claimedAmount += nftDividend;

                unchecked {
                    ++i;
                }
            }
        } catch {
            _dividendWithdrawing[msg.sender] = false;
            revert("Failed to get user NFTs");
        }

        // 更新用户总领取记录
        userClaimedDividends[msg.sender] += totalDividend;

        // 转账分红 - 使用pull模式更安全
        super._update(address(this), msg.sender, totalDividend);

        _dividendWithdrawing[msg.sender] = false;

        emit DividendClaimed(msg.sender, totalDividend);
    }

    // 领取特定NFT的分红 - 加强重入保护
    function claimNFTDividend(
        uint256 tokenId
    ) external nonReentrant whenNotPaused {
        require(nftContract != address(0), "NFT contract not set");
        require(
            !_nftDividendWithdrawing[tokenId],
            "NFT dividend already being withdrawn"
        );

        _nftDividendWithdrawing[tokenId] = true;

        address ownerAddress = IERC721(nftContract).ownerOf(tokenId);
        require(
            !_dividendWithdrawing[ownerAddress],
            "User already withdrawing dividends"
        );

        uint256 dividend = _calculateNFTDividend(tokenId);
        require(dividend > 0, "No dividend to claim for this NFT");

        // 检查合约余额
        require(
            balanceOf(address(this)) >= dividend,
            "Insufficient contract balance"
        );

        // 先更新状态，再转账
        nftDividendInfo[tokenId]
            .lastCumulativeDividend = cumulativeDividendPerShare;
        nftDividendInfo[tokenId].claimedAmount += dividend;
        userClaimedDividends[ownerAddress] += dividend;

        // 转账分红
        super._update(address(this), ownerAddress, dividend);

        _nftDividendWithdrawing[tokenId] = false;

        emit DividendClaimed(ownerAddress, dividend);
    }

    // 获取NFT总数的内部函数
    function _getTotalNfts() private view returns (uint256) {
        if (nftContract == address(0)) return 0;

        try IERC721Enumerable(nftContract).totalSupply() returns (
            uint256 totalSupply
        ) {
            return totalSupply;
        } catch {
            return 0;
        }
    }

    // 紧急暂停/恢复功能
    function pause() external onlyOwner {
        _pause();
    }

    function unpause() external onlyOwner {
        _unpause();
    }

    // 紧急提取合约中的代币 (仅owner)
    function emergencyWithdraw() external onlyOwner {
        uint256 balance = balanceOf(address(this));
        require(balance > 0, "No balance to withdraw");

        // 清理分红状态以防止混乱
        totalDividends = 0;
        cumulativeDividendPerShare = 0;

        super._update(address(this), owner(), balance);
    }

    // 销毁代币功能 (公开接口)
    function burn(uint256 amount) external {
        require(amount > 0, "Amount must be greater than zero");
        require(balanceOf(msg.sender) >= amount, "Insufficient balance");

        _burn(msg.sender, amount);
    }

    // 销毁其他地址的代币 (需要授权)
    function burnFrom(address account, uint256 amount) external {
        require(amount > 0, "Amount must be greater than zero");

        uint256 currentAllowance = allowance(account, msg.sender);
        require(currentAllowance >= amount, "Burn amount exceeds allowance");

        _approve(account, msg.sender, currentAllowance - amount);
        _burn(account, amount);
    }

    // 查看合约当前代币余额和分红统计
    function contractBalance()
        external
        view
        returns (uint256 balance, uint256 totalDivs, uint256 cumulativePerShare)
    {
        return (
            balanceOf(address(this)),
            totalDividends,
            cumulativeDividendPerShare
        );
    }

    // 自动分红处理函数 - 优化Gas使用和安全性
    function _processAutoDividend() private {
        AutoDividendConfig memory config = autoDividendConfig;
        if (nftContract == address(0) || !config.enabled) return;

        uint256 totalNfts = _getTotalNfts();
        if (totalNfts == 0) return;

        uint256 gasUsed = 0;
        uint256 gasLimit = uint256(config.gasLimit);
        uint256 maxNFTs = uint256(config.maxNFTs);
        uint256 startIndex = uint256(config.lastProcessedIndex);

        // 动态调整处理数量以避免gas limit
        uint256 remainingGas = gasleft();
        if (remainingGas < gasLimit) {
            gasLimit = (remainingGas * 80) / 100; // 只使用80%的剩余gas
        }

        uint256 endIndex = startIndex;
        uint256 processedCount = 0;
        uint256 totalDistributed = 0;

        // 预检查合约余额
        uint256 currentBalance = balanceOf(address(this));
        if (currentBalance == 0) return;

        // 批量处理以减少状态写入
        uint256[] memory nftIds = new uint256[](maxNFTs);
        address[] memory owners = new address[](maxNFTs);
        uint256[] memory dividends = new uint256[](maxNFTs);
        uint256 validCount = 0;

        // 第一阶段：收集有效的分红信息
        while (
            processedCount < maxNFTs &&
            endIndex < totalNfts &&
            gasUsed < (gasLimit * 60) / 100 // 预留40%gas用于状态更新
        ) {
            uint256 gasBeforeProcess = gasleft();
            unchecked {
                ++endIndex;
            }

            uint256 nftDividend = _calculateNFTDividend(endIndex);

            if (
                nftDividend > 0 &&
                totalDistributed + nftDividend <= currentBalance
            ) {
                try IERC721(nftContract).ownerOf(endIndex) returns (
                    address nftOwner
                ) {
                    if (
                        nftOwner != address(0) &&
                        !_dividendWithdrawing[nftOwner]
                    ) {
                        nftIds[validCount] = endIndex;
                        owners[validCount] = nftOwner;
                        dividends[validCount] = nftDividend;
                        totalDistributed += nftDividend;
                        unchecked {
                            ++validCount;
                        }
                    }
                } catch {
                    // 忽略无效的NFT
                }
            }

            unchecked {
                ++processedCount;
                gasUsed += gasBeforeProcess - gasleft();
            }
        }

        // 第二阶段：批量执行状态更新和转账
        if (validCount > 0) {
            for (uint256 i; i < validCount; ) {
                uint256 tokenId = nftIds[i];
                address owner = owners[i];
                uint256 dividend = dividends[i];

                // 更新分红记录
                nftDividendInfo[tokenId]
                    .lastCumulativeDividend = cumulativeDividendPerShare;
                nftDividendInfo[tokenId].claimedAmount += dividend;
                userClaimedDividends[owner] += dividend;

                // 转账
                super._update(address(this), owner, dividend);
                emit DividendClaimed(owner, dividend);

                unchecked {
                    ++i;
                }
            }
        }

        // 更新处理索引
        autoDividendConfig.lastProcessedIndex = endIndex >= totalNfts
            ? 0
            : uint32(endIndex);

        if (totalDistributed > 0) {
            emit AutoDividendProcessed(startIndex, endIndex, totalDistributed);
        }
    }

    // 手动触发自动分红（管理员功能）
    function processAutoDividend() external onlyOwner {
        _processAutoDividend();
    }

    // 设置自动分红参数 - 优化存储写入
    function setAutoDividendConfig(
        uint256 _gasLimit,
        uint256 _maxNFTs,
        uint256 _threshold,
        bool _enabled
    ) external onlyOwner {
        require(
            _gasLimit >= 100000 && _gasLimit <= type(uint128).max,
            "Invalid gas limit"
        );
        require(_maxNFTs > 0 && _maxNFTs <= 100, "Invalid max NFTs");
        require(_threshold >= 1e12, "Threshold too low");

        // 批量更新配置以减少SSTORE操作
        autoDividendConfig = AutoDividendConfig({
            gasLimit: uint128(_gasLimit),
            maxNFTs: uint64(_maxNFTs),
            lastProcessedIndex: autoDividendConfig.lastProcessedIndex,
            enabled: _enabled
        });

        autoDividendThreshold = _threshold;

        emit AutoDividendConfigUpdated(_gasLimit, _maxNFTs, _threshold);
    }

    // 获取自动分红状态 - 优化存储读取
    function getAutoDividendStatus()
        external
        view
        returns (
            bool enabled,
            uint256 gasLimit,
            uint256 maxNFTs,
            uint256 threshold,
            uint256 lastProcessedIndex,
            uint256 totalNFTs
        )
    {
        AutoDividendConfig memory config = autoDividendConfig;
        return (
            config.enabled,
            uint256(config.gasLimit),
            uint256(config.maxNFTs),
            autoDividendThreshold,
            uint256(config.lastProcessedIndex),
            _getTotalNfts()
        );
    }

    // 创建和设置PancakeSwap交易对
    function _createAndSetPair() private {
        try IPancakeRouter(pancakeSwapRouter).factory() returns (
            address factory
        ) {
            address pair = IPancakeFactory(factory).getPair(
                address(this),
                wbnbAddress
            );

            // 如果交易对不存在，创建它
            if (pair == address(0)) {
                pair = IPancakeFactory(factory).createPair(
                    address(this),
                    wbnbAddress
                );
            }

            // 设置交易对标记（不设为免税，这样可以正常收税）
            if (pair != address(0)) {
                isPair[pair] = true;
                emit PairUpdated(pair, true);
            }
        } catch {
            // 如果创建失败，不中断部署流程
        }
    }

    // 手动创建和设置交易对（管理员功能）
    function createAndSetPair() external onlyOwner {
        _createAndSetPair();
    }
}

// IERC721接口定义
interface IERC721 {
    function balanceOf(address owner) external view returns (uint256);

    function ownerOf(uint256 tokenId) external view returns (address);
}

// IERC721Enumerable接口定义
interface IERC721Enumerable {
    function totalSupply() external view returns (uint256);

    function getUserNFTs(address user) external view returns (uint256[] memory);
}
