// SPDX-License-Identifier: MIT
pragma solidity ^0.8.20;

import "@openzeppelin/contracts/access/Ownable.sol";
import "@openzeppelin/contracts/utils/ReentrancyGuard.sol";
import "@openzeppelin/contracts/utils/Pausable.sol";
import "@openzeppelin/contracts/token/ERC20/IERC20.sol";
import "@openzeppelin/contracts/token/ERC721/IERC721.sol";

// PancakeSwap接口定义
interface IPancakeFactory {
    function getPair(
        address tokenA,
        address tokenB
    ) external view returns (address pair);
}

interface IPancakePair {
    function getReserves()
        external
        view
        returns (uint112 reserve0, uint112 reserve1, uint32 blockTimestampLast);

    function token0() external view returns (address);

    function token1() external view returns (address);
}

interface IPancakeRouter {
    function swapExactTokensForTokens(
        uint amountIn,
        uint amountOutMin,
        address[] calldata path,
        address to,
        uint deadline
    ) external returns (uint[] memory amounts);

    function getAmountsOut(
        uint amountIn,
        address[] calldata path
    ) external view returns (uint[] memory amounts);

    function factory() external pure returns (address);
}

// 挖矿合约 - 实现三种挖矿机制和邀请奖励系统
// 新版本特性：2倍奖励按固定6期释放，每期30天，每期释放1/6的总量
// 释放时间表：第1期(30天)、第2期(60天)、...、第6期(180天)
contract MiningContract is Ownable, ReentrancyGuard, Pausable {
    // 合约地址
    address public tokenAAddress;
    address public tokenBAddress;
    address public nftContractAddress;

    // DEX相关地址
    address public constant USDT = 0x20d8afb5175EF30B163eb6B17a6cADa30CA4C442; // 实际部署使用的USDT地址
    address public constant PANCAKE_ROUTER =
        0x946C1B0F2DfB932968222AADFfdDD22ACBB76197; // PancakeSwap V2 Router
    address public PANCAKE_FACTORY; // PancakeSwap V2 Factory

    // 价格保护参数
    uint256 public constant MIN_LIQUIDITY = 1000 * 1e18; // 最小流动性要求 (1000 USDT)
    uint256 public constant MAX_REASONABLE_PRICE = 1000000 * 1e18; // 最大合理价格 (1M USDT)
    uint256 public constant MIN_REASONABLE_PRICE = 1e12; // 最小合理价格 (0.000001 USDT)

    // 挖矿参数
    uint256 private constant REWARD_MULTIPLIER = 2; // 奖励倍数 (2倍价值)
    uint256 private constant INVITE_REWARD_RATE = 10; // 邀请奖励率 10%
    uint256 private constant RATE_DIVISOR = 100; // 比例除数
    uint256 private constant PRECISION = 1e18; // 精度常量

    // 实时线性释放参数（可配置）
    uint256 public totalReleaseDuration = 180 days; // 总释放持续时间，默认180天

    // 提取收益记录
    struct ClaimRecord {
        uint256 amount; // 提取的TokenB数量
        uint256 usdtValue; // USDT价值
        uint256 timestamp; // 提取时间
        uint8 mechanism; // 机制类型 (1 或 2)
        uint256 normalAmount; // 正常释放提取量
        uint256 acceleratedAmount; // 加速释放提取量
    }

    // 用户数据结构
    struct UserInfo {
        address inviter; // 邀请人
        uint256 totalInvited; // 总邀请人数
        uint256 acceleratedReleaseAmount; // 当前未使用的加速释放额度
        uint256 totalAcceleratedReceived; // 历史总累计获得的加速额度
        uint256 totalAcceleratedUsed; // 历史总累计使用的加速额度
    }

    // 机制一：使用USDT购买TokenA并销毁获得TokenB的挖矿记录
    struct MiningRecord1 {
        uint256 burnedTokenAAmount; // 销毁的TokenA数量
        uint256 burnedTokenAValue; // 销毁时的TokenA价值 (USDT计价)
        uint256 expectedTokenBAmount; // 预期获得的TokenB数量
        uint256 totalReleaseValue; // 总释放价值
        uint256 releasedValue; // 已释放价值
        uint256 acceleratedReleasedValue; // 加速释放的价值
        uint256 normalReleasedValue; // 正常释放的价值
        uint256 acceleratedTime; // 加速释放占用的时间（秒）
        uint256 startTime; // 挖矿开始时间（不可变）
        uint256 lastClaimTime; // 最后领取时间（每次领取后更新）
        bool active; // 是否激活
    }

    // 机制二：持有NFT并销毁TokenB的挖矿记录
    struct MiningRecord2 {
        uint256 burnedTokenBAmount; // 销毁的TokenB数量
        uint256 burnedTokenBValue; // 销毁时的TokenB价值 (USDT计价)
        uint256 totalReleaseValue; // 总释放价值 (2倍价值)
        uint256 releasedValue; // 已释放价值
        uint256 acceleratedReleasedValue; // 加速释放的价值
        uint256 normalReleasedValue; // 正常释放的价值
        uint256 acceleratedTime; // 加速释放占用的时间（秒）
        uint256 startTime; // 挖矿开始时间（不可变）
        uint256 lastClaimTime; // 最后领取时间（每次领取后更新）
        bool active; // 是否激活
    }

    // 映射
    mapping(address => UserInfo) public userInfo;
    mapping(address => MiningRecord1[]) public userMiningRecords1; // 用户机制一挖矿记录
    mapping(address => MiningRecord2[]) public userMiningRecords2; // 用户机制二挖矿记录
    mapping(address => ClaimRecord[]) public userClaimRecords; // 用户提取记录
    mapping(address => bool) public hasInviter; // 是否有邀请人

    // 邀请关系映射
    mapping(address => address[]) public invitees; // 邀请人 => 被邀请人列表

    // 全局统计
    uint256 public totalBurnedTokenA; // 总销毁TokenA数量
    uint256 public totalBurnedTokenB; // 总销毁TokenB数量
    uint256 public totalReleasedTokenB; // 总释放TokenB数量

    // 事件定义
    event InviterSet(address indexed user, address indexed inviter);
    event TokenABurned(
        address indexed user,
        uint256 amount,
        uint256 usdtValue,
        uint256 expectedTokenB
    );
    event TokenBBurned(address indexed user, uint256 amount, uint256 usdtValue);
    event TokenBReleased(address indexed user, uint256 amount, uint8 mechanism);
    event AcceleratedReleaseAdded(
        address indexed inviter,
        address indexed invitee,
        uint256 amount
    );
    event AcceleratedReleaseUsed(address indexed user, uint256 amount);
    event TokenBClaimed(
        address indexed user,
        uint256 amount,
        uint256 usdtValue,
        uint8 mechanism,
        uint256 normalAmount,
        uint256 acceleratedAmount
    );
    event USDTSwappedForTokenA(
        address indexed user,
        uint256 usdtAmount,
        uint256 tokenAAmount
    );

    constructor(
        address _tokenAAddress,
        address _tokenBAddress,
        address _nftContractAddress
    ) Ownable(msg.sender) {
        require(_tokenAAddress != address(0), "TokenA address cannot be zero");
        require(_tokenBAddress != address(0), "TokenB address cannot be zero");
        require(
            _nftContractAddress != address(0),
            "NFT address cannot be zero"
        );
        PANCAKE_FACTORY = IPancakeRouter(PANCAKE_ROUTER).factory();
        tokenAAddress = _tokenAAddress;
        tokenBAddress = _tokenBAddress;
        nftContractAddress = _nftContractAddress;
    }

    // 设置邀请人
    function setInviter(address inviter) public {
        require(inviter != address(0), "Inviter cannot be zero address");
        require(inviter != msg.sender, "Cannot invite yourself");
        require(!hasInviter[msg.sender], "Inviter already set");

        userInfo[msg.sender].inviter = inviter;
        userInfo[inviter].totalInvited++;
        hasInviter[msg.sender] = true;

        // 添加到邀请人的被邀请人列表
        invitees[inviter].push(msg.sender);

        emit InviterSet(msg.sender, inviter);
    }

    // 机制一：使用USDT购买TokenA并销毁获得TokenB
    function buyAndBurnTokenAWithUSDT(
        uint256 usdtAmount,
        address inviter
    ) external nonReentrant whenNotPaused {
        require(usdtAmount > 0, "USDT amount must be greater than zero");
        require(
            IERC20(USDT).balanceOf(msg.sender) >= usdtAmount,
            "Insufficient USDT balance"
        );
        if (
            inviter != address(0) && userInfo[msg.sender].inviter == address(0)
        ) {
            setInviter(inviter);
        }

        // 从用户转入USDT到合约
        IERC20(USDT).transferFrom(msg.sender, address(this), usdtAmount);

        // 获取TokenA当前价格 (USDT计价)
        uint256 tokenAPrice = _getTokenPriceFromDEX(tokenAAddress);
        require(tokenAPrice > 0, "Invalid TokenA price");

        // 计算能够购买的TokenA数量
        uint256 tokenAAmount = (usdtAmount * PRECISION) / tokenAPrice;
        require(tokenAAmount > 0, "Insufficient USDT for TokenA purchase");

        // 使用DEX兑换USDT为TokenA
        uint256 actualTokenAAmount = _swapUSDTForTokenA(
            usdtAmount,
            tokenAAmount
        );

        // 触发兑换事件
        emit USDTSwappedForTokenA(msg.sender, usdtAmount, actualTokenAAmount);

        // 销毁兑换得到的TokenA
        ITokenA(tokenAAddress).burn(
            IERC20(tokenAAddress).balanceOf(address(this))
        );

        // 计算预期获得的TokenB数量（基于USDT价值的2倍）
        uint256 tokenBPrice = _getTokenPriceFromDEX(tokenBAddress);
        require(tokenBPrice > 0, "Invalid TokenB price");

        uint256 expectedTokenBAmount = (usdtAmount *
            REWARD_MULTIPLIER *
            PRECISION) / tokenBPrice;

        // 创建挖矿记录
        userMiningRecords1[msg.sender].push(
            MiningRecord1({
                burnedTokenAAmount: actualTokenAAmount,
                burnedTokenAValue: usdtAmount, // 直接使用USDT价值
                expectedTokenBAmount: expectedTokenBAmount,
                totalReleaseValue: usdtAmount * REWARD_MULTIPLIER,
                releasedValue: 0,
                acceleratedReleasedValue: 0,
                normalReleasedValue: 0,
                acceleratedTime: 0,
                startTime: block.timestamp, // 记录开始时间
                lastClaimTime: block.timestamp, // 初始化最后领取时间
                active: true
            })
        );

        // 处理邀请奖励
        _processInviteReward(msg.sender, usdtAmount);

        // 更新全局统计
        totalBurnedTokenA += actualTokenAAmount;

        emit TokenABurned(
            msg.sender,
            actualTokenAAmount,
            usdtAmount,
            expectedTokenBAmount
        );
    }

    // 机制二：持有NFT并销毁TokenB
    function burnTokenBWithNFT(
        uint256 amount,
        address inviter
    ) external nonReentrant whenNotPaused {
        require(amount > 0, "Amount must be greater than zero");
        require(
            IERC721(nftContractAddress).balanceOf(msg.sender) > 0,
            "Must hold at least one NFT"
        );
        require(
            IERC20(tokenBAddress).balanceOf(msg.sender) >= amount,
            "Insufficient TokenB balance"
        );
        if (
            inviter != address(0) && userInfo[msg.sender].inviter == address(0)
        ) {
            setInviter(inviter);
        }
        // 获取TokenB当前价格 (USDT计价)
        uint256 tokenBPrice = _getTokenPriceFromDEX(tokenBAddress);
        require(tokenBPrice > 0, "Invalid TokenB price");

        // 计算销毁价值 (USDT计价) - 优化计算
        uint256 burnedValue;
        unchecked {
            burnedValue = (amount * tokenBPrice) / PRECISION;
        }

        // 销毁TokenB
        ITokenB(tokenBAddress).burnFrom(msg.sender, amount);

        // 创建挖矿记录
        userMiningRecords2[msg.sender].push(
            MiningRecord2({
                burnedTokenBAmount: amount,
                burnedTokenBValue: burnedValue,
                totalReleaseValue: burnedValue * REWARD_MULTIPLIER,
                releasedValue: 0,
                acceleratedReleasedValue: 0,
                normalReleasedValue: 0,
                acceleratedTime: 0,
                startTime: block.timestamp, // 记录开始时间
                lastClaimTime: block.timestamp, // 初始化最后领取时间
                active: true
            })
        );

        // 处理邀请奖励
        _processInviteReward(msg.sender, burnedValue);

        // 更新全局统计
        totalBurnedTokenB += amount;

        emit TokenBBurned(msg.sender, amount, burnedValue);
    }

    // 处理邀请奖励
    function _processInviteReward(address user, uint256 burnedValue) private {
        address inviter = userInfo[user].inviter;
        if (inviter != address(0)) {
            uint256 rewardAmount = (burnedValue * INVITE_REWARD_RATE) /
                RATE_DIVISOR;
            userInfo[inviter].acceleratedReleaseAmount += rewardAmount;
            userInfo[inviter].totalAcceleratedReceived += rewardAmount;

            emit AcceleratedReleaseAdded(inviter, user, rewardAmount);
        }
    }

    // 机制一：领取TokenB奖励 - 优化循环
    function claimTokenBFromMechanism1() external nonReentrant whenNotPaused {
        uint256 totalClaimable = 0;
        MiningRecord1[] storage records = userMiningRecords1[msg.sender];

        // 1. 先处理正常释放 - 优化循环
        uint256 recordsLength = records.length;
        for (uint256 i; i < recordsLength; ) {
            if (!records[i].active) {
                unchecked {
                    ++i;
                }
                continue;
            }

            uint256 claimable = _calculateReleasableAmount1(i, msg.sender);
            if (claimable > 0) {
                totalClaimable += claimable;
                records[i].releasedValue += claimable;
                records[i].normalReleasedValue += claimable;
                // 更新最后领取时间为当前时间（实现实时产出）
                records[i].lastClaimTime = block.timestamp;

                // 如果完全释放完毕，标记为非激活状态
                if (records[i].releasedValue >= records[i].totalReleaseValue) {
                    records[i].active = false;
                }
            }
            unchecked {
                ++i;
            }
        }

        // 2. 再处理加速释放额度 (如果能够全部释放就直接释放)
        uint256 acceleratedAmount = userInfo[msg.sender]
            .acceleratedReleaseAmount;
        uint256 actualAcceleratedUsed = 0;

        if (acceleratedAmount > 0) {
            uint256 maxAccelerated = _calculateMaxAcceleratedRelease(
                msg.sender
            );
            uint256 actualAccelerated = acceleratedAmount > maxAccelerated
                ? maxAccelerated
                : acceleratedAmount;

            if (actualAccelerated > 0) {
                actualAcceleratedUsed = _distributeAcceleratedRelease1(
                    msg.sender,
                    actualAccelerated
                );
                totalClaimable += actualAcceleratedUsed;
                userInfo[msg.sender]
                    .acceleratedReleaseAmount -= actualAcceleratedUsed;
                userInfo[msg.sender]
                    .totalAcceleratedUsed += actualAcceleratedUsed;

                emit AcceleratedReleaseUsed(msg.sender, actualAcceleratedUsed);
            }
        }

        require(totalClaimable > 0, "No tokens to claim");

        // 计算实际TokenB数量 - 优化计算
        uint256 tokenBPrice = _getTokenPriceFromDEX(tokenBAddress);
        require(tokenBPrice > 0, "Invalid TokenB price");

        uint256 tokenBAmount;
        unchecked {
            tokenBAmount = (totalClaimable * PRECISION) / tokenBPrice;
            totalReleasedTokenB += tokenBAmount;
        }

        // 转账TokenB给用户
        require(ITokenB(tokenBAddress).transfer(msg.sender, tokenBAmount), "TokenB transfer failed");

        // 记录提取记录
        uint256 normalUsdtAmount = totalClaimable - actualAcceleratedUsed;
        userClaimRecords[msg.sender].push(ClaimRecord({
            amount: tokenBAmount,
            usdtValue: totalClaimable,
            timestamp: block.timestamp,
            mechanism: 1,
            normalAmount: normalUsdtAmount,
            acceleratedAmount: actualAcceleratedUsed
        }));

        emit TokenBReleased(msg.sender, tokenBAmount, 1);
        emit TokenBClaimed(msg.sender, tokenBAmount, totalClaimable, 1, normalUsdtAmount, actualAcceleratedUsed);
    }

    // 机制二：领取TokenB奖励 - 优化循环
    function claimTokenBFromMechanism2() external nonReentrant whenNotPaused {
        require(
            IERC721(nftContractAddress).balanceOf(msg.sender) > 0,
            "Must hold at least one NFT"
        );

        uint256 totalClaimable = 0;
        MiningRecord2[] storage records = userMiningRecords2[msg.sender];

        // 1. 先处理正常释放 - 优化循环
        uint256 recordsLength = records.length;
        for (uint256 i; i < recordsLength; ) {
            if (!records[i].active) {
                unchecked {
                    ++i;
                }
                continue;
            }

            uint256 claimable = _calculateReleasableAmount2(i, msg.sender);
            if (claimable > 0) {
                totalClaimable += claimable;
                records[i].releasedValue += claimable;
                records[i].normalReleasedValue += claimable;
                // 更新最后领取时间为当前时间（实现实时产出）
                records[i].lastClaimTime = block.timestamp;

                // 如果完全释放完毕，标记为非激活状态
                if (records[i].releasedValue >= records[i].totalReleaseValue) {
                    records[i].active = false;
                }
            }
            unchecked {
                ++i;
            }
        }

        // 2. 再处理加速释放额度 (如果能够全部释放就直接释放)
        uint256 acceleratedAmount = userInfo[msg.sender]
            .acceleratedReleaseAmount;
        uint256 actualAcceleratedUsed = 0;

        if (acceleratedAmount > 0) {
            uint256 maxAccelerated = _calculateMaxAcceleratedRelease(
                msg.sender
            );
            uint256 actualAccelerated = acceleratedAmount > maxAccelerated
                ? maxAccelerated
                : acceleratedAmount;

            if (actualAccelerated > 0) {
                actualAcceleratedUsed = _distributeAcceleratedRelease2(
                    msg.sender,
                    actualAccelerated
                );
                totalClaimable += actualAcceleratedUsed;
                userInfo[msg.sender]
                    .acceleratedReleaseAmount -= actualAcceleratedUsed;
                userInfo[msg.sender]
                    .totalAcceleratedUsed += actualAcceleratedUsed;

                emit AcceleratedReleaseUsed(msg.sender, actualAcceleratedUsed);
            }
        }

        require(totalClaimable > 0, "No tokens to claim");

        // 计算实际TokenB数量 - 优化计算
        uint256 tokenBPrice = _getTokenPriceFromDEX(tokenBAddress);
        require(tokenBPrice > 0, "Invalid TokenB price");

        uint256 tokenBAmount;
        unchecked {
            tokenBAmount = (totalClaimable * PRECISION) / tokenBPrice;
            totalReleasedTokenB += tokenBAmount;
        }

        // 转账TokenB给用户
        require(ITokenB(tokenBAddress).transfer(msg.sender, tokenBAmount), "TokenB transfer failed");

        // 记录提取记录
        uint256 normalUsdtAmount = totalClaimable - actualAcceleratedUsed;
        userClaimRecords[msg.sender].push(ClaimRecord({
            amount: tokenBAmount,
            usdtValue: totalClaimable,
            timestamp: block.timestamp,
            mechanism: 2,
            normalAmount: normalUsdtAmount,
            acceleratedAmount: actualAcceleratedUsed
        }));

        emit TokenBReleased(msg.sender, tokenBAmount, 2);
        emit TokenBClaimed(msg.sender, tokenBAmount, totalClaimable, 2, normalUsdtAmount, actualAcceleratedUsed);
    }

    // 计算机制一的可释放数量 (修正的实时线性释放算法)
    function _calculateReleasableAmount1(
        uint256 recordIndex,
        address user
    ) private view returns (uint256) {
        MiningRecord1 storage record = userMiningRecords1[user][recordIndex];

        // 如果记录已完全释放，返回0
        if (record.releasedValue >= record.totalReleaseValue) {
            return 0;
        }

        // 计算从开始到现在的总时间，加上加速释放占用的时间
        uint256 totalTimeElapsed = block.timestamp - record.startTime + record.acceleratedTime;

        // 如果超过总释放时间，限制在总释放时间内
        if (totalTimeElapsed > totalReleaseDuration) {
            totalTimeElapsed = totalReleaseDuration;
        }

        // 计算到目前为止总共应该释放的价值
        uint256 totalShouldHaveReleased = (record.totalReleaseValue *
            totalTimeElapsed) / totalReleaseDuration;

        // 可领取的金额 = 应该释放的总额 - 已释放的金额
        if (totalShouldHaveReleased > record.releasedValue) {
            return totalShouldHaveReleased - record.releasedValue;
        }

        return 0;
    }

    // 计算机制二的可释放数量 (修正的实时线性释放算法)
    function _calculateReleasableAmount2(
        uint256 recordIndex,
        address user
    ) private view returns (uint256) {
        MiningRecord2 storage record = userMiningRecords2[user][recordIndex];

        // 如果记录已完全释放，返回0
        if (record.releasedValue >= record.totalReleaseValue) {
            return 0;
        }

        // 计算从开始到现在的总时间，加上加速释放占用的时间
        uint256 totalTimeElapsed = block.timestamp - record.startTime + record.acceleratedTime;

        // 如果超过总释放时间，限制在总释放时间内
        if (totalTimeElapsed > totalReleaseDuration) {
            totalTimeElapsed = totalReleaseDuration;
        }

        // 计算到目前为止总共应该释放的价值
        uint256 totalShouldHaveReleased = (record.totalReleaseValue *
            totalTimeElapsed) / totalReleaseDuration;

        // 可领取的金额 = 应该释放的总额 - 已释放的金额
        if (totalShouldHaveReleased > record.releasedValue) {
            return totalShouldHaveReleased - record.releasedValue;
        }

        return 0;
    }

    // 计算最大可加速释放数量 (总剩余 - 当前正常可领取)
    function _calculateMaxAcceleratedRelease(
        address user
    ) private view returns (uint256) {
        uint256 totalAcceleratable = 0;

        // 计算机制一的可加速释放价值 - 优化循环
        MiningRecord1[] storage records1 = userMiningRecords1[user];
        uint256 records1Length = records1.length;
        for (uint256 i; i < records1Length; ) {
            if (records1[i].active) {
                uint256 remainingValue = records1[i].totalReleaseValue -
                    records1[i].releasedValue;
                uint256 currentNormalClaimable = _calculateReleasableAmount1(
                    i,
                    user
                );
                // 可加速的部分 = 总剩余 - 当前可正常领取的
                if (remainingValue > currentNormalClaimable) {
                    unchecked {
                        totalAcceleratable +=
                            remainingValue -
                            currentNormalClaimable;
                    }
                }
            }
            unchecked {
                ++i;
            }
        }

        // 计算机制二的可加速释放价值 - 优化循环
        MiningRecord2[] storage records2 = userMiningRecords2[user];
        uint256 records2Length = records2.length;
        for (uint256 i; i < records2Length; ) {
            if (records2[i].active) {
                uint256 remainingValue = records2[i].totalReleaseValue -
                    records2[i].releasedValue;
                uint256 currentNormalClaimable = _calculateReleasableAmount2(
                    i,
                    user
                );
                // 可加速的部分 = 总剩余 - 当前可正常领取的
                if (remainingValue > currentNormalClaimable) {
                    unchecked {
                        totalAcceleratable +=
                            remainingValue -
                            currentNormalClaimable;
                    }
                }
            }
            unchecked {
                ++i;
            }
        }

        return totalAcceleratable;
    }

    // 分配加速释放额度到具体的挖矿记录 (机制一) - 直接释放模式
    function _distributeAcceleratedRelease1(
        address user,
        uint256 acceleratedAmount
    ) private returns (uint256 actualUsed) {
        MiningRecord1[] storage records = userMiningRecords1[user];
        uint256 remainingAccelerated = acceleratedAmount;
        actualUsed = 0;

        for (
            uint256 i = 0;
            i < records.length && remainingAccelerated > 0;
            i++
        ) {
            if (!records[i].active) continue;

            // 计算这个记录可加速释放的最大值 (总剩余 - 当前正常可领取)
            uint256 remainingValue = records[i].totalReleaseValue -
                records[i].releasedValue;
            uint256 currentNormalClaimable = _calculateReleasableAmount1(
                i,
                user
            );
            uint256 maxAcceleratedForRecord = remainingValue >
                currentNormalClaimable
                ? remainingValue - currentNormalClaimable
                : 0;

            if (maxAcceleratedForRecord > 0) {
                uint256 acceleratedForThisRecord = remainingAccelerated >
                    maxAcceleratedForRecord
                    ? maxAcceleratedForRecord
                    : remainingAccelerated;

                // 直接释放：增加已释放数量和加速释放记录
                records[i].releasedValue += acceleratedForThisRecord;
                records[i].acceleratedReleasedValue += acceleratedForThisRecord;
                
                // 计算并记录加速释放占用的时间
                uint256 acceleratedTimeForThisRecord = (acceleratedForThisRecord * totalReleaseDuration) / records[i].totalReleaseValue;
                records[i].acceleratedTime += acceleratedTimeForThisRecord;
                
                remainingAccelerated -= acceleratedForThisRecord;
                actualUsed += acceleratedForThisRecord;

                // 如果完全释放完毕，标记为非激活状态
                if (records[i].releasedValue >= records[i].totalReleaseValue) {
                    records[i].active = false;
                }
            }
        }

        return actualUsed;
    }

    // 分配加速释放额度到具体的挖矿记录 (机制二) - 直接释放模式
    function _distributeAcceleratedRelease2(
        address user,
        uint256 acceleratedAmount
    ) private returns (uint256 actualUsed) {
        MiningRecord2[] storage records = userMiningRecords2[user];
        uint256 remainingAccelerated = acceleratedAmount;
        actualUsed = 0;

        for (
            uint256 i = 0;
            i < records.length && remainingAccelerated > 0;
            i++
        ) {
            if (!records[i].active) continue;

            // 计算这个记录可加速释放的最大值 (总剩余 - 当前正常可领取)
            uint256 remainingValue = records[i].totalReleaseValue -
                records[i].releasedValue;
            uint256 currentNormalClaimable = _calculateReleasableAmount2(
                i,
                user
            );
            uint256 maxAcceleratedForRecord = remainingValue >
                currentNormalClaimable
                ? remainingValue - currentNormalClaimable
                : 0;

            if (maxAcceleratedForRecord > 0) {
                uint256 acceleratedForThisRecord = remainingAccelerated >
                    maxAcceleratedForRecord
                    ? maxAcceleratedForRecord
                    : remainingAccelerated;

                // 直接释放：增加已释放数量和加速释放记录
                records[i].releasedValue += acceleratedForThisRecord;
                records[i].acceleratedReleasedValue += acceleratedForThisRecord;
                
                // 计算并记录加速释放占用的时间
                uint256 acceleratedTimeForThisRecord = (acceleratedForThisRecord * totalReleaseDuration) / records[i].totalReleaseValue;
                records[i].acceleratedTime += acceleratedTimeForThisRecord;
                
                remainingAccelerated -= acceleratedForThisRecord;
                actualUsed += acceleratedForThisRecord;

                // 如果完全释放完毕，标记为非激活状态
                if (records[i].releasedValue >= records[i].totalReleaseValue) {
                    records[i].active = false;
                }
            }
        }

        return actualUsed;
    }

    // 查询用户机制一可领取数量 - 优化循环
    function getUserClaimableAmount1(
        address user
    ) external view returns (uint256) {
        uint256 totalClaimable = 0;
        MiningRecord1[] storage records = userMiningRecords1[user];

        uint256 recordsLength = records.length;
        for (uint256 i; i < recordsLength; ) {
            if (records[i].active) {
                totalClaimable += _calculateReleasableAmount1(i, user);
            }
            unchecked {
                ++i;
            }
        }

        return totalClaimable;
    }

    // 查询用户机制二可领取数量 - 优化循环
    function getUserClaimableAmount2(
        address user
    ) external view returns (uint256) {
        uint256 totalClaimable = 0;
        MiningRecord2[] storage records = userMiningRecords2[user];

        uint256 recordsLength = records.length;
        for (uint256 i; i < recordsLength; ) {
            if (records[i].active) {
                totalClaimable += _calculateReleasableAmount2(i, user);
            }
            unchecked {
                ++i;
            }
        }

        return totalClaimable;
    }

    // 新增：实时收益查询接口（供前端调用）
    function getUserRealtimeEarnings(
        address user
    )
        external
        view
        returns (
            uint256 totalClaimableValue, // 总可领取价值(USDT)
            uint256 totalClaimableTokenB, // 总可领取TokenB数量
            uint256 currentEarningRate, // 当前每秒收益率(USDT)
            uint256 dailyEarningRate, // 每日收益率(USDT)
            uint256 totalValueLocked, // 总锁定价值
            uint256 totalValueReleased, // 总已释放价值
            uint256 estimatedTimeToComplete, // 预计完全释放剩余时间(秒)
            uint256 nextUpdateTime // 下次更新时间(实时更新=当前时间)
        )
    {
        // 计算两个机制的可领取金额
        uint256 claimable1 = this.getUserClaimableAmount1(user);
        uint256 claimable2 = this.getUserClaimableAmount2(user);
        totalClaimableValue = claimable1 + claimable2;

        // 计算统计数据
        (totalValueLocked, totalValueReleased) = _getUserTotalStats(user);

        // 计算更准确的收益率和预计完成时间
        uint256 remainingValue = totalValueLocked - totalValueReleased;
        if (remainingValue > 0) {
            // 计算更精确的收益率和完成时间
            (currentEarningRate, estimatedTimeToComplete) = _calculateAccurateEarningRate(user, remainingValue);
            dailyEarningRate = currentEarningRate * 86400; // 每日收益率 (86400秒/天)
        }

        // 转换为TokenB数量
        if (totalClaimableValue > 0) {
            uint256 tokenBPrice = _getTokenPriceFromDEX(tokenBAddress);
            if (tokenBPrice > 0) {
                totalClaimableTokenB =
                    (totalClaimableValue * PRECISION) /
                    tokenBPrice;
            }
        }

        // 实时更新，始终是当前时间
        nextUpdateTime = block.timestamp;
    }

    // 获取用户总统计数据的内部函数（只统计活跃记录）
    function _getUserTotalStats(
        address user
    )
        private
        view
        returns (uint256 totalValueLocked, uint256 totalValueReleased)
    {
        // 统计机制一（只统计活跃记录）
        MiningRecord1[] storage records1 = userMiningRecords1[user];
        for (uint256 i = 0; i < records1.length; i++) {
            if (records1[i].active) {
                totalValueLocked += records1[i].totalReleaseValue;
                totalValueReleased += records1[i].releasedValue;
            }
        }

        // 统计机制二（只统计活跃记录）
        MiningRecord2[] storage records2 = userMiningRecords2[user];
        for (uint256 i = 0; i < records2.length; i++) {
            if (records2[i].active) {
                totalValueLocked += records2[i].totalReleaseValue;
                totalValueReleased += records2[i].releasedValue;
            }
        }
    }

    // 计算更准确的收益率和预计完成时间
    function _calculateAccurateEarningRate(
        address user,
        uint256 remainingValue
    ) 
        private 
        view 
        returns (uint256 earningRate, uint256 timeToComplete)
    {
        uint256 totalEarningRate = 0;
        uint256 maxTimeToComplete = 0;

        // 计算机制一的收益率
        MiningRecord1[] storage records1 = userMiningRecords1[user];
        for (uint256 i = 0; i < records1.length; i++) {
            if (records1[i].active) {
                uint256 recordRemaining = records1[i].totalReleaseValue - records1[i].releasedValue;
                if (recordRemaining > 0) {
                    // 计算这个记录的实际剩余时间
                    uint256 totalTimeElapsed = block.timestamp - records1[i].startTime + records1[i].acceleratedTime;
                    uint256 timeRemaining = totalReleaseDuration > totalTimeElapsed ? 
                        totalReleaseDuration - totalTimeElapsed : 0;
                    
                    if (timeRemaining > 0) {
                        uint256 recordEarningRate = recordRemaining / timeRemaining;
                        totalEarningRate += recordEarningRate;
                        if (timeRemaining > maxTimeToComplete) {
                            maxTimeToComplete = timeRemaining;
                        }
                    }
                }
            }
        }

        // 计算机制二的收益率
        MiningRecord2[] storage records2 = userMiningRecords2[user];
        for (uint256 i = 0; i < records2.length; i++) {
            if (records2[i].active) {
                uint256 recordRemaining = records2[i].totalReleaseValue - records2[i].releasedValue;
                if (recordRemaining > 0) {
                    // 计算这个记录的实际剩余时间
                    uint256 totalTimeElapsed = block.timestamp - records2[i].startTime + records2[i].acceleratedTime;
                    uint256 timeRemaining = totalReleaseDuration > totalTimeElapsed ? 
                        totalReleaseDuration - totalTimeElapsed : 0;
                    
                    if (timeRemaining > 0) {
                        uint256 recordEarningRate = recordRemaining / timeRemaining;
                        totalEarningRate += recordEarningRate;
                        if (timeRemaining > maxTimeToComplete) {
                            maxTimeToComplete = timeRemaining;
                        }
                    }
                }
            }
        }

        earningRate = totalEarningRate;
        timeToComplete = maxTimeToComplete;
    }

    // 新增：获取单个挖矿记录的实时状态
    function getMiningRecordRealtimeStatus1(
        address user,
        uint256 recordIndex
    )
        external
        view
        returns (
            uint256 totalValue, // 总价值
            uint256 releasedValue, // 已释放价值
            uint256 currentClaimable, // 当前可领取
            uint256 releaseRate, // 每秒释放率
            uint256 startTime, // 开始时间
            uint256 lastClaimTime, // 最后领取时间
            uint256 estimatedEndTime, // 预计结束时间
            bool isActive // 是否激活
        )
    {
        require(
            recordIndex < userMiningRecords1[user].length,
            "Invalid record index"
        );

        MiningRecord1 storage record = userMiningRecords1[user][recordIndex];
        totalValue = record.totalReleaseValue;
        releasedValue = record.releasedValue;
        currentClaimable = _calculateReleasableAmount1(recordIndex, user);
        lastClaimTime = record.lastClaimTime;
        isActive = record.active;

        if (totalValue > 0) {
            releaseRate = totalValue / totalReleaseDuration;
            estimatedEndTime = lastClaimTime + totalReleaseDuration;
        }

        startTime = record.startTime; // 挖矿开始时间
    }

    // 新增：获取单个挖矿记录的实时状态（机制二）
    function getMiningRecordRealtimeStatus2(
        address user,
        uint256 recordIndex
    )
        external
        view
        returns (
            uint256 totalValue, // 总价值
            uint256 releasedValue, // 已释放价值
            uint256 currentClaimable, // 当前可领取
            uint256 releaseRate, // 每秒释放率
            uint256 startTime, // 开始时间
            uint256 lastClaimTime, // 最后领取时间
            uint256 estimatedEndTime, // 预计结束时间
            bool isActive // 是否激活
        )
    {
        require(
            recordIndex < userMiningRecords2[user].length,
            "Invalid record index"
        );

        MiningRecord2 storage record = userMiningRecords2[user][recordIndex];
        totalValue = record.totalReleaseValue;
        releasedValue = record.releasedValue;
        currentClaimable = _calculateReleasableAmount2(recordIndex, user);
        lastClaimTime = record.lastClaimTime;
        isActive = record.active;

        if (totalValue > 0) {
            releaseRate = totalValue / totalReleaseDuration;
            estimatedEndTime = lastClaimTime + totalReleaseDuration;
        }

        startTime = record.startTime; // 挖矿开始时间
    }

    // 新增：查询用户挖矿记录的释放进度信息
    function getUserReleaseProgress(
        address user
    )
        external
        view
        returns (
            uint256 totalRecords1,
            uint256 totalRecords2,
            uint256 completedRecords1,
            uint256 completedRecords2,
            uint256 totalValueLocked1,
            uint256 totalValueLocked2,
            uint256 totalValueReleased1,
            uint256 totalValueReleased2
        )
    {
        MiningRecord1[] storage records1 = userMiningRecords1[user];
        MiningRecord2[] storage records2 = userMiningRecords2[user];

        totalRecords1 = records1.length;
        totalRecords2 = records2.length;

        // 统计机制一 - 优化循环
        uint256 records1Length = records1.length;
        for (uint256 i; i < records1Length; ) {
            if (!records1[i].active) {
                unchecked {
                    ++completedRecords1;
                }
            }
            totalValueLocked1 += records1[i].totalReleaseValue;
            totalValueReleased1 += records1[i].releasedValue;
            unchecked {
                ++i;
            }
        }

        // 统计机制二 - 优化循环
        uint256 records2Length = records2.length;
        for (uint256 i; i < records2Length; ) {
            if (!records2[i].active) {
                unchecked {
                    ++completedRecords2;
                }
            }
            totalValueLocked2 += records2[i].totalReleaseValue;
            totalValueReleased2 += records2[i].releasedValue;
            unchecked {
                ++i;
            }
        }
    }

    // 查询用户邀请信息（向后兼容版本）
    function getUserInviteInfo(
        address user
    )
        external
        view
        returns (
            address inviter,
            uint256 totalInvited,
            uint256 acceleratedReleaseAmount,
            address[] memory inviteeList
        )
    {
        UserInfo storage info = userInfo[user];
        return (
            info.inviter,
            info.totalInvited,
            info.acceleratedReleaseAmount,
            invitees[user]
        );
    }

    // 查询用户完整邀请信息（新版本，包含详细加速额度信息）
    function getUserInviteInfoFull(
        address user
    )
        external
        view
        returns (
            address inviter,
            uint256 totalInvited,
            uint256 currentUnusedAmount,
            uint256 totalReceivedAmount,
            uint256 totalUsedAmount,
            address[] memory inviteeList
        )
    {
        UserInfo storage info = userInfo[user];
        return (
            info.inviter,
            info.totalInvited,
            info.acceleratedReleaseAmount,
            info.totalAcceleratedReceived,
            info.totalAcceleratedUsed,
            invitees[user]
        );
    }

    // 查询用户挖矿记录数量
    function getUserMiningRecordCount(
        address user
    ) external view returns (uint256 count1, uint256 count2) {
        return (
            userMiningRecords1[user].length,
            userMiningRecords2[user].length
        );
    }

    // 查询用户机制一指定记录的详细信息
    function getUserMiningRecord1Detail(
        address user,
        uint256 recordIndex
    )
        external
        view
        returns (
            uint256 burnedTokenAAmount,
            uint256 burnedTokenAValue,
            uint256 expectedTokenBAmount,
            uint256 totalReleaseValue,
            uint256 releasedValue,
            uint256 acceleratedReleasedValue,
            uint256 normalReleasedValue,
            uint256 acceleratedTime,
            uint256 startTime,
            uint256 lastClaimTime,
            bool active
        )
    {
        require(
            recordIndex < userMiningRecords1[user].length,
            "Invalid record index"
        );

        MiningRecord1 storage record = userMiningRecords1[user][recordIndex];
        return (
            record.burnedTokenAAmount,
            record.burnedTokenAValue,
            record.expectedTokenBAmount,
            record.totalReleaseValue,
            record.releasedValue,
            record.acceleratedReleasedValue,
            record.normalReleasedValue,
            record.acceleratedTime,
            record.startTime,
            record.lastClaimTime,
            record.active
        );
    }

    // 查询用户机制二指定记录的详细信息
    function getUserMiningRecord2Detail(
        address user,
        uint256 recordIndex
    )
        external
        view
        returns (
            uint256 burnedTokenBAmount,
            uint256 burnedTokenBValue,
            uint256 totalReleaseValue,
            uint256 releasedValue,
            uint256 acceleratedReleasedValue,
            uint256 normalReleasedValue,
            uint256 acceleratedTime,
            uint256 startTime,
            uint256 lastClaimTime,
            bool active
        )
    {
        require(
            recordIndex < userMiningRecords2[user].length,
            "Invalid record index"
        );

        MiningRecord2 storage record = userMiningRecords2[user][recordIndex];
        return (
            record.burnedTokenBAmount,
            record.burnedTokenBValue,
            record.totalReleaseValue,
            record.releasedValue,
            record.acceleratedReleasedValue,
            record.normalReleasedValue,
            record.acceleratedTime,
            record.startTime,
            record.lastClaimTime,
            record.active
        );
    }

    // 批量查询用户机制一所有记录的加速释放信息
    function getUserAcceleratedReleaseInfo1(
        address user
    )
        external
        view
        returns (
            uint256[] memory acceleratedAmounts,
            uint256[] memory normalAmounts,
            uint256[] memory totalAmounts
        )
    {
        MiningRecord1[] storage records = userMiningRecords1[user];
        uint256 length = records.length;

        acceleratedAmounts = new uint256[](length);
        normalAmounts = new uint256[](length);
        totalAmounts = new uint256[](length);

        for (uint256 i = 0; i < length; i++) {
            acceleratedAmounts[i] = records[i].acceleratedReleasedValue;
            normalAmounts[i] = records[i].normalReleasedValue;
            totalAmounts[i] = records[i].releasedValue;
        }
    }

    // 批量查询用户机制二所有记录的加速释放信息
    function getUserAcceleratedReleaseInfo2(
        address user
    )
        external
        view
        returns (
            uint256[] memory acceleratedAmounts,
            uint256[] memory normalAmounts,
            uint256[] memory totalAmounts
        )
    {
        MiningRecord2[] storage records = userMiningRecords2[user];
        uint256 length = records.length;

        acceleratedAmounts = new uint256[](length);
        normalAmounts = new uint256[](length);
        totalAmounts = new uint256[](length);

        for (uint256 i = 0; i < length; i++) {
            acceleratedAmounts[i] = records[i].acceleratedReleasedValue;
            normalAmounts[i] = records[i].normalReleasedValue;
            totalAmounts[i] = records[i].releasedValue;
        }
    }

    // 使用DEX将USDT兑换为TokenA
    function _swapUSDTForTokenA(
        uint256 usdtAmount,
        uint256 minTokenAAmount
    ) private returns (uint256) {
        // 设置兑换路径：USDT -> TokenA
        address[] memory path = new address[](2);
        path[0] = USDT;
        path[1] = tokenAAddress;

        // 批准Router使用USDT
        IERC20(USDT).approve(PANCAKE_ROUTER, usdtAmount);

        // 执行兑换
        uint[] memory amounts = IPancakeRouter(PANCAKE_ROUTER)
            .swapExactTokensForTokens(
                usdtAmount,
                (minTokenAAmount * 95) / 100, // 5%滑点保护
                path,
                address(this),
                block.timestamp + 300 // 5分钟超时
            );

        return amounts[1]; // 返回实际获得的TokenA数量
    }

    // 预览USDT能够兑换的TokenA数量
    function previewUSDTToTokenA(
        uint256 usdtAmount
    ) external view returns (uint256) {
        address[] memory path = new address[](2);
        path[0] = USDT;
        path[1] = tokenAAddress;

        uint[] memory amounts = IPancakeRouter(PANCAKE_ROUTER).getAmountsOut(
            usdtAmount,
            path
        );
        return amounts[1];
    }

    // 从DEX获取代币价格 (带基础异常检测) - USDT计价
    function _getTokenPriceFromDEX(
        address token
    ) private view returns (uint256) {
        require(token != address(0), "Invalid token address");

        if (token == USDT) {
            return 1e18; // USDT价格为1
        }

        address pair = IPancakeFactory(PANCAKE_FACTORY).getPair(token, USDT);
        require(pair != address(0), "Trading pair does not exist");

        (uint112 reserve0, uint112 reserve1, ) = IPancakePair(pair)
            .getReserves();
        require(reserve0 > 0 && reserve1 > 0, "Insufficient liquidity in pair");

        address token0 = IPancakePair(pair).token0();

        uint256 tokenReserve;
        uint256 usdtReserve;

        if (token0 == token) {
            tokenReserve = uint256(reserve0);
            usdtReserve = uint256(reserve1);
        } else {
            tokenReserve = uint256(reserve1);
            usdtReserve = uint256(reserve0);
        }

        // 检查最小流动性要求
        require(
            usdtReserve >= MIN_LIQUIDITY,
            "Insufficient liquidity for reliable pricing"
        );

        // 计算价格 (tokenPrice = usdtReserve * 1e18 / tokenReserve)
        uint256 price = (usdtReserve * 1e18) / tokenReserve;

        // 基础价格合理性检查
        require(price >= MIN_REASONABLE_PRICE, "Price too low, possible error");
        require(
            price <= MAX_REASONABLE_PRICE,
            "Price too high, possible error"
        );

        return price;
    }

    // 获取代币价格 (公开接口，用于前端查询)
    function getTokenPrice(address token) external view returns (uint256) {
        return _getTokenPriceFromDEX(token);
    }

    // 检查代币价格是否可靠
    function isTokenPriceReliable(address token) external view returns (bool) {
        if (token == USDT) return true;

        address pair = IPancakeFactory(PANCAKE_FACTORY).getPair(token, USDT);
        if (pair == address(0)) return false;

        (uint112 reserve0, uint112 reserve1, ) = IPancakePair(pair)
            .getReserves();
        if (reserve0 == 0 || reserve1 == 0) return false;

        address token0 = IPancakePair(pair).token0();
        uint256 usdtReserve = (token0 == token)
            ? uint256(reserve1)
            : uint256(reserve0);

        return usdtReserve >= MIN_LIQUIDITY;
    }

    // 获取交易对储备量信息
    function getPairInfo(
        address token
    )
        external
        view
        returns (
            address pair,
            uint256 tokenReserve,
            uint256 usdtReserve,
            uint256 price
        )
    {
        require(token != address(0), "Invalid token address");

        pair = IPancakeFactory(PANCAKE_FACTORY).getPair(token, USDT);
        require(pair != address(0), "Trading pair does not exist");

        (uint112 reserve0, uint112 reserve1, ) = IPancakePair(pair)
            .getReserves();
        address token0 = IPancakePair(pair).token0();

        if (token0 == token) {
            tokenReserve = uint256(reserve0);
            usdtReserve = uint256(reserve1);
        } else {
            tokenReserve = uint256(reserve1);
            usdtReserve = uint256(reserve0);
        }

        if (tokenReserve > 0) {
            price = (usdtReserve * 1e18) / tokenReserve;
        } else {
            price = 0;
        }
    }

    // 设置总释放持续时间
    function setTotalReleaseDuration(uint256 _duration) external onlyOwner {
        require(_duration > 0, "Invalid release duration");
        require(_duration >= 1 days, "Duration too short");
        require(_duration <= 365 days, "Duration too long");
        totalReleaseDuration = _duration;

        emit ReleaseDurationUpdated(_duration);
    }

    // 事件定义
    event ReleaseDurationUpdated(uint256 newDuration);

    // 设置合约地址
    function setContractAddresses(
        address _tokenAAddress,
        address _tokenBAddress,
        address _nftContractAddress
    ) external onlyOwner {
        if (_tokenAAddress != address(0)) tokenAAddress = _tokenAAddress;
        if (_tokenBAddress != address(0)) tokenBAddress = _tokenBAddress;
        if (_nftContractAddress != address(0))
            nftContractAddress = _nftContractAddress;
    }

    // 紧急暂停/恢复功能
    function pause() external onlyOwner {
        _pause();
    }

    function unpause() external onlyOwner {
        _unpause();
    }

    // 查询用户提取记录数量
    function getUserClaimRecordCount(address user) external view returns (uint256) {
        return userClaimRecords[user].length;
    }

    // 查询用户指定提取记录的详细信息
    function getUserClaimRecordDetail(
        address user,
        uint256 recordIndex
    )
        external
        view
        returns (
            uint256 amount,
            uint256 usdtValue,
            uint256 timestamp,
            uint8 mechanism,
            uint256 normalAmount,
            uint256 acceleratedAmount
        )
    {
        require(
            recordIndex < userClaimRecords[user].length,
            "Invalid record index"
        );

        ClaimRecord storage record = userClaimRecords[user][recordIndex];
        return (
            record.amount,
            record.usdtValue,
            record.timestamp,
            record.mechanism,
            record.normalAmount,
            record.acceleratedAmount
        );
    }

    // 查询用户完整的加速释放信息（包括历史累计）
    function getUserAcceleratedReleaseInfo(
        address user
    )
        external
        view
        returns (
            uint256 currentUnused,      // 当前未使用的加速额度
            uint256 totalReceived,      // 历史总累计获得的加速额度
            uint256 totalUsed           // 历史总累计使用的加速额度
        )
    {
        UserInfo storage info = userInfo[user];
        return (
            info.acceleratedReleaseAmount,
            info.totalAcceleratedReceived,
            info.totalAcceleratedUsed
        );
    }

    // 批量查询用户最近的提取记录（最多返回50条）
    function getUserRecentClaimRecords(
        address user,
        uint256 limit
    )
        external
        view
        returns (
            uint256[] memory amounts,
            uint256[] memory usdtValues,
            uint256[] memory timestamps,
            uint8[] memory mechanisms,
            uint256[] memory normalAmounts,
            uint256[] memory acceleratedAmounts
        )
    {
        uint256 recordCount = userClaimRecords[user].length;
        if (recordCount == 0) {
            return (
                new uint256[](0),
                new uint256[](0),
                new uint256[](0),
                new uint8[](0),
                new uint256[](0),
                new uint256[](0)
            );
        }

        // 限制最多返回50条记录或实际记录数
        uint256 actualLimit = limit > 50 ? 50 : limit;
        uint256 returnCount = recordCount > actualLimit ? actualLimit : recordCount;
        
        amounts = new uint256[](returnCount);
        usdtValues = new uint256[](returnCount);
        timestamps = new uint256[](returnCount);
        mechanisms = new uint8[](returnCount);
        normalAmounts = new uint256[](returnCount);
        acceleratedAmounts = new uint256[](returnCount);

        // 从最新的记录开始返回
        for (uint256 i = 0; i < returnCount; i++) {
            uint256 recordIndex = recordCount - 1 - i;
            ClaimRecord storage record = userClaimRecords[user][recordIndex];
            
            amounts[i] = record.amount;
            usdtValues[i] = record.usdtValue;
            timestamps[i] = record.timestamp;
            mechanisms[i] = record.mechanism;
            normalAmounts[i] = record.normalAmount;
            acceleratedAmounts[i] = record.acceleratedAmount;
        }
    }

    // 紧急提取合约中的代币 (仅owner)
    function emergencyWithdraw(address token) external onlyOwner {
        uint256 balance = IERC20(token).balanceOf(address(this));
        if (balance > 0) {
            IERC20(token).transfer(owner(), balance);
        }
    }
}

// TokenA接口
interface ITokenA {
    function burn(uint256 amount) external;

    function burnFrom(address account, uint256 amount) external;
}

// TokenB接口
interface ITokenB {
    function transfer(address to, uint256 amount) external returns (bool);

    function burn(uint256 amount) external;

    function burnFrom(address account, uint256 amount) external;
}
