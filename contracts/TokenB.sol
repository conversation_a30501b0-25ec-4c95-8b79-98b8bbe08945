// SPDX-License-Identifier: MIT
pragma solidity ^0.8.20;

import "@openzeppelin/contracts/token/ERC20/ERC20.sol";
import "@openzeppelin/contracts/access/Ownable.sol";
import "@openzeppelin/contracts/utils/ReentrancyGuard.sol";
import "@openzeppelin/contracts/utils/Pausable.sol";

// PancakeSwap接口定义
interface IPancakeRouter {
    function factory() external pure returns (address);
}

interface IPancakeFactory {
    function createPair(
        address tokenA,
        address tokenB
    ) external returns (address pair);

    function getPair(
        address tokenA,
        address tokenB
    ) external view returns (address pair);
}

// TokenB合约 - 子币，包含税收机制和铸造功能
contract TokenB is ERC20, Ownable, ReentrancyGuard, Pausable {
    // 常量定义
    uint256 public constant INITIAL_SUPPLY = 2_1000_000_000 * 1e18; // 2100亿枚
    uint256 public constant BUY_TAX_RATE = 3; // 买入税率3%
    uint256 public constant SELL_TAX_RATE = 3; // 卖出税率3%
    uint256 public constant TAX_DIVISOR = 100; // 税率除数

    // 税收分配比例
    uint256 public constant MARKETING_SHARE = 50; // 50%进入营销钱包
    uint256 public constant BURN_SHARE = 50; // 50%销毁
    uint256 public constant SHARE_DIVISOR = 100; // 分配比例除数

    // 地址定义
    address public marketingWallet; // 营销钱包地址
    address public pancakeSwapRouter; // PancakeSwap路由地址
    address public wbnbAddress; // WBNB地址

    // 税收豁免地址
    mapping(address => bool) public taxExempt;

    // 交易对地址映射
    mapping(address => bool) public isPair;

    // 事件定义
    event TaxCollected(uint256 marketingAmount, uint256 burnAmount);
    event TaxExemptUpdated(address indexed account, bool exempt);
    event PairUpdated(address indexed pair, bool isPair);

    constructor(
        address _marketingWallet,
        address _pancakeSwapRouter,
        address _wbnbAddress
    ) ERC20("TokenB", "TKB") Ownable(msg.sender) {
        require(
            _marketingWallet != address(0),
            "Marketing wallet cannot be zero address"
        );
        require(
            _pancakeSwapRouter != address(0),
            "Router cannot be zero address"
        );
        require(_wbnbAddress != address(0), "WBNB cannot be zero address");

        marketingWallet = _marketingWallet;
        pancakeSwapRouter = _pancakeSwapRouter;
        wbnbAddress = _wbnbAddress;

        // 设置税收豁免地址
        taxExempt[msg.sender] = true;
        taxExempt[address(this)] = true;
        taxExempt[_marketingWallet] = true;

        // 自动创建和设置PancakeSwap交易对
        _createAndSetPair();

        // 铸造初始供应量给部署者
        _mint(msg.sender, INITIAL_SUPPLY);
    }

    // 设置税收豁免地址
    function setTaxExempt(address account, bool exempt) external onlyOwner {
        taxExempt[account] = exempt;
        emit TaxExemptUpdated(account, exempt);
    }

    // 设置交易对地址
    function setPair(address pair, bool _isPair) external onlyOwner {
        isPair[pair] = _isPair;
        emit PairUpdated(pair, _isPair);
    }

    // 设置营销钱包地址
    function setMarketingWallet(address _marketingWallet) external onlyOwner {
        require(
            _marketingWallet != address(0),
            "Marketing wallet cannot be zero address"
        );
        marketingWallet = _marketingWallet;
    }

    // 重写transfer函数以包含税收机制
    function _update(
        address from,
        address to,
        uint256 amount
    ) internal override whenNotPaused {
        require(amount > 0, "Transfer amount must be greater than zero");

        // 如果是铸造或销毁，跳过税收逻辑
        if (from == address(0) || to == address(0)) {
            super._update(from, to, amount);
            return;
        }

        // 常规转账验证
        require(to != address(0), "Transfer to zero address");

        // 检查是否需要收税 - 优化布尔运算
        bool shouldTakeTax = !(taxExempt[from] || taxExempt[to]);

        if (shouldTakeTax) {
            uint256 taxAmount = 0;

            // 判断是买入还是卖出
            if (isPair[from]) {
                // 买入交易
                taxAmount = (amount * BUY_TAX_RATE) / TAX_DIVISOR;
            } else if (isPair[to]) {
                // 卖出交易
                taxAmount = (amount * SELL_TAX_RATE) / TAX_DIVISOR;
            }

            if (taxAmount > 0) {
                // 收取税收
                super._update(from, address(this), taxAmount);

                // 分配税收
                _distributeTax(taxAmount);

                // 减少实际转账金额
                unchecked {
                    amount -= taxAmount;
                }
            }
        }

        // 执行实际转账
        super._update(from, to, amount);
    }

    // 分配税收 - 优化计算和条件检查
    function _distributeTax(uint256 taxAmount) private {
        // 优化计算 - 使用位移操作
        uint256 marketingAmount;
        uint256 burnAmount;

        unchecked {
            // MARKETING_SHARE = 50, 相当于除以2
            marketingAmount = taxAmount >> 1; // taxAmount / 2
            burnAmount = taxAmount - marketingAmount;
        }

        // 批量执行转账以减少gas
        if (marketingAmount > 0) {
            super._update(address(this), marketingWallet, marketingAmount);
        }

        if (burnAmount > 0) {
            _burn(address(this), burnAmount);
        }

        emit TaxCollected(marketingAmount, burnAmount);
    }

    // 销毁代币功能 - 优化条件检查
    function burn(uint256 amount) external {
        require(
            amount > 0 && balanceOf(msg.sender) >= amount,
            "Invalid amount or insufficient balance"
        );
        _burn(msg.sender, amount);
    }

    // 销毁其他地址的代币 - 优化授权检查和更新
    function burnFrom(address account, uint256 amount) external {
        require(amount > 0, "Amount must be greater than zero");

        uint256 currentAllowance = allowance(account, msg.sender);
        require(currentAllowance >= amount, "Burn amount exceeds allowance");

        unchecked {
            _approve(account, msg.sender, currentAllowance - amount);
        }
        _burn(account, amount);
    }

    // 紧急暂停/恢复功能
    function pause() external onlyOwner {
        _pause();
    }

    function unpause() external onlyOwner {
        _unpause();
    }

    // 紧急提取合约中的代币 (仅owner)
    function emergencyWithdraw() external onlyOwner {
        uint256 balance = balanceOf(address(this));
        require(balance > 0, "No balance to withdraw");
        super._update(address(this), owner(), balance);
    }

    // 查看合约当前代币余额
    function contractBalance() external view returns (uint256) {
        return balanceOf(address(this));
    }

    // 批量转账功能 - 优化gas使用
    function batchTransfer(
        address[] calldata recipients,
        uint256[] calldata amounts
    ) external {
        require(
            recipients.length == amounts.length && recipients.length > 0,
            "Invalid arrays"
        );
        require(recipients.length <= 100, "Too many recipients"); // 防止gas limit

        uint256 totalAmount = 0;
        uint256 recipientsLength = recipients.length;

        // 先计算总金额
        for (uint256 i; i < recipientsLength; ) {
            require(
                recipients[i] != address(0) && amounts[i] > 0,
                "Invalid recipient or amount"
            );
            totalAmount += amounts[i];
            unchecked {
                ++i;
            }
        }

        require(balanceOf(msg.sender) >= totalAmount, "Insufficient balance");

        // 执行转账
        for (uint256 i; i < recipientsLength; ) {
            transfer(recipients[i], amounts[i]);
            unchecked {
                ++i;
            }
        }
    }

    // 创建和设置PancakeSwap交易对
    function _createAndSetPair() private {
        try IPancakeRouter(pancakeSwapRouter).factory() returns (
            address factory
        ) {
            address pair = IPancakeFactory(factory).getPair(
                address(this),
                wbnbAddress
            );

            // 如果交易对不存在，创建它
            if (pair == address(0)) {
                pair = IPancakeFactory(factory).createPair(
                    address(this),
                    wbnbAddress
                );
            }

            // 设置交易对标记（不设为免税，这样可以正常收税）
            if (pair != address(0)) {
                isPair[pair] = true;
                emit PairUpdated(pair, true);
            }
        } catch {
            // 如果创建失败，不中断部署流程
        }
    }

    // 手动创建和设置交易对（管理员功能）
    function createAndSetPair() external onlyOwner {
        _createAndSetPair();
    }
}
