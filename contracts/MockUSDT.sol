// SPDX-License-Identifier: MIT
pragma solidity ^0.8.20;

import "@openzeppelin/contracts/token/ERC20/ERC20.sol";

/**
 * @title MockUSDT
 * @dev 用于测试的模拟USDT代币
 */
contract MockUSDT is ERC20 {
    constructor() ERC20("Mock USDT", "USDT") {
        // 铸造100万USDT给部署者
        _mint(msg.sender, 1000000 * 10**6);
    }
    
    function decimals() public pure override returns (uint8) {
        return 6; // USDT使用6位小数
    }
    
    // 方便测试：任何人都可以铸造代币
    function mint(address to, uint256 amount) external {
        _mint(to, amount);
    }
}