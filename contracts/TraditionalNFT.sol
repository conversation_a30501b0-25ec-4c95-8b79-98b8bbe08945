// SPDX-License-Identifier: MIT
pragma solidity ^0.8.20;

import "@openzeppelin/contracts/token/ERC721/ERC721.sol";
import "@openzeppelin/contracts/token/ERC721/extensions/ERC721Enumerable.sol";
import "@openzeppelin/contracts/token/ERC721/extensions/ERC721URIStorage.sol";
import "@openzeppelin/contracts/access/Ownable.sol";
import "@openzeppelin/contracts/utils/ReentrancyGuard.sol";
import "@openzeppelin/contracts/utils/Pausable.sol";

/**
 * @title TraditionalNFT
 * @dev Traditional ERC721 NFT contract implementation
 * Features: minting, batch minting, metadata setting, pause functionality, enumerable functionality
 */
contract TraditionalNFT is
    ERC721,
    ERC721Enumerable,
    ERC721URIStorage,
    Ownable,
    ReentrancyGuard,
    Pausable
{
    // Contract state variables
    uint256 public constant MAX_SUPPLY = 2100; // Maximum supply
    uint256 private _nextTokenId; // Next token ID
    string private _baseTokenURI; // Base URI

    // Price settings
    uint256 public mintPrice = 100 ether; // Mint price
    uint256 public maxMintPerAddress = 10; // Maximum mint per address

    // Minting statistics
    mapping(address => uint256) public minted; // User minted count

    // Event definitions
    event NFTMinted(
        address indexed to,
        uint256 indexed tokenId,
        string tokenURI
    );
    event BatchMinted(address indexed to, uint256[] tokenIds);
    event BaseURIUpdated(string newBaseURI);
    event MintPriceUpdated(uint256 newPrice);
    event MaxMintPerAddressUpdated(uint256 newMax);
    event Withdrawal(address indexed to, uint256 amount);

    constructor(
        string memory name,
        string memory symbol,
        string memory baseURI
    ) ERC721(name, symbol) Ownable(msg.sender) {
        _baseTokenURI = baseURI;
        _nextTokenId = 1; // Start from 1
    }

    /**
     * @dev Set base URI
     * @param newBaseURI New base URI
     */
    function setBaseURI(string calldata newBaseURI) external onlyOwner {
        _baseTokenURI = newBaseURI;
        emit BaseURIUpdated(newBaseURI);
    }

    /**
     * @dev Set mint price
     * @param newPrice New mint price
     */
    function setMintPrice(uint256 newPrice) external onlyOwner {
        mintPrice = newPrice;
        emit MintPriceUpdated(newPrice);
    }

    /**
     * @dev Set maximum mint per address
     * @param newMax New maximum mint count
     */
    function setMaxMintPerAddress(uint256 newMax) external onlyOwner {
        maxMintPerAddress = newMax;
        emit MaxMintPerAddressUpdated(newMax);
    }

    /**
     * @dev Public mint function (paid)
     * @param quantity Quantity to mint
     */
    function mint(
        uint256 quantity
    ) external payable nonReentrant whenNotPaused {
        require(quantity > 0, "Quantity must be greater than 0");
        require(quantity <= 20, "Cannot mint more than 20 at once");
        require(
            _nextTokenId + quantity <= MAX_SUPPLY + 1,
            "Exceeds maximum supply"
        );
        require(
            minted[msg.sender] + quantity <= maxMintPerAddress,
            "Exceeds maximum mint per address"
        );
        require(msg.value >= mintPrice * quantity, "Insufficient payment");

        _mintBatch(msg.sender, quantity);
        minted[msg.sender] += quantity;
    }

    /**
     * @dev Free mint function (owner only)
     * @param to Recipient address
     * @param quantity Quantity to mint
     */
    function mintFor(
        address to,
        uint256 quantity
    ) external onlyOwner whenNotPaused {
        require(to != address(0), "Cannot mint to zero address");
        require(quantity > 0, "Quantity must be greater than 0");
        require(quantity <= 50, "Cannot mint more than 50 at once");
        require(
            _nextTokenId + quantity <= MAX_SUPPLY + 1,
            "Exceeds maximum supply"
        );

        _mintBatch(to, quantity);
    }

    /**
     * @dev Batch mint to multiple addresses (owner only)
     * @param recipients Array of recipient addresses
     * @param quantities Array of corresponding mint quantities
     */
    function batchMintToMultiple(
        address[] calldata recipients,
        uint256[] calldata quantities
    ) external onlyOwner whenNotPaused {
        require(
            recipients.length == quantities.length,
            "Arrays length mismatch"
        );
        require(recipients.length > 0, "Empty arrays");
        require(recipients.length <= 100, "Too many recipients");

        uint256 totalQuantity = 0;
        for (uint256 i = 0; i < quantities.length; i++) {
            totalQuantity += quantities[i];
        }
        require(
            _nextTokenId + totalQuantity <= MAX_SUPPLY + 1,
            "Exceeds maximum supply"
        );

        for (uint256 i = 0; i < recipients.length; i++) {
            require(recipients[i] != address(0), "Cannot mint to zero address");
            require(quantities[i] > 0, "Quantity must be greater than 0");
            _mintBatch(recipients[i], quantities[i]);
        }
    }

    /**
     * @dev Internal batch mint function
     * @param to Recipient address
     * @param quantity Quantity to mint
     */
    function _mintBatch(address to, uint256 quantity) internal {
        uint256[] memory tokenIds = new uint256[](quantity);

        for (uint256 i = 0; i < quantity; i++) {
            uint256 tokenId = _nextTokenId;
            _safeMint(to, tokenId);
            tokenIds[i] = tokenId;
            _nextTokenId++;
        }

        emit BatchMinted(to, tokenIds);
    }

    /**
     * @dev Set URI for specific token
     * @param tokenId Token ID
     * @param uri Token URI
     */
    function setTokenURI(
        uint256 tokenId,
        string memory uri
    ) external onlyOwner {
        require(_ownerOf(tokenId) != address(0), "Token does not exist");
        _setTokenURI(tokenId, uri);
    }

    /**
     * @dev Get current minted token count
     */
    function totalMinted() external view returns (uint256) {
        return _nextTokenId - 1;
    }

    /**
     * @dev Get remaining mintable supply
     */
    function remainingSupply() external view returns (uint256) {
        return MAX_SUPPLY + 1 - _nextTokenId;
    }

    /**
     * @dev Check if a token exists
     * @param tokenId Token ID
     */
    function exists(uint256 tokenId) external view returns (bool) {
        return _ownerOf(tokenId) != address(0);
    }

    /**
     * @dev Get all token IDs owned by user
     * @param owner Owner address
     */
    function tokensOfOwner(
        address owner
    ) external view returns (uint256[] memory) {
        uint256 tokenCount = balanceOf(owner);
        if (tokenCount == 0) {
            return new uint256[](0);
        }

        uint256[] memory tokenIds = new uint256[](tokenCount);
        for (uint256 i = 0; i < tokenCount; i++) {
            tokenIds[i] = tokenOfOwnerByIndex(owner, i);
        }
        return tokenIds;
    }

    /**
     * @dev Pause contract
     */
    function pause() external onlyOwner {
        _pause();
    }

    /**
     * @dev Unpause contract
     */
    function unpause() external onlyOwner {
        _unpause();
    }

    /**
     * @dev Withdraw ETH from contract (owner only)
     */
    function withdraw() external onlyOwner {
        uint256 balance = address(this).balance;
        require(balance > 0, "No funds to withdraw");

        (bool success, ) = payable(owner()).call{value: balance}("");
        require(success, "Withdrawal failed");

        emit Withdrawal(owner(), balance);
    }

    /**
     * @dev Emergency withdraw specified amount of ETH (owner only)
     * @param amount Withdrawal amount
     */
    function emergencyWithdraw(uint256 amount) external onlyOwner {
        require(amount > 0, "Amount must be greater than 0");
        require(address(this).balance >= amount, "Insufficient balance");

        (bool success, ) = payable(owner()).call{value: amount}("");
        require(success, "Withdrawal failed");

        emit Withdrawal(owner(), amount);
    }

    /**
     * @dev Get base URI
     */
    function _baseURI() internal view override returns (string memory) {
        return _baseTokenURI;
    }

    /**
     * @dev Override _update function to support pause functionality
     */
    function _update(
        address to,
        uint256 tokenId,
        address auth
    ) internal override(ERC721, ERC721Enumerable) returns (address) {
        return super._update(to, tokenId, auth);
    }

    /**
     * @dev Override _increaseBalance function
     */
    function _increaseBalance(
        address account,
        uint128 value
    ) internal override(ERC721, ERC721Enumerable) {
        super._increaseBalance(account, value);
    }

    /**
     * @dev Override tokenURI function
     */
    function tokenURI(
        uint256 tokenId
    ) public view override(ERC721, ERC721URIStorage) returns (string memory) {
        return super.tokenURI(tokenId);
    }

    /**
     * @dev Override supportsInterface function
     */
    function supportsInterface(
        bytes4 interfaceId
    )
        public
        view
        override(ERC721, ERC721Enumerable, ERC721URIStorage)
        returns (bool)
    {
        return super.supportsInterface(interfaceId);
    }

    /**
     * @dev Receive ETH
     */
    receive() external payable {}

    /**
     * @dev Fallback function
     */
    fallback() external payable {}
}
