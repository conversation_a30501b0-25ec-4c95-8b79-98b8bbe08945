# NFT持有者批量空投脚本

## 概述

这个脚本用于向NFT持有者批量发送GRAT代币。提供了两个版本：
1. **标准版本** (`batch-airdrop-to-nft-holders.js`) - 使用单笔转账
2. **优化版本** (`optimized-nft-airdrop.js`) - 使用批量转账功能，大幅节省Gas费用

## 功能特性

### ✅ 核心功能
- 🔍 自动扫描NFT合约，获取所有持有者
- 🎯 去重处理，确保每个地址只发送一次
- 💰 自动计算所需代币总量并验证余额
- 📊 实时显示进度和统计信息
- 💾 保存详细的执行结果到JSON文件
- ⛽ Gas费用估算和优化

### ✅ 安全特性
- 🔐 私钥本地输入，不存储
- ✅ 多重确认机制
- 🛡️ 地址格式验证
- 💸 余额检查和保护
- 📝 详细的错误处理和日志

## 使用方法

### 1. 运行标准版本（单笔转账）

```bash
npx hardhat run scripts/batch-airdrop-to-nft-holders.js --network <network-name>
```

### 2. 运行优化版本（批量转账，推荐）

```bash
npx hardhat run scripts/optimized-nft-airdrop.js --network <network-name>
```

### 3. 交互式输入

脚本运行后会提示输入以下信息：

```
请输入发送者私钥: [64位十六进制私钥]
请输入NFT合约地址: [NFT合约地址]
请输入GRAT代币合约地址: [GRAT代币合约地址]
请输入每个NFT持有者获得的代币数量 (默认3000): [代币数量]
```

## 输入说明

### 私钥格式
- 64位十六进制字符串
- 不需要 `0x` 前缀
- 示例: `abcd1234567890abcd1234567890abcd1234567890abcd1234567890abcd1234`

### 合约地址格式
- 标准以太坊地址格式
- 需要 `0x` 前缀
- 示例: `0x1234567890123456789012345678901234567890`

### 代币数量
- 支持小数
- 默认值为 3000
- 示例: `3000`, `1500.5`, `10000`

## 执行流程

### 1. 信息验证阶段
```
🔍 验证私钥格式
🔍 验证合约地址
🔍 检查网络连接
🔍 获取代币信息
🔍 检查发送者余额
```

### 2. NFT扫描阶段
```
📊 获取NFT总供应量
🔍 扫描所有NFT持有者
📈 统计持有者信息
💎 计算所需代币总量
```

### 3. 执行确认阶段
```
📋 显示执行计划
💰 余额充足性检查
⛽ Gas费用估算
🚀 最终确认
```

### 4. 批量转账阶段
```
🔄 执行转账操作
📝 记录交易哈希
⏳ 等待交易确认
✅ 验证转账结果
```

## 输出结果

### 控制台输出
- 实时进度显示
- 成功/失败统计
- Gas费用信息
- 交易哈希

### 结果文件
脚本会在 `airdrop-results/` 目录下生成详细的JSON结果文件：

```json
{
  "timestamp": "2024-01-01T12:00:00.000Z",
  "network": "mainnet",
  "chainId": "1",
  "sender": "0x...",
  "nftContract": "0x...",
  "tokenContract": "0x...",
  "tokenAmount": "3000",
  "tokenSymbol": "GRAT",
  "totalHolders": 150,
  "totalTokensSent": "450000.0",
  "transactionHash": "0x...",
  "gasUsed": "2500000",
  "gasCost": "0.05",
  "recipients": ["0x...", "0x..."]
}
```

## 两个版本的区别

### 标准版本 (`batch-airdrop-to-nft-holders.js`)
- ✅ 使用单笔 `transfer()` 调用
- ✅ 更高的成功率
- ✅ 支持任何ERC20代币
- ❌ Gas费用较高
- ❌ 执行时间较长

### 优化版本 (`optimized-nft-airdrop.js`) 🌟推荐
- ✅ 使用 `batchTransfer()` 功能
- ✅ 大幅节省Gas费用（可节省60-80%）
- ✅ 执行速度快
- ✅ 包含备选方案
- ❌ 需要代币合约支持批量转账

## Gas费用对比

假设向100个地址发送代币：

| 版本 | Gas使用量 | 预估费用 (20 gwei) |
|------|-----------|-------------------|
| 标准版本 | ~2,100,000 | ~0.042 ETH |
| 优化版本 | ~500,000 | ~0.01 ETH |
| **节省** | **~76%** | **~76%** |

## 错误处理

### 常见错误及解决方案

1. **私钥格式错误**
   ```
   错误: 私钥格式不正确，应为64位十六进制字符串
   解决: 确保私钥为64位，不包含0x前缀
   ```

2. **余额不足**
   ```
   错误: 代币余额不足！
   解决: 确保发送者账户有足够的代币余额
   ```

3. **Gas费用不足**
   ```
   错误: insufficient funds for gas
   解决: 确保发送者账户有足够的ETH支付Gas费用
   ```

4. **NFT合约无效**
   ```
   错误: NFT总供应量为0
   解决: 检查NFT合约地址是否正确
   ```

## 安全建议

### 🔒 私钥安全
- 不要在公共网络上运行脚本
- 使用后立即清除命令行历史
- 考虑使用专门的空投钱包

### 💰 资金安全
- 先在测试网络上测试
- 分批执行大量空投
- 保留足够的Gas费用余额

### 🔍 验证检查
- 仔细核对所有地址
- 确认代币数量正确
- 在主网执行前进行测试

## 测试建议

### 1. 测试网测试
```bash
# 在Goerli测试网上测试
npx hardhat run scripts/optimized-nft-airdrop.js --network goerli
```

### 2. 小规模测试
- 先向少量地址（如5-10个）测试
- 验证功能正常后再大规模执行

### 3. 主网执行
- 确保所有测试通过
- 准备充足的Gas费用
- 选择网络拥堵较少的时间

## 技术支持

如遇到问题，请检查：
1. 网络连接是否正常
2. 合约地址是否正确
3. 私钥和余额是否充足
4. Hardhat配置是否正确

---

**⚠️ 重要提醒**: 
- 在主网执行前务必在测试网充分测试
- 确保理解所有操作的风险
- 建议使用优化版本以节省Gas费用
