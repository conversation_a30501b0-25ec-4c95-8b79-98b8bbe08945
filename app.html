<!doctype html>
<html lang="en" class="scroll-smooth">
    <head>
        <meta charset="UTF-8" />
        <meta name="viewport" content="width=device-width, initial-scale=1.0" />
        <title>Gratitude Protocol - Launch App</title>
        <script src="https://cdn.tailwindcss.com"></script>
        <script src="https://cdn.jsdelivr.net/npm/ethers@6.7.1/dist/ethers.umd.min.js"></script>
        <link rel="preconnect" href="https://fonts.googleapis.com" />
        <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin />
        <link
            href="https://fonts.googleapis.com/css2?family=Noto+Sans+JP:wght@400;700&family=Noto+Sans+SC:wght@400;700;900&family=Poppins:wght@400;600;700;800;900&display=swap"
            rel="stylesheet"
        />
        <style>
            body {
                font-family: "Poppins", "Noto Sans SC", "Noto Sans JP", sans-serif;
                background-color: #f9fafb;
                color: #1f2937;
            }
            .font-poppins {
                font-family: "Poppins", sans-serif;
            }
            .hero-bg {
                background-color: #f9fafb;
                background-image:
                    radial-gradient(
                        circle at 5% 5%,
                        rgba(109, 40, 217, 0.05),
                        transparent 30%
                    ),
                    radial-gradient(
                        circle at 95% 95%,
                        rgba(245, 158, 11, 0.05),
                        transparent 30%
                    );
                background-size: 100% 100%;
            }
            .card {
                background-color: white;
                border-radius: 1.5rem;
                box-shadow:
                    0 10px 15px -3px rgb(0 0 0 / 0.05),
                    0 4px 6px -4px rgb(0 0 0 / 0.05);
                transition: all 0.3s ease-in-out;
            }
            .card:hover {
                box-shadow:
                    0 20px 25px -5px rgb(0 0 0 / 0.08),
                    0 8px 10px -6px rgb(0 0 0 / 0.08);
                transform: translateY(-2px);
            }
            .primary-btn {
                background-color: #6d28d9;
                color: white;
                transition: all 0.3s ease;
                box-shadow: 0 4px 15px rgba(109, 40, 217, 0.2);
                font-weight: 600;
            }
            .primary-btn:hover:not(:disabled) {
                background-color: #5b21b6;
                transform: translateY(-2px);
                box-shadow: 0 6px 20px rgba(109, 40, 217, 0.3);
            }
            .primary-btn:disabled {
                background-color: #d1d5db;
                cursor: not-allowed;
                box-shadow: none;
                color: #6b7280;
            }
            .secondary-btn {
                background-color: #f5f3ff;
                color: #6d28d9;
                border: 1px solid #ddd6fe;
                transition: all 0.3s ease;
                font-weight: 600;
            }
            .secondary-btn:hover:not(:disabled) {
                background-color: #ede9fe;
                border-color: #c4b5fd;
                transform: translateY(-2px);
            }
            .secondary-btn:disabled {
                background-color: #e5e7eb;
                color: #9ca3af;
                border-color: transparent;
                cursor: not-allowed;
            }
            .amber-btn {
                background-color: #f59e0b;
                color: white;
                transition: all 0.3s ease;
                box-shadow: 0 4px 15px rgba(245, 158, 11, 0.2);
                font-weight: 600;
            }
            .amber-btn:hover:not(:disabled) {
                background-color: #d97706;
                transform: translateY(-2px);
                box-shadow: 0 6px 20px rgba(245, 158, 11, 0.3);
            }
            .amber-btn:disabled {
                background-color: #d1d5db;
                cursor: not-allowed;
                box-shadow: none;
                color: #6b7280;
            }
            .spinner {
                border: 2px solid #e5e7eb;
                border-top: 2px solid #6d28d9;
                border-radius: 50%;
                width: 16px;
                height: 16px;
                animation: spin 1s linear infinite;
            }
            @keyframes spin {
                0% { transform: rotate(0deg); }
                100% { transform: rotate(360deg); }
            }
            .nft-card {
                position: relative;
                overflow: hidden;
            }
            .nft-card::before {
                content: '';
                position: absolute;
                top: 0;
                left: 0;
                right: 0;
                height: 4px;
                background: linear-gradient(90deg, #6d28d9, #f59e0b);
            }
            .status-dot {
                width: 8px;
                height: 8px;
                border-radius: 50%;
                display: inline-block;
                margin-right: 6px;
            }
            .status-connected { background-color: #10b981; }
            .status-disconnected { background-color: #ef4444; }
            .mining-progress {
                background: linear-gradient(90deg, #6d28d9 0%, #8b5cf6 50%, #f59e0b 100%);
                animation: progress-shine 2s ease-in-out infinite;
            }
            @keyframes progress-shine {
                0%, 100% { opacity: 0.8; }
                50% { opacity: 1; }
            }
            
            /* 钱包弹窗样式 */
            .wallet-modal {
                position: fixed;
                top: 0;
                left: 0;
                right: 0;
                bottom: 0;
                background-color: rgba(0, 0, 0, 0.5);
                backdrop-filter: blur(4px);
                z-index: 1000;
                display: flex;
                align-items: center;
                justify-content: center;
                padding: 1rem;
            }
            .wallet-modal.hidden {
                display: none;
            }
            .wallet-modal-content {
                background: white;
                border-radius: 1.5rem;
                max-width: 600px;
                width: 100%;
                max-height: 90vh;
                overflow-y: auto;
                box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
                animation: modalSlideIn 0.3s ease-out;
            }
            @keyframes modalSlideIn {
                from {
                    opacity: 0;
                    transform: scale(0.9) translateY(-20px);
                }
                to {
                    opacity: 1;
                    transform: scale(1) translateY(0);
                }
            }
            .wallet-close-btn {
                position: absolute;
                top: 1rem;
                right: 1rem;
                background: transparent;
                border: none;
                cursor: pointer;
                padding: 0.5rem;
                border-radius: 0.5rem;
                transition: background-color 0.2s;
            }
            .wallet-close-btn:hover {
                background-color: #f3f4f6;
            }
            .wallet-option-modal {
                transition: all 0.2s;
                border: 2px solid transparent;
            }
            .wallet-option-modal:hover:not(.opacity-50) {
                border-color: #e5e7eb;
                transform: translateY(-2px);
            }
            .wallet-option-modal.connecting {
                border-color: #f59e0b;
                background-color: #fffbeb;
            }
            .wallet-option-modal.selected {
                border-color: #6d28d9;
                background-color: #f5f3ff;
            }
            
            /* 移动端优化 */
            @media (max-width: 768px) {
                .wallet-modal {
                    padding: 0.5rem;
                }
                .wallet-modal-content {
                    max-height: 95vh;
                    border-radius: 1rem;
                }
                .wallet-option-modal {
                    padding: 1rem;
                }
            }
            .mechanism-card {
                border: 2px solid transparent;
                transition: all 0.3s ease;
            }
            .mechanism-card.active {
                border-color: #6d28d9;
                box-shadow: 0 0 0 4px rgba(109, 40, 217, 0.1);
            }
            .wallet-option {
                border: 2px solid transparent;
                position: relative;
                overflow: hidden;
            }
            .wallet-option:hover {
                border-color: #6d28d9;
                box-shadow: 0 0 0 4px rgba(109, 40, 217, 0.1);
            }
            .wallet-option.selected {
                border-color: #6d28d9;
                background: linear-gradient(135deg, rgba(109, 40, 217, 0.05), rgba(245, 158, 11, 0.05));
                box-shadow: 0 0 0 4px rgba(109, 40, 217, 0.1);
            }
            .wallet-option.connecting {
                border-color: #f59e0b;
                background: rgba(245, 158, 11, 0.1);
            }
            .wallet-option::after {
                content: '';
                position: absolute;
                top: 0;
                left: -100%;
                width: 100%;
                height: 100%;
                background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
                transition: left 0.6s;
            }
            .wallet-option:hover::after {
                left: 100%;
            }
            .wallet-status-indicator {
                position: absolute;
                top: 8px;
                right: 8px;
                width: 12px;
                height: 12px;
                border-radius: 50%;
                border: 2px solid white;
            }
            .status-available { background-color: #10b981; }
            .status-connecting { background-color: #f59e0b; }
            .status-unavailable { background-color: #ef4444; }
            
            /* 弹窗样式 */
            .wallet-modal {
                position: fixed;
                top: 0;
                left: 0;
                right: 0;
                bottom: 0;
                background: rgba(0, 0, 0, 0.6);
                backdrop-filter: blur(8px);
                z-index: 1000;
                display: flex;
                align-items: center;
                justify-content: center;
                padding: 16px;
                opacity: 0;
                visibility: hidden;
                transition: all 0.3s ease;
            }
            .wallet-modal.show {
                opacity: 1;
                visibility: visible;
            }
            .wallet-modal-content {
                background: white;
                border-radius: 24px;
                box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
                max-width: 600px;
                width: 100%;
                max-height: 90vh;
                overflow-y: auto;
                transform: scale(0.95) translateY(20px);
                transition: transform 0.3s ease;
            }
            .wallet-modal.show .wallet-modal-content {
                transform: scale(1) translateY(0);
            }
            .wallet-option-modal {
                border: 2px solid transparent;
                transition: all 0.3s ease;
                background: white;
                position: relative;
                overflow: hidden;
            }
            .wallet-option-modal:hover {
                border-color: #6d28d9;
                transform: translateY(-2px);
                box-shadow: 0 10px 25px rgba(109, 40, 217, 0.15);
            }
            .wallet-option-modal.selected {
                border-color: #6d28d9;
                background: linear-gradient(135deg, rgba(109, 40, 217, 0.05), rgba(245, 158, 11, 0.05));
                box-shadow: 0 0 0 4px rgba(109, 40, 217, 0.1);
            }
            .wallet-option-modal.connecting {
                border-color: #f59e0b;
                background: rgba(245, 158, 11, 0.1);
                animation: pulse-border 2s ease-in-out infinite;
            }
            .wallet-option-modal.unavailable {
                opacity: 0.6;
                cursor: not-allowed;
            }
            .wallet-option-modal.unavailable:hover {
                transform: none;
                border-color: transparent;
                box-shadow: none;
            }
            @keyframes pulse-border {
                0%, 100% { border-color: #f59e0b; }
                50% { border-color: #fbbf24; }
            }
            .wallet-close-btn {
                position: absolute;
                top: 16px;
                right: 16px;
                width: 32px;
                height: 32px;
                border-radius: 50%;
                background: rgba(156, 163, 175, 0.1);
                border: none;
                display: flex;
                align-items: center;
                justify-content: center;
                cursor: pointer;
                transition: all 0.2s ease;
            }
            .wallet-close-btn:hover {
                background: rgba(156, 163, 175, 0.2);
                transform: scale(1.1);
            }
            
            /* 移动端优化 */
            @media (max-width: 768px) {
                .wallet-modal {
                    padding: 8px;
                }
                .wallet-modal-content {
                    border-radius: 16px;
                    max-height: 95vh;
                }
                .wallet-option-modal {
                    padding: 16px !important;
                }
                .wallet-option-modal h3 {
                    font-size: 18px !important;
                }
                .wallet-option-modal p {
                    font-size: 14px !important;
                }
            }
        </style>
    </head>
    <body class="w-full">
        <!-- Header -->
        <header class="fixed top-0 left-0 right-0 z-50 bg-white/80 backdrop-blur-md border-b border-gray-200/80">
            <div class="container mx-auto px-6 py-4 flex justify-between items-center">
                <a href="index.html" class="text-2xl font-bold font-poppins">
                    <span class="text-violet-700">$</span><span class="text-gray-800">GRAT</span>
                </a>
                <nav class="hidden md:flex items-center space-x-6 lg:space-x-8 text-gray-600 font-semibold">
                    <a href="index.html" class="hover:text-violet-700 transition-colors">首页</a>
                    <a href="#dashboard" class="hover:text-violet-700 transition-colors">仪表板</a>
                    <a href="#mining" class="hover:text-violet-700 transition-colors">挖矿</a>
                    <a href="#nfts" class="hover:text-violet-700 transition-colors">NFT</a>
                </nav>
                <div class="flex items-center gap-4">
                    <div id="wallet-status" class="hidden items-center gap-2 text-sm">
                        <div class="status-dot status-disconnected" id="connection-dot"></div>
                        <span id="wallet-address" class="text-gray-600">未连接</span>
                    </div>
                    <button
                        id="connect-wallet-btn"
                        class="primary-btn font-bold py-2.5 px-6 rounded-lg"
                    >
                        连接钱包
                    </button>
                    <button
                        id="disconnect-wallet-btn"
                        class="hidden secondary-btn font-bold py-2.5 px-4 rounded-lg text-sm"
                        title="断开钱包连接"
                    >
                        断开
                    </button>
                </div>
            </div>
        </header>

        <!-- Main Content -->
        <div class="pt-36 pb-24 hero-bg min-h-screen">
            <div class="container mx-auto px-6">
                <!-- 钱包未连接状态 -->
                <div id="wallet-connect-prompt" class="max-w-3xl mx-auto text-center">
                    <h1 class="text-4xl md:text-6xl font-black text-gray-900 leading-tight mb-6">
                        欢迎来到 <span class="text-violet-700">$GRAT</span> 生态
                    </h1>
                    <p class="text-lg md:text-xl text-gray-600 max-w-2xl mx-auto mb-12">
                        连接您的钱包，开始质押$GRAT、挖掘$TIPS，体验全新的数字小费文化
                    </p>
                    
                    <div class="card p-12 mb-8">
                        <div class="w-24 h-24 bg-violet-100 rounded-full flex items-center justify-center mx-auto mb-6">
                            <svg class="w-12 h-12 text-violet-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z"/>
                            </svg>
                        </div>
                        <h3 class="text-3xl font-bold text-gray-900 mb-4">连接您的钱包</h3>
                        <p class="text-gray-600 mb-8 text-lg">
                            支持MetaMask、OKX Wallet、TokenPocket等主流钱包
                        </p>
                        <button
                            id="show-wallet-modal-btn"
                            class="primary-btn font-bold py-4 px-8 rounded-xl text-lg inline-flex items-center"
                        >
                            <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z"/>
                            </svg>
                            选择钱包连接
                        </button>
                        
                        <div class="bg-amber-50 border border-amber-200 rounded-lg p-4 mt-8">
                            <div class="flex items-start">
                                <svg class="w-5 h-5 text-amber-500 mt-0.5 mr-3 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"/>
                                </svg>
                                <div class="text-sm text-amber-800">
                                    <p class="font-semibold mb-1">提示：</p>
                                    <p>请确保已安装钱包扩展并切换到BSC网络，应用会自动帮您添加BSC网络配置。</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 仪表板内容（钱包连接后显示） -->
                <div id="dashboard-content" class="hidden">
                    <!-- 用户概览 -->
                    <div class="mb-12">
                        <h1 class="text-3xl md:text-5xl font-black text-gray-900 leading-tight mb-8 text-center">
                            我的 <span class="text-violet-700">$GRAT</span> 仪表板
                        </h1>
                        
                        <div class="grid md:grid-cols-3 gap-6 mb-8">
                            <!-- TokenA余额 -->
                            <div class="card p-6">
                                <div class="flex items-center justify-between mb-4">
                                    <h3 class="text-lg font-bold text-gray-900">TokenA 余额</h3>
                                    <div class="w-12 h-12 bg-violet-100 rounded-full flex items-center justify-center">
                                        <span class="text-violet-600 font-bold text-xl">A</span>
                                    </div>
                                </div>
                                <div class="text-3xl font-bold text-violet-700 mb-2" id="token-a-balance">0</div>
                                <div class="text-sm text-gray-500">可用于挖矿</div>
                            </div>

                            <!-- TokenB余额 -->
                            <div class="card p-6">
                                <div class="flex items-center justify-between mb-4">
                                    <h3 class="text-lg font-bold text-gray-900">TokenB 余额</h3>
                                    <div class="w-12 h-12 bg-amber-100 rounded-full flex items-center justify-center">
                                        <span class="text-amber-600 font-bold text-xl">B</span>
                                    </div>
                                </div>
                                <div class="text-3xl font-bold text-amber-600 mb-2" id="token-b-balance">0</div>
                                <div class="text-sm text-gray-500">小费代币</div>
                            </div>

                            <!-- NFT数量 -->
                            <div class="card p-6">
                                <div class="flex items-center justify-between mb-4">
                                    <h3 class="text-lg font-bold text-gray-900">MinerNFT</h3>
                                    <div class="w-12 h-12 bg-green-100 rounded-full flex items-center justify-center">
                                        <svg class="w-6 h-6 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z"/>
                                        </svg>
                                    </div>
                                </div>
                                <div class="text-3xl font-bold text-green-600 mb-2" id="nft-count">0</div>
                                <div class="text-sm text-gray-500">持有数量</div>
                            </div>
                        </div>

                        <!-- 待释放TokenA信息 -->
                        <div class="card p-8 mb-8">
                            <div class="flex items-center justify-between mb-6">
                                <h3 class="text-2xl font-bold text-gray-900">NFT TokenA 释放进度</h3>
                                <button
                                    id="refresh-nft-btn"
                                    class="secondary-btn px-4 py-2 rounded-lg text-sm"
                                >
                                    刷新数据
                                </button>
                            </div>
                            <div id="nft-release-info">
                                <div class="text-center text-gray-500 py-8">
                                    <div class="spinner mx-auto mb-4"></div>
                                    <p>正在加载NFT释放信息...</p>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 挖矿操作区域 -->
                    <div id="mining" class="mb-12">
                        <h2 class="text-3xl font-bold text-gray-900 mb-8 text-center">挖矿操作</h2>
                        
                        <div class="grid md:grid-cols-2 gap-8">
                            <!-- 机制一：销毁TokenA获得TokenB -->
                            <div class="mechanism-card card p-8">
                                <div class="flex items-center mb-6">
                                    <div class="w-16 h-16 bg-violet-100 rounded-2xl flex items-center justify-center mr-4">
                                        <svg class="w-8 h-8 text-violet-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z"/>
                                        </svg>
                                    </div>
                                    <div>
                                        <h3 class="text-2xl font-bold text-gray-900">机制一</h3>
                                        <p class="text-gray-600">销毁TokenA获得TokenB</p>
                                    </div>
                                </div>
                                
                                <div class="space-y-4 mb-6">
                                    <div>
                                        <label class="block text-sm font-semibold text-gray-700 mb-2">
                                            销毁TokenA数量
                                        </label>
                                        <div class="relative">
                                            <input
                                                type="number"
                                                id="burn-token-a-amount"
                                                placeholder="输入要销毁的TokenA数量"
                                                class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-violet-500 focus:border-transparent"
                                            />
                                            <button
                                                id="max-token-a-btn"
                                                class="absolute right-2 top-1/2 transform -translate-y-1/2 text-xs text-violet-600 hover:text-violet-700 font-semibold"
                                            >
                                                MAX
                                            </button>
                                        </div>
                                    </div>
                                    <div class="bg-gray-50 p-4 rounded-lg">
                                        <div class="text-sm text-gray-600 mb-2">预期获得:</div>
                                        <div class="text-lg font-bold text-violet-700" id="expected-token-b">0 TokenB</div>
                                        <div class="text-xs text-gray-500 mt-1">
                                            奖励倍数: 2x | 释放率: 30%/月
                                        </div>
                                    </div>
                                </div>

                                <button
                                    id="burn-token-a-btn"
                                    class="primary-btn w-full py-3 px-6 rounded-lg flex items-center justify-center"
                                    disabled
                                >
                                    <span id="burn-token-a-text">销毁TokenA挖矿</span>
                                    <div id="burn-token-a-spinner" class="spinner hidden ml-2"></div>
                                </button>
                            </div>

                            <!-- 机制二：持有NFT销毁TokenB -->
                            <div class="mechanism-card card p-8">
                                <div class="flex items-center mb-6">
                                    <div class="w-16 h-16 bg-amber-100 rounded-2xl flex items-center justify-center mr-4">
                                        <svg class="w-8 h-8 text-amber-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z"/>
                                        </svg>
                                    </div>
                                    <div>
                                        <h3 class="text-2xl font-bold text-gray-900">机制二</h3>
                                        <p class="text-gray-600">持有NFT销毁TokenB</p>
                                    </div>
                                </div>
                                
                                <div class="space-y-4 mb-6">
                                    <div class="bg-amber-50 p-3 rounded-lg border border-amber-200">
                                        <div class="text-sm text-amber-800">
                                            <span class="font-semibold">需要持有NFT</span>
                                            <span id="nft-requirement-status" class="float-right">✗ 未满足</span>
                                        </div>
                                    </div>
                                    <div>
                                        <label class="block text-sm font-semibold text-gray-700 mb-2">
                                            销毁TokenB数量
                                        </label>
                                        <div class="relative">
                                            <input
                                                type="number"
                                                id="burn-token-b-amount"
                                                placeholder="输入要销毁的TokenB数量"
                                                class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-amber-500 focus:border-transparent"
                                                disabled
                                            />
                                            <button
                                                id="max-token-b-btn"
                                                class="absolute right-2 top-1/2 transform -translate-y-1/2 text-xs text-amber-600 hover:text-amber-700 font-semibold"
                                                disabled
                                            >
                                                MAX
                                            </button>
                                        </div>
                                    </div>
                                    <div class="bg-gray-50 p-4 rounded-lg">
                                        <div class="text-sm text-gray-600 mb-2">预期释放价值:</div>
                                        <div class="text-lg font-bold text-amber-600" id="expected-release-value">0 BNB</div>
                                        <div class="text-xs text-gray-500 mt-1">
                                            奖励倍数: 2x | 释放率: 30%/月
                                        </div>
                                    </div>
                                </div>

                                <button
                                    id="burn-token-b-btn"
                                    class="amber-btn w-full py-3 px-6 rounded-lg flex items-center justify-center"
                                    disabled
                                >
                                    <span id="burn-token-b-text">销毁TokenB挖矿</span>
                                    <div id="burn-token-b-spinner" class="spinner hidden ml-2"></div>
                                </button>
                            </div>
                        </div>
                    </div>

                    <!-- 挖矿记录和领取 -->
                    <div class="mb-12">
                        <h2 class="text-3xl font-bold text-gray-900 mb-8 text-center">挖矿记录 & 奖励领取</h2>
                        
                        <div class="grid md:grid-cols-2 gap-8">
                            <!-- 机制一记录 -->
                            <div class="card p-8">
                                <div class="flex items-center justify-between mb-6">
                                    <h3 class="text-xl font-bold text-gray-900">机制一记录</h3>
                                    <button
                                        id="claim-mechanism1-btn"
                                        class="primary-btn px-4 py-2 rounded-lg"
                                        disabled
                                    >
                                        领取奖励
                                    </button>
                                </div>
                                <div id="mechanism1-records">
                                    <div class="text-center text-gray-500 py-8">
                                        暂无挖矿记录
                                    </div>
                                </div>
                            </div>

                            <!-- 机制二记录 -->
                            <div class="card p-8">
                                <div class="flex items-center justify-between mb-6">
                                    <h3 class="text-xl font-bold text-gray-900">机制二记录</h3>
                                    <button
                                        id="claim-mechanism2-btn"
                                        class="amber-btn px-4 py-2 rounded-lg"
                                        disabled
                                    >
                                        领取奖励
                                    </button>
                                </div>
                                <div id="mechanism2-records">
                                    <div class="text-center text-gray-500 py-8">
                                        暂无挖矿记录
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- NFT详情 -->
                    <div id="nfts" class="mb-12">
                        <h2 class="text-3xl font-bold text-gray-900 mb-8 text-center">我的 MinerNFT</h2>
                        
                        <div class="card p-8">
                            <div class="flex items-center justify-between mb-6">
                                <h3 class="text-xl font-bold text-gray-900">NFT列表</h3>
                                <button
                                    id="claim-all-nft-btn"
                                    class="secondary-btn px-4 py-2 rounded-lg"
                                    disabled
                                >
                                    一键释放所有TokenA
                                </button>
                            </div>
                            <div id="nft-list" class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                                <div class="text-center text-gray-500 py-12 col-span-full">
                                    <svg class="w-16 h-16 text-gray-300 mx-auto mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z"/>
                                    </svg>
                                    <p>暂无NFT</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 钱包选择弹窗 -->
        <div id="wallet-modal" class="wallet-modal hidden">
            <div class="wallet-modal-content p-6 relative">
                <button class="wallet-close-btn" id="close-wallet-modal">
                    <svg class="w-4 h-4 text-gray-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"/>
                    </svg>
                </button>
                
                <div class="text-center mb-8">
                    <div class="w-16 h-16 bg-violet-100 rounded-2xl flex items-center justify-center mx-auto mb-4">
                        <svg class="w-8 h-8 text-violet-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z"/>
                        </svg>
                    </div>
                    <h2 class="text-2xl md:text-3xl font-bold text-gray-900 mb-3">选择钱包</h2>
                    <p class="text-gray-600">选择您喜欢的钱包连接到$GRAT生态系统</p>
                </div>

                <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
                    <!-- MetaMask -->
                    <div class="wallet-option-modal card p-6 cursor-pointer" data-wallet="metamask">
                        <div class="flex items-center">
                            <div class="w-12 h-12 bg-orange-100 rounded-xl flex items-center justify-center mr-4 flex-shrink-0">
                                <svg class="w-7 h-7" viewBox="0 0 40 40" fill="none">
                                    <path d="M36.0112 3L21.9611 13.1253L24.5313 7.1069L36.0112 3Z" fill="#E17726" stroke="#E17726"/>
                                    <path d="M4.00439 3L18.0033 13.1969L15.4688 7.1069L4.00439 3Z" fill="#E27625" stroke="#E27625"/>
                                    <path d="M30.8131 29.0578L26.9414 35.6012L35.1808 37.7557L37.403 29.1694L30.8131 29.0578Z" fill="#E27625" stroke="#E27625"/>
                                    <path d="M2.61279 29.1694L4.82021 37.7557L13.0596 35.6012L9.18793 29.0578L2.61279 29.1694Z" fill="#E27625" stroke="#E27625"/>
                                </svg>
                            </div>
                            <div class="flex-grow">
                                <h3 class="text-lg font-bold text-gray-900 mb-1">MetaMask</h3>
                                <p class="text-sm text-gray-600">最流行的以太坊钱包</p>
                            </div>
                            <div class="wallet-status-indicator ml-3"></div>
                        </div>
                    </div>

                    <!-- OKX Wallet -->
                    <div class="wallet-option-modal card p-6 cursor-pointer" data-wallet="okx">
                        <div class="flex items-center">
                            <div class="w-12 h-12 bg-black rounded-xl flex items-center justify-center mr-4 flex-shrink-0">
                                <svg class="w-7 h-7" viewBox="0 0 24 24" fill="white">
                                    <path d="M12 2L2 7v10c0 5.55 3.84 9.74 9 10 5.16-.26 9-4.45 9-10V7l-10-5z"/>
                                </svg>
                            </div>
                            <div class="flex-grow">
                                <h3 class="text-lg font-bold text-gray-900 mb-1">OKX Wallet</h3>
                                <p class="text-sm text-gray-600">安全便捷的多链钱包</p>
                            </div>
                            <div class="wallet-status-indicator ml-3"></div>
                        </div>
                    </div>

                    <!-- TokenPocket -->
                    <div class="wallet-option-modal card p-6 cursor-pointer" data-wallet="tokenpocket">
                        <div class="flex items-center">
                            <div class="w-12 h-12 bg-blue-100 rounded-xl flex items-center justify-center mr-4 flex-shrink-0">
                                <svg class="w-7 h-7" viewBox="0 0 24 24" fill="#2563eb">
                                    <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 15l-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z"/>
                                </svg>
                            </div>
                            <div class="flex-grow">
                                <h3 class="text-lg font-bold text-gray-900 mb-1">TokenPocket</h3>
                                <p class="text-sm text-gray-600">专业的DeFi钱包</p>
                            </div>
                            <div class="wallet-status-indicator ml-3"></div>
                        </div>
                    </div>

                    <!-- Trust Wallet -->
                    <div class="wallet-option-modal card p-6 cursor-pointer" data-wallet="trust">
                        <div class="flex items-center">
                            <div class="w-12 h-12 bg-blue-500 rounded-xl flex items-center justify-center mr-4 flex-shrink-0">
                                <svg class="w-7 h-7 text-white" fill="currentColor" viewBox="0 0 24 24">
                                    <path d="M12 1L3 5v6c0 5.55 3.84 9.74 9 10 5.16-.26 9-4.45 9-10V5l-9-4z"/>
                                </svg>
                            </div>
                            <div class="flex-grow">
                                <h3 class="text-lg font-bold text-gray-900 mb-1">Trust Wallet</h3>
                                <p class="text-sm text-gray-600">安全的移动端钱包</p>
                            </div>
                            <div class="wallet-status-indicator ml-3"></div>
                        </div>
                    </div>
                </div>

                <div class="bg-gray-50 rounded-lg p-4 mb-6">
                    <div class="flex items-start">
                        <svg class="w-5 h-5 text-blue-500 mt-0.5 mr-3 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"/>
                        </svg>
                        <div class="text-sm text-gray-700">
                            <p class="font-semibold mb-1">连接说明：</p>
                            <ul class="space-y-1">
                                <li>• <span class="inline-block w-2 h-2 bg-green-500 rounded-full mr-2"></span>绿色表示钱包已安装且可用</li>
                                <li>• <span class="inline-block w-2 h-2 bg-red-500 rounded-full mr-2"></span>红色表示钱包未安装</li>
                                <li>• 连接后会自动切换到BSC网络</li>
                            </ul>
                        </div>
                    </div>
                </div>

                <div class="flex flex-col sm:flex-row gap-3">
                    <button
                        id="install-metamask-btn"
                        class="secondary-btn flex-1 py-3 px-4 rounded-lg text-sm font-semibold inline-flex items-center justify-center"
                    >
                        <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-4l-4 4m0 0l-4-4m4 4V4"/>
                        </svg>
                        安装MetaMask
                    </button>
                    <button
                        id="cancel-wallet-modal"
                        class="secondary-btn flex-1 py-3 px-4 rounded-lg text-sm font-semibold"
                    >
                        取消
                    </button>
                </div>
            </div>
        </div>

        <!-- 加载中遮罩 -->
        <div id="loading-overlay" class="hidden fixed inset-0 bg-black bg-opacity-50 z-50 flex items-center justify-center">
            <div class="bg-white rounded-lg p-8 max-w-sm mx-4">
                <div class="text-center">
                    <div class="spinner mx-auto mb-4" style="width: 32px; height: 32px;"></div>
                    <p class="text-gray-700 font-semibold" id="loading-text">处理中...</p>
                </div>
            </div>
        </div>

        <!-- 成功/错误提示 -->
        <div id="notification" class="hidden fixed top-24 right-4 z-50 p-4 rounded-lg shadow-lg max-w-sm">
            <div class="flex items-center">
                <div class="flex-shrink-0">
                    <svg id="notification-icon" class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"/>
                    </svg>
                </div>
                <div class="ml-3">
                    <p id="notification-text" class="text-sm font-medium"></p>
                </div>
                <button id="notification-close" class="ml-auto text-gray-400 hover:text-gray-600">
                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"/>
                    </svg>
                </button>
            </div>
        </div>

        <script>
            // 合约地址配置 (需要替换为实际部署的合约地址)
            const CONTRACT_ADDRESSES = {
                TokenA: "0x...", // 替换为TokenA合约地址
                TokenB: "0x...", // 替换为TokenB合约地址
                MinerNFT: "0x...", // 替换为MinerNFT合约地址
                MiningContract: "0x...", // 替换为MiningContract合约地址
                PriceOracle: "0x..." // 替换为PriceOracle合约地址
            };

            // BSC网络配置
            const BSC_NETWORK = {
                chainId: '0x38', // BSC Mainnet: 56, BSC Testnet: 97
                chainName: 'Binance Smart Chain',
                nativeCurrency: {
                    name: 'BNB',
                    symbol: 'BNB',
                    decimals: 18
                },
                rpcUrls: ['https://bsc-dataseed.binance.org/'],
                blockExplorerUrls: ['https://bscscan.com/']
            };

            // 合约ABI (简化版本，实际使用时需要完整的ABI)
            const ERC20_ABI = [
                "function balanceOf(address owner) view returns (uint256)",
                "function approve(address spender, uint256 amount) returns (bool)",
                "function allowance(address owner, address spender) view returns (uint256)"
            ];

            const ERC721_ABI = [
                "function balanceOf(address owner) view returns (uint256)",
                "function ownerOf(uint256 tokenId) view returns (address)",
                "function getUserNFTs(address user) view returns (uint256[])"
            ];

            const MINING_CONTRACT_ABI = [
                "function burnTokenAForTokenB(uint256 amount)",
                "function burnTokenBWithNFT(uint256 amount)",
                "function claimTokenBFromMechanism1()",
                "function claimTokenBFromMechanism2()",
                "function getUserClaimableAmount1(address user) view returns (uint256)",
                "function getUserClaimableAmount2(address user) view returns (uint256)",
                "function getUserMiningRecordCount(address user) view returns (uint256, uint256)"
            ];

            const MINER_NFT_ABI = [
                "function balanceOf(address owner) view returns (uint256)",
                "function getUserNFTs(address user) view returns (uint256[])",
                "function getReleasableAmount(uint256 tokenId) view returns (uint256)",
                "function releaseTokens(uint256 tokenId)",
                "function batchReleaseTokens()"
            ];

            // 全局变量
            let provider = null;
            let signer = null;
            let userAddress = null;
            let contracts = {};
            let selectedWallet = null;
            let isConnecting = false;

            // 钱包配置
            const WALLET_CONFIGS = {
                metamask: {
                    name: 'MetaMask',
                    icon: '🦊',
                    provider: () => window.ethereum,
                    checkAvailable: () => {
                        return window.ethereum && window.ethereum.isMetaMask && !window.ethereum.isOkxWallet && !window.ethereum.isTokenPocket;
                    },
                    connect: async () => {
                        const provider = window.ethereum;
                        if (!provider || !provider.isMetaMask) throw new Error('请安装MetaMask钱包');
                        return await provider.request({ method: 'eth_requestAccounts' });
                    }
                },
                okx: {
                    name: 'OKX Wallet',
                    icon: '🟡',
                    provider: () => window.okxwallet || (window.ethereum && window.ethereum.isOkxWallet ? window.ethereum : null),
                    checkAvailable: () => {
                        return window.okxwallet || (window.ethereum && window.ethereum.isOkxWallet);
                    },
                    connect: async () => {
                        const provider = window.okxwallet || (window.ethereum && window.ethereum.isOkxWallet ? window.ethereum : null);
                        if (!provider) throw new Error('请安装OKX钱包');
                        return await provider.request({ method: 'eth_requestAccounts' });
                    }
                },
                tokenpocket: {
                    name: 'TokenPocket',
                    icon: '💼',
                    provider: () => {
                        if (window.tokenpocket?.ethereum) return window.tokenpocket.ethereum;
                        if (window.ethereum && window.ethereum.isTokenPocket) return window.ethereum;
                        return null;
                    },
                    checkAvailable: () => {
                        return window.tokenpocket || (window.ethereum && window.ethereum.isTokenPocket);
                    },
                    connect: async () => {
                        let provider = null;
                        if (window.tokenpocket?.ethereum) {
                            provider = window.tokenpocket.ethereum;
                        } else if (window.ethereum && window.ethereum.isTokenPocket) {
                            provider = window.ethereum;
                        }
                        if (!provider) throw new Error('请安装TokenPocket钱包');
                        return await provider.request({ method: 'eth_requestAccounts' });
                    }
                },
                trust: {
                    name: 'Trust Wallet',
                    icon: '🛡️',
                    provider: () => {
                        if (window.trustwallet) return window.trustwallet;
                        if (window.ethereum && window.ethereum.isTrust) return window.ethereum;
                        return null;
                    },
                    checkAvailable: () => {
                        return window.trustwallet || (window.ethereum && window.ethereum.isTrust);
                    },
                    connect: async () => {
                        let provider = null;
                        if (window.trustwallet) {
                            provider = window.trustwallet;
                        } else if (window.ethereum && window.ethereum.isTrust) {
                            provider = window.ethereum;
                        }
                        if (!provider) throw new Error('请安装Trust Wallet');
                        return await provider.request({ method: 'eth_requestAccounts' });
                    }
                }
            };

            // DOM 元素
            const elements = {
                connectWalletBtn: document.getElementById('connect-wallet-btn'),
                disconnectWalletBtn: document.getElementById('disconnect-wallet-btn'),
                walletConnectPrompt: document.getElementById('wallet-connect-prompt'),
                dashboardContent: document.getElementById('dashboard-content'),
                walletStatus: document.getElementById('wallet-status'),
                connectionDot: document.getElementById('connection-dot'),
                walletAddress: document.getElementById('wallet-address'),
                tokenABalance: document.getElementById('token-a-balance'),
                tokenBBalance: document.getElementById('token-b-balance'),
                nftCount: document.getElementById('nft-count')
            };

            // 初始化
            document.addEventListener('DOMContentLoaded', async () => {
                await initializeApp();
                setupEventListeners();
            });

            async function initializeApp() {
                // 初始化钱包选项状态
                initializeWalletOptions();
                
                // 检查是否已连接钱包
                await checkExistingConnection();
            }

            function initializeWalletOptions() {
                const walletOptions = document.querySelectorAll('.wallet-option');
                walletOptions.forEach(option => {
                    const walletType = option.dataset.wallet;
                    const config = WALLET_CONFIGS[walletType];
                    
                    // 添加状态指示器
                    const indicator = document.createElement('div');
                    indicator.className = 'wallet-status-indicator';
                    option.appendChild(indicator);
                    
                    // 检查钱包可用性
                    if (config.checkAvailable()) {
                        indicator.className += ' status-available';
                        option.addEventListener('click', () => handleWalletSelection(walletType));
                    } else {
                        indicator.className += ' status-unavailable';
                        option.addEventListener('click', () => {
                            showNotification(`请安装${config.name}钱包`, 'error');
                        });
                        option.style.opacity = '0.6';
                    }
                });
            }

            async function checkExistingConnection() {
                // 检查各种钱包的现有连接
                for (const [walletType, config] of Object.entries(WALLET_CONFIGS)) {
                    if (config.checkAvailable()) {
                        try {
                            const provider = config.provider();
                            if (provider) {
                                const accounts = await provider.request({ method: 'eth_accounts' });
                                if (accounts.length > 0) {
                                    selectedWallet = walletType;
                                    await connectWalletWithType(walletType);
                                    return;
                                }
                            }
                        } catch (error) {
                            console.log(`检查${config.name}连接失败:`, error);
                        }
                    }
                }
            }

            function setupEventListeners() {
                // 连接钱包按钮 - 点击显示钱包选择弹窗
                if (elements.connectWalletBtn) {
                    elements.connectWalletBtn.addEventListener('click', () => {
                        if (userAddress) {
                            // 已连接时，显示钱包选择弹窗以切换钱包
                            showWalletModal();
                        } else {
                            // 未连接时，显示钱包选择弹窗
                            showWalletModal();
                        }
                    });
                }

                // 断开钱包按钮
                if (elements.disconnectWalletBtn) {
                    elements.disconnectWalletBtn.addEventListener('click', () => {
                        handleDisconnect();
                    });
                }

                // 钱包弹窗事件监听
                setupWalletModalListeners();

                // 显示钱包弹窗按钮
                const showWalletModalBtn = document.getElementById('show-wallet-modal-btn');
                if (showWalletModalBtn) {
                    showWalletModalBtn.addEventListener('click', showWalletModal);
                }

                // 挖矿相关按钮
                document.getElementById('max-token-a-btn').addEventListener('click', setMaxTokenA);
                document.getElementById('max-token-b-btn').addEventListener('click', setMaxTokenB);
                document.getElementById('burn-token-a-btn').addEventListener('click', burnTokenA);
                document.getElementById('burn-token-b-btn').addEventListener('click', burnTokenB);

                // 领取奖励按钮
                document.getElementById('claim-mechanism1-btn').addEventListener('click', claimMechanism1);
                document.getElementById('claim-mechanism2-btn').addEventListener('click', claimMechanism2);
                document.getElementById('claim-all-nft-btn').addEventListener('click', claimAllNFT);

                // 刷新按钮
                document.getElementById('refresh-nft-btn').addEventListener('click', refreshNFTData);

                // 输入框变化监听
                document.getElementById('burn-token-a-amount').addEventListener('input', calculateExpectedTokenB);
                document.getElementById('burn-token-b-amount').addEventListener('input', calculateExpectedReleaseValue);

                // 关闭通知
                document.getElementById('notification-close').addEventListener('click', hideNotification);

                // 钱包事件监听已在setupWalletEventListeners中处理
            }

            // 处理钱包选择
            async function handleWalletSelection(walletType) {
                if (isConnecting) return;
                
                // 清除之前的选择状态
                document.querySelectorAll('.wallet-option').forEach(option => {
                    option.classList.remove('selected', 'connecting');
                });

                // 标记当前选择
                const selectedOption = document.querySelector(`[data-wallet="${walletType}"]`);
                selectedOption.classList.add('connecting');
                
                await connectWalletWithType(walletType);
            }

            // 连接指定类型的钱包
            async function connectWalletWithType(walletType) {
                if (isConnecting) return;
                isConnecting = true;

                const config = WALLET_CONFIGS[walletType];
                const selectedOption = document.querySelector(`[data-wallet="${walletType}"]`);

                try {
                    showLoading(`连接${config.name}中...`);

                    // 执行钱包连接
                    const accounts = await config.connect();

                    if (accounts.length === 0) {
                        throw new Error('未选择账户');
                    }

                    // 获取provider
                    const walletProvider = config.provider();
                    if (!walletProvider) {
                        throw new Error(`无法获取${config.name}提供者`);
                    }

                    // 检查网络
                    const chainId = await walletProvider.request({ method: 'eth_chainId' });
                    if (chainId !== BSC_NETWORK.chainId) {
                        await switchToBSC(walletProvider);
                    }

                    // 初始化provider和signer
                    provider = new ethers.BrowserProvider(walletProvider);
                    signer = await provider.getSigner();
                    userAddress = accounts[0];
                    selectedWallet = walletType;

                    // 初始化合约实例
                    contracts = {
                        tokenA: new ethers.Contract(CONTRACT_ADDRESSES.TokenA, ERC20_ABI, signer),
                        tokenB: new ethers.Contract(CONTRACT_ADDRESSES.TokenB, ERC20_ABI, signer),
                        minerNFT: new ethers.Contract(CONTRACT_ADDRESSES.MinerNFT, MINER_NFT_ABI, signer),
                        miningContract: new ethers.Contract(CONTRACT_ADDRESSES.MiningContract, MINING_CONTRACT_ABI, signer)
                    };

                    // 更新UI
                    updateWalletUI();
                    if (selectedOption) {
                        selectedOption.classList.remove('connecting');
                        selectedOption.classList.add('selected');
                    }
                    
                    elements.walletConnectPrompt.classList.add('hidden');
                    elements.dashboardContent.classList.remove('hidden');

                    // 监听钱包事件
                    setupWalletEventListeners(walletProvider);

                    // 加载用户数据
                    await loadUserData();

                    hideLoading();
                    showNotification(`${config.name}连接成功`, 'success');

                } catch (error) {
                    hideLoading();
                    isConnecting = false;
                    
                    if (selectedOption) {
                        selectedOption.classList.remove('connecting', 'selected');
                    }
                    
                    console.error(`连接${config.name}失败:`, error);
                    showNotification(`连接失败: ${error.message}`, 'error');
                } finally {
                    isConnecting = false;
                }
            }

            // 设置钱包事件监听器
            function setupWalletEventListeners(walletProvider) {
                if (walletProvider && walletProvider.on) {
                    walletProvider.on('accountsChanged', handleAccountsChanged);
                    walletProvider.on('chainChanged', handleChainChanged);
                    walletProvider.on('disconnect', handleDisconnect);
                }
            }

            // 兼容旧的connectWallet函数（用于其他地方调用）
            async function connectWallet() {
                // 如果有已选择的钱包，直接连接
                if (selectedWallet) {
                    await connectWalletWithType(selectedWallet);
                } else {
                    // 否则提示选择钱包
                    showNotification('请选择一个钱包连接', 'error');
                }
            }

            async function switchToBSC(walletProvider = null) {
                const provider = walletProvider || window.ethereum;
                if (!provider) {
                    throw new Error('无法获取钱包提供者');
                }

                try {
                    await provider.request({
                        method: 'wallet_switchEthereumChain',
                        params: [{ chainId: BSC_NETWORK.chainId }]
                    });
                } catch (switchError) {
                    if (switchError.code === 4902) {
                        // 网络不存在，尝试添加
                        await provider.request({
                            method: 'wallet_addEthereumChain',
                            params: [BSC_NETWORK]
                        });
                    } else {
                        throw switchError;
                    }
                }
            }

            function updateWalletUI() {
                if (userAddress && selectedWallet) {
                    const walletConfig = WALLET_CONFIGS[selectedWallet];
                    elements.connectWalletBtn.textContent = `${walletConfig.icon} ${walletConfig.name}`;
                    elements.connectWalletBtn.disabled = false; // 允许点击切换钱包
                    elements.connectWalletBtn.title = '点击切换钱包';
                    elements.disconnectWalletBtn.classList.remove('hidden'); // 显示断开按钮
                    elements.walletStatus.classList.remove('hidden');
                    elements.walletStatus.classList.add('flex');
                    elements.connectionDot.className = 'status-dot status-connected';
                    elements.walletAddress.textContent = `${userAddress.slice(0, 6)}...${userAddress.slice(-4)}`;
                } else {
                    elements.connectWalletBtn.textContent = '连接钱包';
                    elements.connectWalletBtn.disabled = false;
                    elements.connectWalletBtn.title = '';
                    elements.disconnectWalletBtn.classList.add('hidden'); // 隐藏断开按钮
                    elements.walletStatus.classList.add('hidden');
                    elements.connectionDot.className = 'status-dot status-disconnected';
                    elements.walletAddress.textContent = '未连接';
                }
            }

            async function loadUserData() {
                try {
                    showLoading('加载数据中...');

                    // 并行加载所有数据
                    await Promise.all([
                        loadTokenBalances(),
                        loadNFTData(),
                        loadMiningRecords()
                    ]);

                    hideLoading();
                } catch (error) {
                    hideLoading();
                    console.error('加载用户数据失败:', error);
                    showNotification('数据加载失败', 'error');
                }
            }

            async function loadTokenBalances() {
                try {
                    const [tokenABalance, tokenBBalance, nftBalance] = await Promise.all([
                        contracts.tokenA.balanceOf(userAddress),
                        contracts.tokenB.balanceOf(userAddress),
                        contracts.minerNFT.balanceOf(userAddress)
                    ]);

                    elements.tokenABalance.textContent = ethers.formatEther(tokenABalance);
                    elements.tokenBBalance.textContent = ethers.formatEther(tokenBBalance);
                    elements.nftCount.textContent = nftBalance.toString();

                    // 更新NFT要求状态
                    const nftRequirementStatus = document.getElementById('nft-requirement-status');
                    const burnTokenBAmount = document.getElementById('burn-token-b-amount');
                    const maxTokenBBtn = document.getElementById('max-token-b-btn');
                    const burnTokenBBtn = document.getElementById('burn-token-b-btn');

                    if (nftBalance > 0) {
                        nftRequirementStatus.textContent = '✓ 已满足';
                        nftRequirementStatus.className = 'float-right text-green-600';
                        burnTokenBAmount.disabled = false;
                        maxTokenBBtn.disabled = false;
                        burnTokenBBtn.disabled = false;
                    } else {
                        nftRequirementStatus.textContent = '✗ 未满足';
                        nftRequirementStatus.className = 'float-right text-red-600';
                        burnTokenBAmount.disabled = true;
                        maxTokenBBtn.disabled = true;
                        burnTokenBBtn.disabled = true;
                    }

                } catch (error) {
                    console.error('加载代币余额失败:', error);
                }
            }

            async function loadNFTData() {
                try {
                    const nftIds = await contracts.minerNFT.getUserNFTs(userAddress);
                    const nftReleaseInfo = document.getElementById('nft-release-info');
                    const nftList = document.getElementById('nft-list');
                    
                    if (nftIds.length === 0) {
                        nftReleaseInfo.innerHTML = `
                            <div class="text-center text-gray-500 py-8">
                                <p>您还没有MinerNFT</p>
                                <p class="text-sm mt-2">持有NFT可以使用机制二挖矿，并且可以释放内置的TokenA</p>
                            </div>
                        `;
                        return;
                    }

                    // 加载每个NFT的释放信息
                    const nftData = await Promise.all(
                        nftIds.map(async (nftId) => {
                            const releasableAmount = await contracts.minerNFT.getReleasableAmount(nftId);
                            return {
                                id: nftId.toString(),
                                releasableAmount: ethers.formatEther(releasableAmount)
                            };
                        })
                    );

                    // 显示NFT释放汇总
                    const totalReleasable = nftData.reduce((sum, nft) => sum + parseFloat(nft.releasableAmount), 0);
                    
                    nftReleaseInfo.innerHTML = `
                        <div class="grid md:grid-cols-3 gap-4 mb-6">
                            <div class="text-center">
                                <div class="text-2xl font-bold text-violet-700">${nftIds.length}</div>
                                <div class="text-sm text-gray-600">持有NFT数量</div>
                            </div>
                            <div class="text-center">
                                <div class="text-2xl font-bold text-green-600">${totalReleasable.toFixed(4)}</div>
                                <div class="text-sm text-gray-600">可释放TokenA</div>
                            </div>
                            <div class="text-center">
                                <div class="text-2xl font-bold text-blue-600">${(nftIds.length * 100000).toLocaleString()}</div>
                                <div class="text-sm text-gray-600">总TokenA数量</div>
                            </div>
                        </div>
                        ${totalReleasable > 0 ? `
                        <div class="bg-green-50 border border-green-200 rounded-lg p-4">
                            <div class="flex items-center">
                                <svg class="w-5 h-5 text-green-500 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"/>
                                </svg>
                                <span class="text-green-800 font-semibold">有TokenA可以释放！</span>
                            </div>
                        </div>
                        ` : ''}
                    `;

                    // 显示NFT列表
                    nftList.innerHTML = nftData.map(nft => `
                        <div class="nft-card card p-6">
                            <div class="text-center mb-4">
                                <div class="w-16 h-16 bg-gradient-to-br from-violet-500 to-amber-500 rounded-full flex items-center justify-center mx-auto mb-3">
                                    <span class="text-white font-bold text-xl">#${nft.id}</span>
                                </div>
                                <h4 class="font-bold text-gray-900">MinerNFT #${nft.id}</h4>
                            </div>
                            <div class="space-y-2 mb-4">
                                <div class="flex justify-between text-sm">
                                    <span class="text-gray-600">可释放:</span>
                                    <span class="font-semibold text-green-600">${nft.releasableAmount} TokenA</span>
                                </div>
                                <div class="flex justify-between text-sm">
                                    <span class="text-gray-600">总量:</span>
                                    <span class="font-semibold">100,000 TokenA</span>
                                </div>
                            </div>
                            <button 
                                class="secondary-btn w-full py-2 px-4 rounded-lg text-sm"
                                onclick="releaseNFTTokens(${nft.id})"
                                ${parseFloat(nft.releasableAmount) <= 0 ? 'disabled' : ''}
                            >
                                释放TokenA
                            </button>
                        </div>
                    `).join('');

                    // 更新一键释放按钮状态
                    const claimAllBtn = document.getElementById('claim-all-nft-btn');
                    claimAllBtn.disabled = totalReleasable <= 0;

                } catch (error) {
                    console.error('加载NFT数据失败:', error);
                    document.getElementById('nft-release-info').innerHTML = `
                        <div class="text-center text-red-500 py-8">
                            <p>加载NFT数据失败</p>
                        </div>
                    `;
                }
            }

            async function loadMiningRecords() {
                try {
                    const [count1, count2] = await contracts.miningContract.getUserMiningRecordCount(userAddress);
                    const [claimable1, claimable2] = await Promise.all([
                        contracts.miningContract.getUserClaimableAmount1(userAddress),
                        contracts.miningContract.getUserClaimableAmount2(userAddress)
                    ]);

                    // 更新机制一记录
                    const mechanism1Records = document.getElementById('mechanism1-records');
                    if (count1 > 0) {
                        mechanism1Records.innerHTML = `
                            <div class="space-y-3">
                                <div class="flex justify-between items-center p-3 bg-gray-50 rounded-lg">
                                    <span class="text-sm text-gray-600">挖矿记录数量:</span>
                                    <span class="font-semibold">${count1.toString()}</span>
                                </div>
                                <div class="flex justify-between items-center p-3 bg-violet-50 rounded-lg">
                                    <span class="text-sm text-gray-600">可领取价值:</span>
                                    <span class="font-semibold text-violet-700">${ethers.formatEther(claimable1)} BNB</span>
                                </div>
                            </div>
                        `;
                        document.getElementById('claim-mechanism1-btn').disabled = claimable1 <= 0;
                    }

                    // 更新机制二记录
                    const mechanism2Records = document.getElementById('mechanism2-records');
                    if (count2 > 0) {
                        mechanism2Records.innerHTML = `
                            <div class="space-y-3">
                                <div class="flex justify-between items-center p-3 bg-gray-50 rounded-lg">
                                    <span class="text-sm text-gray-600">挖矿记录数量:</span>
                                    <span class="font-semibold">${count2.toString()}</span>
                                </div>
                                <div class="flex justify-between items-center p-3 bg-amber-50 rounded-lg">
                                    <span class="text-sm text-gray-600">可领取价值:</span>
                                    <span class="font-semibold text-amber-600">${ethers.formatEther(claimable2)} BNB</span>
                                </div>
                            </div>
                        `;
                        document.getElementById('claim-mechanism2-btn').disabled = claimable2 <= 0;
                    }

                } catch (error) {
                    console.error('加载挖矿记录失败:', error);
                }
            }

            // 设置最大TokenA数量
            async function setMaxTokenA() {
                try {
                    const balance = await contracts.tokenA.balanceOf(userAddress);
                    document.getElementById('burn-token-a-amount').value = ethers.formatEther(balance);
                    calculateExpectedTokenB();
                } catch (error) {
                    console.error('获取TokenA余额失败:', error);
                }
            }

            // 设置最大TokenB数量
            async function setMaxTokenB() {
                try {
                    const balance = await contracts.tokenB.balanceOf(userAddress);
                    document.getElementById('burn-token-b-amount').value = ethers.formatEther(balance);
                    calculateExpectedReleaseValue();
                } catch (error) {
                    console.error('获取TokenB余额失败:', error);
                }
            }

            // 计算预期TokenB数量
            function calculateExpectedTokenB() {
                const amount = document.getElementById('burn-token-a-amount').value;
                const expectedElement = document.getElementById('expected-token-b');
                
                if (!amount || isNaN(amount) || parseFloat(amount) <= 0) {
                    expectedElement.textContent = '0 TokenB';
                    document.getElementById('burn-token-a-btn').disabled = true;
                    return;
                }

                // 简化计算：假设TokenA价格0.01 BNB，TokenB价格0.02 BNB，2倍奖励
                const tokenAPrice = 0.01;
                const tokenBPrice = 0.02;
                const multiplier = 2;
                
                const burnValue = parseFloat(amount) * tokenAPrice;
                const expectedTokenB = (burnValue * multiplier) / tokenBPrice;
                
                expectedElement.textContent = `${expectedTokenB.toFixed(2)} TokenB`;
                document.getElementById('burn-token-a-btn').disabled = false;
            }

            // 计算预期释放价值
            function calculateExpectedReleaseValue() {
                const amount = document.getElementById('burn-token-b-amount').value;
                const expectedElement = document.getElementById('expected-release-value');
                
                if (!amount || isNaN(amount) || parseFloat(amount) <= 0) {
                    expectedElement.textContent = '0 BNB';
                    return;
                }

                // 简化计算：假设TokenB价格0.02 BNB，2倍奖励
                const tokenBPrice = 0.02;
                const multiplier = 2;
                
                const burnValue = parseFloat(amount) * tokenBPrice;
                const releaseValue = burnValue * multiplier;
                
                expectedElement.textContent = `${releaseValue.toFixed(4)} BNB`;
            }

            // 销毁TokenA挖矿
            async function burnTokenA() {
                const amount = document.getElementById('burn-token-a-amount').value;
                if (!amount || parseFloat(amount) <= 0) {
                    showNotification('请输入有效的TokenA数量', 'error');
                    return;
                }

                try {
                    showLoading('销毁TokenA中...');
                    
                    const amountWei = ethers.parseEther(amount);
                    
                    // 检查授权
                    const allowance = await contracts.tokenA.allowance(userAddress, CONTRACT_ADDRESSES.MiningContract);
                    if (allowance < amountWei) {
                        showLoading('授权TokenA中...');
                        const approveTx = await contracts.tokenA.approve(CONTRACT_ADDRESSES.MiningContract, amountWei);
                        await approveTx.wait();
                    }

                    // 执行销毁
                    const burnTx = await contracts.miningContract.burnTokenAForTokenB(amountWei);
                    await burnTx.wait();

                    hideLoading();
                    showNotification('TokenA销毁成功！', 'success');
                    
                    // 清空输入框并刷新数据
                    document.getElementById('burn-token-a-amount').value = '';
                    calculateExpectedTokenB();
                    await loadUserData();

                } catch (error) {
                    hideLoading();
                    console.error('销毁TokenA失败:', error);
                    showNotification(`销毁失败: ${error.message}`, 'error');
                }
            }

            // 销毁TokenB挖矿
            async function burnTokenB() {
                const amount = document.getElementById('burn-token-b-amount').value;
                if (!amount || parseFloat(amount) <= 0) {
                    showNotification('请输入有效的TokenB数量', 'error');
                    return;
                }

                try {
                    showLoading('销毁TokenB中...');
                    
                    const amountWei = ethers.parseEther(amount);
                    
                    // 检查授权
                    const allowance = await contracts.tokenB.allowance(userAddress, CONTRACT_ADDRESSES.MiningContract);
                    if (allowance < amountWei) {
                        showLoading('授权TokenB中...');
                        const approveTx = await contracts.tokenB.approve(CONTRACT_ADDRESSES.MiningContract, amountWei);
                        await approveTx.wait();
                    }

                    // 执行销毁
                    const burnTx = await contracts.miningContract.burnTokenBWithNFT(amountWei);
                    await burnTx.wait();

                    hideLoading();
                    showNotification('TokenB销毁成功！', 'success');
                    
                    // 清空输入框并刷新数据
                    document.getElementById('burn-token-b-amount').value = '';
                    calculateExpectedReleaseValue();
                    await loadUserData();

                } catch (error) {
                    hideLoading();
                    console.error('销毁TokenB失败:', error);
                    showNotification(`销毁失败: ${error.message}`, 'error');
                }
            }

            // 领取机制一奖励
            async function claimMechanism1() {
                try {
                    showLoading('领取奖励中...');
                    
                    const claimTx = await contracts.miningContract.claimTokenBFromMechanism1();
                    await claimTx.wait();

                    hideLoading();
                    showNotification('机制一奖励领取成功！', 'success');
                    await loadUserData();

                } catch (error) {
                    hideLoading();
                    console.error('领取机制一奖励失败:', error);
                    showNotification(`领取失败: ${error.message}`, 'error');
                }
            }

            // 领取机制二奖励
            async function claimMechanism2() {
                try {
                    showLoading('领取奖励中...');
                    
                    const claimTx = await contracts.miningContract.claimTokenBFromMechanism2();
                    await claimTx.wait();

                    hideLoading();
                    showNotification('机制二奖励领取成功！', 'success');
                    await loadUserData();

                } catch (error) {
                    hideLoading();
                    console.error('领取机制二奖励失败:', error);
                    showNotification(`领取失败: ${error.message}`, 'error');
                }
            }

            // 释放单个NFT的TokenA
            async function releaseNFTTokens(nftId) {
                try {
                    showLoading('释放TokenA中...');
                    
                    const releaseTx = await contracts.minerNFT.releaseTokens(nftId);
                    await releaseTx.wait();

                    hideLoading();
                    showNotification(`NFT #${nftId} TokenA释放成功！`, 'success');
                    await loadUserData();

                } catch (error) {
                    hideLoading();
                    console.error('释放TokenA失败:', error);
                    showNotification(`释放失败: ${error.message}`, 'error');
                }
            }

            // 一键释放所有NFT的TokenA
            async function claimAllNFT() {
                try {
                    showLoading('释放所有TokenA中...');
                    
                    const releaseTx = await contracts.minerNFT.batchReleaseTokens();
                    await releaseTx.wait();

                    hideLoading();
                    showNotification('所有NFT TokenA释放成功！', 'success');
                    await loadUserData();

                } catch (error) {
                    hideLoading();
                    console.error('批量释放TokenA失败:', error);
                    showNotification(`释放失败: ${error.message}`, 'error');
                }
            }

            // 刷新NFT数据
            async function refreshNFTData() {
                await loadNFTData();
                showNotification('NFT数据已刷新', 'success');
            }

            // 处理账户变化
            async function handleAccountsChanged(accounts) {
                if (accounts.length === 0) {
                    // 用户断开连接
                    handleDisconnect();
                } else if (accounts[0] !== userAddress) {
                    // 切换账户，重新连接当前钱包
                    if (selectedWallet) {
                        await connectWalletWithType(selectedWallet);
                    }
                }
            }

            // 处理网络变化
            function handleChainChanged(chainId) {
                if (chainId !== BSC_NETWORK.chainId) {
                    showNotification('请切换到BSC网络', 'error');
                    // 可以选择自动切换或提示用户
                    setTimeout(() => {
                        if (selectedWallet) {
                            switchToBSC(WALLET_CONFIGS[selectedWallet].provider());
                        }
                    }, 2000);
                } else {
                    // 网络正确，重新加载数据
                    if (userAddress) {
                        loadUserData();
                    }
                }
            }

            // 处理钱包断开连接
            function handleDisconnect() {
                userAddress = null;
                provider = null;
                signer = null;
                contracts = {};
                selectedWallet = null;
                
                // 清除钱包选择状态
                document.querySelectorAll('.wallet-option').forEach(option => {
                    option.classList.remove('selected', 'connecting');
                });
                
                updateWalletUI();
                elements.dashboardContent.classList.add('hidden');
                elements.walletConnectPrompt.classList.remove('hidden');
                showNotification('钱包已断开连接', 'error');
            }

            // 显示加载遮罩
            function showLoading(text = '处理中...') {
                document.getElementById('loading-text').textContent = text;
                document.getElementById('loading-overlay').classList.remove('hidden');
            }

            // 隐藏加载遮罩
            function hideLoading() {
                document.getElementById('loading-overlay').classList.add('hidden');
            }

            // 显示通知
            function showNotification(message, type = 'success') {
                const notification = document.getElementById('notification');
                const notificationText = document.getElementById('notification-text');
                const notificationIcon = document.getElementById('notification-icon');

                notificationText.textContent = message;

                if (type === 'success') {
                    notification.className = 'fixed top-24 right-4 z-50 p-4 rounded-lg shadow-lg max-w-sm bg-green-100 text-green-800 border border-green-200';
                    notificationIcon.innerHTML = `
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"/>
                    `;
                } else {
                    notification.className = 'fixed top-24 right-4 z-50 p-4 rounded-lg shadow-lg max-w-sm bg-red-100 text-red-800 border border-red-200';
                    notificationIcon.innerHTML = `
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"/>
                    `;
                }

                notification.classList.remove('hidden');

                // 3秒后自动隐藏
                setTimeout(() => {
                    hideNotification();
                }, 3000);
            }

            // 隐藏通知
            function hideNotification() {
                document.getElementById('notification').classList.add('hidden');
            }

            // 显示钱包选择弹窗
            function showWalletModal() {
                const modal = document.getElementById('wallet-modal');
                modal.classList.remove('hidden');
                // 更新钱包状态指示器
                updateWalletStatusIndicators();
                // 阻止滚动
                document.body.style.overflow = 'hidden';
            }

            // 隐藏钱包选择弹窗
            function hideWalletModal() {
                const modal = document.getElementById('wallet-modal');
                modal.classList.add('hidden');
                // 恢复滚动
                document.body.style.overflow = '';
            }

            // 设置钱包弹窗事件监听器
            function setupWalletModalListeners() {
                // 关闭按钮
                document.getElementById('close-wallet-modal').addEventListener('click', hideWalletModal);
                document.getElementById('cancel-wallet-modal').addEventListener('click', hideWalletModal);

                // 点击背景关闭弹窗
                document.getElementById('wallet-modal').addEventListener('click', (e) => {
                    if (e.target.id === 'wallet-modal') {
                        hideWalletModal();
                    }
                });

                // 钱包选项点击事件
                document.querySelectorAll('.wallet-option-modal').forEach(option => {
                    option.addEventListener('click', async () => {
                        const walletType = option.getAttribute('data-wallet');
                        const config = WALLET_CONFIGS[walletType];
                        
                        if (!config || !config.checkAvailable()) {
                            showNotification(`${config ? config.name : '钱包'} 未安装，请先安装后再试`, 'error');
                            return;
                        }

                        // 隐藏弹窗
                        hideWalletModal();
                        
                        // 连接钱包
                        await handleWalletSelection(walletType);
                    });
                });

                // 安装MetaMask按钮
                document.getElementById('install-metamask-btn').addEventListener('click', () => {
                    window.open('https://metamask.io/download/', '_blank');
                });
            }

            // 更新钱包状态指示器
            function updateWalletStatusIndicators() {
                document.querySelectorAll('.wallet-option-modal').forEach(option => {
                    const walletType = option.getAttribute('data-wallet');
                    const config = WALLET_CONFIGS[walletType];
                    const indicator = option.querySelector('.wallet-status-indicator');
                    
                    if (config && config.checkAvailable()) {
                        // 钱包可用 - 绿色指示器
                        indicator.innerHTML = '<div class="w-3 h-3 bg-green-500 rounded-full"></div>';
                        option.classList.remove('opacity-50');
                        option.style.cursor = 'pointer';
                    } else {
                        // 钱包不可用 - 红色指示器
                        indicator.innerHTML = '<div class="w-3 h-3 bg-red-500 rounded-full"></div>';
                        option.classList.add('opacity-50');
                        option.style.cursor = 'not-allowed';
                    }

                    // 如果是当前连接的钱包，显示特殊状态
                    if (selectedWallet === walletType) {
                        option.classList.add('ring-2', 'ring-violet-500', 'bg-violet-50');
                        indicator.innerHTML = '<div class="w-3 h-3 bg-violet-500 rounded-full animate-pulse"></div>';
                    } else {
                        option.classList.remove('ring-2', 'ring-violet-500', 'bg-violet-50');
                    }
                });
            }

            // 更新原有的钱包选择处理逻辑以适配弹窗
            function initializeWalletOptions() {
                // 更新钱包选择器 (保持与旧版本兼容)
                const walletOptionsContainer = document.getElementById('wallet-options');
                if (walletOptionsContainer) {
                    let walletOptionsHTML = '';
                    
                    for (const [walletType, config] of Object.entries(WALLET_CONFIGS)) {
                        const isAvailable = config.checkAvailable();
                        const statusClass = isAvailable ? 'available' : 'unavailable';
                        const cursorStyle = isAvailable ? 'cursor-pointer' : 'cursor-not-allowed opacity-50';
                        
                        walletOptionsHTML += `
                            <div class="wallet-option ${statusClass} ${cursorStyle} card p-6 hover:shadow-lg transition-all duration-300" 
                                 data-wallet="${walletType}">
                                <div class="flex items-center justify-between mb-4">
                                    <div class="flex items-center">
                                        <div class="w-12 h-12 mr-4 bg-gray-100 rounded-xl flex items-center justify-center">
                                            ${config.icon || '💳'}
                                        </div>
                                        <div>
                                            <h3 class="text-lg font-bold text-gray-900">${config.name}</h3>
                                            <p class="text-sm text-gray-600">${config.description}</p>
                                        </div>
                                    </div>
                                    <div class="wallet-status-indicator">
                                        <div class="w-3 h-3 ${isAvailable ? 'bg-green-500' : 'bg-red-500'} rounded-full"></div>
                                    </div>
                                </div>
                                <div class="text-sm text-gray-500">
                                    状态: ${isAvailable ? '✅ 已安装' : '❌ 未安装'}
                                </div>
                            </div>
                        `;
                    }
                    
                    walletOptionsContainer.innerHTML = walletOptionsHTML;
                    
                    // 添加点击事件监听器给旧版本的钱包选项
                    document.querySelectorAll('#wallet-options .wallet-option').forEach(option => {
                        option.addEventListener('click', async () => {
                            const walletType = option.getAttribute('data-wallet');
                            await handleWalletSelection(walletType);
                        });
                    });
                }

                // 对于弹窗版本，我们在这里也要初始化状态
                updateWalletStatusIndicators();
            }
        </script>
    </body>
</html>