# 部署指南

## 🚀 部署准备

### 环境要求

- Node.js >= 16.0.0
- NPM >= 8.0.0
- 足够的BNB用于Gas费用
- BSC钱包私钥
- BSCScan API密钥（用于合约验证）

### 预估Gas消耗

| 合约 | 部署Gas | 预估费用(BSC) |
|------|---------|---------------|
| PriceOracle | ~1,500,000 | ~0.003 BNB |
| TokenA | ~2,800,000 | ~0.005 BNB |
| TokenB | ~3,200,000 | ~0.006 BNB |
| MinerNFT | ~3,500,000 | ~0.007 BNB |
| MiningContract | ~4,000,000 | ~0.008 BNB |
| **总计** | ~15,000,000 | **~0.03 BNB** |

*注：实际费用根据网络拥堵情况可能有所变化*

## ⚙️ 配置步骤

### 1. 环境变量配置

复制环境变量模板并填入相关信息：

```bash
cp .env.example .env
```

编辑`.env`文件：

```env
# 部署账户私钥 (不要包含0x前缀)
PRIVATE_KEY=your_private_key_here

# BSCScan API密钥 (用于合约验证)
BSCSCAN_API_KEY=your_bscscan_api_key_here

# 营销钱包地址 (必须是有效地址)
MARKETING_WALLET=******************************************

# Gas报告 (可选)
REPORT_GAS=false

# 网络RPC配置 (可选，使用默认值)
BSC_TESTNET_RPC=https://data-seed-prebsc-1-s1.binance.org:8545/
BSC_MAINNET_RPC=https://bsc-dataseed1.binance.org/
```

### 2. 验证配置

检查配置是否正确：

```bash
# 检查账户余额
npx hardhat run scripts/check-balance.js --network bsc_testnet

# 编译合约
npm run compile
```

## 🔧 部署流程

### 测试网部署

```bash
# 1. 部署到BSC测试网
npm run deploy:testnet

# 2. 验证合约 (可选)
npm run verify:testnet

# 3. 检查部署状态
npx hardhat run scripts/interact.js --network bsc_testnet status
```

### 主网部署

```bash
# 1. 最终检查
npm test                    # 运行完整测试
npm run compile            # 重新编译

# 2. 部署到BSC主网
npm run deploy:mainnet

# 3. 验证合约
npm run verify:mainnet

# 4. 检查部署状态
npx hardhat run scripts/interact.js --network bsc_mainnet status
```

## 📋 部署后检查清单

### 自动配置项

部署脚本会自动完成以下配置：

- ✅ 设置TokenA的NFT合约地址
- ✅ 为TokenB添加MiningContract的铸造权限
- ✅ 设置挖矿合约的税收豁免状态
- ✅ 向NFT合约转入TokenA用于释放

### 手动验证项

请手动验证以下配置：

```bash
# 1. 检查合约地址
cat deployments/<network>-deployment.json

# 2. 验证合约关系
npx hardhat run scripts/verify-setup.js --network <network>

# 3. 测试基础功能
npx hardhat run scripts/interact.js --network <network>
```

### 验证检查项

- [ ] 所有合约成功部署
- [ ] 合约地址已记录
- [ ] TokenA总供应量正确 (250,000,000)
- [ ] TokenB总供应量正确 (2,100,000,000,000)
- [ ] NFT最大供应量正确 (2,100)
- [ ] 营销钱包地址设置正确
- [ ] 税收豁免地址配置正确
- [ ] 铸造权限设置正确
- [ ] NFT合约有足够的TokenA余额

## 🔧 部署故障排除

### 常见错误及解决方案

#### 1. Gas估算失败

**错误信息**：
```
Error: cannot estimate gas; transaction may fail
```

**解决方案**：
- 检查账户BNB余额是否充足
- 检查网络连接是否正常
- 增加gasLimit设置

```javascript
// 在hardhat.config.js中增加gas配置
networks: {
  bsc_mainnet: {
    gasPrice: 5000000000, // 5 gwei
    gas: 10000000
  }
}
```

#### 2. 营销钱包地址无效

**错误信息**：
```
Marketing wallet cannot be zero address
```

**解决方案**：
- 确保在`.env`文件中设置了有效的`MARKETING_WALLET`地址
- 检查地址格式是否正确（以0x开头，42字符长度）

#### 3. 合约验证失败

**错误信息**：
```
Verification failed
```

**解决方案**：
- 等待30秒后重试验证
- 检查BSCScan API密钥是否正确
- 确保构造函数参数匹配

```bash
# 手动验证单个合约
npx hardhat verify --network bsc_mainnet CONTRACT_ADDRESS "constructor_arg1" "constructor_arg2"
```

#### 4. 私钥格式错误

**错误信息**：
```
Invalid private key
```

**解决方案**：
- 确保私钥不包含`0x`前缀
- 检查私钥长度是否为64字符
- 确保私钥对应的账户有足够余额

## 📊 部署成本优化

### Gas优化建议

1. **选择合适的时间**：
   - 避免网络拥堵时段
   - 监控Gas价格走势

2. **批量操作**：
   - 使用批量部署脚本
   - 减少单独的配置交易

3. **合约大小优化**：
   - 启用Solidity优化器
   - 移除非必要的功能

### 费用对比

| 网络 | Gas价格 | 部署费用 | 建议用途 |
|------|---------|----------|----------|
| BSC测试网 | 免费 | 0 BNB | 开发测试 |
| BSC主网 | 5 gwei | ~0.03 BNB | 生产环境 |

## 🔐 安全检查

### 部署前安全检查

- [ ] 代码审计完成
- [ ] 测试覆盖率 > 90%
- [ ] 私钥安全存储
- [ ] 多签钱包准备（如需要）
- [ ] 紧急暂停机制测试

### 部署后安全验证

```bash
# 1. 检查合约Owner
npx hardhat console --network <network>
> const contract = await ethers.getContractAt("TokenA", "CONTRACT_ADDRESS")
> await contract.owner()

# 2. 检查权限设置
> await contract.taxExempt("MINING_CONTRACT_ADDRESS")

# 3. 测试紧急功能
> await contract.pause()
> await contract.unpause()
```

## 📝 部署记录模板

```markdown
# 部署记录 - <网络名称>

## 基本信息
- 部署日期：YYYY-MM-DD HH:mm:ss
- 部署者：0x...
- 网络：BSC <主网/测试网>
- Gas价格：X gwei
- 总费用：X BNB

## 合约地址
- PriceOracle: 0x...
- TokenA: 0x...
- TokenB: 0x...
- MinerNFT: 0x...
- MiningContract: 0x...

## 验证状态
- [ ] PriceOracle 已验证
- [ ] TokenA 已验证
- [ ] TokenB 已验证
- [ ] MinerNFT 已验证
- [ ] MiningContract 已验证

## 配置检查
- [ ] 营销钱包设置正确
- [ ] 税收豁免配置完成
- [ ] 铸造权限设置完成
- [ ] NFT合约TokenA余额充足

## 注意事项
- 记录任何特殊配置
- 备份重要交易哈希
- 更新相关文档
```

## 🔄 升级和维护

### 合约升级策略

由于合约不可升级，建议：

1. 在测试网充分测试
2. 考虑使用代理模式（未来版本）
3. 实现数据迁移机制
4. 准备新版本部署方案

### 定期维护任务

- 监控合约状态
- 更新价格预言机
- 检查安全状态
- 备份关键数据

---

**⚠️ 重要提示：部署到主网前请确保充分测试，智能合约一旦部署无法修改！**