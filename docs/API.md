# API文档

## 📚 合约接口文档

本文档详细描述了DeFi生态系统中所有智能合约的公共接口。

## 🪙 TokenA合约

TokenA是生态系统的母币，具有税收和自动分红功能。

### 基础信息

| 属性 | 值 |
|------|-----|
| 名称 | TokenA |
| 符号 | TKA |
| 精度 | 18 |
| 总供应量 | 250,000,000 TKA |

### 读取函数

#### `balanceOf(address account) → uint256`
查询指定地址的代币余额。

```solidity
uint256 balance = tokenA.balanceOf(userAddress);
```

#### `totalSupply() → uint256`
获取代币总供应量。

#### `taxExempt(address account) → bool`
检查地址是否享有税收豁免。

#### `isPair(address pair) → bool`
检查地址是否为交易对。

#### `totalDividends() → uint256`
获取累计分红总额。

#### `calculateDividend(address user) → uint256`
计算用户可领取的分红数量。

```solidity
uint256 dividend = tokenA.calculateDividend(userAddress);
```

#### `contractBalance() → uint256`
查看合约当前代币余额。

### 写入函数

#### `transfer(address to, uint256 amount) → bool`
转账代币（包含税收机制）。

```solidity
bool success = tokenA.transfer(recipient, amount);
```

#### `approve(address spender, uint256 amount) → bool`
授权代币额度。

#### `claimDividend()`
领取分红。

```solidity
tokenA.claimDividend();
```

### 管理员函数

#### `setNftContract(address _nftContract)`
设置NFT合约地址。

#### `setTaxExempt(address account, bool exempt)`
设置税收豁免地址。

#### `setPair(address pair, bool _isPair)`
设置交易对地址。

#### `setMarketingWallet(address _marketingWallet)`
设置营销钱包地址。

#### `pause() / unpause()`
暂停/恢复合约。

#### `emergencyWithdraw()`
紧急提取合约中的代币。

### 事件

```solidity
event TaxCollected(uint256 marketingAmount, uint256 burnAmount, uint256 dividendAmount);
event DividendDistributed(uint256 amount);
event DividendClaimed(address indexed user, uint256 amount);
event TaxExemptUpdated(address indexed account, bool exempt);
event PairUpdated(address indexed pair, bool isPair);
```

---

## 🪙 TokenB合约

TokenB是生态系统的子币，具有税收和铸造功能。

### 基础信息

| 属性 | 值 |
|------|-----|
| 名称 | TokenB |
| 符号 | TKB |
| 精度 | 18 |
| 初始供应量 | 2,100,000,000,000 TKB |

### 读取函数

#### `balanceOf(address account) → uint256`
查询指定地址的代币余额。

#### `totalMinted() → uint256`
获取已铸造总量。

#### `remainingMintableAmount() → uint256`
获取剩余可铸造量。

#### `hasRole(bytes32 role, address account) → bool`
检查地址是否拥有指定角色。

```solidity
bool isMinter = tokenB.hasRole(tokenB.MINTER_ROLE(), address);
```

### 写入函数

#### `transfer(address to, uint256 amount) → bool`
转账代币（包含税收机制）。

#### `burn(uint256 amount)`
销毁代币。

```solidity
tokenB.burn(amount);
```

#### `burnFrom(address account, uint256 amount)`
销毁其他地址的代币（需要授权）。

### 铸造函数

#### `mint(address to, uint256 amount)`
铸造代币（仅铸造者）。

```solidity
tokenB.mint(recipient, amount);
```

#### `batchMint(address[] recipients, uint256[] amounts)`
批量铸造代币。

```solidity
address[] memory recipients = [addr1, addr2];
uint256[] memory amounts = [amount1, amount2];
tokenB.batchMint(recipients, amounts);
```

### 管理员函数

#### `addMinter(address minter)`
添加铸造者权限。

#### `removeMinter(address minter)`
移除铸造者权限。

#### `setTaxExempt(address account, bool exempt)`
设置税收豁免地址。

#### `setMaxMintableAmount(uint256 _maxMintableAmount)`
设置最大可铸造量。

### 事件

```solidity
event TaxCollected(uint256 marketingAmount, uint256 burnAmount);
event TokensMinted(address indexed to, uint256 amount);
event MaxMintableAmountUpdated(uint256 newAmount);
```

---

## 🖼️ MinerNFT合约

MinerNFT合约管理生态系统中的NFT资产，每个NFT包含内置的TokenA。

### 基础信息

| 属性 | 值 |
|------|-----|
| 名称 | MinerNFT |
| 符号 | MNFT |
| 最大供应量 | 2,100 |
| 每NFT代币 | 100,000 TKA |

### 读取函数

#### `balanceOf(address owner) → uint256`
查询用户持有的NFT数量。

#### `ownerOf(uint256 tokenId) → address`
查询NFT的拥有者。

#### `getReleasableAmount(uint256 tokenId) → uint256`
计算NFT可释放的代币数量。

```solidity
uint256 releasable = minerNFT.getReleasableAmount(tokenId);
```

#### `getUserNFTCount(address user) → uint256`
获取用户持有的NFT数量。

#### `getUserNFTs(address user) → uint256[]`
获取用户持有的NFT列表。

```solidity
uint256[] memory userNFTs = minerNFT.getUserNFTs(userAddress);
```

#### `getUserTotalReleasableAmount(address user) → uint256`
获取用户总的可释放代币数量。

#### `nftInfo(uint256 tokenId) → (uint256 totalTokens, uint256 releasedTokens, uint256 lastReleaseTime)`
获取NFT的详细信息。

```solidity
(uint256 total, uint256 released, uint256 lastRelease) = minerNFT.nftInfo(tokenId);
```

### 写入函数

#### `releaseTokens(uint256 tokenId)`
释放单个NFT的代币。

```solidity
minerNFT.releaseTokens(tokenId);
```

#### `batchReleaseTokens()`
批量释放用户所有NFT的代币。

```solidity
minerNFT.batchReleaseTokens();
```

### 管理员函数

#### `mint(address to)`
铸造NFT给指定地址。

#### `batchMint(address[] recipients)`
批量铸造NFT。

```solidity
address[] memory recipients = [addr1, addr2, addr3];
minerNFT.batchMint(recipients);
```

#### `setTokenAAddress(address _tokenAAddress)`
设置TokenA合约地址。

#### `setReleaseParameters(uint256 _startTime, uint256 _interval, uint256 _rate)`
设置释放参数。

```solidity
minerNFT.setReleaseParameters(
    block.timestamp + 1 days,  // 开始时间
    30 days,                   // 释放间隔
    10                         // 释放比例 (10%)
);
```

### 事件

```solidity
event NFTMinted(address indexed to, uint256 indexed tokenId);
event TokensReleased(uint256 indexed tokenId, address indexed owner, uint256 amount);
event BatchTokensReleased(address indexed owner, uint256[] tokenIds, uint256 totalAmount);
event ReleaseParametersUpdated(uint256 startTime, uint256 interval, uint256 rate);
```

---

## ⛏️ MiningContract合约

挖矿合约实现三种挖矿机制和邀请奖励系统。

### 读取函数

#### `getUserInviteInfo(address user) → (address inviter, uint256 totalInvited, uint256 acceleratedReleaseAmount, address[] inviteeList)`
查询用户邀请信息。

```solidity
(
    address inviter,
    uint256 totalInvited,
    uint256 acceleratedAmount,
    address[] memory invitees
) = miningContract.getUserInviteInfo(userAddress);
```

#### `getUserMiningRecordCount(address user) → (uint256 count1, uint256 count2)`
查询用户挖矿记录数量。

#### `getUserClaimableAmount1(address user) → uint256`
查询用户机制一可领取数量。

#### `getUserClaimableAmount2(address user) → uint256`
查询用户机制二可领取数量。

#### `totalBurnedTokenA() → uint256`
获取累计销毁TokenA数量。

#### `totalBurnedTokenB() → uint256`
获取累计销毁TokenB数量。

#### `totalReleasedTokenB() → uint256`
获取累计释放TokenB数量。

### 写入函数

#### `setInviter(address inviter)`
设置邀请人。

```solidity
miningContract.setInviter(inviterAddress);
```

#### `burnTokenAForTokenB(uint256 amount)`
销毁TokenA获得TokenB（机制一）。

```solidity
// 先授权
tokenA.approve(miningContract.address, amount);
// 执行销毁
miningContract.burnTokenAForTokenB(amount);
```

#### `burnTokenBWithNFT(uint256 amount)`
持有NFT并销毁TokenB（机制二）。

```solidity
// 需要持有NFT
require(minerNFT.balanceOf(msg.sender) > 0, "Must hold NFT");
// 授权并销毁
tokenB.approve(miningContract.address, amount);
miningContract.burnTokenBWithNFT(amount);
```

#### `claimTokenBFromMechanism1()`
领取机制一的TokenB奖励。

```solidity
miningContract.claimTokenBFromMechanism1();
```

#### `claimTokenBFromMechanism2()`
领取机制二的TokenB奖励。

```solidity
miningContract.claimTokenBFromMechanism2();
```

### 管理员函数

#### `setReleaseInterval(uint256 _releaseInterval)`
设置释放间隔。

#### `setContractAddresses(address _tokenA, address _tokenB, address _nft, address _oracle)`
设置合约地址。

### 事件

```solidity
event InviterSet(address indexed user, address indexed inviter);
event TokenABurned(address indexed user, uint256 amount, uint256 value, uint256 expectedTokenB);
event TokenBBurned(address indexed user, uint256 amount, uint256 value);
event TokenBReleased(address indexed user, uint256 amount, uint8 mechanism);
event AcceleratedReleaseAdded(address indexed inviter, address indexed invitee, uint256 amount);
event AcceleratedReleaseUsed(address indexed user, uint256 amount);
```

---

## 📊 PriceOracle合约

价格预言机合约提供代币价格查询服务。

### 读取函数

#### `getTokenPrice(address token) → uint256`
获取Token相对于BNB的价格。

```solidity
uint256 price = priceOracle.getTokenPrice(tokenAddress);
// 返回价格 * 1e18
```

#### `getTokenPriceRatio(address tokenA, address tokenB) → uint256`
获取两个Token的价格比率。

```solidity
uint256 ratio = priceOracle.getTokenPriceRatio(tokenA, tokenB);
// 返回 tokenA/tokenB * 1e18
```

#### `getCachedPrice(address token) → (uint256 price, uint256 lastUpdate, bool isValid)`
获取缓存的价格信息。

```solidity
(uint256 price, uint256 lastUpdate, bool isValid) = priceOracle.getCachedPrice(token);
```

#### `isPriceReliable(address token) → bool`
检查价格是否可靠。

#### `getPairAddress(address tokenA, address tokenB) → address`
获取交易对地址。

#### `getPairReserves(address tokenA, address tokenB) → (uint256 reserveA, uint256 reserveB, uint32 blockTimestampLast)`
获取交易对储备量。

### 写入函数

#### `updatePrice(address token)`
更新价格缓存。

```solidity
priceOracle.updatePrice(tokenAddress);
```

#### `batchUpdatePrices(address[] tokens)`
批量更新价格。

```solidity
address[] memory tokens = [tokenA, tokenB];
priceOracle.batchUpdatePrices(tokens);
```

### 管理员函数

#### `setPriceParameters(uint256 _maxPriceDeviation, uint256 _minUpdateInterval, uint256 _maxPriceAge)`
设置价格保护参数。

#### `setManualPrice(address token, uint256 price)`
手动设置价格（紧急情况）。

#### `clearPriceCache(address token)`
清除价格缓存。

### 事件

```solidity
event PriceUpdated(address indexed token, uint256 price, uint256 timestamp);
event PriceParametersUpdated(uint256 maxDeviation, uint256 minInterval, uint256 maxAge);
```

---

## 📝 使用示例

### 完整挖矿流程示例

```javascript
// 1. 用户获取NFT
await minerNFT.mint(userAddress);

// 2. 设置邀请关系
await miningContract.connect(user).setInviter(inviterAddress);

// 3. 销毁TokenA挖矿
const burnAmount = ethers.utils.parseEther("1000");
await tokenA.connect(user).approve(miningContract.address, burnAmount);
await miningContract.connect(user).burnTokenAForTokenB(burnAmount);

// 4. 等待释放期后领取奖励
// 假设30天后...
await miningContract.connect(user).claimTokenBFromMechanism1();

// 5. 释放NFT中的TokenA
await minerNFT.connect(user).batchReleaseTokens();
```

### 价格查询示例

```javascript
// 获取TokenA价格
const tokenAPrice = await priceOracle.getTokenPrice(tokenA.address);
console.log(`TokenA价格: ${ethers.utils.formatEther(tokenAPrice)} BNB`);

// 获取价格比率
const ratio = await priceOracle.getTokenPriceRatio(tokenA.address, tokenB.address);
console.log(`TokenA/TokenB比率: ${ethers.utils.formatEther(ratio)}`);
```

### 分红查询和领取示例

```javascript
// 查询可领取分红
const dividend = await tokenA.calculateDividend(userAddress);
console.log(`可领取分红: ${ethers.utils.formatEther(dividend)} TKA`);

// 领取分红
if (dividend.gt(0)) {
    await tokenA.connect(user).claimDividend();
}
```

---

## ⚠️ 重要注意事项

1. **Gas估算**：在调用写入函数前建议先进行Gas估算
2. **授权检查**：涉及代币转移的操作需要先进行授权
3. **时间限制**：某些功能有时间限制，注意检查时间条件
4. **权限验证**：管理员函数需要相应权限才能调用
5. **事件监听**：重要操作建议监听相关事件
6. **错误处理**：合约调用可能失败，需要适当的错误处理

---

**📖 更多详细信息请参考源码注释和测试用例。**