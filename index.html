<!doctype html>
<html lang="en" class="scroll-smooth">
    <head>
        <meta charset="UTF-8" />
        <meta name="viewport" content="width=device-width, initial-scale=1.0" />
        <title>
            Gratitude Protocol ($GRAT) & TipCoin ($TIPS) - Make Tipping Great
            Again
        </title>
        <script src="https://cdn.tailwindcss.com"></script>
        <link rel="preconnect" href="https://fonts.googleapis.com" />
        <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin />
        <link
            href="https://fonts.googleapis.com/css2?family=Noto+Sans+JP:wght@400;700&family=Noto+Sans+SC:wght@400;700;900&family=Poppins:wght@400;600;700;800;900&display=swap"
            rel="stylesheet"
        />
        <style>
            body {
                font-family:
                    "Poppins", "Noto Sans SC", "Noto Sans JP", sans-serif;
                background-color: #f9fafb; /* gray-50 */
                color: #1f2937; /* gray-800 */
            }
            .font-poppins {
                font-family: "Poppins", sans-serif;
            }
            .hero-bg {
                background-color: #f9fafb;
                background-image:
                    radial-gradient(
                        circle at 5% 5%,
                        rgba(109, 40, 217, 0.05),
                        transparent 30%
                    ),
                    radial-gradient(
                        circle at 95% 95%,
                        rgba(245, 158, 11, 0.05),
                        transparent 30%
                    );
                background-size: 100% 100%;
            }
            .card {
                background-color: white;
                border-radius: 1.5rem; /* 24px */
                box-shadow:
                    0 10px 15px -3px rgb(0 0 0 / 0.05),
                    0 4px 6px -4px rgb(0 0 0 / 0.05);
                transition: all 0.3s ease-in-out;
            }
            .card:hover {
                box-shadow:
                    0 20px 25px -5px rgb(0 0 0 / 0.08),
                    0 8px 10px -6px rgb(0 0 0 / 0.08);
                transform: translateY(-5px);
            }
            .primary-btn {
                background-color: #6d28d9; /* violet-700 */
                color: white;
                transition: all 0.3s ease;
                box-shadow: 0 4px 15px rgba(109, 40, 217, 0.2);
                font-weight: 600;
            }
            .primary-btn:hover:not(:disabled) {
                background-color: #5b21b6; /* violet-800 */
                transform: translateY(-2px);
                box-shadow: 0 6px 20px rgba(109, 40, 217, 0.3);
            }
            .primary-btn:disabled {
                background-color: #d1d5db; /* gray-300 */
                cursor: not-allowed;
                box-shadow: none;
                color: #6b7280; /* gray-500 */
            }
            .secondary-btn {
                background-color: #f5f3ff; /* violet-50 */
                color: #6d28d9; /* violet-700 */
                border: 1px solid #ddd6fe; /* violet-200 */
                transition: all 0.3s ease;
                font-weight: 600;
            }
            .secondary-btn:hover:not(:disabled) {
                background-color: #ede9fe; /* violet-100 */
                border-color: #c4b5fd; /* violet-300 */
                transform: translateY(-2px);
            }
            .secondary-btn:disabled {
                background-color: #e5e7eb; /* gray-200 */
                color: #9ca3af; /* gray-400 */
                border-color: transparent;
                cursor: not-allowed;
            }
            .section-title {
                font-family: "Poppins", sans-serif;
                font-weight: 800;
                font-size: 3rem; /* 48px */
                letter-spacing: -0.05em;
                color: #111827; /* gray-900 */
            }
            .section-subtitle {
                color: #4b5563; /* gray-600 */
            }
            .section-bg {
                background-color: #ffffff;
                border-top: 1px solid #e5e7eb;
                border-bottom: 1px solid #e5e7eb;
            }

            /* 动画 */
            .spinner {
                border: 2px solid #e5e7eb; /* gray-200 */
                border-top: 2px solid #6d28d9; /* violet-700 */
                border-radius: 50%;
                width: 16px;
                height: 16px;
                animation: spin 1s linear infinite;
            }
            @keyframes spin {
                0% {
                    transform: rotate(0deg);
                }
                100% {
                    transform: rotate(360deg);
                }
            }

            .fade-in-section {
                opacity: 0;
                transform: translateY(20px);
                transition:
                    opacity 0.8s ease-out,
                    transform 0.8s ease-out;
            }
            .fade-in-section.is-visible {
                opacity: 1;
                transform: translateY(0);
            }

            @keyframes orbit {
                0% {
                    transform: rotate(0deg) translateX(120px) rotate(0deg);
                }
                100% {
                    transform: rotate(360deg) translateX(120px) rotate(-360deg);
                }
            }
            @keyframes pulse {
                0%,
                100% {
                    transform: scale(1);
                    box-shadow: 0 0 0 0 rgba(109, 40, 217, 0.2);
                }
                50% {
                    transform: scale(1.05);
                    box-shadow: 0 0 25px 15px rgba(109, 40, 217, 0);
                }
            }
            .hero-animation-center {
                animation: pulse 5s ease-in-out infinite;
            }
            .hero-animation-orbit {
                animation: orbit 12s linear infinite;
            }
        </style>
    </head>
    <body class="w-full">
        <!-- Header -->
        <header
            class="fixed top-0 left-0 right-0 z-50 bg-white/80 backdrop-blur-md border-b border-gray-200/80"
        >
            <div
                class="container mx-auto px-6 py-4 flex justify-between items-center"
            >
                <a href="#" class="text-2xl font-bold font-poppins">
                    <span class="text-violet-700">$</span
                    ><span class="text-gray-800">GRAT</span>
                </a>
                <nav
                    class="hidden md:flex items-center space-x-6 lg:space-x-8 text-gray-600 font-semibold"
                >
                    <a
                        href="#grat"
                        class="hover:text-violet-700 transition-colors"
                        data-translate-key="nav_grat"
                    ></a>
                    <a
                        href="#tips"
                        class="hover:text-violet-700 transition-colors"
                        data-translate-key="nav_tips"
                    ></a>
                    <a
                        href="#tokenomics"
                        class="hover:text-violet-700 transition-colors"
                        data-translate-key="nav_tokenomics"
                    ></a>
                    <a
                        href="#how"
                        class="hover:text-violet-700 transition-colors"
                        data-translate-key="nav_how"
                    ></a>
                    <a
                        href="#ai-tools"
                        class="hover:text-violet-700 transition-colors"
                        data-translate-key="nav_ai"
                    ></a>
                    <a
                        href="#community"
                        class="hover:text-violet-700 transition-colors"
                        data-translate-key="nav_community"
                    ></a>
                </nav>
                <div class="flex items-center gap-4">
                    <div class="relative hidden md:inline-block">
                        <button
                            id="lang-switcher"
                            class="bg-gray-100 px-3 py-2 rounded-lg flex items-center gap-2 text-gray-600 hover:bg-gray-200"
                        >
                            <span id="current-lang-flag"></span>
                            <span id="current-lang-text"></span>
                            <svg
                                class="w-4 h-4 text-gray-500"
                                fill="none"
                                stroke="currentColor"
                                viewBox="0 0 24 24"
                                xmlns="http://www.w3.org/2000/svg"
                            >
                                <path
                                    stroke-linecap="round"
                                    stroke-linejoin="round"
                                    stroke-width="2"
                                    d="M19 9l-7 7-7-7"
                                ></path>
                            </svg>
                        </button>
                        <div
                            id="lang-menu"
                            class="hidden absolute top-full right-0 mt-2 bg-white rounded-xl shadow-lg py-1 w-40 border border-gray-100"
                        ></div>
                    </div>
                    <a
                        href="app.html"
                        class="primary-btn font-bold py-2.5 px-6 rounded-lg hidden md:inline-block"
                        data-translate-key="launch_app"
                    ></a>
                    <button
                        id="mobile-menu-btn"
                        class="md:hidden text-gray-800"
                    >
                        <svg
                            class="h-6 w-6"
                            fill="none"
                            viewBox="0 0 24 24"
                            stroke="currentColor"
                        >
                            <path
                                stroke-linecap="round"
                                stroke-linejoin="round"
                                stroke-width="2"
                                d="M4 6h16M4 12h16m-7 6h7"
                            />
                        </svg>
                    </button>
                </div>
            </div>
            <div
                id="mobile-menu"
                class="hidden md:hidden bg-white/95 backdrop-blur-sm border-t"
            >
                <nav
                    id="mobile-nav-links"
                    class="flex flex-col items-center space-y-4 py-4 text-gray-600 font-semibold"
                ></nav>
            </div>
        </header>

        <!-- Main Content -->
        <div id="content-container">
            <!-- Hero Section -->
            <main id="home" class="hero-bg pt-36 pb-24 overflow-hidden">
                <div class="container mx-auto px-6 text-center">
                    <div class="max-w-4xl mx-auto">
                        <h1
                            class="text-4xl md:text-7xl font-black text-gray-900 leading-tight mb-6"
                            data-translate-key="hero_title"
                        ></h1>
                        <p
                            class="text-lg md:text-xl text-gray-600 max-w-2xl mx-auto mb-10"
                            data-translate-key="hero_subtitle"
                        ></p>
                        <div
                            class="flex flex-col sm:flex-row justify-center items-center gap-4"
                        >
                            <a
                                href="#how"
                                class="w-full sm:w-auto secondary-btn font-bold py-3 px-8 rounded-xl text-lg"
                                data-translate-key="hero_btn_grat"
                            ></a>
                            <a
                                href="app.html"
                                class="w-full sm:w-auto primary-btn font-bold py-3 px-8 rounded-xl text-lg"
                                data-translate-key="hero_btn_tips"
                            ></a>
                        </div>
                    </div>
                    <!-- New Hero Animation -->
                    <div
                        class="mt-28 relative flex justify-center items-center h-72"
                    >
                        <div class="absolute w-72 h-72">
                            <div
                                class="absolute inset-0 border-2 border-violet-500/10 rounded-full"
                            ></div>
                            <div
                                class="absolute inset-4 border border-violet-500/5 rounded-full"
                            ></div>
                            <div
                                class="hero-animation-center absolute top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2 w-28 h-28 bg-white/50 rounded-full flex items-center justify-center font-poppins font-bold text-3xl border border-violet-200 shadow-lg"
                            >
                                <span class="text-violet-700">$GRAT</span>
                            </div>
                            <div
                                class="hero-animation-orbit absolute top-1/2 left-1/2 w-20 h-20 -mt-10 -ml-10 bg-amber-400 rounded-full flex items-center justify-center font-poppins font-bold text-xl text-white shadow-lg"
                            >
                                $TIPS
                            </div>
                        </div>
                    </div>
                </div>
            </main>

            <!-- GRAT Section -->
            <section id="grat" class="py-28 fade-in-section">
                <div class="container mx-auto px-6">
                    <div class="text-center mb-16">
                        <h2
                            class="section-title"
                            data-translate-key="grat_title"
                        ></h2>
                        <p
                            class="section-subtitle mt-4 text-lg max-w-2xl mx-auto"
                            data-translate-key="grat_subtitle"
                        ></p>
                    </div>
                    <div class="grid md:grid-cols-3 gap-8">
                        <div class="card p-8 text-center">
                            <div
                                class="flex items-center justify-center w-16 h-16 rounded-2xl bg-violet-100 text-violet-600 mx-auto mb-6"
                            >
                                <svg
                                    xmlns="http://www.w3.org/2000/svg"
                                    class="h-8 w-8"
                                    fill="none"
                                    viewBox="0 0 24 24"
                                    stroke="currentColor"
                                    stroke-width="2"
                                >
                                    <path
                                        stroke-linecap="round"
                                        stroke-linejoin="round"
                                        d="M5 3v4M3 5h4M6 17v4m-2-2h4m5-16l2.286 6.857L21 12l-5.714 2.143L13 21l-2.286-6.857L5 12l5.714-2.143L13 3z"
                                    />
                                </svg>
                            </div>
                            <h3
                                class="text-2xl font-bold text-gray-900 mb-2 font-poppins"
                                data-translate-key="grat_card1_title"
                            ></h3>
                            <p
                                class="text-gray-600"
                                data-translate-key="grat_card1_desc"
                            ></p>
                        </div>
                        <div class="card p-8 text-center">
                            <div
                                class="flex items-center justify-center w-16 h-16 rounded-2xl bg-violet-100 text-violet-600 mx-auto mb-6"
                            >
                                <svg
                                    xmlns="http://www.w3.org/2000/svg"
                                    class="h-8 w-8"
                                    fill="none"
                                    viewBox="0 0 24 24"
                                    stroke="currentColor"
                                    stroke-width="2"
                                >
                                    <path
                                        stroke-linecap="round"
                                        stroke-linejoin="round"
                                        d="M12 8c1.657 0 3 1.343 3 3s-1.343 3-3 3-3-1.343-3-3 1.343-3 3-3zm0 0a3.001 3.001 0 110 6 3.001 3.001 0 010-6zm-7 12a9 9 0 1118 0H5z"
                                    />
                                </svg>
                            </div>
                            <h3
                                class="text-2xl font-bold text-gray-900 mb-2 font-poppins"
                                data-translate-key="grat_card2_title"
                            ></h3>
                            <p
                                class="text-gray-600"
                                data-translate-key="grat_card2_desc"
                            ></p>
                        </div>
                        <div class="card p-8 text-center">
                            <div
                                class="flex items-center justify-center w-16 h-16 rounded-2xl bg-violet-100 text-violet-600 mx-auto mb-6"
                            >
                                <svg
                                    xmlns="http://www.w3.org/2000/svg"
                                    class="h-8 w-8"
                                    fill="none"
                                    viewBox="0 0 24 24"
                                    stroke="currentColor"
                                    stroke-width="2"
                                >
                                    <path
                                        stroke-linecap="round"
                                        stroke-linejoin="round"
                                        d="M13 7h8m0 0v8m0-8l-8 8-4-4-6 6"
                                    />
                                </svg>
                            </div>
                            <h3
                                class="text-2xl font-bold text-gray-900 mb-2 font-poppins"
                                data-translate-key="grat_card3_title"
                            ></h3>
                            <p
                                class="text-gray-600"
                                data-translate-key="grat_card3_desc"
                            ></p>
                        </div>
                    </div>
                </div>
            </section>

            <!-- TIPS Section -->
            <section id="tips" class="py-28 section-bg fade-in-section">
                <div class="container mx-auto px-6">
                    <div class="text-center mb-16">
                        <h2
                            class="section-title"
                            data-translate-key="tips_title"
                        ></h2>
                        <p
                            class="section-subtitle mt-4 text-lg max-w-2xl mx-auto"
                            data-translate-key="tips_subtitle"
                        ></p>
                    </div>
                    <div class="grid md:grid-cols-3 gap-8">
                        <div class="card p-8 text-center">
                            <div
                                class="flex items-center justify-center w-16 h-16 rounded-2xl bg-amber-100 text-amber-600 mx-auto mb-6"
                            >
                                <svg
                                    xmlns="http://www.w3.org/2000/svg"
                                    class="h-8 w-8"
                                    fill="none"
                                    viewBox="0 0 24 24"
                                    stroke="currentColor"
                                    stroke-width="2"
                                >
                                    <path
                                        stroke-linecap="round"
                                        stroke-linejoin="round"
                                        d="M17 9V7a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2m2 4h10a2 2 0 002-2v-6a2 2 0 00-2-2H9a2 2 0 00-2 2v6a2 2 0 002 2zm7-5a2 2 0 11-4 0 2 2 0 014 0z"
                                    />
                                </svg>
                            </div>
                            <h3
                                class="text-2xl font-bold text-gray-900 mb-2 font-poppins"
                                data-translate-key="tips_card1_title"
                            ></h3>
                            <p
                                class="text-gray-600"
                                data-translate-key="tips_card1_desc"
                            ></p>
                        </div>
                        <div class="card p-8 text-center">
                            <div
                                class="flex items-center justify-center w-16 h-16 rounded-2xl bg-amber-100 text-amber-600 mx-auto mb-6"
                            >
                                <svg
                                    xmlns="http://www.w3.org/2000/svg"
                                    class="h-8 w-8"
                                    fill="none"
                                    viewBox="0 0 24 24"
                                    stroke="currentColor"
                                    stroke-width="2"
                                >
                                    <path
                                        stroke-linecap="round"
                                        stroke-linejoin="round"
                                        d="M14.828 14.828a4 4 0 01-5.656 0M9 10h.01M15 10h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"
                                    />
                                </svg>
                            </div>
                            <h3
                                class="text-2xl font-bold text-gray-900 mb-2 font-poppins"
                                data-translate-key="tips_card2_title"
                            ></h3>
                            <p
                                class="text-gray-600"
                                data-translate-key="tips_card2_desc"
                            ></p>
                        </div>
                        <div class="card p-8 text-center">
                            <div
                                class="flex items-center justify-center w-16 h-16 rounded-2xl bg-amber-100 text-amber-600 mx-auto mb-6"
                            >
                                <svg
                                    xmlns="http://www.w3.org/2000/svg"
                                    class="h-8 w-8"
                                    fill="none"
                                    viewBox="0 0 24 24"
                                    stroke="currentColor"
                                    stroke-width="2"
                                >
                                    <path
                                        stroke-linecap="round"
                                        stroke-linejoin="round"
                                        d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z"
                                    />
                                </svg>
                            </div>
                            <h3
                                class="text-2xl font-bold text-gray-900 mb-2 font-poppins"
                                data-translate-key="tips_card3_title"
                            ></h3>
                            <p
                                class="text-gray-600"
                                data-translate-key="tips_card3_desc"
                            ></p>
                        </div>
                    </div>
                </div>
            </section>

            <!-- Tokenomics Section -->
            <section id="tokenomics" class="py-28 fade-in-section">
                <div class="container mx-auto px-6">
                    <div class="text-center mb-16">
                        <h2
                            class="section-title"
                            data-translate-key="tokenomics_title"
                        ></h2>
                        <p
                            class="section-subtitle mt-4 text-lg max-w-2xl mx-auto"
                            data-translate-key="tokenomics_subtitle"
                        ></p>
                    </div>
                    <div
                        class="grid md:grid-cols-2 lg:grid-cols-4 gap-8 max-w-6xl mx-auto"
                    >
                        <div class="card p-6 flex items-center gap-4">
                            <div
                                class="w-16 h-16 rounded-full flex-shrink-0 flex items-center justify-center"
                                style="
                                    background: conic-gradient(
                                        #fbbf24 0% 60%,
                                        #e5e7eb 60% 100%
                                    );
                                "
                            >
                                <span class="text-gray-800 font-bold text-xl"
                                    >60%</span
                                >
                            </div>
                            <div>
                                <h3
                                    class="text-lg font-bold text-gray-900"
                                    data-translate-key="tokenomics_card1_title"
                                ></h3>
                                <p
                                    class="text-sm text-gray-600"
                                    data-translate-key="tokenomics_card1_desc"
                                ></p>
                            </div>
                        </div>
                        <div class="card p-6 flex items-center gap-4">
                            <div
                                class="w-16 h-16 rounded-full flex-shrink-0 flex items-center justify-center"
                                style="
                                    background: conic-gradient(
                                        #8b5cf6 0% 20%,
                                        #e5e7eb 20% 100%
                                    );
                                "
                            >
                                <span class="text-white font-bold text-xl"
                                    >20%</span
                                >
                            </div>
                            <div>
                                <h3
                                    class="text-lg font-bold text-gray-900"
                                    data-translate-key="tokenomics_card2_title"
                                ></h3>
                                <p
                                    class="text-sm text-gray-600"
                                    data-translate-key="tokenomics_card2_desc"
                                ></p>
                            </div>
                        </div>
                        <div class="card p-6 flex items-center gap-4">
                            <div
                                class="w-16 h-16 rounded-full flex-shrink-0 flex items-center justify-center"
                                style="
                                    background: conic-gradient(
                                        #10b981 0% 15%,
                                        #e5e7eb 15% 100%
                                    );
                                "
                            >
                                <span class="text-white font-bold text-xl"
                                    >15%</span
                                >
                            </div>
                            <div>
                                <h3
                                    class="text-lg font-bold text-gray-900"
                                    data-translate-key="tokenomics_card3_title"
                                ></h3>
                                <p
                                    class="text-sm text-gray-600"
                                    data-translate-key="tokenomics_card3_desc"
                                ></p>
                            </div>
                        </div>
                        <div class="card p-6 flex items-center gap-4">
                            <div
                                class="w-16 h-16 rounded-full flex-shrink-0 flex items-center justify-center"
                                style="
                                    background: conic-gradient(
                                        #3b82f6 0% 5%,
                                        #e5e7eb 5% 100%
                                    );
                                "
                            >
                                <span class="text-white font-bold text-xl"
                                    >5%</span
                                >
                            </div>
                            <div>
                                <h3
                                    class="text-lg font-bold text-gray-900"
                                    data-translate-key="tokenomics_card4_title"
                                ></h3>
                                <p
                                    class="text-sm text-gray-600"
                                    data-translate-key="tokenomics_card4_desc"
                                ></p>
                            </div>
                        </div>
                    </div>
                </div>
            </section>

            <!-- How it works Section -->
            <section id="how" class="py-28 fade-in-section">
                <div class="container mx-auto px-6">
                    <div class="text-center mb-20">
                        <h2
                            class="section-title"
                            data-translate-key="how_title"
                        ></h2>
                        <p
                            class="section-subtitle mt-4 text-lg max-w-2xl mx-auto"
                            data-translate-key="how_subtitle"
                        ></p>
                    </div>
                    <div class="relative">
                        <div
                            class="hidden md:block absolute top-1/2 left-0 w-full h-0.5 -translate-y-1/2 bg-gray-200"
                        >
                            <div
                                class="absolute top-0 left-0 h-full bg-violet-500 w-1/3"
                            ></div>
                            <div
                                class="absolute top-0 left-1/3 h-full bg-amber-500 w-2/3"
                            ></div>
                        </div>
                        <div
                            class="grid md:grid-cols-4 gap-12 text-center relative z-10"
                        >
                            <div class="flex flex-col items-center">
                                <div
                                    class="flex items-center justify-center w-24 h-24 rounded-full bg-white border-4 border-violet-500 mb-4 text-4xl font-bold text-violet-500 shadow-lg"
                                >
                                    1
                                </div>
                                <h3
                                    class="text-xl font-bold mb-2 text-gray-900"
                                    data-translate-key="how_step1_title"
                                ></h3>
                                <p
                                    class="text-gray-600"
                                    data-translate-key="how_step1_desc"
                                ></p>
                            </div>
                            <div class="flex flex-col items-center">
                                <div
                                    class="flex items-center justify-center w-24 h-24 rounded-full bg-white border-4 border-violet-500 mb-4 text-4xl font-bold text-violet-500 shadow-lg"
                                >
                                    2
                                </div>
                                <h3
                                    class="text-xl font-bold mb-2 text-gray-900"
                                    data-translate-key="how_step2_title"
                                ></h3>
                                <p
                                    class="text-gray-600"
                                    data-translate-key="how_step2_desc"
                                ></p>
                            </div>
                            <div class="flex flex-col items-center">
                                <div
                                    class="flex items-center justify-center w-24 h-24 rounded-full bg-white border-4 border-amber-500 mb-4 text-4xl font-bold text-amber-500 shadow-lg"
                                >
                                    3
                                </div>
                                <h3
                                    class="text-xl font-bold mb-2 text-gray-900"
                                    data-translate-key="how_step3_title"
                                ></h3>
                                <p
                                    class="text-gray-600"
                                    data-translate-key="how_step3_desc"
                                ></p>
                            </div>
                            <div class="flex flex-col items-center">
                                <div
                                    class="flex items-center justify-center w-24 h-24 rounded-full bg-white border-4 border-amber-500 mb-4 text-4xl font-bold text-amber-500 shadow-lg"
                                >
                                    4
                                </div>
                                <h3
                                    class="text-xl font-bold mb-2 text-gray-900"
                                    data-translate-key="how_step4_title"
                                ></h3>
                                <p
                                    class="text-gray-600"
                                    data-translate-key="how_step4_desc"
                                ></p>
                            </div>
                        </div>
                    </div>
                </div>
            </section>

            <!-- AI Tools Section -->
            <section id="ai-tools" class="py-28 section-bg fade-in-section">
                <div class="container mx-auto px-6">
                    <div class="text-center mb-16">
                        <h2
                            class="section-title"
                            data-translate-key="ai_title"
                        ></h2>
                        <p
                            class="section-subtitle mt-4 text-lg max-w-2xl mx-auto"
                            data-translate-key="ai_subtitle"
                        ></p>
                    </div>
                    <div class="grid md:grid-cols-2 gap-8 max-w-5xl mx-auto">
                        <div class="card p-8 flex flex-col">
                            <h3
                                class="text-2xl font-bold text-gray-900 mb-4 font-poppins flex items-center"
                            >
                                <span class="text-3xl mr-3">💌</span>
                                <span
                                    data-translate-key="ai_card1_title"
                                ></span>
                            </h3>
                            <p
                                class="text-gray-600 mb-4 flex-grow"
                                data-translate-key="ai_card1_desc"
                            ></p>
                            <div class="space-y-4 mb-4">
                                <input
                                    id="recipient"
                                    type="text"
                                    data-translate-key-placeholder="ai_card1_placeholder1"
                                    class="w-full bg-gray-100 rounded-lg p-3 text-gray-800 placeholder-gray-400 border border-gray-200 focus:border-violet-500 focus:ring-violet-500"
                                />
                                <input
                                    id="reason"
                                    type="text"
                                    data-translate-key-placeholder="ai_card1_placeholder2"
                                    class="w-full bg-gray-100 rounded-lg p-3 text-gray-800 placeholder-gray-400 border border-gray-200 focus:border-violet-500 focus:ring-violet-500"
                                />
                            </div>
                            <button
                                id="generate-note-btn"
                                class="secondary-btn font-bold py-3 px-6 rounded-lg w-full flex items-center justify-center"
                            >
                                <span
                                    id="generate-note-text"
                                    data-translate-key="ai_card1_button"
                                ></span>
                                <div
                                    id="generate-note-spinner"
                                    class="spinner hidden ml-2"
                                ></div>
                            </button>
                            <div id="note-output-container" class="mt-4 hidden">
                                <div
                                    id="note-output"
                                    class="bg-gray-100 p-4 rounded-lg text-gray-700 whitespace-pre-wrap max-h-48 overflow-y-auto border border-gray-200"
                                ></div>
                                <button
                                    id="copy-note-btn"
                                    data-translate-key="copy_button"
                                    class="mt-2 text-sm text-violet-600 hover:text-violet-500 font-semibold"
                                ></button>
                            </div>
                        </div>
                        <div class="card p-8 flex flex-col">
                            <h3
                                class="text-2xl font-bold text-gray-900 mb-4 font-poppins flex items-center"
                            >
                                <span class="text-3xl mr-3">💡</span>
                                <span
                                    data-translate-key="ai_card2_title"
                                ></span>
                            </h3>
                            <p
                                class="text-gray-600 mb-4 flex-grow"
                                data-translate-key="ai_card2_desc"
                            ></p>
                            <div class="space-y-4 mb-4">
                                <input
                                    id="meme-keyword"
                                    type="text"
                                    data-translate-key-placeholder="ai_card2_placeholder1"
                                    class="w-full bg-gray-100 rounded-lg p-3 text-gray-800 placeholder-gray-400 border border-gray-200 focus:border-violet-500 focus:ring-violet-500"
                                />
                            </div>
                            <button
                                id="generate-meme-btn"
                                class="primary-btn font-bold py-3 px-6 rounded-lg w-full flex items-center justify-center"
                            >
                                <span
                                    id="generate-meme-text"
                                    data-translate-key="ai_card2_button"
                                ></span>
                                <div
                                    id="generate-meme-spinner"
                                    class="spinner hidden ml-2"
                                    style="border-top-color: #ffffff"
                                ></div>
                            </button>
                            <div id="meme-output-container" class="mt-4 hidden">
                                <div
                                    id="meme-output"
                                    class="bg-gray-100 p-4 rounded-lg text-gray-700 whitespace-pre-wrap max-h-48 overflow-y-auto border border-gray-200"
                                ></div>
                                <button
                                    id="copy-meme-btn"
                                    data-translate-key="copy_button"
                                    class="mt-2 text-sm text-violet-600 hover:text-violet-500 font-semibold"
                                ></button>
                            </div>
                        </div>
                    </div>
                </div>
            </section>

            <!-- Community Section -->
            <section id="community" class="py-28 fade-in-section">
                <div class="container mx-auto px-6 text-center">
                    <h2
                        class="section-title"
                        data-translate-key="community_title"
                    ></h2>
                    <p
                        class="section-subtitle mt-4 text-lg max-w-2xl mx-auto"
                        data-translate-key="community_subtitle"
                    ></p>
                    <div class="mt-10 flex justify-center gap-6">
                        <a
                            href="#"
                            class="bg-gray-200 hover:bg-gray-300 transition-colors p-4 rounded-full"
                            ><svg
                                class="w-8 h-8 text-gray-600"
                                role="img"
                                viewBox="0 0 24 24"
                                xmlns="[http://www.w3.org/2000/svg](http://www.w3.org/2000/svg)"
                            >
                                <title>Twitter</title>
                                <path
                                    d="M23.953 4.57a10 10 0 01-2.825.775 4.958 4.958 0 002.163-2.723c-.951.555-2.005.959-3.127 1.184a4.92 4.92 0 00-8.384 4.482C7.69 8.095 4.067 6.13 1.64 3.162a4.822 4.822 0 00-.666 2.475c0 1.71.87 3.213 2.188 4.096a4.904 4.904 0 01-2.228-.616v.06a4.923 4.923 0 003.946 4.827 4.996 4.996 0 01-2.223.085a4.923 4.923 0 004.6 3.419A9.9 9.9 0 010 17.54a13.94 13.94 0 007.548 2.212c9.142 0 14.307-7.487 14.307-14.307 0-.218-.005-.436-.013-.652a10.118 10.118 0 002.488-2.548z"
                                /></svg
                        ></a>
                        <a
                            href="#"
                            class="bg-gray-200 hover:bg-gray-300 transition-colors p-4 rounded-full"
                            ><svg
                                class="w-8 h-8 text-gray-600"
                                role="img"
                                viewBox="0 0 24 24"
                                xmlns="[http://www.w3.org/2000/svg](http://www.w3.org/2000/svg)"
                            >
                                <title>Discord</title>
                                <path
                                    d="M20.317 4.369a19.791 19.791 0 00-4.885-1.515.074.074 0 00-.079.037c-.21.375-.444.864-.608 1.25a18.278 18.278 0 00-5.484 0 12.55 12.55 0 00-.617-1.25.077.077 0 00-.079-.037A19.715 19.715 0 003.678 4.37a.07.07 0 00-.034.044c-.386 1.74-.633 3.63-.58 5.562a.078.078 0 00.042.074c1.733.91 3.362 1.573 4.962 1.99a.076.076 0 00.086-.05c.34-.76.58-1.54.77-2.34a.074.074 0 00-.04-.085c-.93-.42-.183-.84-.28-1.24a.07.07 0 01.02-.077c.36-.24.71-.52.99-.83a.076.076 0 01.08-.01c1.24.47 2.49.82 3.75.98a.075.075 0 01.08.01c.28.31.63.59.98.83a.07.07 0 01.02.076c-.1.4-.15.82-.28 1.24a.073.073 0 00-.04.085c.19.8.43 1.58.77 2.34a.077.077 0 00.086.05c1.6-.416 3.23-.98 4.96-1.99a.077.077 0 00.043-.074c.054-1.93-.2-3.82-.58-5.561a.07.07 0 00-.034-.045zM8.02 15.33c-1.183 0-2.15-1.08-2.15-2.42s.967-2.42 2.15-2.42c1.192 0 2.15 1.08 2.15 2.42s-.958 2.42-2.15 2.42zm7.974 0c-1.183 0-2.15-1.08-2.15-2.42s.967-2.42 2.15-2.42c1.192 0 2.15 1.08 2.15 2.42s-.958 2.42-2.15 2.42z"
                                /></svg
                        ></a>
                        <a
                            href="#"
                            class="bg-gray-200 hover:bg-gray-300 transition-colors p-4 rounded-full"
                            ><svg
                                class="w-8 h-8 text-gray-600"
                                role="img"
                                viewBox="0 0 24 24"
                                xmlns="[http://www.w3.org/2000/svg](http://www.w3.org/2000/svg)"
                            >
                                <title>Telegram</title>
                                <path
                                    d="M11.944 0A12 12 0 0 0 0 12a12 12 0 0 0 12 12 12 12 0 0 0 12-12A12 12 0 0 0 12 0a12 12 0 0 0-.056 0zm4.962 7.224c.1-.002.321.023.465.14a.506.506 0 0 1 .171.325c.016.093.036.306.02.472-.18 1.898-.962 6.502-1.36 8.627-.17.91-.494 1.208-.822 1.23-.696.045-1.225-.46-1.9-1.002-.61-.48-1.032-.765-1.67-1.22-.72-.52-1.26-.89-1.13.313.12.33.24.66.36.99.14.37.02.58-.09.6-.24.04-.66-.14-1.14-.42a13.48 13.48 0 0 1-2.13-1.47c-1.46-1.2-2.1-1.88-1.94-2.92.12-.72.85-2.41 1.5-4.11.4-.97.77-1.87 1.15-2.78.25-.6.54-1.15.82-1.67.28-.52.56-.98.85-1.38.28-.4.56-.77.84-1.1.28-.33.56-.63.84-.9.28-.27.56-.5.85-.7.28-.2.56-.35.84-.45.28-.1.56-.15.84-.15zm-1.64 3.28c-.06.48-.12.96-.18 1.44-.06.48-.12.96-.18 1.44-.06.48-.12.96-.18 1.44a.49.49 0 0 1-.29.42c-.18.06-.36 0-.54-.12-.18-.12-.36-.24-.54-.36a1.49 1.49 0 0 1-.42-.48c-.06-.12-.12-.24-.18-.36l-.12-.48c-.06-.24-.12-.48-.18-.72-.06-.24-.12-.48-.18-.72l-.12-.42c-.06-.24-.12-.48-.18-.72-.06-.24-.12-.48-.18-.72l-.12-.42a.49.49 0 0 1 .12-.6c.12-.06.24 0 .36.06.12.06.24.12.36.18.18.12.36.24.54.36a1.49 1.49 0 0 1 .42.48z"
                                /></svg
                        ></a>
                    </div>
                </div>
            </section>
        </div>

        <!-- Footer -->
        <footer class="bg-white">
            <div class="container mx-auto px-6 py-8">
                <div id="footer-content" class="text-center text-gray-500">
                    <p>&copy; 2025 Gratitude Protocol. All Rights Reserved.</p>
                    <p
                        class="text-xs mt-2"
                        data-translate-key="footer_disclaimer"
                    ></p>
                </div>
            </div>
        </footer>

        <script>
            document.addEventListener("DOMContentLoaded", () => {
                // --- Gemini API Call Function ---
                const callGemini = async (prompt) => {
                    // 调用自己的Cloudflare Worker API代理
                    const apiUrl =
                        "https://holy-star-43e9.jun7788q.workers.dev"; // 替换为你的Worker域名
                    const payload = {
                        contents: [{ role: "user", parts: [{ text: prompt }] }],
                    };
                    try {
                        const response = await fetch(apiUrl, {
                            method: "POST",
                            headers: { "Content-Type": "application/json" },
                            body: JSON.stringify(payload),
                        });
                        if (!response.ok)
                            throw new Error(
                                `API call failed with status: ${response.status}`,
                            );
                        const result = await response.json();
                        if (result.candidates?.[0]?.content?.parts?.[0]?.text) {
                            return result.candidates[0].content.parts[0].text;
                        } else {
                            if (result.promptFeedback?.blockReason)
                                return `[AI Generation Blocked: ${result.promptFeedback.blockReason}]`;
                            return getCurrentLanguage().error_generic;
                        }
                    } catch (error) {
                        console.error("Error calling Gemini API:", error);
                        return getCurrentLanguage().error_api;
                    }
                };

                // --- I18n (Internationalization) Logic ---
                const languages = {
                    en: { name: "English", flag: "🇬🇧" },
                    zh: { name: "简体中文", flag: "🇨🇳" },
                    es: { name: "Español", flag: "🇪🇸" },
                    ja: { name: "日本語", flag: "🇯🇵" },
                };

                const translations = {
                    en: {
                        nav_grat: "Protocol ($GRAT)",
                        nav_tips: "Coin ($TIPS)",
                        nav_tokenomics: "Tokenomics",
                        nav_how: "How It Works",
                        nav_ai: "✨AI Tools",
                        nav_community: "Community",
                        launch_app: "Launch App",
                        hero_title:
                            "Forget Likes, It's Time to Give Some $TIPS!",
                        hero_subtitle:
                            "Stake $GRAT, mine $TIPS. Join a global digital tipping culture revival.",
                        hero_btn_grat: "Learn More",
                        hero_btn_tips: "Launch App",
                        grat_title: "Gratitude Protocol ($GRAT)",
                        grat_subtitle:
                            "The value heart of the ecosystem, the source of tipping culture.",
                        grat_card1_title: "Mining Output",
                        grat_card1_desc:
                            "Staking $GRAT is the only way to produce $TIPS. You're not speculating, you're minting culture.",
                        grat_card2_title: "Governance Weight",
                        grat_card2_desc:
                            "Hold $GRAT, and you own the protocol. Vote together to decide the future of the ecosystem.",
                        grat_card3_title: "Value Accrual",
                        grat_card3_desc:
                            "The prosperity of $TIPS will directly drive the demand for $GRAT. Investing in $GRAT is investing in the entire ecosystem.",
                        tips_title: "TipCoin ($TIPS)",
                        tips_subtitle:
                            "Make Tipping Great Again, cheer for every creation.",
                        tips_card1_title: "P2P Tipping",
                        tips_card1_desc:
                            "No middlemen, zero delay. Every bit of your appreciation goes 100% to the person you want to thank.",
                        tips_card2_title: "Ignite Meme Creativity",
                        tips_card2_desc:
                            "Create memes about 'tipping' to earn $TIPS! Let fun and value spread together.",
                        tips_card3_title: "Empower All Creators",
                        tips_card3_desc:
                            "Whoever you are, if your contribution deserves to be seen, it deserves to be rewarded with $TIPS.",
                        tokenomics_title: "Tokenomics",
                        tokenomics_subtitle:
                            "A fair and transparent distribution for community growth.",
                        tokenomics_card1_title: "Staking Rewards",
                        tokenomics_card1_desc:
                            "Distributed to $GRAT stakers who generate $TIPS.",
                        tokenomics_card2_title: "Liquidity Pool",
                        tokenomics_card2_desc:
                            "To ensure a healthy and stable market for both tokens.",
                        tokenomics_card3_title: "Ecosystem Fund",
                        tokenomics_card3_desc:
                            "For partnerships, marketing, and community grants.",
                        tokenomics_card4_title: "Team & Advisors",
                        tokenomics_card4_desc:
                            "Vested over 4 years to ensure long-term commitment.",
                        how_title: "Economic Flywheel",
                        how_subtitle: "A self-reinforcing value cycle.",
                        how_step1_title: "Get $GRAT",
                        how_step1_desc:
                            "Obtain the value core of the ecosystem from the market.",
                        how_step2_title: "Stake $GRAT",
                        how_step2_desc:
                            "Activate your personal 'Tip Coin Minting Machine'.",
                        how_step3_title: "Produce $TIPS",
                        how_step3_desc:
                            "Continuously receive the circulating currency.",
                        how_step4_title: "Tip & Spread",
                        how_step4_desc:
                            "Expand cultural influence and increase the value of $GRAT.",
                        ai_title: "✨ AI-Powered Tools",
                        ai_subtitle:
                            "Powered by Gemini, unleash your community's creativity.",
                        ai_card1_title: "AI Thank-You Note Generator",
                        ai_card1_desc:
                            "Don't know how to express gratitude? Let AI write a sincere and fun thank-you note for you to send with your $TIPS!",
                        ai_card1_placeholder1:
                            "e.g., an artist, a developer...",
                        ai_card1_placeholder2:
                            "e.g., for drawing that cool avatar...",
                        ai_card1_button: "Generate Note",
                        ai_card2_title: "AI Meme Idea Workshop",
                        ai_card2_desc:
                            "Running out of inspiration? Generate viral meme captions and ideas with one click to ignite your community!",
                        ai_card2_placeholder1:
                            "Optional: enter a keyword, e.g., Pepe...",
                        ai_card2_button: "✨ Get Meme Ideas",
                        copy_button: "Copy Text",
                        copy_success: "Copied!",
                        community_title: "Join Us",
                        community_subtitle:
                            "Tippers are the new whales. Become a pioneer of the digital tipping culture, a community full of goodwill awaits you.",
                        footer_disclaimer:
                            "Disclaimer: Digital currency investment involves risks. Please do your own research before participating. This project is a cultural experiment and does not constitute financial advice.",
                        error_api:
                            "An error occurred. Please check the console for details.",
                        error_generic:
                            "Sorry, I couldn't generate a response. Please try again.",
                        error_input: "Please enter a recipient and a reason.",
                    },
                    zh: {
                        nav_grat: "协议 ($GRAT)",
                        nav_tips: "代币 ($TIPS)",
                        nav_tokenomics: "代币经济",
                        nav_how: "如何运作",
                        nav_ai: "✨AI工具",
                        nav_community: "社区",
                        launch_app: "启动应用",
                        hero_title: "忘掉点赞，是时候给TA一点 $TIPS 了！",
                        hero_subtitle:
                            "质押$GRAT，挖掘$TIPS。加入一场全球性的数字小费文化复兴运动。",
                        hero_btn_grat: "了解更多",
                        hero_btn_tips: "启动应用",
                        grat_title: "赞赏协议 ($GRAT)",
                        grat_subtitle: "生态的价值之心，小费文化的源泉",
                        grat_card1_title: "挖矿产出",
                        grat_card1_desc:
                            "质押$GRAT是产出$TIPS的唯一途径。你不是在投机，你是在铸造文化。",
                        grat_card2_title: "治理权重",
                        grat_card2_desc:
                            "持有$GRAT，你就是协议的主人。共同投票，决定生态的未来方向。",
                        grat_card3_title: "价值捕获",
                        grat_card3_desc:
                            "$TIPS的繁荣将直接推动$GRAT的需求。投资$GRAT，就是投资整个生态。",
                        tips_title: "小费币 ($TIPS)",
                        tips_subtitle: "让打赏再次伟大，为每一次创造欢呼",
                        tips_card1_title: "点对点打赏",
                        tips_card1_desc:
                            "无中间商，零延迟。你的每一份心意，都100%直达你想要感谢的人。",
                        tips_card2_title: "引爆Meme创造力",
                        tips_card2_desc:
                            "创造关于“打赏”的Meme，也能赚取$TIPS！让快乐和价值一起传播。",
                        tips_card3_title: "赋能所有创造者",
                        tips_card3_desc:
                            "无论你是谁，只要你的付出值得被看见，就值得被$TIPS奖励。",
                        tokenomics_title: "代币经济学",
                        tokenomics_subtitle: "公平透明的分配，助力社区成长。",
                        tokenomics_card1_title: "质押奖励",
                        tokenomics_card1_desc: "分配给产出$TIPS的$GRAT质押者。",
                        tokenomics_card2_title: "流动性资金池",
                        tokenomics_card2_desc: "确保两种代币市场的健康与稳定。",
                        tokenomics_card3_title: "生态系统基金",
                        tokenomics_card3_desc: "用于合作、市场营销和社区激励。",
                        tokenomics_card4_title: "团队与顾问",
                        tokenomics_card4_desc: "四年线性解锁，确保长期承诺。",
                        how_title: "经济飞轮",
                        how_subtitle: "一个自我强化的价值循环",
                        how_step1_title: "获取 $GRAT",
                        how_step1_desc: "在市场上获得生态的价值核心。",
                        how_step2_title: "质押 $GRAT",
                        how_step2_desc: "激活你的“小费铸造机”。",
                        how_step3_title: "产出 $TIPS",
                        how_step3_desc: "源源不断地获得流通代币。",
                        how_step4_title: "打赏 & 传播",
                        how_step4_desc: "扩大文化影响力，提升$GRAT价值。",
                        ai_title: "✨ AI 赋能工具",
                        ai_subtitle: "由 Gemini 强力驱动，释放你的社区创造力",
                        ai_card1_title: "AI感谢信生成器",
                        ai_card1_desc:
                            "不知道如何表达感谢？让AI帮你写一段真诚又有趣的感谢信，发送$TIPS时附上它！",
                        ai_card1_placeholder1:
                            "例如：一位艺术家，一个开发者...",
                        ai_card1_placeholder2: "例如：感谢他画的超酷头像...",
                        ai_card1_button: "生成感谢信",
                        ai_card2_title: "AI Meme创意工坊",
                        ai_card2_desc:
                            "灵感枯竭了？一键生成病毒式Meme文案和创意，引爆你的社区！",
                        ai_card2_placeholder1:
                            "可选：输入一个关键词，如Pepe...",
                        ai_card2_button: "✨ 获取Meme灵感",
                        copy_button: "复制文本",
                        copy_success: "已复制!",
                        community_title: "加入我们",
                        community_subtitle:
                            "Tippers are the new whales. 成为数字小费文化的先锋，一个充满善意的社区正在等你。",
                        footer_disclaimer:
                            "免责声明：数字货币投资存在风险，请在参与前进行自己的研究。本项目仅为文化实验，不构成财务建议。",
                        error_api: "发生错误，请检查控制台获取详细信息。",
                        error_generic: "抱歉，无法生成回应，请重试。",
                        error_input: "请输入接收人和感谢理由。",
                    },
                    es: {
                        nav_grat: "Protocolo ($GRAT)",
                        nav_tips: "Moneda ($TIPS)",
                        nav_tokenomics: "Tokenomics",
                        nav_how: "Cómo Funciona",
                        nav_ai: "✨Herramientas IA",
                        nav_community: "Comunidad",
                        launch_app: "Lanzar App",
                        hero_title:
                            "¡Olvida los Likes, es Hora de Dar unas $TIPS!",
                        hero_subtitle:
                            "Haz stake de $GRAT, mina $TIPS. Únete a un renacimiento global de la cultura de propinas digitales.",
                        hero_btn_grat: "Aprende Más",
                        hero_btn_tips: "Lanzar App",
                        grat_title: "Protocolo de Gratitud ($GRAT)",
                        grat_subtitle:
                            "El corazón de valor del ecosistema, la fuente de la cultura de propinas.",
                        grat_card1_title: "Producción Minera",
                        grat_card1_desc:
                            "Hacer stake de $GRAT es la única forma de producir $TIPS. No estás especulando, estás acuñando cultura.",
                        grat_card2_title: "Peso de Gobernanza",
                        grat_card2_desc:
                            "Posee $GRAT y serás dueño del protocolo. Vota para decidir el futuro del ecosistema.",
                        grat_card3_title: "Acumulación de Valor",
                        grat_card3_desc:
                            "La prosperidad de $TIPS impulsará directamente la demanda de $GRAT. Invertir en $GRAT es invertir en todo el ecosistema.",
                        tips_title: "TipCoin ($TIPS)",
                        tips_subtitle:
                            "Hagamos que dar propinas vuelva a ser genial, aplaude cada creación.",
                        tips_card1_title: "Propinas P2P",
                        tips_card1_desc:
                            "Sin intermediarios, sin demoras. Todo tu aprecio va 100% a la persona que quieres agradecer.",
                        tips_card2_title: "Enciende la Creatividad de Memes",
                        tips_card2_desc:
                            "¡Crea memes sobre 'propinas' para ganar $TIPS! Deja que la diversión y el valor se extiendan juntos.",
                        tips_card3_title: "Empoderar a Todos los Creadores",
                        tips_card3_desc:
                            "Quienquiera que seas, si tu contribución merece ser vista, merece ser recompensada con $TIPS.",
                        tokenomics_title: "Tokenomics",
                        tokenomics_subtitle:
                            "Una distribución justa y transparente para el crecimiento de la comunidad.",
                        tokenomics_card1_title: "Recompensas de Staking",
                        tokenomics_card1_desc:
                            "Distribuido a los stakers de $GRAT que generan $TIPS.",
                        tokenomics_card2_title: "Fondo de Liquidez",
                        tokenomics_card2_desc:
                            "Para asegurar un mercado saludable y estable para ambos tokens.",
                        tokenomics_card3_title: "Fondo del Ecosistema",
                        tokenomics_card3_desc:
                            "Para asociaciones, marketing y subvenciones comunitarias.",
                        tokenomics_card4_title: "Equipo y Asesores",
                        tokenomics_card4_desc:
                            "Adquirido durante 4 años para asegurar el compromiso a largo plazo.",
                        how_title: "Volante Económico",
                        how_subtitle: "Un ciclo de valor que se auto-refuerza.",
                        how_step1_title: "Obtén $GRAT",
                        how_step1_desc:
                            "Consigue el núcleo de valor del ecosistema en el mercado.",
                        how_step2_title: "Haz Stake de $GRAT",
                        how_step2_desc:
                            "Activa tu 'Máquina de Acuñar Propinas' personal.",
                        how_step3_title: "Produce $TIPS",
                        how_step3_desc:
                            "Recibe continuamente la moneda de circulación.",
                        how_step4_title: "Da Propinas y Difunde",
                        how_step4_desc:
                            "Expande la influencia cultural y aumenta el valor de $GRAT.",
                        ai_title: "✨ Herramientas con IA",
                        ai_subtitle:
                            "Impulsado por Gemini, libera la creatividad de tu comunidad.",
                        ai_card1_title: "Generador de Notas de Agradecimiento",
                        ai_card1_desc:
                            "¿No sabes cómo expresar gratitud? ¡Deja que la IA escriba una nota sincera y divertida para enviar con tus $TIPS!",
                        ai_card1_placeholder1:
                            "ej: un artista, un desarrollador...",
                        ai_card1_placeholder2:
                            "ej: por dibujar ese avatar genial...",
                        ai_card1_button: "Generar Nota",
                        ai_card2_title: "Taller de Ideas para Memes",
                        ai_card2_desc:
                            "¿Sin inspiración? ¡Genera textos e ideas de memes virales con un clic para encender tu comunidad!",
                        ai_card2_placeholder1:
                            "Opcional: introduce una palabra clave, ej: Pepe...",
                        ai_card2_button: "✨ Obtener Ideas de Memes",
                        copy_button: "Copiar Texto",
                        copy_success: "¡Copiado!",
                        community_title: "Únete a Nosotros",
                        community_subtitle:
                            "Los que dan propinas son las nuevas ballenas. Conviértete en un pionero de la cultura de propinas digitales, una comunidad llena de buena voluntad te espera.",
                        footer_disclaimer:
                            "Aviso legal: La inversión en moneda digital implica riesgos. Por favor, investigue por su cuenta antes de participar. Este proyecto es un experimento cultural y no constituye asesoramiento financiero.",
                        error_api:
                            "Ocurrió un error. Por favor, revisa la consola para más detalles.",
                        error_generic:
                            "Lo siento, no pude generar una respuesta. Por favor, inténtalo de nuevo.",
                        error_input:
                            "Por favor, introduce un destinatario y una razón.",
                    },
                    ja: {
                        nav_grat: "プロトコル ($GRAT)",
                        nav_tips: "コイン ($TIPS)",
                        nav_tokenomics: "トークノミクス",
                        nav_how: "仕組み",
                        nav_ai: "✨AIツール",
                        nav_community: "コミュニティ",
                        launch_app: "アプリを起動",
                        hero_title:
                            "「いいね」は忘れよう、$TIPS を贈る時が来た！",
                        hero_subtitle:
                            "「$GRAT」をステークし、「$TIPS」を採掘。世界的なデジタルチップ文化復興運動に参加しよう。",
                        hero_btn_grat: "もっと知る",
                        hero_btn_tips: "アプリを起動",
                        grat_title: "感謝プロトコル ($GRAT)",
                        grat_subtitle:
                            "エコシステムの価値の中心、チップ文化の源泉。",
                        grat_card1_title: "マイニング産出",
                        grat_card1_desc:
                            "$GRATのステーキングは$TIPSを生産する唯一の方法です。投機ではなく、文化を創造しているのです。",
                        grat_card2_title: "ガバナンス权重",
                        grat_card2_desc:
                            "$GRATを保有し、プロトコルの所有者になりましょう。共に投票し、エコシステムの未来を決定します。",
                        grat_card3_title: "価値の獲得",
                        grat_card3_desc:
                            "$TIPSの繁栄は$GRATの需要を直接押し上げます。$GRATへの投資は、エコシステム全体への投資です。",
                        tips_title: "チップコイン ($TIPS)",
                        tips_subtitle:
                            "チップ文化を再び偉大に。あらゆる創造を応援しよう。",
                        tips_card1_title: "P2Pチップ",
                        tips_card1_desc:
                            "仲介者なし、遅延ゼロ。あなたの感謝の気持ちが100%相手に届きます。",
                        tips_card2_title: "ミーム創造性の爆発",
                        tips_card2_desc:
                            "「チップ」に関するミームを作成して$TIPSを獲得！楽しさと価値を一緒に広めましょう。",
                        tips_card3_title: "全クリエイターを支援",
                        tips_card3_desc:
                            "あなたが誰であれ、その貢献が認められる価値があるなら、$TIPSで報われる価値があります。",
                        tokenomics_title: "トークノミクス",
                        tokenomics_subtitle:
                            "コミュニティの成長のための公正で透明な分配。",
                        tokenomics_card1_title: "ステーキング報酬",
                        tokenomics_card1_desc:
                            "$TIPSを生成する$GRATステーカーに分配されます。",
                        tokenomics_card2_title: "流動性プール",
                        tokenomics_card2_desc:
                            "両トークンの健全で安定した市場を確保するため。",
                        tokenomics_card3_title: "エコシステム基金",
                        tokenomics_card3_desc:
                            "パートナーシップ、マーケティング、コミュニティ助成金のため。",
                        tokenomics_card4_title: "チームとアドバイザー",
                        tokenomics_card4_desc:
                            "長期的なコミットメントを確保するため、4年間にわたり権利確定。",
                        how_title: "経済フライホイール",
                        how_subtitle: "自己強化型の価値サイクル。",
                        how_step1_title: "$GRATを入手",
                        how_step1_desc:
                            "市場からエコシステムの価値の中核を手に入れます。",
                        how_step2_title: "$GRATをステーク",
                        how_step2_desc:
                            "あなた専用の「チップコイン製造機」を起動します。",
                        how_step3_title: "$TIPSを産出",
                        how_step3_desc: "流通通貨を継続的に受け取ります。",
                        how_step4_title: "チップして拡散",
                        how_step4_desc:
                            "文化的影響を拡大し、$GRATの価値を高めます。",
                        ai_title: "✨ AI搭載ツール",
                        ai_subtitle:
                            "Gemini搭載、コミュニティの創造性を解き放つ。",
                        ai_card1_title: "AI感謝状ジェネレーター",
                        ai_card1_desc:
                            "感謝の伝え方が分からない？$TIPSと一緒に送る、心からの楽しい感謝状をAIに書いてもらいましょう！",
                        ai_card1_placeholder1: "例：アーティスト、開発者...",
                        ai_card1_placeholder2:
                            "例：素敵なアバターを描いてくれた...",
                        ai_card1_button: "感謝状を作成",
                        ai_card2_title: "AIミームアイデア工房",
                        ai_card2_desc:
                            "インスピレーションが枯渇した？ワンクリックでバイラルミームのキャプションやアイデアを生成し、コミュニティを盛り上げよう！",
                        ai_card2_placeholder1:
                            "任意：キーワードを入力, 例：Pepe...",
                        ai_card2_button: "✨ ミームのアイデアを得る",
                        copy_button: "テキストをコピー",
                        copy_success: "コピーしました！",
                        community_title: "参加する",
                        community_subtitle:
                            "チップする者が新しいクジラだ。デジタルチップ文化のパイオニアになろう。善意に満ちたコミュニティがあなたを待っています。",
                        footer_disclaimer:
                            "免責事項：デジタル通貨への投資にはリスクが伴います。参加する前にご自身で調査を行ってください。このプロジェクトは文化的な実験であり、財務上のアドバイスを構成するものではありません。",
                        error_api:
                            "エラーが発生しました。詳細はコンソールを確認してください。",
                        error_generic:
                            "申し訳ありません、応答を生成できませんでした。もう一度お試しください。",
                        error_input: "宛先と感謝の理由を入力してください。",
                    },
                };

                let currentLanguage = "en";
                const getCurrentLanguage = () =>
                    translations[currentLanguage] || translations.en;

                const setLanguage = (lang) => {
                    if (!languages[lang]) return;
                    currentLanguage = lang;
                    const langData = getCurrentLanguage();
                    document.documentElement.lang = lang;
                    document
                        .querySelectorAll("[data-translate-key]")
                        .forEach((el) => {
                            const key = el.dataset.translateKey;
                            if (langData[key]) el.textContent = langData[key];
                        });
                    document
                        .querySelectorAll("[data-translate-key-placeholder]")
                        .forEach((el) => {
                            const key = el.dataset.translateKeyPlaceholder;
                            if (langData[key]) el.placeholder = langData[key];
                        });
                    document.getElementById("current-lang-text").textContent =
                        languages[lang].name;
                    document.getElementById("current-lang-flag").textContent =
                        languages[lang].flag;
                    langMenu.classList.add("hidden");
                };

                const langMenu = document.getElementById("lang-menu");
                Object.keys(languages).forEach((lang) => {
                    const link = document.createElement("a");
                    link.href = "#";
                    link.className =
                        "block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 flex items-center gap-2";
                    link.dataset.lang = lang;
                    link.innerHTML = `<span>${languages[lang].flag}</span> <span>${languages[lang].name}</span>`;
                    link.addEventListener("click", (e) => {
                        e.preventDefault();
                        setLanguage(lang);
                    });
                    langMenu.appendChild(link);
                });

                const mobileMenuBtn =
                    document.getElementById("mobile-menu-btn");
                const mobileMenu = document.getElementById("mobile-menu");
                const mobileNavLinks =
                    document.getElementById("mobile-nav-links");
                mobileNavLinks.innerHTML =
                    document.querySelector("header nav").innerHTML;
                const mobileLangSwitcher = document.createElement("div");
                mobileLangSwitcher.className =
                    "pt-4 border-t border-gray-200 w-full text-center";
                Object.keys(languages).forEach((lang) => {
                    const langBtn = document.createElement("button");
                    langBtn.className =
                        "px-4 py-2 rounded-md w-32 my-1 bg-gray-100 hover:bg-gray-200";
                    langBtn.dataset.lang = lang;
                    langBtn.innerHTML = `<span>${languages[lang].flag}</span> <span>${languages[lang].name}</span>`;
                    langBtn.addEventListener("click", () => {
                        setLanguage(lang);
                        mobileMenu.classList.add("hidden");
                    });
                    mobileLangSwitcher.appendChild(langBtn);
                });
                mobileNavLinks.appendChild(mobileLangSwitcher);
                mobileMenuBtn.addEventListener("click", () =>
                    mobileMenu.classList.toggle("hidden"),
                );

                const generateNoteBtn =
                    document.getElementById("generate-note-btn");
                generateNoteBtn.addEventListener("click", async () => {
                    const recipient =
                        document.getElementById("recipient").value;
                    const reason = document.getElementById("reason").value;
                    const langData = getCurrentLanguage();
                    if (!recipient || !reason) {
                        document.getElementById("note-output").textContent =
                            langData.error_input;
                        document
                            .getElementById("note-output-container")
                            .classList.remove("hidden");
                        return;
                    }
                    toggleLoading(
                        generateNoteBtn,
                        "generate-note-spinner",
                        true,
                    );
                    const langName = languages[currentLanguage].name;
                    const prompt = `Act as a crypto enthusiast. Write a short, fun thank-you note in ${langName} (30-50 words) to a "${recipient}" for "${reason}". End with a fun crypto sign-off and #TipCoin.`;
                    const result = await callGemini(prompt);
                    document.getElementById("note-output").textContent = result;
                    document
                        .getElementById("note-output-container")
                        .classList.remove("hidden");
                    toggleLoading(
                        generateNoteBtn,
                        "generate-note-spinner",
                        false,
                    );
                });

                const generateMemeBtn =
                    document.getElementById("generate-meme-btn");
                generateMemeBtn.addEventListener("click", async () => {
                    const keyword =
                        document.getElementById("meme-keyword").value;
                    toggleLoading(
                        generateMemeBtn,
                        "generate-meme-spinner",
                        true,
                    );
                    const langName = languages[currentLanguage].name;
                    const prompt = `You are a viral meme expert. Generate one creative meme idea in ${langName} about TipCoin ($TIPS), a project about tipping. Include: 1. Meme Format (e.g., Drake Hotline Bling). 2. Text/Caption. ${keyword ? `3. Incorporate keyword "${keyword}".` : ""} 4. Hashtags: #TipCoin #MakeTippingGreatAgain. Be creative.`;
                    const result = await callGemini(prompt);
                    document.getElementById("meme-output").textContent = result;
                    document
                        .getElementById("meme-output-container")
                        .classList.remove("hidden");
                    toggleLoading(
                        generateMemeBtn,
                        "generate-meme-spinner",
                        false,
                    );
                });

                const toggleLoading = (button, spinnerId, isLoading) => {
                    button.disabled = isLoading;
                    document
                        .getElementById(spinnerId)
                        .classList.toggle("hidden", !isLoading);
                };

                const setupCopyButton = (buttonId, outputId) => {
                    const copyBtn = document.getElementById(buttonId);
                    copyBtn.addEventListener("click", () => {
                        const textToCopy =
                            document.getElementById(outputId).textContent;
                        navigator.clipboard.writeText(textToCopy).then(() => {
                            const langData = getCurrentLanguage();
                            const originalText = copyBtn.textContent;
                            copyBtn.textContent = langData.copy_success;
                            setTimeout(() => {
                                copyBtn.textContent = originalText;
                            }, 2000);
                        });
                    });
                };
                setupCopyButton("copy-note-btn", "note-output");
                setupCopyButton("copy-meme-btn", "meme-output");

                const observer = new IntersectionObserver(
                    (entries) => {
                        entries.forEach((entry) => {
                            if (entry.isIntersecting)
                                entry.target.classList.add("is-visible");
                        });
                    },
                    { threshold: 0.1 },
                );
                document
                    .querySelectorAll(".fade-in-section")
                    .forEach((section) => observer.observe(section));

                const langSwitcher = document.getElementById("lang-switcher");
                langSwitcher.addEventListener("click", () =>
                    document
                        .getElementById("lang-menu")
                        .classList.toggle("hidden"),
                );
                document.addEventListener("click", (e) => {
                    if (!langSwitcher.contains(e.target))
                        document
                            .getElementById("lang-menu")
                            .classList.add("hidden");
                });

                setLanguage("en");
            });
        </script>
    </body>
</html>
