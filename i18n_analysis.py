#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
多语言文件分析工具
分析 i18n 语言文件的结构一致性和完整性
"""

import json
import os
from collections import defaultdict
from typing import Dict, Set, List, Any

class I18nAnalyzer:
    def __init__(self, locales_dir: str):
        self.locales_dir = locales_dir
        self.language_files = {}
        self.all_keys = set()
        self.language_keys = {}
        
    def load_language_files(self):
        """加载所有语言文件"""
        language_files = ['zh.json', 'en.json', 'ja.json', 'ko.json', 'fr.json', 'de.json', 'es.json']
        
        for file_name in language_files:
            file_path = os.path.join(self.locales_dir, file_name)
            lang_code = file_name.replace('.json', '')
            
            try:
                with open(file_path, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                    self.language_files[lang_code] = data
                    keys = self._extract_keys(data)
                    self.language_keys[lang_code] = keys
                    self.all_keys.update(keys)
                    print(f"✓ 加载成功: {file_name} - {len(keys)} 个键")
            except Exception as e:
                print(f"✗ 加载失败: {file_name} - {e}")
    
    def _extract_keys(self, data: Dict, prefix: str = "") -> Set[str]:
        """递归提取所有键路径"""
        keys = set()
        for key, value in data.items():
            current_key = f"{prefix}.{key}" if prefix else key
            keys.add(current_key)
            
            if isinstance(value, dict):
                keys.update(self._extract_keys(value, current_key))
        
        return keys
    
    def analyze_structure_consistency(self):
        """分析结构一致性"""
        print("\n" + "="*60)
        print("📊 结构一致性分析")
        print("="*60)
        
        # 统计每种语言的键数量
        print("\n📈 各语言键值对数量统计:")
        for lang, keys in self.language_keys.items():
            print(f"  {lang:3s}: {len(keys):4d} 个键值对")
        
        # 找出缺失的键值对
        print("\n🔍 缺失键值对分析:")
        missing_keys = {}
        for lang in self.language_keys:
            missing = self.all_keys - self.language_keys[lang]
            if missing:
                missing_keys[lang] = sorted(missing)
                print(f"\n  {lang} 语言缺失 {len(missing)} 个键:")
                for key in sorted(missing):
                    print(f"    - {key}")
        
        if not missing_keys:
            print("  ✓ 所有语言文件的键值对都完整，没有缺失")
        
        # 找出多余的键值对
        print("\n🔍 多余键值对分析:")
        extra_keys = {}
        base_keys = min(self.language_keys.values(), key=len)  # 找到键最少的语言作为基准
        
        for lang, keys in self.language_keys.items():
            extra = keys - base_keys
            if extra:
                extra_keys[lang] = sorted(extra)
                print(f"\n  {lang} 语言多出 {len(extra)} 个键:")
                for key in sorted(extra):
                    print(f"    + {key}")
        
        if not extra_keys:
            print("  ✓ 没有发现多余的键值对")
    
    def analyze_key_validation(self):
        """分析键名规范性"""
        print("\n" + "="*60)
        print("🔤 键名规范性检查")
        print("="*60)
        
        irregular_keys = []
        for key in sorted(self.all_keys):
            # 检查是否使用驼峰命名
            parts = key.split('.')
            for part in parts:
                if '_' in part or '-' in part:
                    irregular_keys.append(key)
                    break
                # 检查是否首字母大写
                if part[0].isupper() and part not in ['NFT', 'API', 'AI', 'P2P', 'BSC', 'DEX', 'DeFi']:
                    irregular_keys.append(key)
                    break
        
        if irregular_keys:
            print(f"\n⚠️  发现 {len(irregular_keys)} 个不规范的键名:")
            for key in irregular_keys:
                print(f"    - {key}")
        else:
            print("\n✓ 所有键名都符合驼峰命名规范")
        
        # 检查可能的重复键
        print(f"\n📊 键名统计:")
        print(f"  总键数: {len(self.all_keys)}")
        print(f"  顶级分组: {len([k for k in self.all_keys if '.' not in k])}")
        
        # 分析顶级分组
        top_level_groups = defaultdict(int)
        for key in self.all_keys:
            if '.' in key:
                group = key.split('.')[0]
                top_level_groups[group] += 1
        
        print(f"\n📂 顶级分组分析:")
        for group, count in sorted(top_level_groups.items()):
            print(f"  {group:20s}: {count:3d} 个子键")
    
    def find_potential_duplicates(self):
        """查找可能的重复内容"""
        print("\n" + "="*60)
        print("🔍 潜在重复内容分析")
        print("="*60)
        
        # 以英文作为基准检查重复值
        if 'en' not in self.language_files:
            print("⚠️  没有找到英文基准文件")
            return
            
        en_values = {}
        self._collect_values(self.language_files['en'], en_values)
        
        # 查找重复值
        value_counts = defaultdict(list)
        for key, value in en_values.items():
            if isinstance(value, str) and len(value.strip()) > 0:
                normalized_value = value.lower().strip()
                value_counts[normalized_value].append(key)
        
        duplicates = {v: keys for v, keys in value_counts.items() if len(keys) > 1}
        
        if duplicates:
            print(f"\n⚠️  发现 {len(duplicates)} 组可能的重复内容:")
            for value, keys in list(duplicates.items())[:10]:  # 只显示前10个
                print(f"\n  '{value[:50]}...' 出现在:")
                for key in keys:
                    print(f"    - {key}")
        else:
            print("\n✓ 没有发现明显的重复内容")
    
    def _collect_values(self, data: Dict, result: Dict, prefix: str = ""):
        """递归收集所有值"""
        for key, value in data.items():
            current_key = f"{prefix}.{key}" if prefix else key
            
            if isinstance(value, dict):
                self._collect_values(value, result, current_key)
            else:
                result[current_key] = value
    
    def generate_missing_template(self):
        """生成缺失键值对的模板"""
        print("\n" + "="*60)
        print("📝 修复建议")
        print("="*60)
        
        # 检查是否有缺失
        has_missing = False
        for lang, keys in self.language_keys.items():
            missing = self.all_keys - keys
            if missing:
                has_missing = True
                break
        
        if not has_missing:
            print("\n✅ 所有语言文件都完整，无需修复")
            return
        
        print("\n🔧 建议的修复方案:")
        print("1. 添加缺失的键值对到相应语言文件")
        print("2. 使用占位符文本标记未翻译内容")
        print("3. 进行专业翻译和本地化审核")
        print("4. 验证修复后的文件完整性")
        
        # 生成修复脚本
        print(f"\n📋 可以使用以下命令检查修复结果:")
        print(f"python3 {__file__}")

def main():
    # 分析 i18n 文件
    locales_dir = "/Users/<USER>/Documents/RustroverProjects/miner-token/dapp/src/i18n/locales"
    
    print("🌐 多语言文件分析工具")
    print("="*60)
    
    analyzer = I18nAnalyzer(locales_dir)
    analyzer.load_language_files()
    
    if not analyzer.language_files:
        print("❌ 没有找到任何语言文件")
        return
    
    # 执行分析
    analyzer.analyze_structure_consistency()
    analyzer.analyze_key_validation()
    analyzer.find_potential_duplicates()
    analyzer.generate_missing_template()
    
    print("\n" + "="*60)
    print("✅ 分析完成")
    print("="*60)

if __name__ == "__main__":
    main()