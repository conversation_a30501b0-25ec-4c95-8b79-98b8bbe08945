# 智能合约测试指南

## 测试环境配置

### 1. 环境变量设置

复制并配置环境变量文件：
```bash
cp .env.example .env
```

编辑 `.env` 文件：
```env
# 基础配置
FORK_ENABLED=false          # 设置为true启用fork测试
REPORT_GAS=true            # 启用Gas报告
PRIVATE_KEY=your_key       # 用于真实网络部署
BSCSCAN_API_KEY=your_key   # 用于合约验证
```

### 2. 测试类型说明

#### 本地测试（默认）
- **环境**: Hardhat本地网络
- **特点**: 快速、独立、可重复
- **命令**: `npm test` 或 `npm run test:all`

#### Fork测试（推荐）
- **环境**: Fork BSC测试网到本地
- **特点**: 真实环境、真实合约交互
- **命令**: `npm run test:fork`

#### Gas分析测试
- **环境**: 包含详细的Gas使用报告
- **命令**: `npm run test:gas` 或 `npm run test:fork:gas`

## 测试命令详解

### 基础测试命令

```bash
# 编译合约
npm run compile

# 基础功能测试
npm test

# 运行所有测试
npm run test:all

# 测试覆盖率报告
npm run test:coverage
```

### Fork环境测试

```bash
# 启用fork模式测试
npm run test:fork

# Fork模式 + Gas报告
npm run test:fork:gas

# 启动fork网络节点（用于手动测试）
npm run node:fork

# 启动fork网络控制台
npm run console:fork
```

### 网络节点命令

```bash
# 启动本地Hardhat网络
npm run node

# 启动BSC测试网fork节点
npm run node:fork

# 启动BSC主网fork节点（谨慎使用）
npm run node:mainnet
```

## 测试文件说明

### test/Basic.test.js
- **用途**: 基础功能测试
- **内容**: 合约部署、基本功能验证
- **环境**: 本地网络

### test/Integration.test.js
- **用途**: 集成测试
- **内容**: 合约间交互、完整流程测试
- **环境**: 本地网络

### test/TokenA.test.js
- **用途**: TokenA专项测试
- **内容**: 税收机制、分红功能、安全性测试
- **环境**: 本地网络

### test/Fork.test.js
- **用途**: Fork环境测试
- **内容**: 真实环境交互、PancakeSwap集成测试
- **环境**: BSC测试网fork

## 高级测试场景

### 1. Fork特定区块进行测试

在 `hardhat.config.js` 中设置：
```javascript
forking: {
  url: "https://data-seed-prebsc-1-s1.binance.org:8545/",
  blockNumber: 12345678  // 指定区块号
}
```

### 2. 模拟大量用户测试

```bash
# 启动拥有20个测试账户的网络
npm run node:fork
```

每个账户默认有10,000 BNB用于测试。

### 3. Gas优化效果验证

运行对比测试：
```bash
# 优化前的测试（如果有备份）
npm run test:gas

# 当前优化版本的测试
npm run test:fork:gas
```

### 4. 安全性测试

专门的安全测试脚本：
```bash
# 重入攻击测试
npx hardhat test test/Security.test.js

# 边界条件测试
npx hardhat test test/EdgeCases.test.js
```

## 测试最佳实践

### 1. 测试前准备
- 确保网络连接稳定（fork测试需要）
- 检查RPC节点可用性
- 验证环境变量配置

### 2. 测试执行顺序
1. 基础功能测试（本地）
2. 集成测试（本地）
3. Fork环境测试
4. Gas优化验证
5. 安全性测试

### 3. 测试数据收集
- Gas使用量对比
- 交易成功率
- 执行时间统计
- 错误处理验证

## 故障排除

### 常见问题

#### 1. Fork连接失败
```
Error: could not detect network
```
**解决方案**: 检查网络连接，尝试更换RPC节点

#### 2. Gas估算错误
```
Error: cannot estimate gas
```
**解决方案**: 检查合约参数，确认账户余额充足

#### 3. 合约验证失败
```
Error: Transaction reverted
```
**解决方案**: 检查交易参数，启用详细错误日志

### 调试技巧

#### 1. 启用详细日志
```bash
DEBUG=* npm run test:fork
```

#### 2. 使用Hardhat控制台调试
```bash
npm run console:fork
```

然后在控制台中：
```javascript
const [owner] = await ethers.getSigners();
const balance = await ethers.provider.getBalance(owner.address);
console.log(ethers.formatEther(balance));
```

#### 3. 分步骤测试
将复杂测试拆分为多个小步骤，逐步验证每个功能点。

## 性能基准

### 预期Gas使用量（优化后）

| 操作 | 优化前 | 优化后 | 节省率 |
|------|--------|--------|--------|
| TokenA转账 | ~65,000 | ~45,000 | 30% |
| NFT铸造 | ~150,000 | ~120,000 | 20% |
| 分红领取 | ~200,000 | ~130,000 | 35% |
| 挖矿操作 | ~300,000 | ~220,000 | 27% |

### 测试通过标准

- ✅ 所有基础功能测试通过
- ✅ 集成测试100%通过
- ✅ Fork环境测试通过
- ✅ Gas使用量符合预期优化目标
- ✅ 无安全漏洞检测到
- ✅ 边界条件处理正确

## 持续集成

### GitHub Actions配置（可选）

创建 `.github/workflows/test.yml`：
```yaml
name: Smart Contract Tests
on: [push, pull_request]
jobs:
  test:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v2
      - uses: actions/setup-node@v2
        with:
          node-version: '18'
      - run: npm install
      - run: npm run compile
      - run: npm run test:all
      - run: npm run test:coverage
```

这个测试指南提供了全面的测试策略，确保智能合约的质量和安全性。