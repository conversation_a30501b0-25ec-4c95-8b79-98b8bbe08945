# NFT预售智能合约部署指南

## 📋 概述

本指南详细说明如何部署和配置NFT预售智能合约，实现从复制地址到智能合约交互的升级。

## 🔧 合约功能

### 核心功能
- **预售管理**: 15,000 USDT总额度预售
- **邀请系统**: 完整的推荐关系记录
- **资金管理**: 安全的USDT接收和提取
- **权限控制**: 基于OpenZeppelin的访问控制
- **安全保护**: 重入攻击防护、暂停机制

### 购买限制
- **最小购买**: 1.5 USDT
- **最大购买**: 1,500 USDT（每用户）
- **总额度**: 15,000 USDT
- **支付方式**: USDT (ERC-20)

## 🚀 部署步骤

### 1. 准备工作

#### 安装依赖
```bash
npm install --save-dev @openzeppelin/contracts@^4.9.3
npm install --save-dev hardhat@^2.17.1
npm install --save-dev @nomicfoundation/hardhat-toolbox@^3.0.2
```

#### 创建合约文件

**NFTPresale.sol**
```solidity
// SPDX-License-Identifier: MIT
pragma solidity ^0.8.19;

import "@openzeppelin/contracts/token/ERC20/IERC20.sol";
import "@openzeppelin/contracts/security/ReentrancyGuard.sol";
import "@openzeppelin/contracts/access/Ownable.sol";
import "@openzeppelin/contracts/security/Pausable.sol";

contract NFTPresale is ReentrancyGuard, Ownable, Pausable {
    IERC20 public immutable usdtToken;
    
    uint256 public constant TOTAL_PRESALE_AMOUNT = 15000 * 10**6; // 15,000 USDT
    uint256 public constant MIN_PURCHASE_AMOUNT = 15 * 10**5; // 1.5 USDT
    uint256 public constant MAX_PURCHASE_AMOUNT = 1500 * 10**6; // 1,500 USDT
    
    uint256 public totalRaised;
    uint256 public totalParticipants;
    bool public presaleActive;
    uint256 public presaleStartTime;
    uint256 public presaleEndTime;
    
    mapping(address => uint256) public userPurchased;
    mapping(address => address) public userReferrer;
    mapping(address => address[]) public referrerToReferees;
    mapping(address => uint256) public referrerCount;
    mapping(address => uint256) public referrerTotalAmount;
    
    address[] public participants;
    mapping(address => bool) public hasParticipated;
    
    event PresaleStarted(uint256 startTime, uint256 endTime);
    event PresaleEnded(uint256 totalRaised, uint256 totalParticipants);
    event TokensPurchased(address indexed buyer, uint256 amount, address indexed referrer, uint256 timestamp);
    event ReferralRecorded(address indexed referrer, address indexed referee, uint256 amount);
    event FundsWithdrawn(address indexed owner, uint256 amount);
    
    constructor(address _usdtToken) {
        require(_usdtToken != address(0), "Invalid USDT address");
        usdtToken = IERC20(_usdtToken);
        presaleActive = false;
    }
    
    function startPresale(uint256 _startTime, uint256 _endTime) external onlyOwner {
        require(!presaleActive, "Presale already active");
        require(_startTime >= block.timestamp, "Invalid start time");
        require(_endTime > _startTime, "Invalid end time");
        
        presaleStartTime = _startTime;
        presaleEndTime = _endTime;
        presaleActive = true;
        
        emit PresaleStarted(_startTime, _endTime);
    }
    
    function endPresale() external onlyOwner {
        require(presaleActive, "Presale not active");
        presaleActive = false;
        emit PresaleEnded(totalRaised, totalParticipants);
    }
    
    function buyTokens(uint256 amount, address referrer) external nonReentrant whenNotPaused {
        require(presaleActive, "Presale not active");
        require(block.timestamp >= presaleStartTime && block.timestamp <= presaleEndTime, "Presale not active");
        require(amount >= MIN_PURCHASE_AMOUNT, "Amount too low");
        require(userPurchased[msg.sender] + amount <= MAX_PURCHASE_AMOUNT, "User cap exceeded");
        require(totalRaised + amount <= TOTAL_PRESALE_AMOUNT, "Total cap exceeded");
        
        if (referrer != address(0) && referrer != msg.sender && userReferrer[msg.sender] == address(0)) {
            userReferrer[msg.sender] = referrer;
            referrerToReferees[referrer].push(msg.sender);
            referrerCount[referrer]++;
            emit ReferralRecorded(referrer, msg.sender, amount);
        }
        
        require(usdtToken.transferFrom(msg.sender, address(this), amount), "Transfer failed");
        
        userPurchased[msg.sender] += amount;
        totalRaised += amount;
        
        if (userReferrer[msg.sender] != address(0)) {
            referrerTotalAmount[userReferrer[msg.sender]] += amount;
        }
        
        if (!hasParticipated[msg.sender]) {
            participants.push(msg.sender);
            hasParticipated[msg.sender] = true;
            totalParticipants++;
        }
        
        emit TokensPurchased(msg.sender, amount, userReferrer[msg.sender], block.timestamp);
        
        if (totalRaised >= TOTAL_PRESALE_AMOUNT) {
            presaleActive = false;
            emit PresaleEnded(totalRaised, totalParticipants);
        }
    }
    
    function withdrawFunds(uint256 amount) external onlyOwner nonReentrant {
        uint256 contractBalance = usdtToken.balanceOf(address(this));
        require(contractBalance > 0, "No funds to withdraw");
        
        uint256 withdrawAmount = amount == 0 ? contractBalance : amount;
        require(withdrawAmount <= contractBalance, "Insufficient balance");
        
        require(usdtToken.transfer(owner(), withdrawAmount), "Transfer failed");
        emit FundsWithdrawn(owner(), withdrawAmount);
    }
    
    function setPaused(bool _paused) external onlyOwner {
        if (_paused) {
            _pause();
        } else {
            _unpause();
        }
    }
    
    // 查询函数
    function getPresaleInfo() external view returns (
        uint256 _totalRaised,
        uint256 _totalParticipants,
        uint256 _totalCap,
        bool _active,
        uint256 _startTime,
        uint256 _endTime,
        uint256 _progress
    ) {
        return (
            totalRaised,
            totalParticipants,
            TOTAL_PRESALE_AMOUNT,
            presaleActive && block.timestamp >= presaleStartTime && block.timestamp <= presaleEndTime,
            presaleStartTime,
            presaleEndTime,
            totalRaised * 100 / TOTAL_PRESALE_AMOUNT
        );
    }
    
    function getUserInfo(address user) external view returns (
        uint256 purchased,
        address referrer,
        uint256 refereeCount,
        uint256 referrerTotal
    ) {
        return (
            userPurchased[user],
            userReferrer[user],
            referrerCount[user],
            referrerTotalAmount[user]
        );
    }
    
    function canPurchase(address user, uint256 amount) external view returns (bool, string memory) {
        if (!presaleActive) return (false, "Presale not active");
        if (block.timestamp < presaleStartTime) return (false, "Presale not started");
        if (block.timestamp > presaleEndTime) return (false, "Presale ended");
        if (amount < MIN_PURCHASE_AMOUNT) return (false, "Amount too low");
        if (userPurchased[user] + amount > MAX_PURCHASE_AMOUNT) return (false, "User cap exceeded");
        if (totalRaised + amount > TOTAL_PRESALE_AMOUNT) return (false, "Total cap exceeded");
        
        return (true, "");
    }
    
    function getRemainingPurchaseAmount(address user) external view returns (uint256) {
        uint256 userRemaining = MAX_PURCHASE_AMOUNT - userPurchased[user];
        uint256 totalRemaining = TOTAL_PRESALE_AMOUNT - totalRaised;
        return userRemaining < totalRemaining ? userRemaining : totalRemaining;
    }
    
    function getContractBalance() external view returns (uint256) {
        return usdtToken.balanceOf(address(this));
    }
    
    function getReferees(address referrer) external view returns (address[] memory) {
        return referrerToReferees[referrer];
    }
}
```

### 2. 配置Hardhat

**hardhat.config.js**
```javascript
require("@nomicfoundation/hardhat-toolbox");

const PRIVATE_KEY = process.env.PRIVATE_KEY || "0000000000000000000000000000000000000000000000000000000000000000";
const BSC_API_KEY = process.env.BSC_API_KEY || "";

module.exports = {
  solidity: {
    version: "0.8.19",
    settings: {
      optimizer: {
        enabled: true,
        runs: 200,
      },
    },
  },
  networks: {
    bscTestnet: {
      url: "https://data-seed-prebsc-1-s1.binance.org:8545/",
      chainId: 97,
      accounts: [PRIVATE_KEY],
      gasPrice: ***********,
    },
    bscMainnet: {
      url: "https://bsc-dataseed.binance.org/",
      chainId: 56,
      accounts: [PRIVATE_KEY],
      gasPrice: **********,
    },
  },
  etherscan: {
    apiKey: {
      bsc: BSC_API_KEY,
      bscTestnet: BSC_API_KEY,
    },
  },
};
```

### 3. 部署脚本

**scripts/deploy.js**
```javascript
const { ethers } = require("hardhat");

async function main() {
  console.log("开始部署 NFTPresale 合约...");

  const [deployer] = await ethers.getSigners();
  console.log("部署账户:", deployer.address);

  // BSC主网USDT地址: ******************************************
  // BSC测试网USDT地址: ******************************************
  const network = await ethers.provider.getNetwork();
  const usdtAddress = network.chainId === 56 
    ? "******************************************" // BSC主网
    : "******************************************"; // BSC测试网

  const NFTPresale = await ethers.getContractFactory("NFTPresale");
  const nftPresale = await NFTPresale.deploy(usdtAddress);

  await nftPresale.deployed();

  console.log("NFTPresale 合约部署到:", nftPresale.address);
  console.log("USDT地址:", usdtAddress);
  console.log("网络:", network.chainId === 56 ? "BSC主网" : "BSC测试网");

  return {
    contractAddress: nftPresale.address,
    usdtAddress: usdtAddress,
    network: network.chainId
  };
}

if (require.main === module) {
  main()
    .then(() => process.exit(0))
    .catch((error) => {
      console.error(error);
      process.exit(1);
    });
}

module.exports = { main };
```

### 4. 部署到BSC

#### BSC测试网部署
```bash
# 设置环境变量
export PRIVATE_KEY="your_private_key"
export BSC_API_KEY="your_bscscan_api_key"

# 编译合约
npx hardhat compile

# 部署到BSC测试网
npx hardhat run scripts/deploy.js --network bscTestnet

# 验证合约
npx hardhat verify --network bscTestnet <CONTRACT_ADDRESS> <USDT_ADDRESS>
```

#### BSC主网部署
```bash
# 部署到BSC主网
npx hardhat run scripts/deploy.js --network bscMainnet

# 验证合约
npx hardhat verify --network bscMainnet <CONTRACT_ADDRESS> <USDT_ADDRESS>
```

## ⚙️ 前端配置

### 1. 更新环境变量

**.env**
```bash
# NFT预售合约地址（部署后填写）
REACT_APP_NFT_PRESALE_CONTRACT=0x_YOUR_CONTRACT_ADDRESS
REACT_APP_USDT_CONTRACT=******************************************

# 预售配置
REACT_APP_ENABLE_NFT_PRESALE=true
REACT_APP_PRESALE_NETWORK=BSC (Binance Smart Chain)
REACT_APP_PRESALE_TELEGRAM_URL=https://t.me/your_channel
REACT_APP_PRESALE_SUPPORT_EMAIL=<EMAIL>
```

### 2. 验证集成

1. **检查合约ABI**: 确认 `src/utils/constants.js` 中的 `NFT_PRESALE_ABI` 正确
2. **测试钱包连接**: 确认可以连接MetaMask等钱包
3. **测试购买流程**: 
   - 连接钱包
   - 输入购买金额
   - 授权USDT
   - 执行购买交易

## 🔧 管理员操作

### 启动预售
```javascript
// 使用owner账户调用
const startTime = Math.floor(Date.now() / 1000) + 300; // 5分钟后开始
const endTime = startTime + (7 * 24 * 60 * 60); // 7天后结束

await nftPresale.startPresale(startTime, endTime);
```

### 提取资金
```javascript
// 提取所有资金
await nftPresale.withdrawFunds(0);

// 提取指定金额（以wei为单位）
const amount = ethers.parseUnits("1000", 6); // 1000 USDT
await nftPresale.withdrawFunds(amount);
```

### 结束预售
```javascript
await nftPresale.endPresale();
```

### 暂停合约
```javascript
// 暂停
await nftPresale.setPaused(true);

// 恢复
await nftPresale.setPaused(false);
```

## 📊 查询功能

### 预售信息
```javascript
const info = await nftPresale.getPresaleInfo();
console.log("总筹集:", ethers.formatUnits(info._totalRaised, 6), "USDT");
console.log("参与人数:", info._totalParticipants.toString());
console.log("进度:", info._progress.toString(), "%");
```

### 用户信息
```javascript
const userInfo = await nftPresale.getUserInfo(userAddress);
console.log("已购买:", ethers.formatUnits(userInfo.purchased, 6), "USDT");
console.log("邀请人:", userInfo.referrer);
console.log("邀请数量:", userInfo.refereeCount.toString());
```

## 🔐 安全考虑

1. **权限管理**: 确保只有owner能调用管理函数
2. **重入防护**: 合约已集成ReentrancyGuard
3. **暂停机制**: 紧急情况下可暂停合约
4. **输入验证**: 所有用户输入都进行验证
5. **事件日志**: 关键操作都记录事件
6. **资金安全**: 定期提取资金，避免积累过多

## 🧪 测试

### 单元测试
```bash
npx hardhat test
```

### 网络测试
1. 在BSC测试网部署合约
2. 获取测试USDT代币
3. 测试完整购买流程
4. 验证邀请机制
5. 测试管理员功能

## 📝 部署检查清单

- [ ] 合约代码已审核
- [ ] 测试网测试通过
- [ ] 环境变量已配置
- [ ] 前端集成完成
- [ ] 管理员权限确认
- [ ] 安全措施到位
- [ ] 用户文档准备
- [ ] 应急方案制定

## 🆘 故障排除

### 常见问题

1. **合约部署失败**
   - 检查私钥格式
   - 确认网络配置
   - 验证USDT地址

2. **前端连接失败**
   - 检查合约地址
   - 验证ABI匹配
   - 确认网络ID

3. **购买交易失败**
   - 检查USDT余额
   - 确认授权额度
   - 验证购买限制

4. **权限错误**
   - 确认调用者是owner
   - 检查合约状态
   - 验证网络连接

## 📞 支持

如有问题，请联系开发团队或查阅技术文档。