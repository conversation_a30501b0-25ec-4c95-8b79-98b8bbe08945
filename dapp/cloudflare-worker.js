// Cloudflare Worker for AI API proxy
// This worker securely handles API requests to Google Gemini API
// Deploy this to Cloudflare Workers to hide your API key

// API key will be accessed from environment variables: env.GEMINI_API_KEY
const GEMINI_BASE_URL = 'https://generativelanguage.googleapis.com/v1beta/models/gemini-pro:generateContent';

// CORS headers for allowing requests from your DApp
const corsHeaders = {
  'Access-Control-Allow-Origin': '*', // In production, replace with your domain
  'Access-Control-Allow-Methods': 'POST, OPTIONS',
  'Access-Control-Allow-Headers': 'Content-Type',
  'Access-Control-Max-Age': '86400',
};

// Handle CORS preflight requests
function handleOptions(request) {
  if (
    request.headers.get('Origin') !== null &&
    request.headers.get('Access-Control-Request-Method') !== null &&
    request.headers.get('Access-Control-Request-Headers') !== null
  ) {
    return new Response(null, {
      headers: corsHeaders,
    });
  } else {
    return new Response(null, {
      headers: {
        Allow: 'POST, OPTIONS',
      },
    });
  }
}

// Generate thank you note
async function generateThankYou(request, env) {
  try {
    const { recipient, reason, language } = await request.json();

    if (!recipient || !reason) {
      return new Response(
        JSON.stringify({
          success: false,
          error: 'Missing required fields: recipient and reason'
        }),
        {
          status: 400,
          headers: { ...corsHeaders, 'Content-Type': 'application/json' }
        }
      );
    }

    const prompt = `Write a sincere and fun thank you note in ${language} for ${recipient} ${reason}. 
    The note should be warm, appreciative, and include a mention of $TIPS cryptocurrency as a digital tip. 
    Keep it between 50-100 words and make it personal and engaging. 
    Format it as a friendly message that could be sent along with a crypto tip.`;

    const response = await fetch(`${GEMINI_BASE_URL}?key=${env.GEMINI_API_KEY}`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        contents: [{
          parts: [{
            text: prompt
          }]
        }]
      })
    });

    if (!response.ok) {
      throw new Error(`Gemini API request failed: ${response.status}`);
    }

    const data = await response.json();
    
    if (data.candidates && data.candidates[0] && data.candidates[0].content) {
      const content = data.candidates[0].content.parts[0].text.trim();
      return new Response(
        JSON.stringify({
          success: true,
          content: content
        }),
        {
          headers: { ...corsHeaders, 'Content-Type': 'application/json' }
        }
      );
    } else {
      throw new Error('Invalid response format from Gemini API');
    }
  } catch (error) {
    console.error('Thank you generation error:', error);
    return new Response(
      JSON.stringify({
        success: false,
        error: 'Failed to generate thank you note'
      }),
      {
        status: 500,
        headers: { ...corsHeaders, 'Content-Type': 'application/json' }
      }
    );
  }
}

// Generate meme idea
async function generateMeme(request, env) {
  try {
    const { topic, style, language } = await request.json();

    if (!topic || !style) {
      return new Response(
        JSON.stringify({
          success: false,
          error: 'Missing required fields: topic and style'
        }),
        {
          status: 400,
          headers: { ...corsHeaders, 'Content-Type': 'application/json' }
        }
      );
    }

    const prompt = `Create a ${style} meme concept about ${topic} in ${language}. 
    The meme should be shareable and engaging, suitable for social media. 
    Provide a creative title and a detailed description of the meme concept, including:
    1. The visual elements or setup
    2. The main text/caption
    3. Why it would be funny/engaging
    
    Make it original and worth tipping with $TIPS cryptocurrency. 
    Format your response as JSON with "title" and "description" fields.`;

    const response = await fetch(`${GEMINI_BASE_URL}?key=${env.GEMINI_API_KEY}`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        contents: [{
          parts: [{
            text: prompt
          }]
        }]
      })
    });

    if (!response.ok) {
      throw new Error(`Gemini API request failed: ${response.status}`);
    }

    const data = await response.json();
    
    if (data.candidates && data.candidates[0] && data.candidates[0].content) {
      const responseText = data.candidates[0].content.parts[0].text.trim();
      
      let content;
      try {
        // Try to parse as JSON first
        const jsonMatch = responseText.match(/\{[\s\S]*\}/);
        if (jsonMatch) {
          content = JSON.parse(jsonMatch[0]);
        } else {
          throw new Error('No JSON found');
        }
      } catch (parseError) {
        // If JSON parsing fails, create structured response from text
        const lines = responseText.split('\n').filter(line => line.trim());
        content = {
          title: lines[0] || 'Creative Meme Idea',
          description: lines.slice(1).join('\n') || responseText
        };
      }
      
      return new Response(
        JSON.stringify({
          success: true,
          content: content
        }),
        {
          headers: { ...corsHeaders, 'Content-Type': 'application/json' }
        }
      );
    } else {
      throw new Error('Invalid response format from Gemini API');
    }
  } catch (error) {
    console.error('Meme generation error:', error);
    return new Response(
      JSON.stringify({
        success: false,
        error: 'Failed to generate meme idea'
      }),
      {
        status: 500,
        headers: { ...corsHeaders, 'Content-Type': 'application/json' }
      }
    );
  }
}

// Main request handler
export default {
  async fetch(request, env, ctx) {
    const url = new URL(request.url);
    
    // Handle CORS preflight requests
    if (request.method === 'OPTIONS') {
      return handleOptions(request);
    }

    // Route requests based on path
    if (request.method === 'POST') {
      if (url.pathname === '/generate-thank-you') {
        return generateThankYou(request, env);
      } else if (url.pathname === '/generate-meme') {
        return generateMeme(request, env);
      }
    }

    // Return 404 for unknown routes
    return new Response(
      JSON.stringify({
        success: false,
        error: 'Not found'
      }),
      {
        status: 404,
        headers: { ...corsHeaders, 'Content-Type': 'application/json' }
      }
    );
  },
};