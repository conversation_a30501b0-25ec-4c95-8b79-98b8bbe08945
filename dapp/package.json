{"name": "dapp", "version": "1.0.0", "type": "module", "main": "index.js", "scripts": {"dev": "vite", "build": "vite build", "preview": "vite preview", "test": "echo \"Error: no test specified\" && exit 1"}, "keywords": [], "author": "", "license": "ISC", "description": "", "dependencies": {"ethers": "^6.14.4", "i18next": "^25.2.1", "i18next-browser-languagedetector": "^8.2.0", "react": "^19.1.0", "react-dom": "^19.1.0", "react-i18next": "^15.5.3", "react-router-dom": "^7.6.2", "tailwindcss": "^3.4.17"}, "devDependencies": {"@vitejs/plugin-react": "^4.6.0", "autoprefixer": "^10.4.21", "postcss": "^8.5.6", "vite": "^6.3.5"}}