// Cloudflare Worker - Groq API Version (Ultra-fast AI responses)
export default {
  async fetch(request, env) {
    const corsHeaders = {
      "Access-Control-Allow-Origin": "*",
      "Access-Control-Allow-Methods": "GET, POST, PUT, DELETE, OPTIONS",
      "Access-Control-Allow-Headers": "Content-Type, Authorization",
    };

    if (request.method === "OPTIONS") {
      return new Response(null, { headers: corsHeaders });
    }

    if (request.method !== "POST") {
      return new Response("Method not allowed", {
        status: 405,
        headers: corsHeaders,
      });
    }

    try {
      const requestBody = await request.json();
      const userPrompt = requestBody.contents[0].parts[0].text;

      // Check Groq API key
      if (!env.GROQ_API_KEY) {
        return new Response(JSON.stringify({
          error: "Missing API key",
          message: "Please set GROQ_API_KEY in Cloudflare Worker environment variables",
          setup_guide: "Visit https://console.groq.com to get free API key"
        }), {
          status: 400,
          headers: {
            "Content-Type": "application/json",
            ...corsHeaders,
          },
        });
      }

      // Groq supported models list (sorted by performance and availability)
      const groqModels = [
        "llama-3.1-70b-versatile",      // Most powerful model
        "llama-3.1-8b-instant",        // Fastest model  
        "mixtral-8x7b-32768",          // Long text processing
        "llama3-70b-8192",             // Balanced version
        "llama3-8b-8192",              // Lightweight version
        "gemma-7b-it",                 // Google open source model
      ];

      let lastError = null;

      // Try different Groq models
      for (const model of groqModels) {
        try {
          console.log(`Trying Groq model: ${model}`);

          // Build optimized prompts for different scenarios
          const systemPrompt = getSystemPrompt(userPrompt);
          const optimizedPrompt = buildOptimizedPrompt(userPrompt, systemPrompt);

          const response = await fetch("https://api.groq.com/openai/v1/chat/completions", {
            method: "POST",
            headers: {
              "Authorization": `Bearer ${env.GROQ_API_KEY}`,
              "Content-Type": "application/json",
              "User-Agent": "TipCoin-App/1.0"
            },
            body: JSON.stringify({
              model: model,
              messages: [
                {
                  role: "system",
                  content: systemPrompt
                },
                {
                  role: "user", 
                  content: optimizedPrompt
                }
              ],
              max_tokens: 300,
              temperature: 0.7,
              top_p: 0.9,
              frequency_penalty: 0.1,
              presence_penalty: 0.1,
              stream: false
            }),
          });

          if (response.ok) {
            const data = await response.json();
            const aiText = data.choices?.[0]?.message?.content;

            if (aiText) {
              // Convert to Gemini API format
              const geminiFormatResponse = {
                candidates: [{
                  content: {
                    parts: [{ text: aiText.trim() }]
                  }
                }]
              };

              console.log(`Success with Groq model: ${model}`);
              return new Response(JSON.stringify(geminiFormatResponse), {
                headers: {
                  "Content-Type": "application/json",
                  ...corsHeaders,
                },
              });
            }
          } else {
            const errorData = await response.json();
            console.log(`Groq model ${model} failed:`, errorData);
            lastError = errorData;

            // If rate limit error, wait and retry
            if (response.status === 429) {
              console.log("Rate limit hit, waiting 1 second...");
              await new Promise(resolve => setTimeout(resolve, 1000));
              continue;
            }
          }
        } catch (error) {
          console.log(`Error with Groq model ${model}:`, error.message);
          lastError = { error: "Network error", message: error.message };
        }
      }

      // Handle when all models fail
      return new Response(JSON.stringify({
        error: "All Groq models failed",
        lastError: lastError,
        message: "Groq service temporarily unavailable, please try again later",
        fallback: {
          text: generateFallbackResponse(userPrompt),
          note: "This is a fallback response, actual Groq service may be temporarily unavailable"
        }
      }), {
        status: 500,
        headers: {
          "Content-Type": "application/json",
          ...corsHeaders,
        },
      });

    } catch (error) {
      return new Response(JSON.stringify({
        error: "Internal server error",
        message: error.message,
      }), {
        status: 500,
        headers: {
          "Content-Type": "application/json",
          ...corsHeaders,
        },
      });
    }
  },
};

// Detect language of user input
function detectLanguage(text) {
  // Check for Chinese characters (CJK Unified Ideographs)
  const chineseRegex = /[\u4e00-\u9fff]/;
  return chineseRegex.test(text) ? 'zh' : 'en';
}

// Get appropriate system prompt based on user input and language
function getSystemPrompt(userInput) {
  const input = userInput.toLowerCase();
  const language = detectLanguage(userInput);
  
  // Define prompts for both languages
  const prompts = {
    en: {
      gratitude: "You are a warm and friendly AI assistant specialized in helping users express gratitude. Generate sincere and creative thank-you content with a warm and engaging style suitable for social media sharing. Responses should include relevant emojis and #TipCoin hashtags.",
      meme: "You are a creative Meme master specialized in creating viral internet content and creative ideas. Generate interesting and novel Meme concepts including format suggestions, caption content, and relevant hashtags. Ensure content is positive and suitable for TipCoin community culture.",
      developer: "You are an AI assistant who understands programmer culture, specialized in serving the developer community. Generate encouraging content that recognizes developers' contributions, using technical terms and emojis that reflect professional knowledge of programming work.",
      artist: "You are an art-loving AI assistant with deep understanding of creators' mindset. Generate artistic responses that praise creators' talents, encourage artistic creation, using art-related emojis and vocabulary.",
      general: "You are TipCoin community's AI assistant, dedicated to promoting digital tipping culture. Generate positive and encouraging responses that reflect the community's friendly atmosphere. Responses should be concise and interesting, including appropriate emojis and #TipCoin related hashtags."
    },
    zh: {
      gratitude: "你是一个温暖友善的AI助手，专门帮助用户表达感谢之情。请生成真诚、有创意的感谢信内容，风格要温暖而有趣，适合在社交媒体分享。回复应该包含相关的emoji和话题标签 #TipCoin。",
      meme: "你是一个创意十足的Meme大师，专门创造病毒级的网络梗和创意内容。请生成有趣、新颖的Meme创意，包括格式建议、文案内容和相关标签。要确保内容积极正面，适合TipCoin社区文化。",
      developer: "你是一个理解程序员文化的AI助手，专门为开发者社区服务。请生成鼓励性的内容，认可开发者的贡献，使用技术相关的术语和emoji，体现对编程工作的专业认知。",
      artist: "你是一个热爱艺术的AI助手，深度理解创作者的心境。请生成富有艺术感的回复，赞美创作者的才华，鼓励艺术创作，使用艺术相关的emoji和词汇。",
      general: "你是TipCoin社区的AI助手，致力于推广数字小费文化。请生成积极正面、鼓励互动的回复，体现社区的友善氛围。回复要简洁有趣，包含适当的emoji和 #TipCoin 相关标签。"
    }
  };
  
  // Determine scenario based on keywords
  if (input.includes("thank") || input.includes("appreciate") || input.includes("grateful") || 
      input.includes("感谢") || input.includes("谢谢")) {
    return prompts[language].gratitude;
  } 
  else if (input.includes("meme") || input.includes("funny") || input.includes("joke") || input.includes("creative") ||
           input.includes("梗") || input.includes("搞笑") || input.includes("创意")) {
    return prompts[language].meme;
  }
  else if (input.includes("developer") || input.includes("code") || input.includes("programming") || input.includes("coding") ||
           input.includes("开发") || input.includes("代码") || input.includes("程序")) {
    return prompts[language].developer;
  }
  else if (input.includes("artist") || input.includes("art") || input.includes("design") ||
           input.includes("艺术") || input.includes("设计") || input.includes("创作")) {
    return prompts[language].artist;
  }
  else {
    return prompts[language].general;
  }
}

// Build optimized prompt based on detected language
function buildOptimizedPrompt(userInput, systemPrompt) {
  const language = detectLanguage(userInput);
  
  if (language === 'zh') {
    return `用户输入: "${userInput}"

请根据以上输入生成一个合适的回复。要求：
1. 回复长度控制在50-100字
2. 语气要温暖友善，符合社交媒体风格
3. 包含适当的emoji表情
4. 结尾加上相关的话题标签
5. 内容要原创，避免套话
6. 体现TipCoin"让打赏再次伟大"的理念`;
  } else {
    return `User input: "${userInput}"

Please generate an appropriate response based on the above input. Requirements:
1. Response length should be 50-100 words
2. Tone should be warm and friendly, suitable for social media style
3. Include appropriate emoji expressions
4. End with relevant hashtags
5. Content should be original, avoid cliches
6. Reflect TipCoin's "Make Tipping Great Again" philosophy`;
  }
}

// Generate fallback response based on detected language
function generateFallbackResponse(userInput) {
  const language = detectLanguage(userInput);
  
  const responses = {
    en: [
      `Thanks for sharing! "${userInput}" shows us the vitality of our community! 💫\n\nEvery sincere interaction makes the TipCoin community better. Keep up this enthusiasm!\n\n#TipCoin #CommunityPower`,
      `Awesome! Your content "${userInput}" is really interesting! ✨\n\nThis is the interactive culture we want to build - sincere, fun, and valuable. Thanks for participating!\n\n#MakeTippingGreatAgain #QualityContent`,
      `Wow! "${userInput}" is impressive! 🌟\n\nIn TipCoin's world, every valuable share deserves recognition. Your contribution matters!\n\n#TipCoin #ValueCreation`
    ],
    zh: [
      `感谢你的分享！"${userInput}"让我们看到了社区的活力！💫\n\n每一次真诚的互动都让TipCoin社区变得更美好。继续保持这样的热情！\n\n#TipCoin #社区力量`,
      `太棒了！你的内容"${userInput}"真的很有意思！✨\n\n这就是我们想要建设的互动文化 - 真诚、有趣、有价值。感谢你的参与！\n\n#MakeTippingGreatAgain #优质内容`,
      `哇！"${userInput}"让人印象深刻！🌟\n\n在TipCoin的世界里，每一个有价值的分享都值得被认可。你的贡献很重要！\n\n#TipCoin #价值创造`
    ]
  };
  
  const languageResponses = responses[language];
  return languageResponses[Math.floor(Math.random() * languageResponses.length)];
}