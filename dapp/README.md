# Gratitude Protocol DApp

基于React的去中心化应用，用于与Gratitude Protocol ($GRAT) 和 TipCoin ($TIPS) 生态系统交互。

## 功能特性

### 🏠 官网首页 (HomePage)
- 项目介绍和生态系统说明
- 多语言支持 (中文/英文/日文)
- AI工具集成 (感谢信生成器、Meme创意工坊)
- 代币经济学展示
- 响应式设计，支持移动端

### 🚀 启动应用 (LaunchApp)
- **多钱包支持**: MetaMask、OKX Wallet、TokenPocket、Trust Wallet
- **智能钱包检测**: 自动检测已安装的钱包并显示状态
- **BSC网络集成**: 自动切换到Binance Smart Chain
- **双重挖矿机制**:
  - 机制一: 销毁TokenA获得2倍价值的TokenB
  - 机制二: 持有NFT销毁TokenB获得2倍价值释放
- **NFT管理**: MinerNFT查看和TokenA释放功能
- **实时数据**: 余额监控、挖矿记录、奖励领取

### 🔧 技术特性
- **React 19**: 最新React版本
- **TailwindCSS**: 现代化CSS框架
- **Ethers.js v6**: 区块链交互
- **Vite**: 高性能构建工具
- **响应式设计**: 完美适配桌面和移动设备

## 快速开始

### 环境要求
- Node.js 16+
- npm 或 yarn

### 安装依赖

\`\`\`bash
npm install
\`\`\`

### 启动开发服务器

\`\`\`bash
npm run dev
\`\`\`

应用将在 http://localhost:3000 启动

### 构建生产版本

\`\`\`bash
npm run build
\`\`\`

### 预览生产版本

\`\`\`bash
npm run preview
\`\`\`

## 项目结构

\`\`\`
dapp/
├── public/                 # 静态资源
├── src/
│   ├── components/         # 可复用组件
│   │   ├── WalletModal.jsx     # 钱包选择弹窗
│   │   ├── Notification.jsx    # 通知组件
│   │   └── LoadingOverlay.jsx  # 加载遮罩
│   ├── hooks/              # 自定义Hook
│   │   └── useWallet.js        # 钱包连接逻辑
│   ├── pages/              # 页面组件
│   │   ├── HomePage.jsx        # 官网首页
│   │   └── LaunchApp.jsx       # 启动应用
│   ├── styles/             # 样式文件
│   │   └── index.css          # 全局样式
│   ├── utils/              # 工具函数
│   │   └── constants.js       # 常量配置
│   ├── App.jsx            # 主应用组件
│   └── main.jsx           # 入口文件
├── index.html             # HTML模板
├── vite.config.js         # Vite配置
├── tailwind.config.js     # TailwindCSS配置
└── package.json           # 项目配置
\`\`\`

## 配置说明

### 合约地址配置

在 \`src/utils/constants.js\` 中配置合约地址:

\`\`\`javascript
export const CONTRACT_ADDRESSES = {
  TokenA: "0x...",           // TokenA合约地址
  TokenB: "0x...",           // TokenB合约地址
  MinerNFT: "0x...",         // MinerNFT合约地址
  MiningContract: "0x...",   // MiningContract合约地址
  PriceOracle: "0x..."       // PriceOracle合约地址
}
\`\`\`

### 网络配置

默认配置为BSC主网，如需使用测试网，修改 \`BSC_NETWORK\` 配置:

\`\`\`javascript
export const BSC_NETWORK = {
  chainId: '0x61',  // BSC Testnet: 97 (0x61), BSC Mainnet: 56 (0x38)
  chainName: 'Binance Smart Chain Testnet',
  // ...
}
\`\`\`

## 钱包支持

| 钱包 | 状态 | 平台支持 | 说明 |
|------|------|----------|------|
| 🦊 **MetaMask** | ✅ 完全支持 | 浏览器扩展/移动端 | 最流行的以太坊钱包 |
| 🟡 **OKX Wallet** | ✅ 完全支持 | 浏览器扩展/移动端 | 安全便捷的多链钱包 |
| 💼 **TokenPocket** | ✅ 完全支持 | 浏览器扩展/移动端 | 专业的DeFi钱包 |
| 🛡️ **Trust Wallet** | ✅ 完全支持 | 移动端钱包 | 安全的移动端钱包 |

## 开发说明

### 代码风格
- 使用React Hooks进行状态管理
- 组件采用函数式组件
- 使用TailwindCSS进行样式设计
- 遵循ES6+语法标准

### 安全注意事项
- 合约地址务必在部署前进行验证
- 建议在测试网充分测试后再部署主网
- 用户私钥和助记词永远不会被应用访问或存储

### 性能优化
- 使用React.memo优化重渲染
- 合理使用useCallback和useMemo
- 懒加载大型组件
- 合约调用结果缓存

## 部署

### 构建项目
\`\`\`bash
npm run build
\`\`\`

### 部署到静态托管
构建后的 \`dist\` 目录可以部署到任何静态托管服务:
- Vercel
- Netlify  
- GitHub Pages
- IPFS

### 环境变量
可通过环境变量配置:
- \`VITE_CONTRACT_TOKENA\`: TokenA合约地址
- \`VITE_CONTRACT_TOKENB\`: TokenB合约地址
- \`VITE_CONTRACT_MINERNFT\`: MinerNFT合约地址
- \`VITE_CONTRACT_MINING\`: MiningContract合约地址

## 贡献指南

1. Fork 项目
2. 创建特性分支 (\`git checkout -b feature/AmazingFeature\`)
3. 提交更改 (\`git commit -m 'Add some AmazingFeature'\`)
4. 推送到分支 (\`git push origin feature/AmazingFeature\`)
5. 开启 Pull Request

## 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情

## 支持与反馈

- GitHub Issues: [项目Issues](https://github.com/your-org/gratitude-protocol/issues)
- 官方网站: [Gratitude Protocol](https://gratitude-protocol.com)
- 社区Discord: [加入Discord](https://discord.gg/gratitude)

---

**免责声明**: 本项目仅用于教育和演示目的。在实际使用前，请确保进行充分的安全测试和审计。数字货币投资有风险，请谨慎参与。