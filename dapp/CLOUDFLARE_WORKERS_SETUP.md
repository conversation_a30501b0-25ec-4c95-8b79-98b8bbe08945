# Cloudflare Workers AI Proxy Setup

This guide explains how to deploy a Cloudflare Worker to securely proxy AI requests and hide your Google Gemini API key.

## Why Use Cloudflare Workers?

- **Security**: API keys are stored server-side, never exposed to clients
- **Performance**: Edge computing reduces latency
- **Cost**: Cloudflare Workers free tier includes 100,000 requests/day
- **Reliability**: Global edge network with high availability
- **CORS**: Properly configured CORS headers for web app integration

## Prerequisites

1. A Cloudflare account (free tier is sufficient)
2. Google Gemini API key from [Google AI Studio](https://makersuite.google.com/app/apikey)

## Step 1: Set Up Cloudflare Workers

### Option A: Using Cloudflare Dashboard (Recommended)

1. **Log in to Cloudflare Dashboard**
   - Go to [dash.cloudflare.com](https://dash.cloudflare.com)
   - Navigate to "Workers & Pages"

2. **Create a New Worker**
   - Click "Create application"
   - Choose "Create Worker"
   - Give it a name like `ai-proxy` or `grat-ai-worker`

3. **Deploy the Worker Code**
   - Copy the contents of `cloudflare-worker.js`
   - Paste it into the Cloudflare Workers editor
   - Click "Save and Deploy"

### Option B: Using Wrangler CLI

1. **Install Wrangler**
   ```bash
   npm install -g wrangler
   ```

2. **Login to Cloudflare**
   ```bash
   wrangler login
   ```

3. **Create Worker Project**
   ```bash
   wrangler init ai-proxy
   cd ai-proxy
   ```

4. **Copy Worker Code**
   - Copy `cloudflare-worker.js` to `src/worker.js`

5. **Deploy**
   ```bash
   wrangler deploy
   ```

## Step 2: Configure Environment Variables

### Set the Gemini API Key

1. **In Cloudflare Dashboard:**
   - Go to your worker
   - Click "Settings" → "Variables"
   - Add environment variable:
     - Name: `GEMINI_API_KEY`
     - Value: Your Google Gemini API key
     - Type: Secret (encrypted)

2. **Using Wrangler CLI:**
   ```bash
   wrangler secret put GEMINI_API_KEY
   # Enter your API key when prompted
   ```

## Step 3: Update Worker Code

1. **Modify the API Key Reference**
   
   Replace this line in the worker:
   ```javascript
   const GEMINI_API_KEY = 'YOUR_GEMINI_API_KEY_HERE';
   ```
   
   With:
   ```javascript
   const GEMINI_API_KEY = env.GEMINI_API_KEY;
   ```

2. **Update CORS Settings (Production)**
   
   For production, restrict CORS to your domain:
   ```javascript
   const corsHeaders = {
     'Access-Control-Allow-Origin': 'https://yourdomain.com',
     'Access-Control-Allow-Methods': 'POST, OPTIONS',
     'Access-Control-Allow-Headers': 'Content-Type',
   };
   ```

## Step 4: Configure Your DApp

1. **Get Your Worker URL**
   - Your worker URL will be: `https://worker-name.your-username.workers.dev`
   - Example: `https://ai-proxy.johndoe.workers.dev`

2. **Update Environment Variables**
   ```bash
   # Create .env file
   cp .env.example .env
   
   # Edit .env file
   REACT_APP_AI_WORKER_URL=https://your-worker-name.your-username.workers.dev
   ```

3. **Restart Development Server**
   ```bash
   npm run dev
   ```

## Step 5: Test the Integration

1. **Test Thank You Generator**
   - Navigate to AI Tools section
   - Fill in recipient and reason
   - Click "Generate Note"

2. **Test Meme Workshop**
   - Enter a topic and select style
   - Click "Generate Meme Idea"

3. **Check Browser Network Tab**
   - Requests should go to your Cloudflare Worker URL
   - No direct requests to Google APIs

## API Endpoints

Your Cloudflare Worker exposes these endpoints:

### POST /generate-thank-you
```json
{
  "recipient": "artist",
  "reason": "for creating that amazing artwork",
  "language": "English"
}
```

**Response:**
```json
{
  "success": true,
  "content": "Thank you so much for creating that amazing artwork! Your creativity really brightened my day and shows incredible talent. I'm sending you some $TIPS as a small token of appreciation for sharing your gift with the world. Keep creating beautiful things!"
}
```

### POST /generate-meme
```json
{
  "topic": "cryptocurrency",
  "style": "funny",
  "language": "English"
}
```

**Response:**
```json
{
  "success": true,
  "content": {
    "title": "When You Finally Understand Blockchain",
    "description": "Image shows a person staring at a complex diagram with confused expression, then the same person with enlightened expression. Top text: 'Me trying to understand blockchain for the 100th time' Bottom text: 'Me realizing it's just a fancy ledger with trust issues'"
  }
}
```

## Security Best Practices

1. **API Key Management**
   - Never commit API keys to version control
   - Use Cloudflare's encrypted environment variables
   - Rotate API keys regularly

2. **CORS Configuration**
   - Restrict origins to your domain in production
   - Don't use wildcard (*) for production deployments

3. **Rate Limiting**
   - Consider implementing rate limiting for production use
   - Monitor usage in Cloudflare Analytics

4. **Error Handling**
   - Worker includes comprehensive error handling
   - Logs errors for debugging without exposing sensitive data

## Monitoring and Analytics

1. **Cloudflare Analytics**
   - View request volume and response times
   - Monitor error rates and performance

2. **Custom Metrics**
   - Add custom logging for specific events
   - Track AI generation success rates

## Troubleshooting

### Common Issues

1. **"Worker not found" error**
   - Check the worker URL is correct
   - Ensure worker is deployed and active

2. **CORS errors**
   - Verify CORS headers in worker code
   - Check browser console for specific error messages

3. **API key errors**
   - Confirm GEMINI_API_KEY environment variable is set
   - Test API key directly with Google AI Studio

4. **Generation failures**
   - Check Cloudflare Workers logs
   - Verify Gemini API quota and permissions

### Testing Worker Directly

You can test your worker with curl:

```bash
# Test thank you generation
curl -X POST https://your-worker.workers.dev/generate-thank-you \
  -H "Content-Type: application/json" \
  -d '{
    "recipient": "developer",
    "reason": "for building this amazing app",
    "language": "English"
  }'

# Test meme generation
curl -X POST https://your-worker.workers.dev/generate-meme \
  -H "Content-Type: application/json" \
  -d '{
    "topic": "coding",
    "style": "funny",
    "language": "English"
  }'
```

## Cost Considerations

- **Cloudflare Workers**: 100,000 requests/day free, then $0.50 per million requests
- **Google Gemini API**: Check current pricing at [Google AI Pricing](https://ai.google.dev/pricing)
- **Total**: Very cost-effective for typical usage patterns

## Next Steps

1. Deploy the worker and test functionality
2. Configure your DApp with the worker URL
3. Test all AI features in different languages
4. Monitor usage and performance
5. Consider implementing additional security measures for production

Your AI proxy is now secure, scalable, and ready for production use!