# AI Module Setup Guide

## Overview
The AI module has been optimized to use **Groq API** via Cloudflare Workers for ultra-fast AI responses. This provides:

- ⚡ **Ultra-fast response times** (often < 1 second)
- 🔄 **Multiple model fallbacks** for high reliability  
- 🌍 **Multi-language support** (EN, ZH, JA, FR, DE, ES, KO)
- 🔒 **Secure API key handling** via Cloudflare Workers
- 💾 **Intelligent fallback responses** when service is unavailable

## Architecture

```
Frontend (React) → Cloudflare Worker → Groq API (Llama 3.1)
```

## Setup Instructions

### 1. Deploy Cloudflare Worker

1. Copy the `worker-groq.js` file content
2. Create a new Cloudflare Worker at https://workers.cloudflare.com
3. Paste the worker code and deploy
4. Note your worker URL (e.g., `https://ai-proxy.your-username.workers.dev`)

### 2. Configure Environment Variables

**In Cloudflare Worker:**
```bash
# Set in Worker environment variables
GROQ_API_KEY=your_groq_api_key_here
```

**In React App:**
```bash
# Copy .env.example to .env and update:
REACT_APP_AI_WORKER_URL=https://your-worker-subdomain.your-username.workers.dev
```

### 3. Get Groq API Key

1. Visit https://console.groq.com
2. Sign up for free account
3. Generate API key
4. Add it to your Cloudflare Worker environment variables

## Features

### Supported AI Models (Auto-fallback)
1. `llama-3.1-70b-versatile` - Most powerful model
2. `llama-3.1-8b-instant` - Fastest model  
3. `mixtral-8x7b-32768` - Long text processing
4. `llama3-70b-8192` - Balanced version
5. `llama3-8b-8192` - Lightweight version
6. `gemma-7b-it` - Google open source model

### AI Tools Available
- **Thank You Note Generator**: Creates personalized gratitude messages
- **Meme Workshop**: Generates creative meme ideas and formats
- **General AI Chat**: Handles any user input with contextual responses

### Multi-language Support
- English (en)
- Chinese (zh) 
- Japanese (ja)
- French (fr)
- German (de)
- Spanish (es)
- Korean (ko)

## API Usage

### Thank You Note Generation
```javascript
const result = await aiService.generateThankYouNote(
  "developer",           // recipient
  "for fixing the bug",  // reason
  "en"                  // language
);
```

### Meme Idea Generation
```javascript
const result = await aiService.generateMemeIdea(
  "cryptocurrency",      // topic
  "funny",              // style: funny, wholesome, sarcastic, inspirational
  "en"                  // language
);
```

### General AI Response
```javascript
const result = await aiService.generateResponse(
  "Create a post about TipCoin",  // prompt
  "en"                           // language
);
```

## Error Handling

The system includes multiple layers of error handling:

1. **Model Fallbacks**: If one Groq model fails, tries others automatically
2. **Rate Limit Handling**: Automatic retry with delays for rate limits
3. **Network Error Recovery**: Graceful degradation with fallback responses
4. **Language-specific Fallbacks**: Appropriate responses in user's language

## Performance

- **Response Time**: Typically < 1 second
- **Availability**: 99.9%+ with multiple fallback layers
- **Rate Limits**: Groq provides generous free tier limits
- **Caching**: Responses are optimized for social media sharing

## Troubleshooting

### Common Issues

**1. "All Groq models failed" error**
- Check GROQ_API_KEY is set correctly in Worker environment
- Verify API key is valid at https://console.groq.com
- Check Worker logs for specific error messages

**2. Worker URL not working**
- Ensure Worker is deployed and published
- Check REACT_APP_AI_WORKER_URL matches your Worker URL
- Verify CORS is enabled in Worker (already configured)

**3. Slow responses**
- Check Worker region matches user location
- Monitor Groq API status at https://status.groq.com
- Check network connectivity

### Testing
```bash
# Test Worker directly
curl -X POST https://your-worker-url.workers.dev \
  -H "Content-Type: application/json" \
  -d '{"contents":[{"parts":[{"text":"Hello world"}]}]}'
```

## Development

### Local Development
```bash
# Install dependencies
npm install

# Start development server
npm start

# The AI module will use demo responses if Worker URL is not configured
```

### Production Deployment
```bash
# Build for production
npm run build

# Deploy to your hosting platform
# Make sure REACT_APP_AI_WORKER_URL is set correctly
```

## Security Notes

- ✅ API keys are never exposed to frontend
- ✅ All requests go through Cloudflare Workers proxy
- ✅ CORS is properly configured
- ✅ Rate limiting is handled gracefully
- ✅ No sensitive data is logged

## Support

For issues or questions:
1. Check Worker logs in Cloudflare dashboard
2. Monitor Groq API status
3. Review browser console for client-side errors
4. Test Worker endpoint directly with curl

---

**Powered by Groq AI - Making Tipping Great Again! 🚀**