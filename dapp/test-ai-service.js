// Quick test script to verify AI service fixes
// This can be run in browser console to test the service

// Simulate environment variable check
console.log('Testing AI Service...')

// Test environment variable access
const testEnvVar = (name, defaultValue = '') => {
  try {
    // In React build process, environment variables are injected at build time
    if (typeof process !== 'undefined' && process.env) {
      return process.env[name] || defaultValue
    }
    
    // Fallback for runtime or if process is not available
    if (typeof window !== 'undefined' && window[name]) {
      return window[name]
    }
    
    return defaultValue
  } catch (error) {
    console.warn(`Failed to access environment variable ${name}:`, error)
    return defaultValue
  }
}

// Test the function
const testUrl = testEnvVar('REACT_APP_AI_WORKER_URL', 'https://your-worker-subdomain.your-username.workers.dev')
console.log('Worker URL:', testUrl)

// Test development mode detection
const isDevelopment = testUrl.includes('your-worker-subdomain')
console.log('Development mode:', isDevelopment)

if (isDevelopment) {
  console.log('✅ Service will use fallback responses in development mode')
} else {
  console.log('✅ Service will try to connect to actual Groq worker')
}

console.log('Test completed - AI service should now work without process.env errors')