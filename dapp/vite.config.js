import { defineConfig, loadEnv } from 'vite'
import react from '@vitejs/plugin-react'

// https://vitejs.dev/config/
export default defineConfig(({ mode }) => {
  // Load environment variables
  const env = loadEnv(mode, process.cwd(), '')
  
  return {
    plugins: [react()],
    server: {
      port: 3000,
      open: true
    },
    build: {
      outDir: 'dist',
      sourcemap: true
    },
    define: {
      // Expose environment variables to client code
      'process.env.REACT_APP_AI_WORKER_URL': JSON.stringify(env.REACT_APP_AI_WORKER_URL),
      'process.env.REACT_APP_ENABLE_LAUNCH_APP': JSON.stringify(env.REACT_APP_ENABLE_LAUNCH_APP),
      // NFT presale related environment variables
      'process.env.REACT_APP_ENABLE_NFT_PRESALE': JSON.stringify(env.REACT_APP_ENABLE_NFT_PRESALE),
      'process.env.REACT_APP_PRESALE_ADDRESS': JSON.stringify(env.REACT_APP_PRESALE_ADDRESS),
      'process.env.REACT_APP_PRESALE_PRICE': JSON.stringify(env.REACT_APP_PRESALE_PRICE),
      'process.env.REACT_APP_PRESALE_NETWORK': JSON.stringify(env.REACT_APP_PRESALE_NETWORK),
      'process.env.REACT_APP_PRESALE_END_TIME': JSON.stringify(env.REACT_APP_PRESALE_END_TIME),
      'process.env.REACT_APP_PRESALE_MAX_SUPPLY': JSON.stringify(env.REACT_APP_PRESALE_MAX_SUPPLY),
      'process.env.REACT_APP_PRESALE_TELEGRAM_URL': JSON.stringify(env.REACT_APP_PRESALE_TELEGRAM_URL),
      'process.env.REACT_APP_PRESALE_SUPPORT_EMAIL': JSON.stringify(env.REACT_APP_PRESALE_SUPPORT_EMAIL),
      // Smart contract addresses
      'process.env.REACT_APP_NFT_PRESALE_CONTRACT': JSON.stringify(env.REACT_APP_NFT_PRESALE_CONTRACT),
      'process.env.REACT_APP_USDT_CONTRACT': JSON.stringify(env.REACT_APP_USDT_CONTRACT)
    }
  }
})