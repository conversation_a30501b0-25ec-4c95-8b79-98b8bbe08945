<svg width="64" height="64" viewBox="0 0 64 64" fill="none" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <!-- Modern gradient for background - -->
    <linearGradient id="modernTipsGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#34D399;stop-opacity:1" />
      <stop offset="30%" style="stop-color:#10B981;stop-opacity:1" />
      <stop offset="70%" style="stop-color:#059669;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#047857;stop-opacity:1" />
    </linearGradient>

    <!-- Coin gradient -  -->
    <linearGradient id="modernCoinGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#FEF3C7;stop-opacity:1" />
      <stop offset="30%" style="stop-color:#FCD34D;stop-opacity:1" />
      <stop offset="70%" style="stop-color:#F59E0B;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#D97706;stop-opacity:1" />
    </linearGradient>

    <!-- White overlay gradient -  -->
    <linearGradient id="whiteOverlay" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#FFFFFF;stop-opacity:0.9" />
      <stop offset="100%" style="stop-color:#F3E8FF;stop-opacity:0.7" />
    </linearGradient>

    <!-- Hand gradient -  -->
    <linearGradient id="handGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#FBBF24;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#F59E0B;stop-opacity:1" />
    </linearGradient>

    <!-- Glow effect -  -->
    <filter id="modernGlow" x="-50%" y="-50%" width="200%" height="200%">
      <feGaussianBlur stdDeviation="2" result="coloredBlur"/>
      <feMerge>
        <feMergeNode in="coloredBlur"/>
        <feMergeNode in="SourceGraphic"/>
      </feMerge>
    </filter>

    <!-- Soft shadow -->
    <filter id="softShadow" x="-20%" y="-20%" width="140%" height="140%">
      <feOffset dx="1" dy="2"/>
      <feGaussianBlur stdDeviation="2"/>
      <feFlood flood-color="#000000" flood-opacity="0.2"/>
      <feComposite in2="SourceGraphic" operator="over"/>
    </filter>
  </defs>

  <!-- Main circle background with modern gradient -->
  <circle cx="32" cy="32" r="30" fill="url(#modernTipsGradient)" filter="url(#modernGlow)"/>

  <!-- Subtle inner ring -  -->
  <circle cx="32" cy="32" r="24" fill="none" stroke="url(#whiteOverlay)" stroke-width="1" opacity="0.6"/>

  <!-- Hand giving/receiving gesture -->
  <g transform="translate(32, 32)">
    <!-- Palm/hand base -->
    <path d="M-8 0 Q-10 -2 -8 -4 Q-6 -6 -2 -4 L6 -2 Q8 -1 8 2 Q8 5 6 6 L-6 8 Q-8 8 -8 6 Z"
          fill="url(#handGradient)"
          stroke="#D97706"
          stroke-width="0.5"
          filter="url(#softShadow)"/>

    <!-- Thumb -->
    <path d="M-6 -2 Q-8 -4 -6 -6 Q-4 -7 -2 -5 Q-1 -3 -2 -1 Q-4 0 -6 -2 Z"
          fill="url(#handGradient)"
          stroke="#D97706"
          stroke-width="0.5"/>

    <!-- Coin being given/received -->
    <circle cx="2" cy="-8" r="6" fill="url(#modernCoinGradient)" stroke="#D97706" stroke-width="1"/>
    <circle cx="2" cy="-8" r="4" fill="none" stroke="#F59E0B" stroke-width="0.5" opacity="0.7"/>

    <!-- Dollar symbol on coin -->
    <path d="M2 -11 L2 -5 M-1 -10 Q-1 -11 0 -11 L3 -11 Q4 -11 4 -10 Q4 -9 3 -9 L1 -9 Q0 -9 0 -8 Q0 -7 1 -7 L4 -7 Q5 -7 5 -6"
          stroke="white"
          stroke-width="1"
          fill="none"
          stroke-linecap="round"/>
  </g>

  <!-- Floating coins around -->
  <g opacity="0.6">
    <!-- Small coin 1 -->
    <circle cx="18" cy="20" r="3" fill="url(#modernCoinGradient)" stroke="#D97706" stroke-width="0.5"/>
    <text x="18" y="22" font-family="Arial, sans-serif" font-size="3" font-weight="bold" text-anchor="middle" fill="white">$</text>

    <!-- Small coin 2 -->
    <circle cx="46" cy="18" r="2.5" fill="url(#modernCoinGradient)" stroke="#D97706" stroke-width="0.5"/>
    <text x="46" y="19.5" font-family="Arial, sans-serif" font-size="2.5" font-weight="bold" text-anchor="middle" fill="white">¢</text>

    <!-- Small coin 3 -->
    <circle cx="50" cy="44" r="3.5" fill="url(#modernCoinGradient)" stroke="#D97706" stroke-width="0.5"/>
    <text x="50" y="46" font-family="Arial, sans-serif" font-size="3.5" font-weight="bold" text-anchor="middle" fill="white">$</text>

    <!-- Small coin 4 -->
    <circle cx="16" cy="46" r="2" fill="url(#modernCoinGradient)" stroke="#D97706" stroke-width="0.5"/>
    <text x="16" y="47" font-family="Arial, sans-serif" font-size="2" font-weight="bold" text-anchor="middle" fill="white">¢</text>
  </g>

  <!-- Motion trails -->
  <g opacity="0.4" stroke="url(#whiteOverlay)" stroke-width="1.5" fill="none" stroke-linecap="round">
    <path d="M20 18 Q22 20 24 22"/>
    <path d="M44 20 Q42 22 40 24"/>
    <path d="M48 42 Q46 44 44 46"/>
    <path d="M18 44 Q20 42 22 40"/>
  </g>

  <!-- Sparkle effects -->
  <g opacity="0.8">
    <!-- Plus sparkles -->
    <g stroke="white" stroke-width="1.5" stroke-linecap="round">
      <path d="M12 28 L16 28 M14 26 L14 30"/>
      <path d="M52 36 L56 36 M54 34 L54 38"/>
      <path d="M20 52 L24 52 M22 50 L22 54"/>
    </g>

    <!-- Dot sparkles -->
    <circle cx="48" cy="28" r="1" fill="white"/>
    <circle cx="16" cy="36" r="0.8" fill="white"/>
    <circle cx="24" cy="12" r="0.6" fill="white"/>
    <circle cx="40" cy="52" r="1.2" fill="white"/>
  </g>

  <!-- "T" letter overlay (subtle) -->
  <text x="32" y="52" font-family="Arial, sans-serif" font-size="8" font-weight="900" text-anchor="middle" fill="url(#whiteOverlay)" opacity="0.6">T</text>
</svg>
