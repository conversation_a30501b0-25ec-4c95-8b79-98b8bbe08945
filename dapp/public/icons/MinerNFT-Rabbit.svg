<svg width="64" height="64" viewBox="0 0 64 64" fill="none" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <!-- Main gradient for background -->
    <linearGradient id="nftBgGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#7C3AED;stop-opacity:1" />
      <stop offset="30%" style="stop-color:#8B5CF6;stop-opacity:1" />
      <stop offset="70%" style="stop-color:#A78BFA;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#C4B5FD;stop-opacity:1" />
    </linearGradient>

    <!-- Rabbit gradient -->
    <linearGradient id="rabbitGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#FBBF24;stop-opacity:1" />
      <stop offset="50%" style="stop-color:#F59E0B;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#D97706;stop-opacity:1" />
    </linearGradient>

    <!-- Mining helmet gradient -->
    <linearGradient id="helmetGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#EF4444;stop-opacity:1" />
      <stop offset="50%" style="stop-color:#DC2626;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#B91C1C;stop-opacity:1" />
    </linearGradient>

    <!-- Mining pickaxe gradient -->
    <linearGradient id="pickaxeGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#71717A;stop-opacity:1" />
      <stop offset="50%" style="stop-color:#52525B;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#3F3F46;stop-opacity:1" />
    </linearGradient>

    <!-- Coin gradient -->
    <linearGradient id="coinGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#FEF3C7;stop-opacity:1" />
      <stop offset="50%" style="stop-color:#FCD34D;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#F59E0B;stop-opacity:1" />
    </linearGradient>

    <!-- Glow effect -->
    <filter id="nftGlow" x="-50%" y="-50%" width="200%" height="200%">
      <feGaussianBlur stdDeviation="3" result="coloredBlur"/>
      <feMerge>
        <feMergeNode in="coloredBlur"/>
        <feMergeNode in="SourceGraphic"/>
      </feMerge>
    </filter>

    <!-- Shadow effect -->
    <filter id="nftShadow" x="-20%" y="-20%" width="140%" height="140%">
      <feOffset dx="2" dy="3"/>
      <feGaussianBlur stdDeviation="2"/>
      <feFlood flood-color="#000000" flood-opacity="0.3"/>
      <feComposite in2="SourceGraphic" operator="over"/>
    </filter>
  </defs>

  <!-- Main circular background -->
  <circle cx="32" cy="32" r="30" fill="url(#nftBgGradient)" filter="url(#nftGlow)"/>
  
  <!-- Inner decorative ring -->
  <circle cx="32" cy="32" r="26" fill="none" stroke="#FFFFFF" stroke-width="1" opacity="0.4"/>
  <circle cx="32" cy="32" r="22" fill="none" stroke="#FFFFFF" stroke-width="0.5" opacity="0.6"/>

  <!-- Rabbit character centered -->
  <g transform="translate(32, 32)">
    
    <!-- Mining helmet -->
    <ellipse cx="0" cy="-12" rx="8" ry="6" fill="url(#helmetGradient)" filter="url(#nftShadow)"/>
    <circle cx="0" cy="-12" r="2" fill="#FEF3C7"/>
    <path d="M-2 -12 L2 -12 M0 -14 L0 -10" stroke="#FEF3C7" stroke-width="1" stroke-linecap="round"/>
    
    <!-- Rabbit ears behind helmet -->
    <ellipse cx="-4" cy="-16" rx="2" ry="6" fill="url(#rabbitGradient)" opacity="0.8"/>
    <ellipse cx="4" cy="-16" rx="2" ry="6" fill="url(#rabbitGradient)" opacity="0.8"/>
    <ellipse cx="-4" cy="-16" rx="1" ry="4" fill="#FEF3C7" opacity="0.6"/>
    <ellipse cx="4" cy="-16" rx="1" ry="4" fill="#FEF3C7" opacity="0.6"/>

    <!-- Rabbit head -->
    <circle cx="0" cy="-6" r="6" fill="url(#rabbitGradient)" filter="url(#nftShadow)"/>
    
    <!-- Rabbit face -->
    <circle cx="-2" cy="-8" r="1" fill="#374151"/>  <!-- Left eye -->
    <circle cx="2" cy="-8" r="1" fill="#374151"/>   <!-- Right eye -->
    <circle cx="0" cy="-5" r="0.8" fill="#F472B6"/>  <!-- Nose -->
    <path d="M0 -4 Q-1 -3 -2 -3 Q0 -3 0 -4 Q1 -3 2 -3" stroke="#374151" stroke-width="0.5" fill="none"/>  <!-- Mouth -->

    <!-- Rabbit body -->
    <ellipse cx="0" cy="2" rx="5" ry="7" fill="url(#rabbitGradient)" filter="url(#nftShadow)"/>
    
    <!-- Rabbit arms -->
    <ellipse cx="-6" cy="0" rx="2" ry="4" fill="url(#rabbitGradient)"/>
    <ellipse cx="6" cy="0" rx="2" ry="4" fill="url(#rabbitGradient)"/>
    
    <!-- Mining pickaxe in right hand -->
    <g transform="rotate(15)">
      <rect x="6" y="-2" width="6" height="1" fill="url(#pickaxeGradient)"/>
      <polygon points="12,0 15,-1 15,1" fill="url(#pickaxeGradient)"/>
      <circle cx="7" cy="-1.5" r="0.5" fill="#8B5CF6"/>
    </g>
    
    <!-- Rabbit legs -->
    <ellipse cx="-3" cy="8" rx="2" ry="3" fill="url(#rabbitGradient)"/>
    <ellipse cx="3" cy="8" rx="2" ry="3" fill="url(#rabbitGradient)"/>
    
    <!-- Mining boots -->
    <ellipse cx="-3" cy="10" rx="2.5" ry="2" fill="#374151"/>
    <ellipse cx="3" cy="10" rx="2.5" ry="2" fill="#374151"/>
    
    <!-- Rabbit tail -->
    <circle cx="-5" cy="6" r="1.5" fill="url(#rabbitGradient)" opacity="0.8"/>
    <circle cx="-5" cy="6" r="0.8" fill="#FEF3C7" opacity="0.6"/>

    <!-- GRAT/TIPS coins around the rabbit -->
    <circle cx="-10" cy="-8" r="2.5" fill="url(#coinGradient)" opacity="0.8"/>
    <text x="-10" y="-6.5" font-family="Arial, sans-serif" font-size="2.5" font-weight="bold" text-anchor="middle" fill="#374151">G</text>
    
    <circle cx="10" cy="-6" r="2" fill="url(#coinGradient)" opacity="0.8"/>
    <text x="10" y="-5" font-family="Arial, sans-serif" font-size="2" font-weight="bold" text-anchor="middle" fill="#374151">T</text>
    
    <circle cx="-8" cy="8" r="1.8" fill="url(#coinGradient)" opacity="0.8"/>
    <text x="-8" y="9" font-family="Arial, sans-serif" font-size="1.8" font-weight="bold" text-anchor="middle" fill="#374151">$</text>
    
    <circle cx="9" cy="6" r="2.2" fill="url(#coinGradient)" opacity="0.8"/>
    <text x="9" y="7.5" font-family="Arial, sans-serif" font-size="2.2" font-weight="bold" text-anchor="middle" fill="#374151">¢</text>
  </g>

  <!-- Floating mining-themed sparkles -->
  <g opacity="0.7">
    <!-- Diamond sparkles -->
    <polygon points="12,16 14,18 12,20 10,18" fill="#FEF3C7"/>
    <polygon points="52,28 54,30 52,32 50,30" fill="#FEF3C7"/>
    <polygon points="18,48 20,50 18,52 16,50" fill="#FEF3C7"/>
    <polygon points="46,12 48,14 46,16 44,14" fill="#FEF3C7"/>
    
    <!-- Star sparkles -->
    <g stroke="#FFFFFF" stroke-width="1.5" stroke-linecap="round">
      <path d="M48 48 L52 48 M50 46 L50 50"/>
      <path d="M16 28 L20 28 M18 26 L18 30"/>
      <path d="M12 36 L16 36 M14 34 L14 38"/>
    </g>
    
    <!-- Dot sparkles -->
    <circle cx="24" cy="12" r="1" fill="#FFFFFF"/>
    <circle cx="40" cy="52" r="1.2" fill="#FFFFFF"/>
    <circle cx="8" cy="24" r="0.8" fill="#FFFFFF"/>
    <circle cx="56" cy="40" r="1" fill="#FFFFFF"/>
  </g>

  <!-- NFT badge indicator -->
  <g transform="translate(48, 16)">
    <circle cx="0" cy="0" r="6" fill="#10B981" opacity="0.9"/>
    <text x="0" y="2" font-family="Arial, sans-serif" font-size="4" font-weight="900" text-anchor="middle" fill="white">NFT</text>
  </g>

  <!-- Mining level indicator -->
  <g transform="translate(16, 48)">
    <rect x="-6" y="-2" width="12" height="4" rx="2" fill="#374151" opacity="0.8"/>
    <text x="0" y="1" font-family="Arial, sans-serif" font-size="3" font-weight="bold" text-anchor="middle" fill="#FEF3C7">LV.1</text>
  </g>
</svg>