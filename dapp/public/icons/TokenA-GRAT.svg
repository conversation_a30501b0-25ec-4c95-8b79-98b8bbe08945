<svg width="64" height="64" viewBox="0 0 64 64" fill="none" xmlns="http://www.w3.org/2000/svg">
  <!-- Background circle with gradient -->
  <defs>
    <linearGradient id="gratGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#7C3AED;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#A855F7;stop-opacity:1" />
    </linearGradient>
    <linearGradient id="gratInnerGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#FFFFFF;stop-opacity:0.9" />
      <stop offset="100%" style="stop-color:#F3E8FF;stop-opacity:0.7" />
    </linearGradient>
    <filter id="gratGlow" x="-50%" y="-50%" width="200%" height="200%">
      <feGaussianBlur stdDeviation="2" result="coloredBlur"/>
      <feMerge> 
        <feMergeNode in="coloredBlur"/>
        <feMergeNode in="SourceGraphic"/>
      </feMerge>
    </filter>
  </defs>
  
  <!-- Main circle background -->
  <circle cx="32" cy="32" r="30" fill="url(#gratGradient)" filter="url(#gratGlow)"/>
  
  <!-- Inner decorative ring -->
  <circle cx="32" cy="32" r="24" fill="none" stroke="url(#gratInnerGradient)" stroke-width="1" opacity="0.6"/>
  
  <!-- Central "G" letter with modern styling -->
  <path d="M20 32C20 25.373 25.373 20 32 20C35.5 20 38.5 21.5 40.5 24L37 27C35.8 25.2 34 24 32 24C27.6 24 24 27.6 24 32C24 36.4 27.6 40 32 40C34.5 40 36.5 38.5 37.5 36.5H32V32.5H42V34C42 40.627 36.627 46 30 46C23.373 46 18 40.627 18 34C18 27.373 23.373 22 30 22Z" fill="url(#gratInnerGradient)" transform="translate(2,-2) scale(0.85)"/>
  
  <!-- Stylized "G" for GRAT -->
  <text x="32" y="42" font-family="Arial, sans-serif" font-size="24" font-weight="bold" text-anchor="middle" fill="white">G</text>
  
  <!-- Decorative elements - gratitude rays -->
  <g opacity="0.7">
    <line x1="32" y1="8" x2="32" y2="12" stroke="url(#gratInnerGradient)" stroke-width="2" stroke-linecap="round"/>
    <line x1="32" y1="52" x2="32" y2="56" stroke="url(#gratInnerGradient)" stroke-width="2" stroke-linecap="round"/>
    <line x1="8" y1="32" x2="12" y2="32" stroke="url(#gratInnerGradient)" stroke-width="2" stroke-linecap="round"/>
    <line x1="52" y1="32" x2="56" y2="32" stroke="url(#gratInnerGradient)" stroke-width="2" stroke-linecap="round"/>
    
    <!-- Diagonal rays -->
    <line x1="15.5" y1="15.5" x2="18.5" y2="18.5" stroke="url(#gratInnerGradient)" stroke-width="1.5" stroke-linecap="round"/>
    <line x1="45.5" y1="45.5" x2="48.5" y2="48.5" stroke="url(#gratInnerGradient)" stroke-width="1.5" stroke-linecap="round"/>
    <line x1="48.5" y1="15.5" x2="45.5" y2="18.5" stroke="url(#gratInnerGradient)" stroke-width="1.5" stroke-linecap="round"/>
    <line x1="18.5" y1="45.5" x2="15.5" y2="48.5" stroke="url(#gratInnerGradient)" stroke-width="1.5" stroke-linecap="round"/>
  </g>
</svg>