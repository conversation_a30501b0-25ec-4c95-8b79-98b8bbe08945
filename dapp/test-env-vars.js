// Test if environment variables are accessible after build
console.log('Testing environment variables access:');
console.log('REACT_APP_ENABLE_NFT_PRESALE:', process.env.REACT_APP_ENABLE_NFT_PRESALE);
console.log('REACT_APP_PRESALE_ADDRESS:', process.env.REACT_APP_PRESALE_ADDRESS);
console.log('REACT_APP_PRESALE_PRICE:', process.env.REACT_APP_PRESALE_PRICE);

// Check if undefined
if (process.env.REACT_APP_ENABLE_NFT_PRESALE === undefined) {
  console.error('❌ Environment variables not properly exposed in Vite config');
} else {
  console.log('✅ Environment variables are accessible');
  console.log('✅ NFT Presale should be', process.env.REACT_APP_ENABLE_NFT_PRESALE === 'true' ? 'ENABLED' : 'DISABLED');
}