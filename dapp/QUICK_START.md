# 快速启动指南

## 启动开发服务器

```bash
# 进入dapp目录
cd dapp

# 安装依赖（如果还没有安装）
npm install

# 启动开发服务器
npm run dev
```

服务器将在 http://localhost:3000 启动

## 常用命令

```bash
# 开发模式
npm run dev

# 构建生产版本
npm run build

# 预览生产版本
npm run preview
```

## 项目特性

✅ **React 19** - 最新React版本
✅ **TailwindCSS** - 现代化CSS框架  
✅ **Vite** - 高性能构建工具
✅ **Ethers.js v6** - 区块链交互
✅ **多钱包支持** - MetaMask、OKX、TokenPocket、Trust Wallet
✅ **响应式设计** - 移动端适配
✅ **实时数据** - 余额、挖矿记录监控

## 页面路由

- `/` - 官网首页 (HomePage)
- `/app` - Launch App (LaunchApp)

## 开发环境配置

确保安装了：
- Node.js 16+
- 支持的钱包扩展（开发测试用）

## 注意事项

在使用前请先配置合约地址：
- 编辑 `src/utils/constants.js`
- 替换 `CONTRACT_ADDRESSES` 中的地址
- 确认 `BSC_NETWORK` 配置正确