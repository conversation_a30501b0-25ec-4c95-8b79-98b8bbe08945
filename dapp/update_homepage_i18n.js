// 此脚本用于更新HomePage组件的国际化文本
const fs = require('fs');

const filePath = '/Users/<USER>/Documents/RustroverProjects/miner-token/dapp/src/pages/HomePage.jsx';

// 读取文件内容
let content = fs.readFileSync(filePath, 'utf8');

// 定义需要替换的文本映射
const replacements = [
  // Hero section
  ['Forget Likes, It\'s Time to Give Some $TIPS!', '{t(\'hero.title\')}'],
  ['Stake $GRAT, mine $TIPS. Join a global digital tipping culture revival.', '{t(\'hero.subtitle\')}'],
  ['Learn More', '{t(\'hero.learnMore\')}'],
  ['"Launch App"', '{t(\'hero.launchApp\')}'],
  
  // Protocol section
  ['Gratitude Protocol ($GRAT)', '{t(\'protocol.title\')}'],
  ['The value heart of the ecosystem, the source of tipping culture.', '{t(\'protocol.subtitle\')}'],
  ['Mining Output', '{t(\'protocol.miningOutput.title\')}'],
  ['Staking $GRAT is the only way to produce $TIPS. You\'re not speculating, you\'re minting culture.', '{t(\'protocol.miningOutput.description\')}'],
  ['Governance Weight', '{t(\'protocol.governanceWeight.title\')}'],
  ['Hold $GRAT, and you own the protocol. Vote together to decide the future of the ecosystem.', '{t(\'protocol.governanceWeight.description\')}'],
  ['Value Accrual', '{t(\'protocol.valueAccrual.title\')}'],
  ['The prosperity of $TIPS will directly drive the demand for $GRAT. Investing in $GRAT is investing in the entire ecosystem.', '{t(\'protocol.valueAccrual.description\')}'],
  
  // TIPS section
  ['TipCoin ($TIPS)', '{t(\'tips.title\')}'],
  ['Make Tipping Great Again, cheer for every creation.', '{t(\'tips.subtitle\')}'],
  ['P2P Tipping', '{t(\'tips.p2pTipping.title\')}'],
  ['Direct peer-to-peer tipping without intermediaries. Pure appreciation, instant delivery.', '{t(\'tips.p2pTipping.description\')}'],
  ['Meme Creativity', '{t(\'tips.memeCreativity.title\')}'],
  ['Ignite meme creativity explosion. Every good idea deserves to be rewarded.', '{t(\'tips.memeCreativity.description\')}'],
  ['Empower Creators', '{t(\'tips.empowerCreators.title\')}'],
  ['Empower all creators. From artists to developers, from writers to memers.', '{t(\'tips.empowerCreators.description\')}'],
  
  // Tokenomics section
  ['Tokenomics', '{t(\'tokenomics.title\')}'],
  ['A fair and transparent distribution for community growth.', '{t(\'tokenomics.subtitle\')}'],
  ['Staking Rewards', '{t(\'tokenomics.stakingRewards\')}'],
  ['Community mining and ecosystem growth', '{t(\'tokenomics.stakingRewardsDesc\')}'],
  ['Liquidity Pool', '{t(\'tokenomics.liquidityPool\')}'],
  ['DEX liquidity and trading support', '{t(\'tokenomics.liquidityPoolDesc\')}'],
  ['Ecosystem Fund', '{t(\'tokenomics.ecosystemFund\')}'],
  ['Development and partnerships', '{t(\'tokenomics.ecosystemFundDesc\')}'],
  ['Team & Advisors', '{t(\'tokenomics.teamAndAdvisors\')}'],
  ['Core team and strategic advisors', '{t(\'tokenomics.teamAndAdvisorsDesc\')}'],
  
  // How it works section
  ['How It Works', '{t(\'howItWorks.title\')}'],
  ['Simple steps to join the tipping revolution.', '{t(\'howItWorks.subtitle\')}'],
  ['Stake $GRAT', '{t(\'howItWorks.step1.title\')}'],
  ['Lock your $GRAT tokens in the protocol to start mining $TIPS and earn rewards.', '{t(\'howItWorks.step1.description\')}'],
  ['Mine $TIPS', '{t(\'howItWorks.step2.title\')}'],
  ['Your staked $GRAT automatically generates $TIPS tokens that you can use for tipping.', '{t(\'howItWorks.step2.description\')}'],
  ['Tip & Earn', '{t(\'howItWorks.step3.title\')}'],
  ['Use $TIPS to tip creators and earn rewards through the appreciation economy.', '{t(\'howItWorks.step3.description\')}'],
  
  // AI Tools section
  ['✨ AI-Powered Tools', '{t(\'aiTools.title\')}'],
  ['Powered by Gemini, unleash your community\'s creativity.', '{t(\'aiTools.subtitle\')}'],
  ['AI Thank-You Note Generator', '{t(\'aiTools.thankYouGenerator.title\')}'],
  ['Don\'t know how to express gratitude? Let AI write a sincere and fun thank-you note for you to send with your $TIPS!', '{t(\'aiTools.thankYouGenerator.description\')}'],
  ['"e.g., an artist, a developer..."', '{t(\'aiTools.thankYouGenerator.placeholder1\')}'],
  ['"e.g., for drawing that cool avatar..."', '{t(\'aiTools.thankYouGenerator.placeholder2\')}'],
  ['Generate Note', '{t(\'aiTools.thankYouGenerator.generateBtn\')}'],
  ['AI Meme Workshop', '{t(\'aiTools.memeWorkshop.title\')}'],
  ['Stuck for meme ideas? AI helps you create viral-worthy meme concepts that are worth tipping!', '{t(\'aiTools.memeWorkshop.description\')}'],
  ['"e.g., crypto, cats, coffee..."', '{t(\'aiTools.memeWorkshop.placeholder1\')}'],
  ['Generate Meme Idea', '{t(\'aiTools.memeWorkshop.generateBtn\')}'],
  ['Funny', '{t(\'aiTools.memeWorkshop.styles.funny\')}'],
  ['Wholesome', '{t(\'aiTools.memeWorkshop.styles.wholesome\')}'],
  ['Sarcastic', '{t(\'aiTools.memeWorkshop.styles.sarcastic\')}'],
  ['Inspirational', '{t(\'aiTools.memeWorkshop.styles.inspirational\')}'],
  
  // Community section
  ['Join Us', '{t(\'community.title\')}'],
  ['Tippers are the new whales. Become a pioneer of the digital tipping culture, a community full of goodwill awaits you.', '{t(\'community.subtitle\')}'],
  
  // Footer section
  ['© 2025 Gratitude Protocol. All Rights Reserved.', '{t(\'footer.copyright\')}'],
  ['Disclaimer: Digital currency investment involves risks. Please do your own research before participating. This project is a cultural experiment and does not constitute financial advice.', '{t(\'footer.disclaimer\')}']
];

// 执行替换
replacements.forEach(([oldText, newText]) => {
  content = content.replace(new RegExp(oldText.replace(/[.*+?^${}()|[\]\\]/g, '\\$&'), 'g'), newText);
});

// 写回文件
fs.writeFileSync(filePath, content);
console.log('HomePage i18n 更新完成!');