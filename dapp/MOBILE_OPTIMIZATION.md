# 📱 Miner NFT预售页面移动端适配优化

## 🎯 优化重点

针对NFT预售页面顶部进行了全面的移动端适配优化，确保在所有设备上都有最佳的用户体验。

## ✅ 已完成的移动端优化

### 1. 导航头部 (Header) 优化

**之前的问题：**
- 在小屏幕上导航元素过于拥挤
- Logo和文字在移动端显示不佳
- 按钮文字过长，影响布局

**优化方案：**
```jsx
// 响应式容器内边距
<div className="container mx-auto px-4 sm:px-6 py-3 sm:py-4">

// Logo自适应大小
<Link className="text-lg sm:text-2xl">
  <img className="w-6 h-6 sm:w-8 sm:h-8" />
  <span className="text-violet-700">Miner</span>
  <span className="text-gray-800 hidden sm:inline">NFT</span> {/* 移动端隐藏 */}
</Link>

// 按钮响应式设计
<Link className="py-2 px-3 sm:py-2.5 sm:px-6 text-sm sm:text-base">
  <span className="hidden sm:inline">{t("nftPresale.backToHome")}</span>
  <span className="sm:hidden">← {t("nav.home")}</span> {/* 移动端简化 */}
</Link>
```

### 2. 页面标题 (Title) 优化

**优化前：**
- 标题在移动端过大，影响阅读
- Logo和标题布局在小屏幕上不协调

**优化后：**
```jsx
// 灵活的标题布局
<div className="flex flex-col sm:flex-row items-center justify-center">
  <img className="w-16 h-16 sm:w-20 sm:h-20 mb-3 sm:mb-0 sm:mr-4" />
  <h1 className="text-2xl sm:text-4xl md:text-6xl font-black text-center sm:text-left">
    {t("nftPresale.title")}
  </h1>
</div>

// 响应式副标题
<p className="text-base sm:text-xl text-gray-600 px-4 sm:px-0">
  {t("nftPresale.subtitle")}
</p>
```

### 3. 倒计时 (Countdown) 优化

**问题解决：**
- 倒计时网格在移动端过于紧凑
- 数字和文字在小屏幕上难以阅读

**响应式倒计时：**
```jsx
<div className="grid grid-cols-4 gap-2 sm:gap-4 max-w-xs sm:max-w-md mx-auto">
  <div className="bg-violet-100 rounded-lg p-2 sm:p-4">
    <div className="text-lg sm:text-2xl font-bold text-violet-700">{countdown.days}</div>
    <div className="text-xs sm:text-sm text-gray-600">{t("nftPresale.days")}</div>
  </div>
  {/* 其他时间单位... */}
</div>
```

### 4. 信息卡片 (Info Cards) 优化

**布局改进：**
```jsx
// 响应式网格布局
<div className="grid sm:grid-cols-2 lg:grid-cols-3 gap-4 sm:gap-8">
  
// 卡片内部响应式设计
<div className="card p-4 sm:p-8">
  <div className="flex items-center mb-3 sm:mb-4">
    <div className="w-10 h-10 sm:w-12 sm:h-12 bg-violet-100 rounded-lg">
      <svg className="w-5 h-5 sm:w-6 sm:h-6 text-violet-600" />
    </div>
    <h3 className="text-lg sm:text-2xl font-bold">{title}</h3>
  </div>
  <div className="text-2xl sm:text-4xl font-black text-violet-700">{price}</div>
  <p className="text-sm sm:text-base text-gray-600">{description}</p>
</div>
```

## 📱 移动端设计特点

### 响应式断点
- **xs**: < 640px (手机)
- **sm**: 640px+ (大手机/小平板)
- **md**: 768px+ (平板)
- **lg**: 1024px+ (桌面)

### 关键优化点

1. **空间利用率**：
   - 减少移动端的内边距和外边距
   - 优化元素间距，避免过于拥挤

2. **文字可读性**：
   - 移动端使用较小但清晰的字体
   - 确保对比度和行高适合小屏幕阅读

3. **触摸友好**：
   - 按钮大小符合移动端点击标准
   - 间距充足，避免误触

4. **内容优先级**：
   - 在移动端隐藏次要信息（如"NFT"文字）
   - 简化按钮文字（"返回首页" → "← 首页"）

**🎉 移动端适配优化完成！现在NFT预售页面在所有设备上都能提供出色的用户体验。**