import React from "react";
import { useTranslation } from "react-i18next";

const InviteRewardsModal = ({ isOpen, onClose, userInviteInfo }) => {
  const { t } = useTranslation();

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 z-50 overflow-y-auto">
      <div className="flex items-center justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:items-center sm:p-0">
        {/* Background overlay */}
        <div
          className="fixed inset-0 modal-backdrop transition-opacity"
          onClick={onClose}
        />

        {/* Modal panel */}
        <div className="inline-block align-bottom bg-white rounded-t-lg lg:rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-4xl sm:w-full modal-panel w-full max-h-screen">
          <div className="bg-white h-full flex flex-col">
            {/* Modal header */}
            <div className="flex items-center justify-between p-4 lg:p-6 border-b border-gray-200 flex-shrink-0">
              <h2 className="text-xl lg:text-2xl font-bold text-gray-900">
                {t("invite.rewardHistory")}
              </h2>
              <button
                onClick={onClose}
                className="text-gray-400 hover:text-gray-600 transition-colors p-1"
              >
                <svg
                  className="w-5 h-5 lg:w-6 lg:h-6"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth="2"
                    d="M6 18L18 6M6 6l12 12"
                  />
                </svg>
              </button>
            </div>

            {/* Modal content */}
            <div className="flex-1 p-4 lg:p-6 overflow-y-auto modal-content-scroll min-h-0">
              {/* Invite statistics overview */}
              <div className="grid grid-cols-1 sm:grid-cols-3 gap-3 lg:gap-6 mb-4 lg:mb-8">
                <div className="text-center p-3 lg:p-4 bg-gradient-to-br from-blue-50 to-blue-100 rounded-lg border border-blue-200">
                  <div className="text-2xl lg:text-3xl font-bold text-blue-600 mb-1 lg:mb-2">
                    {userInviteInfo.totalInvited || 0}
                  </div>
                  <div className="text-xs lg:text-sm font-medium text-blue-800">
                    {t("invite.totalInvited")}
                  </div>
                </div>
                <div className="text-center p-3 lg:p-4 bg-gradient-to-br from-green-50 to-green-100 rounded-lg border border-green-200">
                  <div className="text-2xl lg:text-3xl font-bold text-green-600 mb-1 lg:mb-2">
                    {userInviteInfo.inviteeList?.length || 0}
                  </div>
                  <div className="text-xs lg:text-sm font-medium text-green-800">
                    {t("invite.activeInvitees")}
                  </div>
                </div>
                <div className="text-center p-3 lg:p-4 bg-gradient-to-br from-orange-50 to-orange-100 rounded-lg border border-orange-200">
                  <div className="text-2xl lg:text-3xl font-bold text-orange-600 mb-1 lg:mb-2">
                    {parseFloat(
                      userInviteInfo.acceleratedReleaseAmount || 0
                    ).toFixed(4)}
                  </div>
                  <div className="text-xs lg:text-sm font-medium text-orange-800">
                    {t("invite.totalRewards")}
                  </div>
                </div>
              </div>

              {/* My inviter information */}
              {userInviteInfo.inviter && (
                <div className="mb-6 lg:mb-8">
                  <h3 className="text-base lg:text-lg font-semibold text-gray-900 mb-3 lg:mb-4">
                    {t("invite.myInviter")}
                  </h3>
                  <div className="bg-gray-50 rounded-lg p-3 lg:p-4 border border-gray-200">
                    <div className="flex items-center">
                      <div className="w-8 h-8 lg:w-10 lg:h-10 bg-violet-100 rounded-full flex items-center justify-center mr-3">
                        <svg
                          className="w-4 h-4 lg:w-5 lg:h-5 text-violet-600"
                          fill="none"
                          stroke="currentColor"
                          viewBox="0 0 24 24"
                        >
                          <path
                            strokeLinecap="round"
                            strokeLinejoin="round"
                            strokeWidth="2"
                            d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"
                          />
                        </svg>
                      </div>
                      <div>
                        <div className="font-mono text-xs lg:text-sm text-gray-700 break-all">
                          {userInviteInfo.inviter}
                        </div>
                        <div className="text-xs text-gray-500">
                          {t("invite.inviterRole")}
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              )}

              {/* Detailed list of invitees */}
              <div>
                <h3 className="text-base lg:text-lg font-semibold text-gray-900 mb-3 lg:mb-4">
                  {t("invite.inviteeList")} (
                  {userInviteInfo.inviteeList?.length || 0})
                </h3>

                {userInviteInfo.inviteeList &&
                userInviteInfo.inviteeList.length > 0 ? (
                  <div className="space-y-2 lg:space-y-3 max-h-64 overflow-y-auto">
                    {userInviteInfo.inviteeList.map((invitee, index) => (
                      <div
                        key={index}
                        className="bg-gray-50 rounded-lg p-3 lg:p-4 border border-gray-200"
                      >
                        <div className="flex items-center justify-between">
                          <div className="flex items-center">
                            <div className="w-6 h-6 lg:w-8 lg:h-8 bg-green-100 rounded-full flex items-center justify-center mr-2 lg:mr-3">
                              <span className="text-green-600 font-semibold text-xs lg:text-sm">
                                {index + 1}
                              </span>
                            </div>
                            <div>
                              <div className="font-mono text-xs lg:text-sm text-gray-700 break-all">
                                {invitee}
                              </div>
                              <div className="text-xs text-gray-500">
                                {t("invite.invitee")} #{index + 1}
                              </div>
                            </div>
                          </div>
                          <div className="text-right flex-shrink-0 ml-2">
                            <div className="text-xs lg:text-sm font-semibold text-green-600">
                              {t("invite.activeStatus")}
                            </div>
                            <div className="text-xs text-gray-500 hidden lg:block">
                              {t("invite.joinedOn")}{" "}
                              {new Date().toLocaleDateString()}
                            </div>
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                ) : (
                  <div className="text-center py-6 lg:py-8">
                    <div className="w-12 h-12 lg:w-16 lg:h-16 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-3 lg:mb-4">
                      <svg
                        className="w-6 h-6 lg:w-8 lg:h-8 text-gray-400"
                        fill="none"
                        stroke="currentColor"
                        viewBox="0 0 24 24"
                      >
                        <path
                          strokeLinecap="round"
                          strokeLinejoin="round"
                          strokeWidth="2"
                          d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z"
                        />
                      </svg>
                    </div>
                    <h3 className="text-base lg:text-lg font-semibold text-gray-900 mb-2">
                      {t("invite.noInvitees")}
                    </h3>
                    <p className="text-sm lg:text-base text-gray-600">
                      {t("invite.startInviting")}
                    </p>
                  </div>
                )}
              </div>

              {/* Reward explanation */}
              <div className="mt-6 lg:mt-8 p-3 lg:p-4 bg-amber-50 border border-amber-200 rounded-lg">
                <div className="flex items-start">
                  <svg
                    className="w-4 h-4 lg:w-5 lg:h-5 text-amber-500 mt-0.5 mr-2 lg:mr-3 flex-shrink-0"
                    fill="none"
                    stroke="currentColor"
                    viewBox="0 0 24 24"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth="2"
                      d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"
                    />
                  </svg>
                  <div className="text-xs lg:text-sm text-amber-800">
                    <p className="font-semibold mb-1">
                      {t("invite.rewardMechanism")}
                    </p>
                    <p>{t("invite.rewardExplanation")}</p>
                  </div>
                </div>
              </div>
            </div>

            {/* Modal footer */}
            <div className="bg-gray-50 px-4 lg:px-6 py-3 lg:py-4 flex justify-center lg:justify-end flex-shrink-0">
              <button
                type="button"
                className="secondary-btn px-4 lg:px-6 py-2 lg:py-3 rounded-lg text-sm lg:text-base"
                onClick={onClose}
              >
                {t("wallet.cancel")}
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default InviteRewardsModal;
