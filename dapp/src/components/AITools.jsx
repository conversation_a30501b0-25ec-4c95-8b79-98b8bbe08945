import React, { useState } from "react";
import { useTranslation } from "react-i18next";
import aiService from "../services/aiService";

const AITools = () => {
  const { t, i18n } = useTranslation();

  // Thank You Generator State
  const [thankYouForm, setThankYouForm] = useState({
    recipient: "",
    reason: "",
  });
  const [thankYouResult, setThankYouResult] = useState("");
  const [thankYouLoading, setThankYouLoading] = useState(false);
  const [thankYouError, setThankYouError] = useState("");

  // Meme Workshop State
  const [memeForm, setMemeForm] = useState({
    topic: "",
    style: "funny",
  });
  const [memeResult, setMemeResult] = useState(null);
  const [memeLoading, setMemeLoading] = useState(false);
  const [memeError, setMemeError] = useState("");

  // Handle Thank You Note Generation
  const handleGenerateThankYou = async () => {
    if (!thankYouForm.recipient.trim() || !thankYouForm.reason.trim()) {
      setThankYouError(t("aiTools.errors.fillAllFields"));
      return;
    }

    setThankYouLoading(true);
    setThankYouError("");
    setThankYouResult("");

    try {
      const result = await aiService.generateThankYouNote(
        thankYouForm.recipient,
        thankYouForm.reason,
        i18n.language
      );
      setThankYouResult(result);
    } catch (error) {
      setThankYouError(error.message);
    } finally {
      setThankYouLoading(false);
    }
  };

  // Handle Meme Idea Generation
  const handleGenerateMeme = async () => {
    if (!memeForm.topic.trim()) {
      setMemeError(t("aiTools.errors.enterTopic"));
      return;
    }

    setMemeLoading(true);
    setMemeError("");
    setMemeResult(null);

    try {
      const result = await aiService.generateMemeIdea(
        memeForm.topic,
        memeForm.style,
        i18n.language
      );
      setMemeResult(result);
    } catch (error) {
      setMemeError(error.message);
    } finally {
      setMemeLoading(false);
    }
  };

  // Copy to clipboard functionality
  const copyToClipboard = async (text) => {
    try {
      await navigator.clipboard.writeText(text);
      // Could add a toast notification here
    } catch (error) {
      console.error("Failed to copy text:", error);
    }
  };

  return (
    <section id="ai-tools" className="py-28">
      <div className="container mx-auto px-6">
        <div className="text-center mb-16">
          <h2 className="text-4xl md:text-6xl font-black text-gray-900 leading-tight">
            {t("aiTools.title")}
          </h2>
          <p className="text-gray-600 mt-4 text-lg max-w-2xl mx-auto">
            {t("aiTools.subtitle")}
          </p>
          {!aiService.isAvailable() && (
            <div className="mt-4 p-4 bg-amber-50 border border-amber-200 rounded-lg max-w-2xl mx-auto">
              <p className="text-amber-800 text-sm">
                {t("aiTools.apiKeyNotConfigured")}
              </p>
            </div>
          )}
        </div>

        <div className="grid md:grid-cols-2 gap-8 max-w-5xl mx-auto">
          {/* AI Thank-You Note Generator */}
          <div className="card p-8 flex flex-col">
            <h3 className="text-2xl font-bold text-gray-900 mb-4 font-poppins flex items-center">
              <span className="text-3xl mr-3">💌</span>
              <span>{t("aiTools.thankYouGenerator.title")}</span>
            </h3>
            <p className="text-gray-600 mb-4 flex-grow">
              {t("aiTools.thankYouGenerator.description")}
            </p>

            <div className="space-y-4 mb-4">
              <input
                type="text"
                placeholder={t("aiTools.thankYouGenerator.placeholder1")}
                value={thankYouForm.recipient}
                onChange={(e) =>
                  setThankYouForm((prev) => ({
                    ...prev,
                    recipient: e.target.value,
                  }))
                }
                className="w-full bg-gray-100 rounded-lg p-3 text-gray-800 placeholder-gray-400 border border-gray-200 focus:border-violet-500 focus:ring-violet-500 focus:outline-none"
                disabled={thankYouLoading}
              />
              <input
                type="text"
                placeholder={t("aiTools.thankYouGenerator.placeholder2")}
                value={thankYouForm.reason}
                onChange={(e) =>
                  setThankYouForm((prev) => ({
                    ...prev,
                    reason: e.target.value,
                  }))
                }
                className="w-full bg-gray-100 rounded-lg p-3 text-gray-800 placeholder-gray-400 border border-gray-200 focus:border-violet-500 focus:ring-violet-500 focus:outline-none"
                disabled={thankYouLoading}
              />
            </div>

            {thankYouError && (
              <div className="mb-4 p-3 bg-red-50 border border-red-200 rounded-lg">
                <p className="text-red-600 text-sm">{thankYouError}</p>
              </div>
            )}

            {thankYouResult && (
              <div className="mb-4 p-4 bg-green-50 border border-green-200 rounded-lg">
                <p className="text-gray-800 whitespace-pre-wrap">
                  {thankYouResult}
                </p>
                <button
                  onClick={() => copyToClipboard(thankYouResult)}
                  className="mt-2 text-sm text-violet-600 hover:text-violet-700 flex items-center"
                >
                  <svg
                    className="w-4 h-4 mr-1"
                    fill="none"
                    stroke="currentColor"
                    viewBox="0 0 24 24"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth="2"
                      d="M8 16H6a2 2 0 01-2-2V6a2 2 0 012-2h8a2 2 0 012 2v2m-6 12h8a2 2 0 002-2v-8a2 2 0 00-2-2h-8a2 2 0 00-2 2v8a2 2 0 002 2z"
                    />
                  </svg>
                  {t("aiTools.copyToClipboard")}
                </button>
              </div>
            )}

            <button
              onClick={handleGenerateThankYou}
              disabled={thankYouLoading || !aiService.isAvailable()}
              className="secondary-btn font-bold py-3 px-6 rounded-lg w-full flex items-center justify-center disabled:opacity-50 disabled:cursor-not-allowed"
            >
              {thankYouLoading ? (
                <>
                  <svg
                    className="animate-spin -ml-1 mr-3 h-5 w-5"
                    fill="none"
                    viewBox="0 0 24 24"
                  >
                    <circle
                      className="opacity-25"
                      cx="12"
                      cy="12"
                      r="10"
                      stroke="currentColor"
                      strokeWidth="4"
                    ></circle>
                    <path
                      className="opacity-75"
                      fill="currentColor"
                      d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
                    ></path>
                  </svg>
                  {t("aiTools.generating")}
                </>
              ) : (
                t("aiTools.thankYouGenerator.generateBtn")
              )}
            </button>
          </div>

          {/* AI Meme Workshop */}
          <div className="card p-8 flex flex-col">
            <h3 className="text-2xl font-bold text-gray-900 mb-4 font-poppins flex items-center">
              <span className="text-3xl mr-3">🎨</span>
              <span>{t("aiTools.memeWorkshop.title")}</span>
            </h3>
            <p className="text-gray-600 mb-4 flex-grow">
              {t("aiTools.memeWorkshop.description")}
            </p>

            <div className="space-y-4 mb-4">
              <input
                type="text"
                placeholder={t("aiTools.memeWorkshop.placeholder1")}
                value={memeForm.topic}
                onChange={(e) =>
                  setMemeForm((prev) => ({ ...prev, topic: e.target.value }))
                }
                className="w-full bg-gray-100 rounded-lg p-3 text-gray-800 placeholder-gray-400 border border-gray-200 focus:border-violet-500 focus:ring-violet-500 focus:outline-none"
                disabled={memeLoading}
              />
              <select
                value={memeForm.style}
                onChange={(e) =>
                  setMemeForm((prev) => ({ ...prev, style: e.target.value }))
                }
                className="w-full bg-gray-100 rounded-lg p-3 text-gray-800 border border-gray-200 focus:border-violet-500 focus:ring-violet-500 focus:outline-none"
                disabled={memeLoading}
              >
                <option value="funny">
                  {t("aiTools.memeWorkshop.styles.funny")}
                </option>
                <option value="wholesome">
                  {t("aiTools.memeWorkshop.styles.wholesome")}
                </option>
                <option value="sarcastic">
                  {t("aiTools.memeWorkshop.styles.sarcastic")}
                </option>
                <option value="inspirational">
                  {t("aiTools.memeWorkshop.styles.inspirational")}
                </option>
              </select>
            </div>

            {memeError && (
              <div className="mb-4 p-3 bg-red-50 border border-red-200 rounded-lg">
                <p className="text-red-600 text-sm">{memeError}</p>
              </div>
            )}

            {memeResult && (
              <div className="mb-4 p-4 bg-blue-50 border border-blue-200 rounded-lg">
                <h4 className="font-bold text-gray-900 mb-2">
                  {memeResult.title}
                </h4>
                <p className="text-gray-800 text-sm whitespace-pre-wrap">
                  {memeResult.description}
                </p>
                <button
                  onClick={() =>
                    copyToClipboard(
                      `${memeResult.title}\n\n${memeResult.description}`
                    )
                  }
                  className="mt-2 text-sm text-violet-600 hover:text-violet-700 flex items-center"
                >
                  <svg
                    className="w-4 h-4 mr-1"
                    fill="none"
                    stroke="currentColor"
                    viewBox="0 0 24 24"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth="2"
                      d="M8 16H6a2 2 0 01-2-2V6a2 2 0 012-2h8a2 2 0 012 2v2m-6 12h8a2 2 0 002-2v-8a2 2 0 00-2-2h-8a2 2 0 00-2 2v8a2 2 0 002 2z"
                    />
                  </svg>
                  {t("aiTools.copyToClipboard")}
                </button>
              </div>
            )}

            <button
              onClick={handleGenerateMeme}
              disabled={memeLoading || !aiService.isAvailable()}
              className="secondary-btn font-bold py-3 px-6 rounded-lg w-full flex items-center justify-center disabled:opacity-50 disabled:cursor-not-allowed"
            >
              {memeLoading ? (
                <>
                  <svg
                    className="animate-spin -ml-1 mr-3 h-5 w-5"
                    fill="none"
                    viewBox="0 0 24 24"
                  >
                    <circle
                      className="opacity-25"
                      cx="12"
                      cy="12"
                      r="10"
                      stroke="currentColor"
                      strokeWidth="4"
                    ></circle>
                    <path
                      className="opacity-75"
                      fill="currentColor"
                      d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
                    ></path>
                  </svg>
                  {t("aiTools.generating")}
                </>
              ) : (
                t("aiTools.memeWorkshop.generateBtn")
              )}
            </button>
          </div>
        </div>
      </div>
    </section>
  );
};

export default AITools;
