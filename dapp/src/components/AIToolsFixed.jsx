import React, { useState, useEffect } from "react";
import { useTranslation } from "react-i18next";
import aiService from "../services/aiService";

const AIToolsFixed = () => {
  const { t, i18n } = useTranslation();

  // AI Service status
  const [aiServiceInfo, setAiServiceInfo] = useState(null);

  useEffect(() => {
    setAiServiceInfo(aiService.getServiceInfo());
  }, []);

  // Thank You Generator State
  const [thankYouForm, setThankYouForm] = useState({
    recipient: "",
    reason: "",
  });
  const [thankYouResult, setThankYouResult] = useState("");
  const [thankYouLoading, setThankYouLoading] = useState(false);
  const [thankYouError, setThankYouError] = useState("");

  // Meme Workshop State
  const [memeForm, setMemeForm] = useState({
    topic: "",
    style: "funny",
  });
  const [memeResult, setMemeResult] = useState(null);
  const [memeLoading, setMemeLoading] = useState(false);
  const [memeError, setMemeError] = useState("");

  // Handle Thank You Note Generation with real AI
  const handleGenerateThankYou = async () => {
    if (!thankYouForm.recipient.trim() || !thankYouForm.reason.trim()) {
      setThankYouError(t("aiTools.errors.fillAllFields"));
      return;
    }

    setThankYouLoading(true);
    setThankYouError("");
    setThankYouResult("");

    try {
      // Use real AI service with Groq
      const result = await aiService.generateThankYouNote(
        thankYouForm.recipient,
        thankYouForm.reason,
        i18n.language
      );

      setThankYouResult(result);
    } catch (error) {
      console.error("Thank you generation error:", error);
      setThankYouError(
        t("aiTools.errors.generateFailed") ||
          "Failed to generate thank you note. Please try again."
      );

      // Fallback to demo message if AI fails
      const fallbackMessages = {
        en: `Thank you so much, ${thankYouForm.recipient}, ${thankYouForm.reason}! Your contribution means the world to us. Here's some $TIPS as a token of our appreciation! 🎉 #TipCoin`,
        zh: `非常感谢您，${thankYouForm.recipient}，${thankYouForm.reason}！您的贡献对我们意义重大。这里有一些 $TIPS 作为我们感谢的象征！🎉 #TipCoin`,
        ja: `${thankYouForm.recipient}様、${thankYouForm.reason}本当にありがとうございます！🎉 #TipCoin`,
        fr: `Merci beaucoup, ${thankYouForm.recipient}, ${thankYouForm.reason}! 🎉 #TipCoin`,
        de: `Vielen Dank, ${thankYouForm.recipient}, ${thankYouForm.reason}! 🎉 #TipCoin`,
        es: `¡Muchas gracias, ${thankYouForm.recipient}, ${thankYouForm.reason}! 🎉 #TipCoin`,
        ko: `${thankYouForm.recipient}님, ${thankYouForm.reason} 정말 감사합니다! 🎉 #TipCoin`,
      };
      setThankYouResult(fallbackMessages[i18n.language] || fallbackMessages.en);
    } finally {
      setThankYouLoading(false);
    }
  };

  // Handle Meme Idea Generation with real AI
  const handleGenerateMeme = async () => {
    if (!memeForm.topic.trim()) {
      setMemeError(t("aiTools.errors.enterTopic"));
      return;
    }

    setMemeLoading(true);
    setMemeError("");
    setMemeResult(null);

    try {
      // Use real AI service with Groq
      const result = await aiService.generateMemeIdea(
        memeForm.topic,
        memeForm.style,
        i18n.language
      );

      setMemeResult(result);
    } catch (error) {
      console.error("Meme generation error:", error);
      setMemeError(
        t("aiTools.errors.generateFailed") ||
          "Failed to generate meme idea. Please try again."
      );

      // Fallback to demo meme if AI fails
      const fallbackMemes = {
        en: {
          title: `${
            memeForm.style.charAt(0).toUpperCase() + memeForm.style.slice(1)
          } ${memeForm.topic} Meme`,
          description: `A ${memeForm.style} meme about ${memeForm.topic}.\n\nTop text: "When you finally understand ${memeForm.topic}"\nBottom text: "But then realize you need to explain it to everyone else"\n\nPerfect for sharing and definitely worth some $TIPS! #TipCoin #MemeWorkshop`,
        },
        zh: {
          title: `${memeForm.style} ${memeForm.topic} 梗图`,
          description: `一个关于 ${memeForm.topic} 的${memeForm.style}梗图。\n\n上方文字："当你终于理解了 ${memeForm.topic}"\n下方文字："但是然后意识到你需要向其他人解释"\n\n完美的分享内容，绝对值得一些 $TIPS！#TipCoin #梗图工坊`,
        },
      };

      const fallback = fallbackMemes[i18n.language] || fallbackMemes.en;
      setMemeResult(fallback);
    } finally {
      setMemeLoading(false);
    }
  };

  // Copy to clipboard functionality
  const copyToClipboard = async (text) => {
    try {
      await navigator.clipboard.writeText(text);
      // Could add a toast notification here
    } catch (error) {
      console.error("Failed to copy text:", error);
    }
  };

  return (
    <section id="ai-tools" className="py-28">
      <div className="container mx-auto px-6">
        <div className="text-center mb-16">
          <h2 className="text-4xl md:text-6xl font-black text-gray-900 leading-tight">
            {t("aiTools.title")}
          </h2>
          <p className="text-gray-600 mt-4 text-lg max-w-2xl mx-auto">
            {t("aiTools.subtitle")}
          </p>
          {aiServiceInfo && (
            <div
              className={`mt-4 p-4 rounded-lg max-w-2xl mx-auto border ${
                aiServiceInfo.available
                  ? "bg-blue-50 border-blue-200"
                  : "bg-amber-50 border-amber-200"
              }`}
            >
              <p
                className={`text-sm flex items-center justify-center ${
                  aiServiceInfo.available ? "text-blue-800" : "text-amber-800"
                }`}
              >
                <span
                  className={`inline-block w-2 h-2 rounded-full mr-2 ${
                    aiServiceInfo.available
                      ? "bg-green-500 animate-pulse"
                      : "bg-amber-500"
                  }`}
                ></span>
                {aiServiceInfo.available
                  ? `Powered by ${aiServiceInfo.provider} - ${aiServiceInfo.speed} ⚡`
                  : "Development Mode - Using Demo Responses 🔧"}
              </p>
            </div>
          )}
        </div>

        <div className="grid md:grid-cols-2 gap-8 max-w-5xl mx-auto">
          {/* AI Thank-You Note Generator */}
          <div className="card p-8 flex flex-col">
            <h3 className="text-2xl font-bold text-gray-900 mb-4 font-poppins flex items-center">
              <span className="text-3xl mr-3">💌</span>
              <span>{t("aiTools.thankYouGenerator.title")}</span>
            </h3>
            <p className="text-gray-600 mb-4 flex-grow">
              {t("aiTools.thankYouGenerator.description")}
            </p>

            <div className="space-y-4 mb-4">
              <input
                type="text"
                placeholder={t("aiTools.thankYouGenerator.placeholder1")}
                value={thankYouForm.recipient}
                onChange={(e) =>
                  setThankYouForm((prev) => ({
                    ...prev,
                    recipient: e.target.value,
                  }))
                }
                className="w-full bg-gray-100 rounded-lg p-3 text-gray-800 placeholder-gray-400 border border-gray-200 focus:border-violet-500 focus:ring-violet-500 focus:outline-none"
                disabled={thankYouLoading}
              />
              <input
                type="text"
                placeholder={t("aiTools.thankYouGenerator.placeholder2")}
                value={thankYouForm.reason}
                onChange={(e) =>
                  setThankYouForm((prev) => ({
                    ...prev,
                    reason: e.target.value,
                  }))
                }
                className="w-full bg-gray-100 rounded-lg p-3 text-gray-800 placeholder-gray-400 border border-gray-200 focus:border-violet-500 focus:ring-violet-500 focus:outline-none"
                disabled={thankYouLoading}
              />
            </div>

            {thankYouError && (
              <div className="mb-4 p-3 bg-red-50 border border-red-200 rounded-lg">
                <p className="text-red-600 text-sm">{thankYouError}</p>
              </div>
            )}

            {thankYouResult && (
              <div className="mb-4 p-4 bg-green-50 border border-green-200 rounded-lg">
                <p className="text-gray-800 whitespace-pre-wrap">
                  {thankYouResult}
                </p>
                <button
                  onClick={() => copyToClipboard(thankYouResult)}
                  className="mt-2 text-sm text-violet-600 hover:text-violet-700 flex items-center"
                >
                  <svg
                    className="w-4 h-4 mr-1"
                    fill="none"
                    stroke="currentColor"
                    viewBox="0 0 24 24"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth="2"
                      d="M8 16H6a2 2 0 01-2-2V6a2 2 0 012-2h8a2 2 0 012 2v2m-6 12h8a2 2 0 002-2v-8a2 2 0 00-2-2h-8a2 2 0 00-2 2v8a2 2 0 002 2z"
                    />
                  </svg>
                  {t("aiTools.copyToClipboard")}
                </button>
              </div>
            )}

            <button
              onClick={handleGenerateThankYou}
              disabled={thankYouLoading}
              className="secondary-btn font-bold py-3 px-6 rounded-lg w-full flex items-center justify-center disabled:opacity-50 disabled:cursor-not-allowed"
            >
              {thankYouLoading ? (
                <>
                  <svg
                    className="animate-spin -ml-1 mr-3 h-5 w-5"
                    fill="none"
                    viewBox="0 0 24 24"
                  >
                    <circle
                      className="opacity-25"
                      cx="12"
                      cy="12"
                      r="10"
                      stroke="currentColor"
                      strokeWidth="4"
                    ></circle>
                    <path
                      className="opacity-75"
                      fill="currentColor"
                      d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
                    ></path>
                  </svg>
                  {t("aiTools.generating")}
                </>
              ) : (
                t("aiTools.thankYouGenerator.generateBtn")
              )}
            </button>
          </div>

          {/* AI Meme Workshop */}
          <div className="card p-8 flex flex-col">
            <h3 className="text-2xl font-bold text-gray-900 mb-4 font-poppins flex items-center">
              <span className="text-3xl mr-3">🎨</span>
              <span>{t("aiTools.memeWorkshop.title")}</span>
            </h3>
            <p className="text-gray-600 mb-4 flex-grow">
              {t("aiTools.memeWorkshop.description")}
            </p>

            <div className="space-y-4 mb-4">
              <input
                type="text"
                placeholder={t("aiTools.memeWorkshop.placeholder1")}
                value={memeForm.topic}
                onChange={(e) =>
                  setMemeForm((prev) => ({ ...prev, topic: e.target.value }))
                }
                className="w-full bg-gray-100 rounded-lg p-3 text-gray-800 placeholder-gray-400 border border-gray-200 focus:border-violet-500 focus:ring-violet-500 focus:outline-none"
                disabled={memeLoading}
              />
              <select
                value={memeForm.style}
                onChange={(e) =>
                  setMemeForm((prev) => ({ ...prev, style: e.target.value }))
                }
                className="w-full bg-gray-100 rounded-lg p-3 text-gray-800 border border-gray-200 focus:border-violet-500 focus:ring-violet-500 focus:outline-none"
                disabled={memeLoading}
              >
                <option value="funny">
                  {t("aiTools.memeWorkshop.styles.funny")}
                </option>
                <option value="wholesome">
                  {t("aiTools.memeWorkshop.styles.wholesome")}
                </option>
                <option value="sarcastic">
                  {t("aiTools.memeWorkshop.styles.sarcastic")}
                </option>
                <option value="inspirational">
                  {t("aiTools.memeWorkshop.styles.inspirational")}
                </option>
              </select>
            </div>

            {memeError && (
              <div className="mb-4 p-3 bg-red-50 border border-red-200 rounded-lg">
                <p className="text-red-600 text-sm">{memeError}</p>
              </div>
            )}

            {memeResult && (
              <div className="mb-4 p-4 bg-blue-50 border border-blue-200 rounded-lg">
                <h4 className="font-bold text-gray-900 mb-2">
                  {memeResult.title}
                </h4>
                <p className="text-gray-800 text-sm whitespace-pre-wrap">
                  {memeResult.description}
                </p>
                <button
                  onClick={() =>
                    copyToClipboard(
                      `${memeResult.title}\n\n${memeResult.description}`
                    )
                  }
                  className="mt-2 text-sm text-violet-600 hover:text-violet-700 flex items-center"
                >
                  <svg
                    className="w-4 h-4 mr-1"
                    fill="none"
                    stroke="currentColor"
                    viewBox="0 0 24 24"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth="2"
                      d="M8 16H6a2 2 0 01-2-2V6a2 2 0 012-2h8a2 2 0 012 2v2m-6 12h8a2 2 0 002-2v-8a2 2 0 00-2-2h-8a2 2 0 00-2 2v8a2 2 0 002 2z"
                    />
                  </svg>
                  {t("aiTools.copyToClipboard")}
                </button>
              </div>
            )}

            <button
              onClick={handleGenerateMeme}
              disabled={memeLoading}
              className="secondary-btn font-bold py-3 px-6 rounded-lg w-full flex items-center justify-center disabled:opacity-50 disabled:cursor-not-allowed"
            >
              {memeLoading ? (
                <>
                  <svg
                    className="animate-spin -ml-1 mr-3 h-5 w-5"
                    fill="none"
                    viewBox="0 0 24 24"
                  >
                    <circle
                      className="opacity-25"
                      cx="12"
                      cy="12"
                      r="10"
                      stroke="currentColor"
                      strokeWidth="4"
                    ></circle>
                    <path
                      className="opacity-75"
                      fill="currentColor"
                      d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
                    ></path>
                  </svg>
                  {t("aiTools.generating")}
                </>
              ) : (
                t("aiTools.memeWorkshop.generateBtn")
              )}
            </button>
          </div>
        </div>
      </div>
    </section>
  );
};

export default AIToolsFixed;
