import React from "react";
import { useTranslation } from "react-i18next";

const ComingSoonModal = ({ isOpen, onClose }) => {
  const { t } = useTranslation();

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center modal-backdrop">
      {/* Background overlay */}
      <div className="absolute inset-0" onClick={onClose}></div>

      {/* Modal content - Mobile optimized */}
      <div className="relative card modal-panel max-w-md w-full mx-4 sm:mx-6 p-6 sm:p-8 font-poppins">
        {/* Close button */}
        <button
          onClick={onClose}
          className="absolute top-3 right-3 sm:top-4 sm:right-4 text-gray-400 hover:text-gray-600 transition-colors p-2 rounded-lg hover:bg-gray-100 touch-manipulation"
        >
          <svg
            className="w-4 h-4 sm:w-5 sm:h-5"
            fill="none"
            stroke="currentColor"
            viewBox="0 0 24 24"
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth={2}
              d="M6 18L18 6M6 6l12 12"
            />
          </svg>
        </button>

        {/* Icon - mobile responsive */}
        <div className="flex justify-center mb-4 sm:mb-6">
          <div
            className="w-16 h-16 sm:w-20 sm:h-20 rounded-full flex items-center justify-center relative"
            style={{
              background:
                "linear-gradient(135deg, #6d28d9 0%, #8b5cf6 50%, #f59e0b 100%)",
              animation: "pulse-glow 2s ease-in-out infinite",
            }}
          >
            <svg
              className="w-8 h-8 sm:w-10 sm:h-10 text-white"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"
              />
            </svg>
            {/* Outer circle decoration */}
            <div
              className="absolute inset-0 rounded-full border-2 border-violet-200 opacity-30"
              style={{ animation: "ripple 2s ease-out infinite" }}
            ></div>
          </div>
        </div>

        {/* Title - mobile font optimization */}
        <h2 className="text-2xl sm:text-3xl font-bold text-gray-800 text-center mb-3 sm:mb-4 hero-title">
          <span className="bg-gradient-to-r from-violet-700 to-amber-500 bg-clip-text text-transparent">
            {t("comingSoon.title")}
          </span>
        </h2>

        {/* Description - mobile font optimization */}
        <p className="text-gray-600 text-center mb-6 sm:mb-8 leading-relaxed text-base sm:text-lg hero-subtitle">
          {t("comingSoon.description")}
        </p>

        {/* Contact information */}
        <div className="flex items-center justify-center mb-6 sm:mb-8 px-4 py-3 bg-gradient-to-r from-violet-50 to-amber-50 rounded-xl border border-violet-100">
          <svg
            className="w-4 h-4 sm:w-5 sm:h-5 text-violet-600 mr-2 flex-shrink-0"
            fill="none"
            stroke="currentColor"
            viewBox="0 0 24 24"
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth={2}
              d="M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"
            />
          </svg>
          <div className="min-w-0 flex-1">
            <p className="text-xs sm:text-sm text-gray-600 mb-1">
              {t("comingSoon.contactUs")}
            </p>
            <a
              href="mailto:<EMAIL>"
              className="text-sm sm:text-base font-semibold text-violet-700 hover:text-violet-800 transition-colors break-all"
            >
              <EMAIL>
            </a>
          </div>
        </div>

        {/* Button group - mobile optimized */}
        <div className="flex flex-col space-y-3 sm:space-y-4 hero-buttons">
          <button
            onClick={onClose}
            className="primary-btn font-bold py-3 sm:py-4 px-6 sm:px-8 rounded-xl text-base sm:text-lg touch-manipulation"
          >
            {t("comingSoon.understand")}
          </button>

          <p className="text-center text-xs sm:text-sm text-gray-500 font-medium px-2">
            {t("comingSoon.stayTuned")}
          </p>
        </div>

        {/* Decorative gradient background */}
        <div
          className="absolute inset-0 -z-10 rounded-3xl opacity-5"
          style={{
            background: "linear-gradient(135deg, #6d28d9 0%, #f59e0b 100%)",
          }}
        ></div>
      </div>
    </div>
  );
};

export default ComingSoonModal;
