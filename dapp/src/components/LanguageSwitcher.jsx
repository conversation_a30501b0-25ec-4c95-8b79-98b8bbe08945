import React, { useState, useRef, useEffect } from "react";
import { useTranslation } from "react-i18next";

const LanguageSwitcher = ({ className = "" }) => {
  const { i18n, t } = useTranslation();
  const [isOpen, setIsOpen] = useState(false);
  const [dropupMode, setDropupMode] = useState(false);
  const buttonRef = useRef(null);

  const languages = [
    { code: "en", flag: "🇺🇸", name: t("languages.en") },
    { code: "zh", flag: "🇨🇳", name: t("languages.zh") },
    { code: "ja", flag: "🇯🇵", name: t("languages.ja") },
    { code: "fr", flag: "🇫🇷", name: t("languages.fr") },
    { code: "de", flag: "🇩🇪", name: t("languages.de") },
    { code: "es", flag: "🇪🇸", name: t("languages.es") },
    { code: "ko", flag: "🇰🇷", name: t("languages.ko") },
  ];

  const currentLanguage =
    languages.find((lang) => lang.code === i18n.language) || languages[0];

  const changeLanguage = (langCode) => {
    i18n.changeLanguage(langCode);
    setIsOpen(false);
  };

  useEffect(() => {
    if (!isOpen) return;

    const handleResize = () => {
      checkDropdownPosition();
    };

    const handleScroll = () => {
      checkDropdownPosition();
    };

    window.addEventListener("resize", handleResize);
    window.addEventListener("scroll", handleScroll, { passive: true });

    return () => {
      window.removeEventListener("resize", handleResize);
      window.removeEventListener("scroll", handleScroll);
    };
  }, [isOpen]);

  const checkDropdownPosition = () => {
    if (!buttonRef.current) return;

    const buttonRect = buttonRef.current.getBoundingClientRect();
    const viewportHeight = window.innerHeight;
    const scrollY = window.scrollY || window.pageYOffset;

    const dropdownHeight = 7 * 45 + 30 + 30 + 16;

    const spaceBelow = viewportHeight - buttonRect.bottom;
    const spaceAbove = buttonRect.top;

    let shouldDropup = false;

    if (spaceBelow >= dropdownHeight) {
      shouldDropup = false;
    } else if (spaceAbove >= dropdownHeight) {
      shouldDropup = true;
    } else {
      shouldDropup = spaceAbove > spaceBelow;
    }

    console.log("Dropdown positioning:", {
      spaceBelow,
      spaceAbove,
      dropdownHeight,
      shouldDropup,
      buttonRect,
    });

    setDropupMode(shouldDropup);
  };

  // \u952e\u76d8\u5bfc\u822a\u652f\u6301
  const handleKeyDown = (event) => {
    if (event.key === "Escape") {
      setIsOpen(false);
    }
  };

  return (
    <div className={`relative ${className}`} onKeyDown={handleKeyDown}>
      <button
        ref={buttonRef}
        onClick={(e) => {
          e.stopPropagation();
          if (!isOpen) {
            setTimeout(() => checkDropdownPosition(), 0);
          }
          setIsOpen(!isOpen);
        }}
        className="bg-gradient-to-r from-gray-50 to-gray-100 hover:from-gray-100 hover:to-gray-200 px-3 py-2.5 rounded-lg flex items-center gap-2 text-gray-700 transition-all duration-200 w-full justify-between border border-gray-200 hover:border-gray-300 shadow-sm hover:shadow-md focus:outline-none focus:ring-2 focus:ring-violet-500 focus:ring-opacity-50"
        aria-label={`Current language: ${currentLanguage.name}, click to select other languages`}
        aria-expanded={isOpen}
        aria-haspopup="listbox"
      >
        <div className="flex items-center gap-2.5">
          <span className="text-lg">{currentLanguage.flag}</span>
          <span className="text-sm font-medium">{currentLanguage.name}</span>
        </div>
        <div className="flex items-center gap-1">
          <div className="w-1.5 h-1.5 bg-green-500 rounded-full animate-pulse"></div>
          <svg
            className={`w-4 h-4 transition-transform duration-300 ${
              isOpen ? "rotate-180" : ""
            }`}
            fill="none"
            stroke="currentColor"
            viewBox="0 0 24 24"
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth="2"
              d="M19 9l-7 7-7-7"
            />
          </svg>
        </div>
      </button>

      {isOpen && (
        <>
          {/* Background overlay */}
          <div
            className="fixed inset-0 z-[65]"
            onClick={() => setIsOpen(false)}
          />

          {/* Dropdown menu */}
          <div
            className={`absolute right-0 bg-white rounded-xl shadow-xl py-2 w-52 border border-gray-200 z-[70] overflow-hidden language-dropdown ${
              dropupMode ? "bottom-full mb-2" : "top-full mt-2"
            }`}
            role="listbox"
            aria-label="Select language"
          >
            <div className="px-3 py-1 border-b border-gray-100 bg-gray-50">
              <div className="text-xs font-semibold text-gray-500 uppercase tracking-wider">
                Select Language
              </div>
            </div>
            <div
              className={`overflow-y-auto ${
                dropupMode ? "max-h-60" : "max-h-64"
              }`}
            >
              {languages.map((lang, index) => (
                <button
                  key={lang.code}
                  onClick={(e) => {
                    e.stopPropagation();
                    changeLanguage(lang.code);
                  }}
                  className={`w-full px-4 py-2.5 text-left hover:bg-gray-50 flex items-center gap-3 transition-all duration-200 relative language-item focus:outline-none focus:bg-gray-50 ${
                    i18n.language === lang.code
                      ? "bg-gradient-to-r from-violet-50 to-purple-50 text-violet-700 border-r-2 border-violet-500"
                      : "text-gray-700 hover:text-gray-900"
                  }`}
                  role="option"
                  aria-selected={i18n.language === lang.code}
                  aria-label={`Switch to ${lang.name}`}
                >
                  <span className="text-lg">{lang.flag}</span>
                  <div className="flex-1">
                    <span className="font-medium">{lang.name}</span>
                    {i18n.language === lang.code && (
                      <div className="text-xs text-violet-600 font-medium mt-0.5">
                        Current
                      </div>
                    )}
                  </div>
                  {i18n.language === lang.code && (
                    <div className="w-2 h-2 bg-violet-500 rounded-full"></div>
                  )}
                </button>
              ))}
            </div>
            <div className="px-3 py-2 border-t border-gray-100 bg-gray-50">
              <div className="text-xs text-gray-500 text-center">
                🌐 Global Support
              </div>
            </div>
          </div>
        </>
      )}
    </div>
  );
};

export default LanguageSwitcher;
