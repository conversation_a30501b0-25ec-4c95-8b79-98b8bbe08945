import React, { useState, useEffect } from 'react';
import { useWallet } from '../hooks/useWallet';
import { usePresale } from '../hooks/usePresale';

const TestReferralSystem = () => {
  const wallet = useWallet();
  const presale = usePresale(wallet.contracts, wallet.userAddress);
  const [testResults, setTestResults] = useState([]);
  const [loading, setLoading] = useState(false);

  const addTestResult = (message, type = 'info') => {
    setTestResults(prev => [...prev, { message, type, timestamp: Date.now() }]);
  };

  const testReferralFunctions = async () => {
    if (!wallet.isConnected) {
      addTestResult('Please connect wallet first', 'error');
      return;
    }

    setLoading(true);
    setTestResults([]);

    try {
      // Test 1: Generate referral link
      const referralLink = presale.generateReferralLink();
      addTestResult(`Generated referral link: ${referralLink}`, 'success');

      // Test 2: Get user info
      const userInfo = presale.userInfo;
      addTestResult(`User purchased: ${userInfo.purchased}, Referrer: ${userInfo.referrer}, Referee count: ${userInfo.refereeCount}`, 'info');

      // Test 3: Get referral stats
      const stats = await presale.getReferralStats();
      if (stats) {
        addTestResult(`Referral stats: ${JSON.stringify(stats)}`, 'info');
      } else {
        addTestResult('No referral stats available', 'warning');
      }

      // Test 4: Get referees list
      const referees = await presale.getReferees(0, 10);
      addTestResult(`Referees list (first 10): ${referees.join(', ') || 'None'}`, 'info');

      // Test 5: Test referral info loading
      if (presale.referralInfo) {
        addTestResult(`Referral info: ${JSON.stringify(presale.referralInfo)}`, 'info');
      } else {
        addTestResult('No referral info available', 'warning');
      }

      // Test 6: Test purchase capability
      const canPurchase = await presale.canPurchase(1);
      addTestResult(`Can purchase 1 NFT: ${canPurchase.can}`, canPurchase.can ? 'success' : 'error');

      // Test 7: Test URL referrer extraction
      const urlReferrer = presale.getReferrerFromURL();
      addTestResult(`URL referrer: ${urlReferrer}`, 'info');

    } catch (error) {
      addTestResult(`Test failed: ${error.message}`, 'error');
    } finally {
      setLoading(false);
    }
  };

  const testPurchaseFlow = async () => {
    if (!wallet.isConnected) {
      addTestResult('Please connect wallet first', 'error');
      return;
    }

    setLoading(true);
    
    try {
      // Test purchase with quantity
      const quantity = 1;
      const referrer = '******************************************';
      
      addTestResult(`Testing purchase flow for ${quantity} NFT(s)...`, 'info');
      
      // Check if can purchase
      const canPurchase = await presale.canPurchase(quantity);
      if (!canPurchase.can) {
        addTestResult(`Cannot purchase: ${canPurchase.message}`, 'error');
        return;
      }
      
      // Check balances
      addTestResult(`USDT balance: ${presale.balances.usdt}`, 'info');
      addTestResult(`USDT allowance: ${presale.balances.usdtAllowance}`, 'info');
      
      // Note: We don't actually purchase, just test the flow
      addTestResult('Purchase flow test completed (no actual purchase)', 'success');
      
    } catch (error) {
      addTestResult(`Purchase flow test failed: ${error.message}`, 'error');
    } finally {
      setLoading(false);
    }
  };

  const clearResults = () => {
    setTestResults([]);
  };

  return (
    <div className="p-6 bg-white rounded-lg shadow-lg">
      <h2 className="text-2xl font-bold mb-4">Referral System Test</h2>
      
      <div className="mb-4">
        <div className="flex gap-2 mb-2">
          <button
            onClick={testReferralFunctions}
            disabled={loading}
            className="px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600 disabled:opacity-50"
          >
            {loading ? 'Testing...' : 'Test Referral Functions'}
          </button>
          <button
            onClick={testPurchaseFlow}
            disabled={loading}
            className="px-4 py-2 bg-green-500 text-white rounded hover:bg-green-600 disabled:opacity-50"
          >
            {loading ? 'Testing...' : 'Test Purchase Flow'}
          </button>
          <button
            onClick={clearResults}
            className="px-4 py-2 bg-gray-500 text-white rounded hover:bg-gray-600"
          >
            Clear Results
          </button>
        </div>
        
        {wallet.isConnected ? (
          <div className="text-green-600 text-sm">
            Connected: {wallet.formatAddress(wallet.userAddress)}
          </div>
        ) : (
          <div className="text-red-600 text-sm">
            Wallet not connected
          </div>
        )}
      </div>

      <div className="border rounded-lg p-4 bg-gray-50 max-h-96 overflow-y-auto">
        <h3 className="font-bold mb-2">Test Results:</h3>
        {testResults.length === 0 ? (
          <p className="text-gray-500">No test results yet. Run a test to see results.</p>
        ) : (
          <div className="space-y-2">
            {testResults.map((result, index) => (
              <div
                key={index}
                className={`p-2 rounded text-sm ${
                  result.type === 'success' ? 'bg-green-100 text-green-800' :
                  result.type === 'error' ? 'bg-red-100 text-red-800' :
                  result.type === 'warning' ? 'bg-yellow-100 text-yellow-800' :
                  'bg-blue-100 text-blue-800'
                }`}
              >
                <span className="font-mono text-xs text-gray-500">
                  {new Date(result.timestamp).toLocaleTimeString()}
                </span>
                <br />
                {result.message}
              </div>
            ))}
          </div>
        )}
      </div>
    </div>
  );
};

export default TestReferralSystem;