import React, { useState, useEffect, useCallback } from "react";
import { useTranslation } from "react-i18next";
import { ethers } from "ethers";

const NFTModal = ({
  isOpen,
  onClose,
  nftData,
  releaseNFTTokens,
  contracts,
  userAddress,
}) => {
  const { t } = useTranslation();
  const [selectedNFT, setSelectedNFT] = useState(null);
  const [nftDetails, setNftDetails] = useState(null);
  const [loading, setLoading] = useState(false);
  const [releaseSchedule, setReleaseSchedule] = useState({
    percentages: [],
    startTime: 0,
    interval: 0,
  });

  // Format timestamp
  const formatTimestamp = useCallback((timestamp) => {
    return new Date(Number(timestamp) * 1000).toLocaleString('zh-CN', {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit'
    });
  }, []);

  // Format amount
  const formatAmount = useCallback((amount) => {
    return parseFloat(ethers.formatEther(amount)).toFixed(6);
  }, []);

  // Get period label
  const getPeriodLabel = useCallback(
    (period) => {
      const periodNumber = Number(period) + 1;
      return `${t("nft.month")} ${periodNumber}`;
    },
    [t]
  );

  // Get release schedule
  const loadReleaseSchedule = useCallback(async () => {
    if (!contracts.minerNFT) return;

    try {
      const [percentages, startTime, interval] =
        await contracts.minerNFT.getReleaseSchedule();

      setReleaseSchedule({
        percentages: percentages.map((p) => Number(p)),
        startTime: Number(startTime),
        interval: Number(interval),
      });
    } catch (error) {
      console.error("Failed to load release schedule:", error);
    }
  }, [contracts]);

  // Get NFT detailed information
  const loadNFTDetails = useCallback(
    async (tokenId) => {
      if (!contracts.minerNFT || !tokenId) return;

      setLoading(true);
      try {
        const [
          totalTokens,
          releasedTokens,
          releasableNow,
          currentPeriod,
          nextReleaseTime,
        ] = await contracts.minerNFT.getNFTReleaseInfo(tokenId);

        const isMinering = await contracts.minerNFT.isMinering(tokenId);

        setNftDetails({
          tokenId,
          totalTokens: formatAmount(totalTokens),
          releasedTokens: formatAmount(releasedTokens),
          releasableNow: formatAmount(releasableNow),
          currentPeriod: Number(currentPeriod),
          nextReleaseTime: Number(nextReleaseTime),
          releaseProgress: (Number(releasedTokens) / Number(totalTokens)) * 100,
          isMinering: isMinering,
        });
      } catch (error) {
        console.error("Failed to load NFT details:", error);
      } finally {
        setLoading(false);
      }
    },
    [contracts, formatAmount]
  );

  // Select NFT
  const handleSelectNFT = useCallback(
    (nft) => {
      setSelectedNFT(nft);
      loadNFTDetails(nft.tokenId);
    },
    [loadNFTDetails]
  );

  // Return to NFT list
  const handleBackToList = useCallback(() => {
    setSelectedNFT(null);
    setNftDetails(null);
  }, []);

  // Initialize
  useEffect(() => {
    if (isOpen && contracts.minerNFT) {
      loadReleaseSchedule();
    }
  }, [isOpen, contracts, loadReleaseSchedule]);

  // Reset state when modal closes
  useEffect(() => {
    if (!isOpen) {
      handleBackToList();
    }
  }, [isOpen, handleBackToList]);

  // Control body scroll
  useEffect(() => {
    if (isOpen) {
      document.body.classList.add("modal-open");
    } else {
      document.body.classList.remove("modal-open");
    }

    // Cleanup function
    return () => {
      document.body.classList.remove("modal-open");
    };
  }, [isOpen]);

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 z-50 overflow-hidden">
      <div className="flex items-end justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:items-center sm:p-0">
        {/* Background overlay */}
        <div
          className="fixed inset-0 modal-backdrop transition-opacity"
          onClick={onClose}
        />

        {/* Modal panel */}
        <div className="inline-block align-bottom bg-white rounded-t-2xl sm:rounded-lg text-left overflow-hidden shadow-xl transform transition-all w-full h-[90vh] sm:my-8 sm:align-middle sm:max-w-6xl modal-panel">
          <div className="bg-white h-full flex flex-col">
            {/* Modal header -  */}
            <div className="flex items-center justify-between p-4 sm:p-6 border-b border-gray-200 flex-shrink-0">
              <div className="flex items-center">
                {selectedNFT && (
                  <button
                    onClick={handleBackToList}
                    className="mr-3 text-gray-400 hover:text-gray-600"
                  >
                    <svg
                      className="w-6 h-6"
                      fill="none"
                      stroke="currentColor"
                      viewBox="0 0 24 24"
                    >
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth="2"
                        d="M15 19l-7-7 7-7"
                      />
                    </svg>
                  </button>
                )}
                <img
                  src="/icons/MinerNFT-Rabbit.svg"
                  alt="Miner NFT"
                  className="w-6 h-6 sm:w-8 sm:h-8 mr-3"
                />
                <h2 className="text-lg sm:text-2xl font-bold text-gray-900">
                  {selectedNFT
                    ? `Miner #${selectedNFT.tokenId}`
                    : t("nft.title")}
                </h2>
                {!selectedNFT && (
                  <span className="ml-2 sm:ml-3 bg-purple-100 text-purple-800 px-2 sm:px-3 py-1 rounded-full text-xs sm:text-sm font-semibold">
                    {nftData.length}
                  </span>
                )}
              </div>
              <button
                onClick={onClose}
                className="text-gray-400 hover:text-gray-600 transition-colors"
              >
                <svg
                  className="w-6 h-6"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth="2"
                    d="M6 18L18 6M6 6l12 12"
                  />
                </svg>
              </button>
            </div>

            {/* Modal content -  */}
            <div className="flex-1 overflow-y-auto p-4 sm:p-6 min-h-0">
              {!selectedNFT ? (
                // NFT list view
                <div className="h-full flex flex-col">
                  <div className="flex items-center justify-between mb-4 sm:mb-6 flex-shrink-0">
                    <p className="text-gray-600">
                      {nftData.length > 0 ? t("nft.nftList") : t("nft.noNFTs")}
                    </p>
                  </div>

                  <div className="flex-1 overflow-y-auto min-h-0 pb-4">
                    {nftData.length === 0 ? (
                      <div className="text-center text-gray-500 py-8 sm:py-12">
                        <div className="w-20 h-20 sm:w-24 sm:h-24 bg-gradient-to-br from-purple-100 to-purple-200 rounded-3xl flex items-center justify-center mx-auto mb-4 opacity-50">
                          <img
                            src="/icons/MinerNFT-Rabbit.svg"
                            alt="Miner NFT"
                            className="w-12 h-12 sm:w-16 sm:h-16 opacity-60"
                          />
                        </div>
                        <p className="text-base sm:text-lg font-semibold">
                          {t("nft.noNFTs")}
                        </p>
                        <p className="text-sm text-gray-400 mt-2 px-4">
                          {t("nft.mineToEarnNFT")}
                        </p>
                      </div>
                    ) : (
                      <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4 sm:gap-6">
                        {nftData.map((nft) => (
                          <div
                            key={nft.tokenId}
                            className="nft-card card p-4 sm:p-6 hover:shadow-lg transition-shadow"
                          >
                            <div className="text-center mb-4">
                              <div className="w-14 h-14 sm:w-16 sm:h-16 bg-gradient-to-br from-purple-100 to-purple-200 rounded-2xl flex items-center justify-center mx-auto mb-3 sm:mb-4 relative overflow-hidden">
                                <img
                                  src="/icons/MinerNFT-Rabbit.svg"
                                  alt="Miner NFT"
                                  className="w-10 h-10 sm:w-12 sm:h-12"
                                />
                                <span className="absolute bottom-0 right-0 bg-purple-600 text-white text-xs font-bold px-1 py-0.5 rounded-tl-lg">
                                  #{nft.tokenId}
                                </span>
                                {/* Mining status indicator */}
                                <div className="absolute -top-1 -left-1">
                                  {nft.isMinering ? (
                                    <div className="w-5 h-5 bg-gradient-to-r from-blue-500 to-purple-600 rounded-full flex items-center justify-center">
                                      <svg
                                        className="w-3 h-3 text-white animate-spin"
                                        fill="none"
                                        stroke="currentColor"
                                        viewBox="0 0 24 24"
                                      >
                                        <path
                                          strokeLinecap="round"
                                          strokeLinejoin="round"
                                          strokeWidth="2"
                                          d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z"
                                        />
                                        <path
                                          strokeLinecap="round"
                                          strokeLinejoin="round"
                                          strokeWidth="2"
                                          d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"
                                        />
                                      </svg>
                                    </div>
                                  ) : (
                                    <div className="w-5 h-5 bg-gray-200 rounded-full flex items-center justify-center">
                                      <svg
                                        className="w-3 h-3 text-gray-400"
                                        fill="none"
                                        stroke="currentColor"
                                        viewBox="0 0 24 24"
                                      >
                                        <path
                                          strokeLinecap="round"
                                          strokeLinejoin="round"
                                          strokeWidth="2"
                                          d="M20.354 15.354A9 9 0 018.646 3.646 9.003 9.003 0 0012 21a9.003 9.003 0 008.354-5.646z"
                                        />
                                      </svg>
                                    </div>
                                  )}
                                </div>
                              </div>
                              <h4 className="text-base sm:text-lg font-bold text-gray-900">
                                Miner #{nft.tokenId}
                              </h4>
                              {/* Mining status text */}
                              <div className="flex items-center justify-center mt-1">
                                <span
                                  className={`text-xs font-medium px-2 py-1 rounded-full ${
                                    nft.isMinering
                                      ? "bg-blue-100 text-blue-700"
                                      : "bg-gray-100 text-gray-600"
                                  }`}
                                >
                                  {nft.isMinering
                                    ? t("nft.mining")
                                    : t("nft.idle")}
                                </span>
                              </div>
                            </div>

                            <div className="bg-gray-50 rounded-lg p-3 sm:p-4 mb-4">
                              <div className="text-sm text-gray-600 mb-1">
                                {t("nft.releasableTokenA")}
                              </div>
                              <div className="text-base sm:text-lg font-bold text-violet-700">
                                {parseFloat(nft.releasableAmount).toFixed(4)}{" "}
                                GRAT
                              </div>
                            </div>

                            <div className="space-y-2">
                              <button
                                onClick={() => handleSelectNFT(nft)}
                                className="primary-btn w-full py-2 rounded-lg text-sm font-semibold"
                              >
                                {t("nft.viewDetails")}
                              </button>
                              <button
                                onClick={() => releaseNFTTokens(nft.tokenId)}
                                disabled={parseFloat(nft.releasableAmount) <= 0}
                                className="secondary-btn w-full py-2 rounded-lg text-sm font-semibold disabled:opacity-50"
                              >
                                {t("nft.releaseTokenA")}
                              </button>
                            </div>
                          </div>
                        ))}
                      </div>
                    )}
                  </div>
                </div>
              ) : (
                // NFT details view
                <div className="h-full overflow-y-auto min-h-0 pb-4">
                  <div className="space-y-4 sm:space-y-6">
                    {loading ? (
                      <div className="text-center py-8">
                        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-violet-600 mx-auto"></div>
                        <p className="text-gray-500 mt-2">{t("nft.loading")}</p>
                      </div>
                    ) : (
                      nftDetails && (
                        <>
                          {/* NFT */}
                          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4 sm:gap-6">
                            <div className="card p-4 sm:p-6">
                              <h3 className="text-base sm:text-lg font-bold text-gray-900 mb-3 sm:mb-4">
                                {t("nft.totalTokens")}
                              </h3>
                              <div className="text-2xl sm:text-3xl font-bold text-violet-600">
                                {nftDetails.totalTokens} GRAT
                              </div>
                            </div>

                            <div className="card p-4 sm:p-6">
                              <h3 className="text-base sm:text-lg font-bold text-gray-900 mb-3 sm:mb-4">
                                {t("nft.releasedTokens")}
                              </h3>
                              <div className="text-2xl sm:text-3xl font-bold text-green-600">
                                {nftDetails.releasedTokens} GRAT
                              </div>
                              <div className="text-sm text-gray-500 mt-1">
                                {nftDetails.releaseProgress.toFixed(1)}%{" "}
                                {t("nft.released")}
                              </div>
                            </div>

                            <div className="card p-4 sm:p-6 sm:col-span-2 lg:col-span-1">
                              <h3 className="text-base sm:text-lg font-bold text-gray-900 mb-3 sm:mb-4">
                                {t("nft.releasableNow")}
                              </h3>
                              <div className="text-2xl sm:text-3xl font-bold text-orange-600">
                                {nftDetails.releasableNow} GRAT
                              </div>
                              <button
                                onClick={() =>
                                  releaseNFTTokens(nftDetails.tokenId)
                                }
                                disabled={
                                  parseFloat(nftDetails.releasableNow) <= 0
                                }
                                className="mt-3 secondary-btn w-full py-2 rounded-lg text-sm font-semibold disabled:opacity-50"
                              >
                                {t("nft.releaseNow")}
                              </button>
                            </div>

                            <div className="card p-4 sm:p-6 sm:col-span-2 lg:col-span-1">
                              <h3 className="text-base sm:text-lg font-bold text-gray-900 mb-3 sm:mb-4">
                                {t("nft.miningStatus")}
                              </h3>
                              <div className="flex items-center justify-center">
                                {nftDetails.isMinering ? (
                                  <div className="flex flex-col items-center">
                                    <div className="w-12 h-12 sm:w-16 sm:h-16 bg-gradient-to-r from-blue-500 to-purple-600 rounded-full flex items-center justify-center mb-2 relative">
                                      <svg
                                        className="w-6 h-6 sm:w-8 sm:h-8 text-white animate-spin"
                                        fill="none"
                                        stroke="currentColor"
                                        viewBox="0 0 24 24"
                                      >
                                        <path
                                          strokeLinecap="round"
                                          strokeLinejoin="round"
                                          strokeWidth="2"
                                          d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z"
                                        />
                                        <path
                                          strokeLinecap="round"
                                          strokeLinejoin="round"
                                          strokeWidth="2"
                                          d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"
                                        />
                                      </svg>
                                    </div>
                                    <span className="text-sm font-bold text-blue-600">
                                      {t("nft.mining")}
                                    </span>
                                    <span className="text-xs text-gray-500 mt-1">
                                      {t("nft.nonTransferable")}
                                    </span>
                                  </div>
                                ) : (
                                  <div className="flex flex-col items-center">
                                    <div className="w-12 h-12 sm:w-16 sm:h-16 bg-gray-100 rounded-full flex items-center justify-center mb-2">
                                      <svg
                                        className="w-6 h-6 sm:w-8 sm:h-8 text-gray-400"
                                        fill="none"
                                        stroke="currentColor"
                                        viewBox="0 0 24 24"
                                      >
                                        <path
                                          strokeLinecap="round"
                                          strokeLinejoin="round"
                                          strokeWidth="2"
                                          d="M20.354 15.354A9 9 0 018.646 3.646 9.003 9.003 0 0012 21a9.003 9.003 0 008.354-5.646z"
                                        />
                                      </svg>
                                    </div>
                                    <span className="text-sm font-bold text-gray-600">
                                      {t("nft.idle")}
                                    </span>
                                    <span className="text-xs text-gray-500 mt-1">
                                      {t("nft.transferable")}
                                    </span>
                                  </div>
                                )}
                              </div>
                            </div>
                          </div>

                          {/*  */}
                          <div className="card p-4 sm:p-6">
                            <h3 className="text-base sm:text-lg font-bold text-gray-900 mb-3 sm:mb-4">
                              {t("nft.releaseProgress")}
                            </h3>
                            <div className="w-full bg-gray-200 rounded-full h-3 sm:h-4">
                              <div
                                className="bg-gradient-to-r from-violet-600 to-green-600 h-3 sm:h-4 rounded-full transition-all duration-300"
                                style={{
                                  width: `${Math.min(
                                    nftDetails.releaseProgress,
                                    100
                                  )}%`,
                                }}
                              ></div>
                            </div>
                            <div className="flex justify-between text-sm text-gray-600 mt-2">
                              <span>0%</span>
                              <span>
                                {nftDetails.releaseProgress.toFixed(1)}%
                              </span>
                              <span>100%</span>
                            </div>
                          </div>

                          {releaseSchedule.percentages.length > 0 && (
                            <div className="card p-4 sm:p-6">
                              <h3 className="text-base sm:text-lg font-bold text-gray-900 mb-3 sm:mb-4">
                                {t("nft.releaseSchedule")}
                              </h3>

                              <div className="block lg:hidden">
                                <div className="nft-release-schedule-scroll pb-4">
                                  <div
                                    className="flex gap-3 px-2"
                                    style={{
                                      width: `${
                                        releaseSchedule.percentages.length *
                                          85 +
                                        releaseSchedule.percentages.length *
                                          12 +
                                        40
                                      }px`,
                                      minWidth: "100%",
                                    }}
                                  >
                                    {releaseSchedule.percentages.map(
                                      (percent, index) => (
                                        <div
                                          key={index}
                                          className="relative flex-shrink-0 w-20"
                                        >
                                          <div
                                            className={`period-card text-center p-3 rounded-xl text-xs font-medium border-2 transition-all duration-200 ${
                                              index <= nftDetails.currentPeriod
                                                ? "period-completed bg-green-100 text-green-800 border-green-300 shadow-md"
                                                : index ===
                                                  nftDetails.currentPeriod + 1
                                                ? "bg-orange-100 text-orange-800 border-orange-300 shadow-md ring-2 ring-orange-200"
                                                : "bg-gray-50 text-gray-500 border-gray-200 hover:border-gray-300"
                                            }`}
                                          >
                                            <div className="font-bold text-xs mb-1 whitespace-nowrap">
                                              {getPeriodLabel(index)}
                                            </div>
                                            <div className="text-xs font-semibold">
                                              {percent}%
                                            </div>
                                            {index <=
                                              nftDetails.currentPeriod && (
                                              <div className="absolute -top-1 -right-1 w-4 h-4 bg-green-500 rounded-full flex items-center justify-center shadow-sm">
                                                <svg
                                                  className="w-2.5 h-2.5 text-white"
                                                  fill="currentColor"
                                                  viewBox="0 0 20 20"
                                                >
                                                  <path
                                                    fillRule="evenodd"
                                                    d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z"
                                                    clipRule="evenodd"
                                                  />
                                                </svg>
                                              </div>
                                            )}
                                          </div>
                                        </div>
                                      )
                                    )}
                                  </div>
                                </div>

                                {releaseSchedule.percentages.length > 4 && (
                                  <div className="flex justify-center mt-2">
                                    <div className="text-xs text-gray-500 bg-gray-100 px-3 py-1 rounded-full">
                                      <span>
                                        {t("nft.swipeToViewMore") ||
                                          "Swipe to view more"}
                                      </span>
                                    </div>
                                  </div>
                                )}
                              </div>

                              <div className="hidden lg:block">
                                <div className="grid grid-cols-4 xl:grid-cols-6 gap-3 lg:gap-4">
                                  {releaseSchedule.percentages.map(
                                    (percent, index) => (
                                      <div
                                        key={index}
                                        className={`period-card relative text-center p-3 lg:p-4 rounded-xl text-sm lg:text-base border-2 transition-all duration-200 ${
                                          index <= nftDetails.currentPeriod
                                            ? "period-completed bg-green-100 text-green-800 border-green-300 shadow-md"
                                            : index ===
                                              nftDetails.currentPeriod + 1
                                            ? "bg-orange-100 text-orange-800 border-orange-300 shadow-md ring-2 ring-orange-200"
                                            : "bg-gray-50 text-gray-500 border-gray-200 hover:border-gray-300"
                                        }`}
                                      >
                                        <div className="font-bold mb-1">
                                          {getPeriodLabel(index)}
                                        </div>
                                        <div className="text-sm lg:text-base font-semibold">
                                          {percent}%
                                        </div>
                                        {index <= nftDetails.currentPeriod && (
                                          <div className="absolute -top-1 -right-1 w-4 h-4 lg:w-5 lg:h-5 bg-green-500 rounded-full flex items-center justify-center shadow-sm">
                                            <svg
                                              className="w-2.5 h-2.5 lg:w-3 lg:h-3 text-white"
                                              fill="currentColor"
                                              viewBox="0 0 20 20"
                                            >
                                              <path
                                                fillRule="evenodd"
                                                d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z"
                                                clipRule="evenodd"
                                              />
                                            </svg>
                                          </div>
                                        )}
                                      </div>
                                    )
                                  )}
                                </div>
                              </div>

                              <div className="mt-6 p-4 lg:p-6 bg-gradient-to-r from-blue-50 to-purple-50 rounded-xl border border-blue-200">
                                <div className="grid grid-cols-1 lg:grid-cols-2 gap-4 lg:gap-6">
                                  <div className="flex flex-col lg:flex-row lg:items-center lg:space-x-3">
                                    <span className="text-gray-600 font-medium text-sm lg:text-base">
                                      {t("nft.currentPeriod")}:
                                    </span>
                                    <span className="font-bold text-orange-600 text-base lg:text-lg">
                                      {getPeriodLabel(nftDetails.currentPeriod)}
                                    </span>
                                  </div>
                                  <div className="flex flex-col lg:flex-row lg:items-center lg:space-x-3">
                                    <span className="text-gray-600 font-medium text-sm lg:text-base">
                                      {t("nft.nextReleaseTime")}:
                                    </span>
                                    <span className="font-medium text-gray-800 text-sm lg:text-base break-all">
                                      {formatTimestamp(
                                        nftDetails.nextReleaseTime
                                      )}
                                    </span>
                                  </div>
                                </div>
                              </div>
                            </div>
                          )}
                        </>
                      )
                    )}
                  </div>
                </div>
              )}
            </div>

            {/* Modal footer - */}
            <div className="bg-gray-50 px-4 sm:px-6 py-3 sm:py-4 border-t border-gray-200 flex-shrink-0">
              <div className="flex justify-end">
                <button
                  type="button"
                  className="secondary-btn px-4 py-2 rounded-lg"
                  onClick={onClose}
                >
                  {t("wallet.cancel")}
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default NFTModal;
