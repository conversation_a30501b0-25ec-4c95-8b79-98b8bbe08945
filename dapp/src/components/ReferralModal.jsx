import React, { useState, useEffect } from 'react';
import { useTranslation } from 'react-i18next';

const ReferralModal = ({ isOpen, onClose, presaleHook }) => {
  const { t } = useTranslation();
  const [referralStats, setReferralStats] = useState(null);
  const [refereesList, setRefereesList] = useState([]);
  const [currentPage, setCurrentPage] = useState(0);
  const [loading, setLoading] = useState(false);
  const [copySuccess, setCopySuccess] = useState(false);
  
  const pageSize = 10;
  const referralLink = presaleHook.generateReferralLink();
  
  // Load referral statistics
  useEffect(() => {
    if (isOpen && presaleHook.userInfo) {
      loadReferralStats();
    }
  }, [isOpen, presaleHook.userInfo]);

  const loadReferralStats = async () => {
    setLoading(true);
    try {
      const stats = await presaleHook.getReferralStats();
      setReferralStats(stats || {
        totalReferees: 0,
        totalPurchasedByReferees: 0,
        averagePurchasePerReferee: "0",
        totalCommission: "0",
      });
      
      // Load first page of referral list
      const referees = await presaleHook.getReferees(0, pageSize);
      setRefereesList(referees || []);
      setCurrentPage(0);
    } catch (error) {
      console.error('Failed to load referral stats:', error);
      // Set default values to prevent UI crash
      setReferralStats({
        totalReferees: 0,
        totalPurchasedByReferees: 0,
        averagePurchasePerReferee: "0",
        totalCommission: "0",
      });
      setRefereesList([]);
    } finally {
      setLoading(false);
    }
  };

  const loadPage = async (page) => {
    setLoading(true);
    try {
      const start = page * pageSize;
      const referees = await presaleHook.getReferees(start, pageSize);
      setRefereesList(referees || []);
      setCurrentPage(page);
    } catch (error) {
      console.error('Failed to load referees:', error);
      setRefereesList([]);
    } finally {
      setLoading(false);
    }
  };

  const copyReferralLink = () => {
    navigator.clipboard.writeText(referralLink);
    setCopySuccess(true);
    setTimeout(() => setCopySuccess(false), 2000);
  };

  const formatAddress = (address) => {
    if (!address) return '';
    return `${address.slice(0, 6)}...${address.slice(-4)}`;
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-white rounded-lg max-w-2xl w-full max-h-[90vh] overflow-y-auto">
        {/* Header */}
        <div className="flex justify-between items-center p-6 border-b">
          <h2 className="text-xl font-bold text-gray-800">{t('referral.title')}</h2>
          <button
            onClick={onClose}
            className="text-gray-500 hover:text-gray-700 p-1"
          >
            <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
            </svg>
          </button>
        </div>

        {/* Content */}
        <div className="p-6">
          {/* Referral Link */}
          <div className="mb-6">
            <h3 className="text-lg font-semibold mb-3 text-gray-800">{t('referral.yourLink')}</h3>
            <div className="flex items-center gap-2 p-3 bg-gray-50 rounded-lg">
              <input
                type="text"
                value={referralLink}
                readOnly
                className="flex-1 bg-transparent outline-none text-sm text-gray-600"
              />
              <button
                onClick={copyReferralLink}
                className="flex items-center gap-1 px-3 py-1 bg-blue-500 text-white rounded hover:bg-blue-600 transition-colors"
              >
                <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 16H6a2 2 0 01-2-2V6a2 2 0 012-2h8a2 2 0 012 2v2m-6 12h8a2 2 0 002-2v-8a2 2 0 00-2-2h-8a2 2 0 00-2 2v8a2 2 0 002 2z" />
                </svg>
                {copySuccess ? t('referral.copied') : t('referral.copy')}
              </button>
            </div>
          </div>

          {/* My Referrer Info */}
          {presaleHook.userInfo && presaleHook.userInfo.referrer && presaleHook.userInfo.referrer !== "0x0000000000000000000000000000000000000000" && (
            <div className="mb-6">
              <h3 className="text-lg font-semibold mb-3 text-gray-800">{t('referral.myReferrer')}</h3>
              <div className="bg-orange-50 border border-orange-200 rounded-lg p-4">
                <div className="flex items-center">
                  <div className="w-12 h-12 bg-orange-100 rounded-full flex items-center justify-center mr-4">
                    <svg className="w-6 h-6 text-orange-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
                    </svg>
                  </div>
                  <div className="flex-1">
                    <div className="text-sm text-orange-600 font-medium mb-1">{t('referral.referrerAddress')}</div>
                    <div className="font-mono text-sm text-gray-900 break-all">{presaleHook.userInfo.referrer}</div>
                  </div>
                  <button
                    onClick={() => window.open(`https://bscscan.com/address/${presaleHook.userInfo.referrer}`, '_blank')}
                    className="text-orange-500 hover:text-orange-600 p-1"
                    title={t('referral.viewOnBscscan')}
                  >
                    <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10 6H6a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2v-4M14 4h6m0 0v6m0-6L10 14" />
                    </svg>
                  </button>
                </div>
              </div>
            </div>
          )}

          {/* Statistics */}
          {referralStats && (
            <div className="mb-6">
              <h3 className="text-lg font-semibold mb-3 text-gray-800">{t('referral.statistics')}</h3>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div className="bg-blue-50 p-4 rounded-lg text-center">
                  <svg className="w-8 h-8 text-blue-500 mx-auto mb-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z" />
                  </svg>
                  <div className="text-2xl font-bold text-blue-600">{referralStats.totalReferees}</div>
                  <div className="text-sm text-gray-600">{t('referral.totalReferees')}</div>
                </div>
                <div className="bg-green-50 p-4 rounded-lg text-center">
                  <svg className="w-8 h-8 text-green-500 mx-auto mb-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v13m0-13V6a2 2 0 112 2h-2zm0 0V5.5A2.5 2.5 0 109.5 8H12zm-7 4h14M5 12a2 2 0 110-4h14a2 2 0 110 4M5 12v7a2 2 0 002 2h10a2 2 0 002-2v-7" />
                  </svg>
                  <div className="text-2xl font-bold text-green-600">{referralStats.totalPurchasedByReferees}</div>
                  <div className="text-sm text-gray-600">{t('referral.totalPurchased')}</div>
                </div>
                <div className="bg-purple-50 p-4 rounded-lg text-center">
                  <svg className="w-8 h-8 text-purple-500 mx-auto mb-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 7h8m0 0v8m0-8l-8 8-4-4-6 6" />
                  </svg>
                  <div className="text-2xl font-bold text-purple-600">{referralStats.averagePurchasePerReferee}</div>
                  <div className="text-sm text-gray-600">{t('referral.averagePurchase')}</div>
                </div>
              </div>
            </div>
          )}

          {/* Referees List */}
          <div className="mb-6">
            <h3 className="text-lg font-semibold mb-3 text-gray-800">{t('referral.refereesList')}</h3>
            
            {loading ? (
              <div className="text-center py-8">
                <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500 mx-auto"></div>
                <p className="mt-2 text-gray-600">{t('common.loading')}</p>
              </div>
            ) : refereesList.length > 0 ? (
              <div className="space-y-2">
                {refereesList.map((referee, index) => (
                  <div
                    key={referee}
                    className="flex items-center justify-between p-3 bg-gray-50 rounded-lg"
                  >
                    <div className="flex items-center gap-3">
                      <div className="w-8 h-8 bg-blue-500 rounded-full flex items-center justify-center text-white font-semibold">
                        {currentPage * pageSize + index + 1}
                      </div>
                      <div>
                        <div className="font-mono text-sm">{formatAddress(referee)}</div>
                        <div className="text-xs text-gray-500">
                          {t('referral.purchasedAmount', { 
                            amount: presaleHook.referralInfo.referralDetails?.purchasedAmounts?.[index] || '0'
                          })}
                        </div>
                      </div>
                    </div>
                    <button
                      onClick={() => window.open(`https://bscscan.com/address/${referee}`, '_blank')}
                      className="text-blue-500 hover:text-blue-600 p-1"
                    >
                      <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10 6H6a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2v-4M14 4h6m0 0v6m0-6L10 14" />
                      </svg>
                    </button>
                  </div>
                ))}
              </div>
            ) : (
              <div className="text-center py-8 text-gray-500">
                <svg className="w-12 h-12 mx-auto mb-4 text-gray-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z" />
                </svg>
                <p>{t('referral.noReferees')}</p>
              </div>
            )}

            {/* Pagination */}
            {referralStats && referralStats.totalReferees > pageSize && (
              <div className="flex justify-center gap-2 mt-4">
                <button
                  onClick={() => loadPage(currentPage - 1)}
                  disabled={currentPage === 0}
                  className="px-3 py-1 bg-gray-200 rounded hover:bg-gray-300 disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  {t('common.previous')}
                </button>
                <span className="px-3 py-1 text-gray-600">
                  {t('common.page', { current: currentPage + 1, total: Math.ceil(referralStats.totalReferees / pageSize) })}
                </span>
                <button
                  onClick={() => loadPage(currentPage + 1)}
                  disabled={refereesList.length < pageSize}
                  className="px-3 py-1 bg-gray-200 rounded hover:bg-gray-300 disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  {t('common.next')}
                </button>
              </div>
            )}
          </div>

          {/* Instructions */}
          <div className="bg-blue-50 p-4 rounded-lg">
            <h4 className="font-semibold text-blue-800 mb-2">{t('referral.howItWorks')}</h4>
            <ul className="text-sm text-blue-700 space-y-1">
              <li>• {t('referral.instruction1')}</li>
              <li>• {t('referral.instruction2')}</li>
              <li>• {t('referral.instruction3')}</li>
            </ul>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ReferralModal;