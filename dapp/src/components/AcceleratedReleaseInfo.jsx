import React from "react";
import { useTranslation } from "react-i18next";

const AcceleratedReleaseInfo = ({ userInviteInfo, className = "" }) => {
  const { t } = useTranslation();

  // Calculate usage rate
  const totalReceived = parseFloat(userInviteInfo.totalAcceleratedReceived || 0);
  const totalUsed = parseFloat(userInviteInfo.totalAcceleratedUsed || 0);
  const currentUnused = parseFloat(userInviteInfo.acceleratedReleaseAmount || 0);
  
  const usageRate = totalReceived > 0 ? (totalUsed / totalReceived * 100) : 0;

  return (
    <div className={`bg-gradient-to-br from-purple-50 to-indigo-50 rounded-xl p-4 lg:p-6 border border-purple-200 ${className}`}>
      {/* Header */}
      <div className="flex items-center mb-4">
        <div className="w-8 h-8 bg-gradient-to-r from-purple-500 to-indigo-500 rounded-lg flex items-center justify-center mr-3">
          <svg className="w-4 h-4 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M13 10V3L4 14h7v7l9-11h-7z" />
          </svg>
        </div>
        <h3 className="text-lg lg:text-xl font-bold text-gray-900">
          {t("acceleratedRelease.title", "Accelerated Release Quota")}
        </h3>
      </div>

      {/* Core data display */}
      <div className="grid grid-cols-1 sm:grid-cols-3 gap-4 mb-6">
        {/* Current available quota */}
        <div className="text-center p-4 bg-white rounded-lg border border-purple-100 shadow-sm">
          <div className="text-2xl lg:text-3xl font-bold text-purple-600 mb-1">
            {currentUnused.toFixed(4)}
          </div>
          <div className="text-xs lg:text-sm font-medium text-purple-800">
            {t("acceleratedRelease.currentAvailable", "Current Available")}
          </div>
          <div className="text-xs text-gray-500 mt-1">
            USDT
          </div>
        </div>

        {/* Total received historically */}
        <div className="text-center p-4 bg-white rounded-lg border border-green-100 shadow-sm">
          <div className="text-2xl lg:text-3xl font-bold text-green-600 mb-1">
            {totalReceived.toFixed(4)}
          </div>
          <div className="text-xs lg:text-sm font-medium text-green-800">
            {t("acceleratedRelease.totalReceived", "Total Received")}
          </div>
          <div className="text-xs text-gray-500 mt-1">
            USDT
          </div>
        </div>

        {/* Total used historically */}
        <div className="text-center p-4 bg-white rounded-lg border border-orange-100 shadow-sm">
          <div className="text-2xl lg:text-3xl font-bold text-orange-600 mb-1">
            {totalUsed.toFixed(4)}
          </div>
          <div className="text-xs lg:text-sm font-medium text-orange-800">
            {t("acceleratedRelease.totalUsed", "Total Used")}
          </div>
          <div className="text-xs text-gray-500 mt-1">
            USDT
          </div>
        </div>
      </div>

      {/* Usage rate progress bar */}
      {totalReceived > 0 && (
        <div className="mb-4">
          <div className="flex justify-between items-center mb-2">
            <span className="text-sm font-medium text-gray-700">
              {t("acceleratedRelease.usageRate", "Usage Rate")}
            </span>
            <span className="text-sm font-bold text-gray-900">
              {usageRate.toFixed(1)}%
            </span>
          </div>
          <div className="w-full bg-gray-200 rounded-full h-3">
            <div 
              className="bg-gradient-to-r from-purple-500 to-indigo-500 h-3 rounded-full transition-all duration-300" 
              style={{ width: `${Math.min(usageRate, 100)}%` }}
            />
          </div>
          <div className="flex justify-between text-xs text-gray-500 mt-1">
            <span>{t("acceleratedRelease.used", "Used")}: {totalUsed.toFixed(4)} USDT</span>
            <span>{t("acceleratedRelease.available", "Available")}: {currentUnused.toFixed(4)} USDT</span>
          </div>
        </div>
      )}

      {/* Quota status explanation */}
      <div className="bg-white rounded-lg p-4 border border-purple-100">
        <div className="flex items-start">
          <svg className="w-5 h-5 text-purple-500 mt-0.5 mr-3 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
          </svg>
          <div className="text-sm text-gray-700">
            <p className="font-semibold mb-2 text-purple-800">
              {t("acceleratedRelease.howItWorks", "How Accelerated Release Works")}
            </p>
            <ul className="space-y-1 text-xs text-gray-600">
              <li>• {t("acceleratedRelease.explanation1", "Invite others to participate in mining to get 10% accelerated release quota")}</li>
              <li>• {t("acceleratedRelease.explanation2", "Accelerated quota can be used to immediately release locked mining rewards")}</li>
              <li>• {t("acceleratedRelease.explanation3", "Available accelerated quota is used first when claiming")}</li>
              <li>• {t("acceleratedRelease.explanation4", "Using accelerated quota does not affect normal time-based release")}</li>
            </ul>
          </div>
        </div>
      </div>

      {/* No quota state */}
      {totalReceived === 0 && (
        <div className="text-center py-6">
          <div className="w-16 h-16 bg-purple-100 rounded-full flex items-center justify-center mx-auto mb-4">
            <svg className="w-8 h-8 text-purple-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
            </svg>
          </div>
          <h4 className="text-lg font-semibold text-gray-900 mb-2">
            {t("acceleratedRelease.noQuota", "No Accelerated Release Quota")}
          </h4>
          <p className="text-sm text-gray-600 mb-4">
            {t("acceleratedRelease.inviteToEarn", "Invite friends to participate in mining to earn accelerated release quota")}
          </p>
        </div>
      )}
    </div>
  );
};

export default AcceleratedReleaseInfo;