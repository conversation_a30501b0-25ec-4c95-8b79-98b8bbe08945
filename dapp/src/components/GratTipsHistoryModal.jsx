import React from "react";
import { useTranslation } from "react-i18next";
import {
  formatTokenAmount,
  formatUSDTAmount,
  formatTimestamp,
  calculateReleaseProgress,
} from "../utils/formatNumbers";

const GratTipsHistoryModal = ({
  isOpen,
  onClose,
  miningRecords,
  claimMechanism1Rewards,
}) => {
  const { t } = useTranslation();

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 z-50 overflow-y-auto">
      <div className="flex items-center justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:items-center sm:p-0">
        {/* Background overlay */}
        <div
          className="fixed inset-0 modal-backdrop transition-opacity"
          onClick={onClose}
        />

        {/* Modal panel */}
        <div className="inline-block align-bottom bg-white rounded-t-lg lg:rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-4xl sm:w-full modal-panel w-full max-h-screen">
          <div className="bg-white h-full flex flex-col">
            {/* Modal header */}
            <div className="flex items-center justify-between p-4 lg:p-6 border-b border-gray-200 flex-shrink-0">
              <div className="flex items-center">
                <div className="w-8 h-8 lg:w-10 lg:h-10 bg-violet-100 rounded-full flex items-center justify-center mr-3">
                  <img
                    src="/icons/TokenA-GRAT.svg"
                    alt="GRAT Token"
                    className="w-4 h-4 lg:w-5 lg:h-5"
                  />
                </div>
                <h2 className="text-xl lg:text-2xl font-bold text-gray-900">
                  GRAT → TIPS {t("mining.history.records")}
                </h2>
              </div>
              <button
                onClick={onClose}
                className="text-gray-400 hover:text-gray-600 transition-colors p-1"
              >
                <svg
                  className="w-5 h-5 lg:w-6 lg:h-6"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth="2"
                    d="M6 18L18 6M6 6l12 12"
                  />
                </svg>
              </button>
            </div>

            {/* Modal content */}
            <div className="flex-1 p-4 lg:p-6 overflow-y-auto modal-content-scroll min-h-0">
              {/* Records count */}
              <div className="flex items-center justify-between mb-4 lg:mb-6 bg-violet-50 p-3 lg:p-4 rounded-lg">
                <span className="text-sm lg:text-base text-violet-700 font-medium">
                  {t("mining.history.totalRecords")}
                </span>
                <span className="bg-violet-100 text-violet-800 px-3 py-1 rounded-full text-sm lg:text-base font-semibold">
                  {miningRecords?.mechanism1?.length || 0}
                </span>
              </div>

              {/* Records list */}
              {miningRecords?.mechanism1 &&
              miningRecords.mechanism1.length > 0 ? (
                <div className="space-y-3 lg:space-y-4">
                  {miningRecords.mechanism1.map((record, index) => (
                    <div
                      key={index}
                      className="bg-white rounded-lg p-3 lg:p-4 border border-violet-200 shadow-sm"
                    >
                      <div className="flex justify-between items-start mb-2 lg:mb-3">
                        <div className="text-xs lg:text-sm text-gray-600 font-medium">
                          {formatTimestamp(record.timestamp, true)}
                        </div>
                        <span
                          className={`px-2 py-1 rounded-full text-xs font-medium ${
                            record.claimed
                              ? "bg-green-100 text-green-800"
                              : "bg-yellow-100 text-yellow-800"
                          }`}
                        >
                          {record.claimed
                            ? t("mining.history.claimed")
                            : t("mining.history.pending")}
                        </span>
                      </div>

                      <div className="grid grid-cols-1 lg:grid-cols-2 gap-2 lg:gap-4 text-xs lg:text-sm mb-2 lg:mb-3">
                        <div className="bg-violet-50 rounded p-2 lg:p-3">
                          <span className="text-gray-600 block">
                            {t("mining.history.burned")}:
                          </span>
                          <div className="font-semibold text-violet-700">
                            {formatTokenAmount(record.burnedTokenAAmount)} GRAT
                          </div>
                        </div>
                        <div className="bg-orange-50 rounded p-2 lg:p-3">
                          <span className="text-gray-600 block">
                            {t("mining.history.totalUsdtValue")}:
                          </span>
                          <div className="font-semibold text-orange-600">
                            {formatUSDTAmount(record.burnedTokenAValue)}
                          </div>
                        </div>
                      </div>

                      {/* Release details */}
                      <div className="bg-gray-50 rounded p-2 lg:p-3 text-xs lg:text-sm space-y-2">
                        <div className="flex justify-between">
                          <span className="text-gray-600">
                            {t("mining.history.normalReleased") ||
                              "Normal Released"}
                            :
                          </span>
                          <span className="font-semibold text-blue-600">
                            {formatUSDTAmount(record.normalReleasedValue)}
                          </span>
                        </div>
                        <div className="flex justify-between">
                          <span className="text-gray-600">
                            {t("mining.history.acceleratedReleased") ||
                              "Accelerated Released"}
                            :
                          </span>
                          <span className="font-semibold text-orange-600">
                            {formatUSDTAmount(record.acceleratedReleasedValue)}
                          </span>
                        </div>
                        <div className="flex justify-between border-t pt-2">
                          <span className="text-gray-600">
                            {t("mining.history.totalReleased") ||
                              "Total Released"}
                            :
                          </span>
                          <span className="font-semibold text-gray-900">
                            {formatUSDTAmount(record.releasedValue)}
                          </span>
                        </div>
                        <div className="flex justify-between">
                          <span className="text-gray-600">
                            {t("mining.history.remaining") || "Remaining"}:
                          </span>
                          <span className="font-semibold text-gray-600">
                            {formatUSDTAmount(
                              record.totalReleaseValue && record.releasedValue
                                ? parseFloat(record.totalReleaseValue) -
                                    parseFloat(record.releasedValue)
                                : 0
                            )}
                          </span>
                        </div>

                        {/* Release progress bar */}
                        {record.totalReleaseValue &&
                          parseFloat(record.totalReleaseValue) > 0 && (
                            <div className="mt-3 pt-3 border-t">
                              <div className="flex justify-between text-xs text-gray-600 mb-1">
                                <span>
                                  {t("mining.history.releaseProgress")}
                                </span>
                                <span>
                                  {calculateReleaseProgress(
                                    record.releasedValue,
                                    record.totalReleaseValue
                                  ).toFixed(1)}
                                  %
                                </span>
                              </div>
                              <div className="w-full bg-gray-200 rounded-full h-2">
                                <div
                                  className="bg-violet-500 h-2 rounded-full transition-all duration-300"
                                  style={{
                                    width: `${calculateReleaseProgress(
                                      record.releasedValue,
                                      record.totalReleaseValue
                                    )}%`,
                                  }}
                                />
                              </div>
                            </div>
                          )}
                      </div>

                      {record.active &&
                        record.releasedValue &&
                        record.totalReleaseValue &&
                        parseFloat(record.releasedValue) <
                          parseFloat(record.totalReleaseValue) && (
                          <button
                            onClick={() => claimMechanism1Rewards()}
                            className="mt-3 w-full secondary-btn py-2 lg:py-3 text-sm lg:text-base font-semibold rounded-lg hover:bg-violet-600 hover:text-white transition-colors"
                          >
                            {t("mining.history.claimRewards")}
                          </button>
                        )}
                    </div>
                  ))}
                </div>
              ) : (
                <div className="text-center py-6 lg:py-12">
                  <div className="w-12 h-12 lg:w-16 lg:h-16 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-3 lg:mb-4">
                    <svg
                      className="w-6 h-6 lg:w-8 lg:h-8 text-gray-400"
                      fill="none"
                      stroke="currentColor"
                      viewBox="0 0 24 24"
                    >
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth="2"
                        d="M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2"
                      />
                    </svg>
                  </div>
                  <p className="text-sm lg:text-base text-gray-600">
                    {t("mining.history.noRecords")}
                  </p>
                </div>
              )}
            </div>

            {/* Modal footer */}
            <div className="bg-gray-50 px-4 lg:px-6 py-3 lg:py-4 flex justify-center lg:justify-end flex-shrink-0">
              <button
                type="button"
                className="secondary-btn px-4 lg:px-6 py-2 lg:py-3 rounded-lg text-sm lg:text-base"
                onClick={onClose}
              >
                {t("wallet.cancel")}
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default GratTipsHistoryModal;
