import React, { useEffect } from 'react'
import { useTranslation } from 'react-i18next'

const WalletModal = ({ 
  isOpen, 
  onClose, 
  walletConfigs, 
  onWalletSelect, 
  selectedWallet,
  isConnecting,
  getWalletAvailability 
}) => {
  const { t } = useTranslation()
  const walletAvailability = getWalletAvailability()

  // Restore page scroll when closing modal
  useEffect(() => {
    if (isOpen) {
      document.body.style.overflow = 'hidden'
    } else {
      document.body.style.overflow = ''
    }
    
    return () => {
      document.body.style.overflow = ''
    }
  }, [isOpen])

  // Click background to close modal
  const handleBackdropClick = (e) => {
    if (e.target === e.currentTarget) {
      onClose()
    }
  }

  // Close modal with ESC key
  useEffect(() => {
    const handleEscKey = (e) => {
      if (e.key === 'Escape') {
        onClose()
      }
    }

    if (isOpen) {
      document.addEventListener('keydown', handleEscKey)
    }

    return () => {
      document.removeEventListener('keydown', handleEscKey)
    }
  }, [isOpen, onClose])

  if (!isOpen) return null

  return (
    <div className="wallet-modal" onClick={handleBackdropClick}>
      <div className="wallet-modal-content p-6 relative">
        <button className="wallet-close-btn" onClick={onClose}>
          <svg className="w-4 h-4 text-gray-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M6 18L18 6M6 6l12 12"/>
          </svg>
        </button>
        
        <div className="text-center mb-8">
          <div className="w-16 h-16 bg-violet-100 rounded-2xl flex items-center justify-center mx-auto mb-4">
            <svg className="w-8 h-8 text-violet-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z"/>
            </svg>
          </div>
          <h2 className="text-2xl md:text-3xl font-bold text-gray-900 mb-3">{t('wallet.selectWallet')}</h2>
          <p className="text-gray-600">{t('wallet.selectWalletDesc')}</p>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
          {Object.entries(walletConfigs).map(([walletType, config]) => {
            const isAvailable = walletAvailability[walletType]
            const isSelected = selectedWallet === walletType
            const isCurrentlyConnecting = isConnecting && selectedWallet === walletType

            let statusClasses = 'wallet-option-modal card p-6 cursor-pointer'
            if (!isAvailable) {
              statusClasses += ' opacity-50 cursor-not-allowed'
            }
            if (isSelected) {
              statusClasses += ' ring-2 ring-violet-500 bg-violet-50'
            }
            if (isCurrentlyConnecting) {
              statusClasses += ' connecting'
            }

            return (
              <div
                key={walletType}
                className={statusClasses}
                onClick={() => {
                  if (isAvailable && !isConnecting) {
                    onWalletSelect(walletType)
                  }
                }}
              >
                <div className="flex items-center">
                  <div className="w-12 h-12 bg-gray-100 rounded-xl flex items-center justify-center mr-4 flex-shrink-0">
                    <span className="text-2xl">{config.icon}</span>
                  </div>
                  <div className="flex-grow">
                    <h3 className="text-lg font-bold text-gray-900 mb-1">{config.name}</h3>
                    <p className="text-sm text-gray-600">{config.description}</p>
                  </div>
                  <div className="wallet-status-indicator ml-3">
                    {isCurrentlyConnecting ? (
                      <div className="w-3 h-3 bg-amber-500 rounded-full animate-pulse"></div>
                    ) : isSelected ? (
                      <div className="w-3 h-3 bg-violet-500 rounded-full animate-pulse"></div>
                    ) : isAvailable ? (
                      <div className="w-3 h-3 bg-green-500 rounded-full"></div>
                    ) : (
                      <div className="w-3 h-3 bg-red-500 rounded-full"></div>
                    )}
                  </div>
                </div>
              </div>
            )
          })}
        </div>

        <div className="bg-gray-50 rounded-lg p-4 mb-6">
          <div className="flex items-start">
            <svg className="w-5 h-5 text-blue-500 mt-0.5 mr-3 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"/>
            </svg>
            <div className="text-sm text-gray-700">
              <p className="font-semibold mb-1">{t('wallet.connectionInstructions')}</p>
              <ul className="space-y-1">
                <li>• <span className="inline-block w-2 h-2 bg-green-500 rounded-full mr-2"></span>{t('wallet.greenIndicator')}</li>
                <li>• <span className="inline-block w-2 h-2 bg-red-500 rounded-full mr-2"></span>{t('wallet.redIndicator')}</li>
                <li>• {t('wallet.autoSwitchNetwork')}</li>
              </ul>
            </div>
          </div>
        </div>

        <div className="flex flex-col sm:flex-row gap-3">
          <button
            onClick={() => window.open('https://metamask.io/download/', '_blank')}
            className="secondary-btn flex-1 py-3 px-4 rounded-lg text-sm font-semibold inline-flex items-center justify-center"
          >
            <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-4l-4 4m0 0l-4-4m4 4V4"/>
            </svg>
            {t('wallet.installMetaMask')}
          </button>
          <button
            onClick={onClose}
            className="secondary-btn flex-1 py-3 px-4 rounded-lg text-sm font-semibold"
          >
            {t('wallet.cancel')}
          </button>
        </div>
      </div>
    </div>
  )
}

export default WalletModal