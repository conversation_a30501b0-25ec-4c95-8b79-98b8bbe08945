import React, { useState, useEffect, useCallback } from "react";
import { useTranslation } from "react-i18next";
import { ethers } from "ethers";

const InviteModal = ({
  isOpen,
  onClose,
  userInviteInfo,
  contracts,
  userAddress,
  showNotification,
  showLoading,
  hideLoading,
  onInviteInfoUpdate,
  onOpenRewardsHistory,
  inviterFromUrl,
}) => {
  const { t } = useTranslation();
  const [inviterAddress, setInviterAddress] = useState("");
  const [inviteLink, setInviteLink] = useState("");
  const [copiedLink, setCopiedLink] = useState(false);
  const [copiedAddress, setCopiedAddress] = useState(false);
  const [hasInviter, setHasInviter] = useState(false);
  const [isCheckingInviter, setIsCheckingInviter] = useState(false);

  const checkHasInviter = useCallback(async () => {
    if (!contracts.miningContract || !userAddress) return;

    try {
      setIsCheckingInviter(true);
      const hasInviterResult = await contracts.miningContract.hasInviter(
        userAddress
      );
      setHasInviter(hasInviterResult);
      console.log("Inviter check result:", hasInviterResult);
    } catch (error) {
      console.error("Failed to check inviter status:", error);
    } finally {
      setIsCheckingInviter(false);
    }
  }, [contracts.miningContract, userAddress]);

  const generateInviteLink = useCallback(() => {
    if (!userAddress) return "";

    const baseUrl = window.location.origin + window.location.pathname;
    return `${baseUrl}?inviter=${userAddress}`;
  }, [userAddress]);

  useEffect(() => {
    if (isOpen) {
      console.log("Modal opening, checking inviter status...");
      checkHasInviter();
      setInviteLink(generateInviteLink());
    }
  }, [isOpen, checkHasInviter, generateInviteLink]);

  useEffect(() => {
    if (isOpen && userAddress) {
      console.log("Attempting to auto-fill inviter address...");

      if (
        inviterFromUrl &&
        ethers.isAddress(inviterFromUrl) &&
        inviterFromUrl !== userAddress
      ) {
        console.log("✅ Auto-filling with inviterFromUrl:", inviterFromUrl);
        setInviterAddress(inviterFromUrl);
        return;
      }

      const urlParams = new URLSearchParams(window.location.search);
      const inviterParam = urlParams.get("inviter");
      if (
        inviterParam &&
        ethers.isAddress(inviterParam) &&
        inviterParam !== userAddress
      ) {
        setInviterAddress(inviterParam);
        return;
      }
    }
  }, [isOpen, userAddress, inviterFromUrl]);

  useEffect(() => {
    if (isOpen && hasInviter && inviterAddress) {
      setInviterAddress("");
    }
  }, [isOpen, hasInviter]);

  const setInviter = async () => {
    if (!contracts.miningContract || !inviterAddress) {
      showNotification(t("invite.enterValidAddress"), "error");
      return;
    }

    if (!ethers.isAddress(inviterAddress)) {
      showNotification(t("invite.invalidAddress"), "error");
      return;
    }

    if (inviterAddress === userAddress) {
      showNotification(t("invite.cannotInviteSelf"), "error");
      return;
    }

    try {
      showLoading(t("invite.settingInviter"));

      const tx = await contracts.miningContract.setInviter(inviterAddress);
      await tx.wait();

      showNotification(t("invite.inviterSetSuccess"), "success");

      setHasInviter(true);

      if (onInviteInfoUpdate) {
        await onInviteInfoUpdate();
      }

      const url = new URL(window.location);
      url.searchParams.delete("inviter");
      window.history.replaceState({}, "", url.toString());
    } catch (error) {
      console.error("Failed to set inviter:", error);
      showNotification(
        `${t("invite.setInviterFailed")}: ${error.message}`,
        "error"
      );
    } finally {
      hideLoading();
    }
  };

  const copyInviteLink = async () => {
    try {
      await navigator.clipboard.writeText(inviteLink);
      setCopiedLink(true);
      showNotification(t("invite.linkCopied"), "success");
      setTimeout(() => setCopiedLink(false), 2000);
    } catch (error) {
      console.error("Failed to copy link:", error);
      showNotification(t("invite.copyFailed"), "error");
    }
  };

  const copyAddress = async () => {
    try {
      await navigator.clipboard.writeText(userAddress);
      setCopiedAddress(true);
      showNotification(t("invite.addressCopied"), "success");
      setTimeout(() => setCopiedAddress(false), 2000);
    } catch (error) {
      console.error("Failed to copy address:", error);
      showNotification(t("invite.copyFailed"), "error");
    }
  };

  const shareInviteLink = async () => {
    if (navigator.share) {
      try {
        await navigator.share({
          title: t("invite.shareTitle"),
          text: t("invite.shareText"),
          url: inviteLink,
        });
      } catch (error) {
        console.error("Failed to share:", error);
        copyInviteLink();
      }
    } else {
      copyInviteLink();
    }
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 z-50 overflow-y-auto">
      <div className="flex items-center justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:items-center sm:p-0">
        {/* Background overlay */}
        <div
          className="fixed inset-0 modal-backdrop transition-opacity"
          onClick={onClose}
        />

        {/* Modal panel */}
        <div className="inline-block align-bottom bg-white rounded-t-lg sm:rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-2xl sm:w-full modal-panel w-full max-h-screen overflow-y-auto">
          <div className="bg-white">
            {/* Modal header */}
            <div className="flex items-center justify-between p-4 sm:p-6 border-b border-gray-200">
              <h2 className="text-lg sm:text-xl md:text-2xl font-bold text-gray-900">
                {t("invite.title")}
              </h2>
              <button
                onClick={onClose}
                className="text-gray-400 hover:text-gray-600 transition-colors p-1"
              >
                <svg
                  className="w-5 h-5 sm:w-6 sm:h-6"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth="2"
                    d="M6 18L18 6M6 6l12 12"
                  />
                </svg>
              </button>
            </div>

            {/* Modal content */}
            <div className="p-4 sm:p-6 space-y-4 sm:space-y-6">
              {hasInviter && (
                <div className="space-y-4">
                  <div className="grid grid-cols-1 sm:grid-cols-3 gap-4 p-4 bg-gradient-to-r from-green-50 to-blue-50 rounded-lg border border-green-200">
                    <div className="text-center">
                      <div className="text-2xl font-bold text-green-600 mb-1">
                        {userInviteInfo.totalInvited}
                      </div>
                      <div className="text-sm text-gray-600">
                        {t("invite.totalInvited")}
                      </div>
                    </div>
                    <div className="text-center">
                      <div className="text-2xl font-bold text-blue-600 mb-1">
                        {userInviteInfo.inviteeList?.length || 0}
                      </div>
                      <div className="text-sm text-gray-600">
                        {t("invite.activeInvitees")}
                      </div>
                    </div>
                    <div className="text-center">
                      <div className="text-2xl font-bold text-orange-600 mb-1">
                        {parseFloat(
                          userInviteInfo.acceleratedReleaseAmount || 0
                        ).toFixed(4)}
                      </div>
                      <div className="text-sm text-gray-600">
                        {t("invite.totalRewards")}
                      </div>
                    </div>
                  </div>

                  <button
                    onClick={() => {
                      onClose();
                      onOpenRewardsHistory();
                    }}
                    className="w-full secondary-btn py-3 rounded-lg font-semibold flex items-center justify-center"
                  >
                    <svg
                      className="w-5 h-5 mr-2"
                      fill="none"
                      stroke="currentColor"
                      viewBox="0 0 24 24"
                    >
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth="2"
                        d="M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2"
                      />
                    </svg>
                    {t("invite.rewardHistory")}
                  </button>
                </div>
              )}

              {!hasInviter && (
                <div className="space-y-3 sm:space-y-4">
                  <h3 className="text-base sm:text-lg font-semibold text-gray-900">
                    {t("invite.setInviterTitle")}
                  </h3>

                  <div className="p-3 sm:p-4 bg-amber-50 border border-amber-200 rounded-lg">
                    <div className="flex items-start">
                      <svg
                        className="w-4 h-4 sm:w-5 sm:h-5 text-amber-500 mt-0.5 mr-2 sm:mr-3 flex-shrink-0"
                        fill="none"
                        stroke="currentColor"
                        viewBox="0 0 24 24"
                      >
                        <path
                          strokeLinecap="round"
                          strokeLinejoin="round"
                          strokeWidth="2"
                          d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"
                        />
                      </svg>
                      <div className="text-xs sm:text-sm text-amber-800">
                        <p className="font-semibold mb-1">
                          {t("invite.inviterNote")}
                        </p>
                        <p>{t("invite.inviterDesc")}</p>
                      </div>
                    </div>
                  </div>

                  <div className="space-y-3">
                    <label className="block text-xs sm:text-sm font-medium text-gray-700">
                      {t("invite.inviterAddress")}
                    </label>
                    <input
                      type="text"
                      value={inviterAddress}
                      onChange={(e) => setInviterAddress(e.target.value)}
                      placeholder="0x..."
                      className="w-full px-3 py-2 sm:py-3 border border-gray-300 rounded-lg focus:ring-violet-500 focus:border-violet-500 text-sm font-mono"
                    />
                    <button
                      onClick={setInviter}
                      disabled={!inviterAddress}
                      className="w-full primary-btn py-2.5 sm:py-3 rounded-lg font-semibold disabled:opacity-50 text-sm sm:text-base"
                    >
                      {t("invite.setInviter")}
                    </button>
                  </div>
                </div>
              )}

              <div className="space-y-3 sm:space-y-4">
                <h3 className="text-base sm:text-lg font-semibold text-gray-900">
                  {t("invite.inviteOthersTitle")}
                </h3>

                <div className="p-3 sm:p-4 bg-violet-50 border border-violet-200 rounded-lg">
                  <div className="flex items-start">
                    <svg
                      className="w-4 h-4 sm:w-5 sm:h-5 text-violet-500 mt-0.5 mr-2 sm:mr-3 flex-shrink-0"
                      fill="none"
                      stroke="currentColor"
                      viewBox="0 0 24 24"
                    >
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth="2"
                        d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1"
                      />
                    </svg>
                    <div className="text-xs sm:text-sm text-violet-800">
                      <p className="font-semibold mb-1">
                        {t("invite.rewardInfo")}
                      </p>
                      <p>{t("invite.rewardDesc")}</p>
                    </div>
                  </div>
                </div>

                <div className="space-y-2">
                  <label className="block text-xs sm:text-sm font-medium text-gray-700">
                    {t("invite.myAddress")}
                  </label>
                  <div className="flex flex-col sm:flex-row gap-2">
                    <input
                      type="text"
                      value={userAddress}
                      readOnly
                      className="flex-1 px-3 py-2 bg-gray-50 border border-gray-300 rounded-lg text-gray-600 font-mono text-xs sm:text-sm break-all"
                    />
                    <button
                      onClick={copyAddress}
                      className="secondary-btn px-4 py-2 rounded-lg text-sm font-semibold whitespace-nowrap"
                    >
                      {copiedAddress ? t("invite.copied") : t("invite.copy")}
                    </button>
                  </div>
                </div>

                <div className="space-y-2">
                  <label className="block text-xs sm:text-sm font-medium text-gray-700">
                    {t("invite.inviteLink")}
                  </label>
                  <div className="space-y-2 sm:space-y-0">
                    <input
                      type="text"
                      value={inviteLink}
                      readOnly
                      className="w-full px-3 py-2 bg-gray-50 border border-gray-300 rounded-lg text-gray-600 text-xs sm:text-sm break-all"
                    />
                    <div className="flex gap-2">
                      <button
                        onClick={copyInviteLink}
                        className="secondary-btn flex-1 sm:flex-none px-4 py-2 rounded-lg text-sm font-semibold"
                      >
                        {copiedLink ? t("invite.copied") : t("invite.copy")}
                      </button>
                      <button
                        onClick={shareInviteLink}
                        className="primary-btn flex-1 sm:flex-none px-4 py-2 rounded-lg text-sm font-semibold"
                      >
                        {t("invite.share")}
                      </button>
                    </div>
                  </div>
                </div>
              </div>

              {hasInviter &&
                userInviteInfo.inviteeList &&
                userInviteInfo.inviteeList.length > 0 && (
                  <div className="space-y-4">
                    <h3 className="text-lg font-semibold text-gray-900">
                      {t("invite.inviteeList")}
                    </h3>
                    <div className="max-h-32 sm:max-h-40 overflow-y-auto space-y-2">
                      {userInviteInfo.inviteeList.map((invitee, index) => (
                        <div
                          key={index}
                          className="flex flex-col sm:flex-row sm:items-center sm:justify-between p-3 bg-gray-50 rounded-lg space-y-1 sm:space-y-0"
                        >
                          <span className="font-mono text-xs sm:text-sm text-gray-700 break-all">
                            {invitee}
                          </span>
                          <span className="text-xs text-gray-500 whitespace-nowrap">
                            {t("invite.invitee")} #{index + 1}
                          </span>
                        </div>
                      ))}
                    </div>
                  </div>
                )}
            </div>

            {/* Modal footer */}
            <div className="bg-gray-50 px-4 sm:px-6 py-4">
              <button
                type="button"
                className="secondary-btn w-full sm:w-auto px-4 py-3 sm:py-2 rounded-lg"
                onClick={onClose}
              >
                {t("wallet.cancel")}
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default InviteModal;
