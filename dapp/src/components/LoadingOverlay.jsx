import React from 'react'
import { useTranslation } from 'react-i18next'

const LoadingOverlay = ({ isVisible, text }) => {
  const { t } = useTranslation()
  if (!isVisible) return null

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 z-50 flex items-center justify-center">
      <div className="bg-white rounded-lg p-8 max-w-sm mx-4">
        <div className="text-center">
          <div className="spinner mx-auto mb-4" style={{ width: '32px', height: '32px' }}></div>
          <p className="text-gray-700 font-semibold">{text || t('notifications.processing')}</p>
        </div>
      </div>
    </div>
  )
}

export default LoadingOverlay