import React, { useState, useEffect, useCallback } from "react";
import { ethers } from "ethers";
import { useTranslation } from "react-i18next";
import {
  formatTokenAmount,
  formatUSDTAmount,
  formatTimestamp,
} from "../utils/formatNumbers";

const EnhancedMiningHistory = ({
  isOpen,
  onClose,
  contracts,
  userAddress,
  mechanism = 1, // 1 for GRAT→TIPS, 2 for TIPS→TIPS
  onClaimRewards,
  showNotification,
}) => {
  const { t } = useTranslation();
  const [miningRecords, setMiningRecords] = useState([]);
  const [recordsRealtime, setRecordsRealtime] = useState([]);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState(null);

  // Load mining records with real-time data
  const loadMiningRecords = useCallback(async () => {
    if (!contracts.miningContract || !userAddress) return;

    setIsLoading(true);
    setError(null);

    try {
      // Get record count
      const [count1, count2] = await contracts.miningContract.getUserMiningRecordCount(userAddress);
      const recordCount = mechanism === 1 ? count1 : count2;

      const records = [];
      const realtimeData = [];

      // Load each record detail and real-time status
      for (let i = 0; i < recordCount; i++) {
        const [recordDetail, realtimeStatus] = await Promise.all([
          mechanism === 1 
            ? contracts.miningContract.getUserMiningRecord1Detail(userAddress, i)
            : contracts.miningContract.getUserMiningRecord2Detail(userAddress, i),
          mechanism === 1
            ? contracts.miningContract.getMiningRecordRealtimeStatus1(userAddress, i)
            : contracts.miningContract.getMiningRecordRealtimeStatus2(userAddress, i)
        ]);

        // Parse record detail
        let parsedRecord;
        if (mechanism === 1) {
          const [burnedTokenA, burnedValue, expectedTokenB, totalRelease, released, 
                 acceleratedReleased, normalReleased, acceleratedTime, startTime, lastClaim, active] = recordDetail;
          parsedRecord = {
            index: i,
            burnedTokenAAmount: ethers.formatEther(burnedTokenA),
            burnedTokenAValue: ethers.formatEther(burnedValue),
            expectedTokenBAmount: ethers.formatEther(expectedTokenB),
            totalReleaseValue: ethers.formatEther(totalRelease),
            releasedValue: ethers.formatEther(released),
            acceleratedReleasedValue: ethers.formatEther(acceleratedReleased),
            normalReleasedValue: ethers.formatEther(normalReleased),
            acceleratedTime: Number(acceleratedTime),
            startTime: new Date(Number(startTime) * 1000),
            lastClaimTime: new Date(Number(lastClaim) * 1000),
            active
          };
        } else {
          const [burnedTokenB, burnedValue, totalRelease, released, 
                 acceleratedReleased, normalReleased, acceleratedTime, startTime, lastClaim, active] = recordDetail;
          parsedRecord = {
            index: i,
            burnedTokenBAmount: ethers.formatEther(burnedTokenB),
            burnedTokenBValue: ethers.formatEther(burnedValue),
            totalReleaseValue: ethers.formatEther(totalRelease),
            releasedValue: ethers.formatEther(released),
            acceleratedReleasedValue: ethers.formatEther(acceleratedReleased),
            normalReleasedValue: ethers.formatEther(normalReleased),
            acceleratedTime: Number(acceleratedTime),
            startTime: new Date(Number(startTime) * 1000),
            lastClaimTime: new Date(Number(lastClaim) * 1000),
            active
          };
        }

        // Parse real-time status
        const [totalValue, releasedValue, currentClaimable, releaseRate, 
               startTime, lastClaimTime, estimatedEndTime, isActive] = realtimeStatus;
        const parsedRealtime = {
          totalValue: ethers.formatEther(totalValue),
          releasedValue: ethers.formatEther(releasedValue),
          currentClaimable: ethers.formatEther(currentClaimable),
          releaseRate: ethers.formatEther(releaseRate),
          startTime: new Date(Number(startTime) * 1000),
          lastClaimTime: new Date(Number(lastClaimTime) * 1000),
          estimatedEndTime: new Date(Number(estimatedEndTime) * 1000),
          isActive
        };

        records.push(parsedRecord);
        realtimeData.push(parsedRealtime);
      }

      setMiningRecords(records);
      setRecordsRealtime(realtimeData);
    } catch (error) {
      console.error("Failed to load mining records:", error);
      setError(error.message);
    } finally {
      setIsLoading(false);
    }
  }, [contracts.miningContract, userAddress, mechanism]);

  // Load data when modal opens
  useEffect(() => {
    if (isOpen) {
      loadMiningRecords();
      
      // Set up refresh interval
      const interval = setInterval(loadMiningRecords, 30000); // Refresh every 30 seconds
      return () => clearInterval(interval);
    }
  }, [isOpen, loadMiningRecords]);

  // Calculate release progress
  const calculateProgress = (record, realtime) => {
    const total = parseFloat(realtime.totalValue);
    const released = parseFloat(realtime.releasedValue);
    return total > 0 ? (released / total) * 100 : 0;
  };

  // Format duration
  const formatDuration = (ms) => {
    const days = Math.floor(ms / (1000 * 60 * 60 * 24));
    const hours = Math.floor((ms % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60));
    const minutes = Math.floor((ms % (1000 * 60 * 60)) / (1000 * 60));
    
    if (days > 0) return `${days}${t("common.days")} ${hours}${t("common.hours")}`;
    if (hours > 0) return `${hours}${t("common.hours")} ${minutes}${t("common.minutes")}`;
    return `${minutes}${t("common.minutes")}`;
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 z-50 overflow-y-auto">
      <div className="flex items-center justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:items-center sm:p-0">
        {/* Background overlay */}
        <div
          className="fixed inset-0 bg-black bg-opacity-50 transition-opacity"
          onClick={onClose}
        />

        {/* Modal panel */}
        <div className="inline-block align-bottom bg-white rounded-t-lg lg:rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-6xl sm:w-full w-full max-h-screen">
          <div className="bg-white h-full flex flex-col max-h-screen">
            {/* Modal header */}
            <div className="flex items-center justify-between p-4 lg:p-6 border-b border-gray-200 flex-shrink-0">
              <div className="flex items-center">
                <div className={`w-8 h-8 lg:w-10 lg:h-10 rounded-full flex items-center justify-center mr-3 ${
                  mechanism === 1 ? 'bg-violet-100' : 'bg-green-100'
                }`}>
                  <img
                    src={mechanism === 1 ? "/icons/TokenA-GRAT.svg" : "/icons/TokenB-TIPS.svg"}
                    alt={mechanism === 1 ? "GRAT Token" : "TIPS Token"}
                    className="w-4 h-4 lg:w-5 lg:h-5"
                  />
                </div>
                <div>
                  <h2 className="text-xl lg:text-2xl font-bold text-gray-900">
                    {mechanism === 1 ? "GRAT → TIPS" : "TIPS → TIPS"} {t("mining.history.records")}
                  </h2>
                  <p className="text-sm text-gray-500">{t("mining.history.realtimeData")}</p>
                </div>
              </div>
              <button
                onClick={onClose}
                className="text-gray-400 hover:text-gray-600 transition-colors p-1"
              >
                <svg className="w-5 h-5 lg:w-6 lg:h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M6 18L18 6M6 6l12 12" />
                </svg>
              </button>
            </div>

            {/* Modal content */}
            <div className="flex-1 p-4 lg:p-6 overflow-y-auto min-h-0">
              {isLoading ? (
                <div className="flex items-center justify-center py-12">
                  <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-violet-600 mr-3"></div>
                  <span className="text-gray-600">{t("mining.history.loading")}</span>
                </div>
              ) : error ? (
                <div className="text-center py-12">
                  <div className="w-16 h-16 bg-red-100 rounded-full flex items-center justify-center mx-auto mb-4">
                    <svg className="w-8 h-8 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                    </svg>
                  </div>
                  <h3 className="text-lg font-semibold text-gray-900 mb-2">{t("mining.history.loadError")}</h3>
                  <p className="text-gray-600 mb-4">{error}</p>
                  <button
                    onClick={loadMiningRecords}
                    className="secondary-btn px-4 py-2 rounded-lg"
                  >
                    {t("common.retry")}
                  </button>
                </div>
              ) : miningRecords.length === 0 ? (
                <div className="text-center py-12">
                  <div className="w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-4">
                    <svg className="w-8 h-8 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2" />
                    </svg>
                  </div>
                  <h3 className="text-lg font-semibold text-gray-900 mb-2">{t("mining.history.noRecords")}</h3>
                  <p className="text-gray-600">{t("mining.history.startMining")}</p>
                </div>
              ) : (
                <>
                  {/* Summary */}
                  <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
                    <div className={`p-4 rounded-lg border ${mechanism === 1 ? 'bg-violet-50 border-violet-200' : 'bg-green-50 border-green-200'}`}>
                      <div className="text-sm text-gray-600 mb-1">{t("mining.history.totalRecords")}</div>
                      <div className={`text-2xl font-bold ${mechanism === 1 ? 'text-violet-600' : 'text-green-600'}`}>
                        {miningRecords.length}
                      </div>
                    </div>
                    
                    <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
                      <div className="text-sm text-gray-600 mb-1">{t("mining.history.totalClaimable")}</div>
                      <div className="text-2xl font-bold text-blue-600">
                        {recordsRealtime.reduce((sum, r) => sum + parseFloat(r.currentClaimable), 0).toFixed(4)}
                      </div>
                      <div className="text-xs text-gray-500">USDT</div>
                    </div>
                    
                    <div className="bg-orange-50 border border-orange-200 rounded-lg p-4">
                      <div className="text-sm text-gray-600 mb-1">{t("mining.history.activeRecords")}</div>
                      <div className="text-2xl font-bold text-orange-600">
                        {recordsRealtime.filter(r => r.isActive).length}
                      </div>
                    </div>
                  </div>

                  {/* Records list */}
                  <div className="space-y-4">
                    {miningRecords.map((record, index) => {
                      const realtime = recordsRealtime[index];
                      const progress = calculateProgress(record, realtime);
                      const timeUntilEnd = Math.max(0, new Date(realtime.estimatedEndTime).getTime() - Date.now());
                      const hasClaimable = parseFloat(realtime.currentClaimable) > 0;

                      return (
                        <div
                          key={index}
                          className="bg-white rounded-lg p-4 border border-gray-200 shadow-sm"
                        >
                          {/* Record header */}
                          <div className="flex justify-between items-start mb-4">
                            <div className="flex items-center">
                              <div className={`w-8 h-8 rounded-full flex items-center justify-center mr-3 ${
                                realtime.isActive ? 'bg-green-100' : 'bg-gray-100'
                              }`}>
                                <span className={`text-xs font-bold ${realtime.isActive ? 'text-green-600' : 'text-gray-500'}`}>
                                  #{index + 1}
                                </span>
                              </div>
                              <div>
                                <div className="text-sm font-medium text-gray-900">
                                  {formatTimestamp(record.startTime, true)}
                                </div>
                                <div className="text-xs text-gray-500">
                                  {t("mining.history.started")}
                                </div>
                              </div>
                            </div>
                            
                            <div className="flex items-center space-x-2">
                              <span className={`px-2 py-1 rounded-full text-xs font-medium ${
                                realtime.isActive
                                  ? "bg-green-100 text-green-800"
                                  : "bg-gray-100 text-gray-600"
                              }`}>
                                {realtime.isActive ? t("mining.history.active") : t("mining.history.completed")}
                              </span>
                              
                              {hasClaimable && (
                                <span className="px-2 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                                  {t("mining.history.claimable")}
                                </span>
                              )}
                            </div>
                          </div>

                          {/* Record details */}
                          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-4">
                            <div>
                              <div className="text-xs text-gray-500 mb-1">
                                {mechanism === 1 ? t("mining.history.burnedGRAT") : t("mining.history.burnedTIPS")}
                              </div>
                              <div className="text-sm font-semibold text-gray-900">
                                {mechanism === 1 
                                  ? `${parseFloat(record.burnedTokenAAmount).toFixed(4)} GRAT`
                                  : `${parseFloat(record.burnedTokenBAmount).toFixed(4)} TIPS`
                                }
                              </div>
                              <div className="text-xs text-gray-500">
                                ≈ {parseFloat(mechanism === 1 ? record.burnedTokenAValue : record.burnedTokenBValue).toFixed(4)} USDT
                              </div>
                            </div>
                            
                            <div>
                              <div className="text-xs text-gray-500 mb-1">{t("mining.history.totalRelease")}</div>
                              <div className="text-sm font-semibold text-gray-900">
                                {parseFloat(realtime.totalValue).toFixed(4)} USDT
                              </div>
                            </div>
                            
                            <div>
                              <div className="text-xs text-gray-500 mb-1">{t("mining.history.released")}</div>
                              <div className="text-sm font-semibold text-gray-900">
                                {parseFloat(realtime.releasedValue).toFixed(4)} USDT
                              </div>
                              <div className="text-xs text-green-600">
                                {progress.toFixed(1)}% {t("mining.history.progress")}
                              </div>
                            </div>
                            
                            <div>
                              <div className="text-xs text-gray-500 mb-1">{t("mining.history.currentClaimable")}</div>
                              <div className="text-sm font-semibold text-blue-600">
                                {parseFloat(realtime.currentClaimable).toFixed(4)} USDT
                              </div>
                              {realtime.isActive && timeUntilEnd > 0 && (
                                <div className="text-xs text-gray-500">
                                  {formatDuration(timeUntilEnd)} {t("mining.history.timeRemaining")}
                                </div>
                              )}
                            </div>
                          </div>

                          {/* Progress bar */}
                          <div className="mb-4">
                            <div className="w-full bg-gray-200 rounded-full h-2">
                              <div 
                                className={`h-2 rounded-full transition-all duration-500 ${
                                  mechanism === 1 ? 'bg-violet-500' : 'bg-green-500'
                                }`}
                                style={{ width: `${Math.min(progress, 100)}%` }}
                              ></div>
                            </div>
                          </div>

                          {/* Release details */}
                          {(parseFloat(record.acceleratedReleasedValue) > 0 || parseFloat(record.normalReleasedValue) > 0) && (
                            <div className="bg-gray-50 rounded-lg p-3 mb-4">
                              <div className="text-xs text-gray-600 mb-2">{t("mining.history.releaseBreakdown")}</div>
                              <div className="grid grid-cols-2 gap-4 text-xs">
                                <div>
                                  <span className="text-gray-500">{t("mining.history.normalRelease")}:</span>
                                  <span className="ml-1 font-medium">
                                    {parseFloat(record.normalReleasedValue).toFixed(4)} USDT
                                  </span>
                                </div>
                                <div>
                                  <span className="text-gray-500">{t("mining.history.acceleratedRelease")}:</span>
                                  <span className="ml-1 font-medium text-orange-600">
                                    {parseFloat(record.acceleratedReleasedValue).toFixed(4)} USDT
                                  </span>
                                </div>
                              </div>
                            </div>
                          )}
                        </div>
                      );
                    })}
                  </div>

                  {/* Claim all button */}
                  {recordsRealtime.some(r => parseFloat(r.currentClaimable) > 0) && (
                    <div className="mt-6 flex justify-center">
                      <button
                        onClick={() => onClaimRewards && onClaimRewards()}
                        className="primary-btn px-8 py-3 rounded-lg font-semibold flex items-center"
                      >
                        <svg className="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1" />
                        </svg>
                        {t("mining.history.claimAll")}
                      </button>
                    </div>
                  )}
                </>
              )}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default EnhancedMiningHistory;