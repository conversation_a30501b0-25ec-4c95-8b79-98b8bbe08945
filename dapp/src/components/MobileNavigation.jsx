import React, { useState, useRef } from "react";
import { Link, useLocation } from "react-router-dom";
import { useTranslation } from "react-i18next";
import LanguageSwitcher from "./LanguageSwitcher";

const MobileNavigation = ({
  isConnected,
  userAddress,
  formatAddress,
  handleDisconnect,
  onConnectWallet,
  isConnecting,
  // LaunchApp specific props
  balances,
  onMiningClick,
  onNFTClick,
  onInviteClick,
  showQuickActions = false,
}) => {
  const { t } = useTranslation();
  const location = useLocation();
  const [isMenuOpen, setIsMenuOpen] = useState(false);
  const [copySuccess, setCopySuccess] = useState(false);
  const copyButtonRef = useRef(null);

  // Get current path information
  const getCurrentPageInfo = () => {
    const path = location.pathname;
    switch (path) {
      case "/":
        return {
          title: t("nav.home"),
          description: t("nav.homeDesc"),
          icon: "home",
          color: "blue",
        };
      case "/app":
        return {
          title: t("nav.launchApp"),
          description: t("nav.currentPage"),
          icon: "app",
          color: "violet",
        };
      default:
        return {
          title: t("nav.home"),
          description: t("nav.homeDesc"),
          icon: "home",
          color: "blue",
        };
    }
  };

  const currentPageInfo = getCurrentPageInfo();

  const toggleMenu = () => {
    console.log(
      "toggleMenu clicked, current isMenuOpen:",
      isMenuOpen,
      "will set to:",
      !isMenuOpen
    );
    setIsMenuOpen(!isMenuOpen);
  };

  const closeMenu = () => {
    setIsMenuOpen(false);
  };

  return (
    <>
      {/* Mobile menu button */}
      <button
        onClick={toggleMenu}
        className="md:hidden flex items-center justify-center w-10 h-10 rounded-lg hover:bg-gray-100 transition-colors focus:outline-none focus:ring-2 focus:ring-violet-500 focus:ring-opacity-50 bg-violet-50 border border-violet-200"
        aria-label={
          isMenuOpen ? `${t("nav.menu")} - Close` : `${t("nav.menu")} - Open`
        }
        aria-expanded={isMenuOpen}
      >
        <svg
          className="w-6 h-6 text-gray-600"
          fill="none"
          stroke="currentColor"
          viewBox="0 0 24 24"
        >
          {isMenuOpen ? (
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth="2"
              d="M6 18L18 6M6 6l12 12"
            />
          ) : (
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth="2"
              d="M4 6h16M4 12h16M4 18h16"
            />
          )}
        </svg>
      </button>

      {/* Mobile menu overlay */}
      {isMenuOpen && (
        <div
          className="fixed inset-0 z-[100] md:hidden"
          onClick={closeMenu}
          role="dialog"
          aria-modal="true"
          aria-label={t("nav.navigation")}
        >
          <div className="fixed inset-0 bg-black bg-opacity-50" />

          {/* Mobile menu panel */}
          <div
            className="fixed top-0 right-0 h-full w-80 max-w-[90vw] sm:max-w-[90vw] max-[640px]:w-full max-[640px]:max-w-full bg-white shadow-xl mobile-menu-slide z-[110]"
            onClick={(e) => e.stopPropagation()}
          >
            <div className="h-full">
              {/* Enhanced Menu header */}
              <div className="flex items-center justify-between p-4 sm:p-6 border-b border-gray-200 bg-gradient-to-r from-violet-50 to-purple-50">
                <div className="flex items-center space-x-3">
                  <div className="w-10 h-10 bg-violet-100 rounded-xl flex items-center justify-center">
                    <img
                      src="/icons/TokenA-GRAT.svg"
                      alt="GRAT Token"
                      className="w-6 h-6"
                    />
                  </div>
                  <div>
                    <span className="text-base sm:text-lg font-bold block">
                      <span className="text-violet-700">$</span>
                      <span className="text-gray-800">GRAT</span>
                    </span>
                    <span className="text-xs text-gray-500">
                      {t("nav.menu") || "Menu"}
                    </span>
                  </div>
                </div>
                <button
                  onClick={closeMenu}
                  className="w-9 h-9 flex items-center justify-center rounded-xl hover:bg-white hover:shadow-md transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-violet-500 focus:ring-opacity-50"
                  aria-label="Close menu"
                >
                  <svg
                    className="w-5 h-5 text-gray-600"
                    fill="none"
                    stroke="currentColor"
                    viewBox="0 0 24 24"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth="2"
                      d="M6 18L18 6M6 6l12 12"
                    />
                  </svg>
                </button>
              </div>

              {/* Menu content */}
              <div className="flex-1 bg-gray-50 overflow-y-auto">
                {isConnected && userAddress ? (
                  <div className="p-4 sm:p-5 bg-gradient-to-r from-green-50 to-emerald-50 border-b border-green-200">
                    <div className="mb-3">
                      <div className="flex items-center mb-2">
                        <div className="relative mr-3">
                          <div className="w-3 h-3 bg-green-500 rounded-full animate-pulse"></div>
                          <div className="absolute inset-0 w-3 h-3 bg-green-400 rounded-full animate-ping"></div>
                        </div>
                        <div className="text-sm font-semibold text-green-700">
                          {t("wallet.connected")}
                        </div>
                      </div>
                      <div className="flex items-center space-x-2">
                        <div className="flex-1 text-xs text-gray-700 font-mono bg-green-100 px-2 py-1.5 rounded border border-green-300 overflow-hidden">
                          {formatAddress
                            ? formatAddress(userAddress)
                            : userAddress || "No Address"}
                        </div>
                        <button
                          ref={copyButtonRef}
                          onClick={async () => {
                            if (!userAddress) return;
                            try {
                              await navigator.clipboard.writeText(userAddress);
                              setCopySuccess(true);
                              setTimeout(() => {
                                setCopySuccess(false);
                              }, 2000);
                            } catch (err) {
                              console.log("Copy not supported:", err);
                            }
                          }}
                          className="p-1.5 hover:bg-green-100 rounded-lg transition-colors flex-shrink-0"
                          title={
                            copySuccess
                              ? t("wallet.copied") || "Copied!"
                              : t("wallet.copyAddress") || "Copy address"
                          }
                          disabled={!userAddress}
                        >
                          {copySuccess ? (
                            <svg
                              className="w-4 h-4 text-green-600"
                              fill="none"
                              stroke="currentColor"
                              viewBox="0 0 24 24"
                            >
                              <path
                                strokeLinecap="round"
                                strokeLinejoin="round"
                                strokeWidth="2"
                                d="M5 13l4 4L19 7"
                              />
                            </svg>
                          ) : (
                            <svg
                              className="w-4 h-4 text-green-600"
                              fill="none"
                              stroke="currentColor"
                              viewBox="0 0 24 24"
                            >
                              <path
                                strokeLinecap="round"
                                strokeLinejoin="round"
                                strokeWidth="2"
                                d="M8 16H6a2 2 0 01-2-2V6a2 2 0 012-2h8a2 2 0 012 2v2m-6 12h8a2 2 0 002-2v-8a2 2 0 00-2-2h-8a2 2 0 00-2 2v8a2 2 0 002 2z"
                              />
                            </svg>
                          )}
                        </button>
                      </div>
                    </div>

                    {/* Enhanced Stats - LaunchApp specific or general */}
                    {showQuickActions && balances ? (
                      <div className="grid grid-cols-2 gap-2">
                        <div className="bg-white bg-opacity-50 rounded-lg p-2">
                          <div className="text-xs text-green-600 font-medium">
                            {t("dashboard.tokenABalance") || "GRAT Balance"}
                          </div>
                          <div className="text-sm font-bold text-gray-700">
                            {parseFloat(balances.tokenA || "0").toFixed(2)}
                          </div>
                        </div>
                        <div className="bg-white bg-opacity-50 rounded-lg p-2">
                          <div className="text-xs text-green-600 font-medium">
                            {t("dashboard.tokenBBalance") || "TIPS Balance"}
                          </div>
                          <div className="text-sm font-bold text-gray-700">
                            {parseFloat(balances.tokenB || "0").toFixed(2)}
                          </div>
                        </div>
                      </div>
                    ) : (
                      <div className="grid grid-cols-2 gap-2">
                        <div className="bg-white bg-opacity-50 rounded-lg p-2">
                          <div className="text-xs text-green-600 font-medium">
                            {t("wallet.network") || "Network"}
                          </div>
                          <div className="text-sm font-bold text-gray-700">
                            Ethereum
                          </div>
                        </div>
                        <div className="bg-white bg-opacity-50 rounded-lg p-2">
                          <div className="text-xs text-green-600 font-medium">
                            {t("wallet.status") || "Status"}
                          </div>
                          <div className="text-sm font-bold text-green-700">
                            {t("wallet.active") || "Active"}
                          </div>
                        </div>
                      </div>
                    )}
                  </div>
                ) : (
                  <div className="p-4 sm:p-5 bg-gradient-to-r from-orange-50 to-amber-50 border-b border-orange-200">
                    <div className="flex items-center">
                      <div className="w-8 h-8 bg-orange-100 rounded-full flex items-center justify-center mr-3">
                        <svg
                          className="w-4 h-4 text-orange-600"
                          fill="none"
                          stroke="currentColor"
                          viewBox="0 0 24 24"
                        >
                          <path
                            strokeLinecap="round"
                            strokeLinejoin="round"
                            strokeWidth="2"
                            d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.732-.833-2.464 0L4.35 16.5c-.77.833.192 2.5 1.732 2.5z"
                          />
                        </svg>
                      </div>
                      <div>
                        <div className="text-sm font-semibold text-orange-700 mb-1">
                          {t("wallet.notConnected") || "Wallet Not Connected"}
                        </div>
                        <div className="text-xs text-orange-600">
                          {t("wallet.connectToStart") ||
                            "Connect wallet to get started"}
                        </div>
                      </div>
                    </div>
                  </div>
                )}

                {/* Enhanced Navigation links */}
                <nav className="p-4 sm:p-5 space-y-2">
                  <div className="text-xs font-semibold text-gray-500 uppercase tracking-wider mb-3">
                    {t("nav.navigation") || "Navigation"}
                  </div>

                  {/* Current page display */}
                  <div
                    className={`flex items-center py-3 px-4 rounded-xl border relative overflow-hidden ${
                      currentPageInfo.color === "violet"
                        ? "bg-gradient-to-r from-violet-50 to-purple-50 border-violet-200"
                        : "bg-gradient-to-r from-blue-50 to-indigo-50 border-blue-200"
                    }`}
                  >
                    <div
                      className={`w-9 h-9 rounded-lg flex items-center justify-center mr-3 ${
                        currentPageInfo.color === "violet"
                          ? "bg-violet-100"
                          : "bg-blue-100"
                      }`}
                    >
                      {currentPageInfo.icon === "home" ? (
                        <svg
                          className={`w-5 h-5 ${
                            currentPageInfo.color === "violet"
                              ? "text-violet-600"
                              : "text-blue-600"
                          }`}
                          fill="none"
                          stroke="currentColor"
                          viewBox="0 0 24 24"
                        >
                          <path
                            strokeLinecap="round"
                            strokeLinejoin="round"
                            strokeWidth="2"
                            d="M3 12l2-2m0 0l7-7 7 7M5 10v10a1 1 0 001 1h3m10-11l2 2m-2-2v10a1 1 0 01-1 1h-3m-6 0a1 1 0 001-1v-4a1 1 0 011-1h2a1 1 0 011 1v4a1 1 0 001 1m-6 0h6"
                          />
                        </svg>
                      ) : (
                        <svg
                          className={`w-5 h-5 ${
                            currentPageInfo.color === "violet"
                              ? "text-violet-600"
                              : "text-blue-600"
                          }`}
                          fill="none"
                          stroke="currentColor"
                          viewBox="0 0 24 24"
                        >
                          <path
                            strokeLinecap="round"
                            strokeLinejoin="round"
                            strokeWidth="2"
                            d="M13 10V3L4 14h7v7l9-11h-7z"
                          />
                        </svg>
                      )}
                    </div>
                    <div className="flex-1">
                      <span
                        className={`text-base font-semibold ${
                          currentPageInfo.color === "violet"
                            ? "text-violet-700"
                            : "text-blue-700"
                        }`}
                      >
                        {currentPageInfo.title}
                      </span>
                      <div
                        className={`text-xs ${
                          currentPageInfo.color === "violet"
                            ? "text-violet-600"
                            : "text-blue-600"
                        }`}
                      >
                        {currentPageInfo.description}
                      </div>
                    </div>
                    <div className="absolute -top-1 -right-1">
                      <div
                        className={`w-3 h-3 rounded-full current-page-indicator ${
                          currentPageInfo.color === "violet"
                            ? "bg-violet-500"
                            : "bg-blue-500"
                        }`}
                      ></div>
                    </div>
                  </div>

                  {/* Other page links */}
                  <div className="space-y-1 mt-3">
                    {location.pathname !== "/" && (
                      <Link
                        to="/"
                        className="mobile-menu-item flex items-center py-2 px-4 rounded-lg hover:bg-gray-100 transition-all duration-200 group"
                        onClick={closeMenu}
                      >
                        <div className="w-8 h-8 bg-blue-100 rounded-lg flex items-center justify-center mr-3 group-hover:bg-blue-200 transition-colors">
                          <svg
                            className="w-4 h-4 text-blue-600"
                            fill="none"
                            stroke="currentColor"
                            viewBox="0 0 24 24"
                          >
                            <path
                              strokeLinecap="round"
                              strokeLinejoin="round"
                              strokeWidth="2"
                              d="M3 12l2-2m0 0l7-7 7 7M5 10v10a1 1 0 001 1h3m10-11l2 2m-2-2v10a1 1 0 01-1 1h-3m-6 0a1 1 0 001-1v-4a1 1 0 011-1h2a1 1 0 011 1v4a1 1 0 001 1m-6 0h6"
                            />
                          </svg>
                        </div>
                        <div>
                          <span className="text-sm text-gray-700 font-medium">
                            {t("nav.home")}
                          </span>
                          <div className="text-xs text-gray-500">
                            {t("nav.homeDesc")}
                          </div>
                        </div>
                      </Link>
                    )}

                    {location.pathname !== "/app" && (
                      <Link
                        to="/app"
                        className="mobile-menu-item flex items-center py-2 px-4 rounded-lg hover:bg-gray-100 transition-all duration-200 group"
                        onClick={closeMenu}
                      >
                        <div className="w-8 h-8 bg-violet-100 rounded-lg flex items-center justify-center mr-3 group-hover:bg-violet-200 transition-colors">
                          <svg
                            className="w-4 h-4 text-violet-600"
                            fill="none"
                            stroke="currentColor"
                            viewBox="0 0 24 24"
                          >
                            <path
                              strokeLinecap="round"
                              strokeLinejoin="round"
                              strokeWidth="2"
                              d="M13 10V3L4 14h7v7l9-11h-7z"
                            />
                          </svg>
                        </div>
                        <div>
                          <span className="text-sm text-gray-700 font-medium">
                            {t("nav.launchApp")}
                          </span>
                          <div className="text-xs text-gray-500">
                            Enter application
                          </div>
                        </div>
                      </Link>
                    )}
                  </div>
                </nav>
              </div>

              {/* Language switcher - Fixed at bottom */}
              <div className="flex-shrink-0 p-4 sm:p-5 border-t border-gray-200 bg-gradient-to-r from-gray-50 to-slate-50">
                <div className="flex items-center mb-3">
                  <div className="w-6 h-6 bg-gradient-to-r from-violet-500 to-purple-500 rounded-lg flex items-center justify-center mr-2">
                    <svg
                      className="w-3 h-3 text-white"
                      fill="none"
                      stroke="currentColor"
                      viewBox="0 0 24 24"
                    >
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth="2"
                        d="M3 5h12M9 3v2m1.048 9.5A18.022 18.022 0 016.412 9m6.088 9h7M11 21l5-10 5 10M12.751 5C11.783 10.77 8.07 15.61 3 18.129"
                      />
                    </svg>
                  </div>
                  <div className="text-xs font-semibold text-gray-600 uppercase tracking-wider">
                    {t("languages.title") || "Language"}
                  </div>
                </div>
                <div className="bg-white rounded-xl p-3 border border-gray-200 shadow-sm hover:shadow-md transition-shadow relative z-[60] mb-3">
                  <LanguageSwitcher className="w-full" />
                </div>
              </div>

              {/* Enhanced Menu footer */}
              <div className="flex-shrink-0 p-4 sm:p-5 border-t border-gray-200 bg-gradient-to-r from-gray-50 to-slate-50">
                <div className="space-y-3">
                  {/* Action buttons */}
                  {isConnected ? (
                    <button
                      onClick={() => {
                        handleDisconnect();
                        closeMenu();
                      }}
                      className="w-full secondary-btn py-3 sm:py-4 rounded-xl font-semibold text-sm sm:text-base transition-all duration-200 hover:scale-105 flex items-center justify-center gap-2 shadow-sm hover:shadow-md"
                    >
                      <svg
                        className="w-4 h-4"
                        fill="none"
                        stroke="currentColor"
                        viewBox="0 0 24 24"
                      >
                        <path
                          strokeLinecap="round"
                          strokeLinejoin="round"
                          strokeWidth="2"
                          d="M17 16l4-4m0 0l-4-4m4 4H7m6 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h4a3 3 0 013 3v1"
                        />
                      </svg>
                      {t("wallet.disconnect")}
                    </button>
                  ) : (
                    <button
                      onClick={() => {
                        onConnectWallet();
                        closeMenu();
                      }}
                      className="w-full primary-btn py-3 sm:py-4 rounded-xl font-semibold text-sm sm:text-base transition-all duration-200 hover:scale-105 shadow-lg hover:shadow-xl flex items-center justify-center gap-2"
                      disabled={isConnecting}
                    >
                      {isConnecting ? (
                        <>
                          <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
                          {t("wallet.connecting")}
                        </>
                      ) : (
                        <>
                          <svg
                            className="w-4 h-4"
                            fill="none"
                            stroke="currentColor"
                            viewBox="0 0 24 24"
                          >
                            <path
                              strokeLinecap="round"
                              strokeLinejoin="round"
                              strokeWidth="2"
                              d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z"
                            />
                          </svg>
                          {t("wallet.connectWallet")}
                        </>
                      )}
                    </button>
                  )}
                </div>
              </div>
            </div>
          </div>
        </div>
      )}
    </>
  );
};

export default MobileNavigation;
