import React, { useState, useEffect, useCallback } from "react";
import { ethers } from "ethers";
import { useTranslation } from "react-i18next";
import {
  formatTokenAmount,
  formatUSDTAmount,
  formatTimestamp,
} from "../utils/formatNumbers";

const ClaimHistoryModal = ({
  isOpen,
  onClose,
  contracts,
  userAddress,
  showNotification,
}) => {
  const { t } = useTranslation();
  const [claimRecords, setClaimRecords] = useState([]);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState(null);
  const [acceleratedInfo, setAcceleratedInfo] = useState({
    currentUnused: "0",
    totalReceived: "0",
    totalUsed: "0",
  });

  // Load claim records and accelerated release info
  const loadClaimHistory = useCallback(async () => {
    if (!contracts.miningContract || !userAddress) return;

    setIsLoading(true);
    setError(null);

    try {
      // Get claim record count
      const recordCount = await contracts.miningContract.getUserClaimRecordCount(userAddress);
      
      // Get accelerated release info
      const [currentUnused, totalReceived, totalUsed] = await contracts.miningContract.getUserAcceleratedReleaseInfo(userAddress);
      setAcceleratedInfo({
        currentUnused: ethers.formatEther(currentUnused),
        totalReceived: ethers.formatEther(totalReceived),
        totalUsed: ethers.formatEther(totalUsed),
      });

      const records = [];

      // Load recent records (max 20)
      if (recordCount > 0) {
        const limit = Math.min(Number(recordCount), 20);
        const [amounts, usdtValues, timestamps, mechanisms, normalAmounts, acceleratedAmounts] = 
          await contracts.miningContract.getUserRecentClaimRecords(userAddress, limit);

        for (let i = 0; i < amounts.length; i++) {
          records.push({
            amount: ethers.formatEther(amounts[i]),
            usdtValue: ethers.formatEther(usdtValues[i]),
            timestamp: new Date(Number(timestamps[i]) * 1000),
            mechanism: mechanisms[i],
            normalAmount: ethers.formatEther(normalAmounts[i]),
            acceleratedAmount: ethers.formatEther(acceleratedAmounts[i]),
          });
        }
      }

      setClaimRecords(records);
    } catch (error) {
      console.error("Failed to load claim history:", error);
      setError(error.message);
    } finally {
      setIsLoading(false);
    }
  }, [contracts.miningContract, userAddress]);

  // Load data when modal opens
  useEffect(() => {
    if (isOpen) {
      loadClaimHistory();
      
      // Set up refresh interval
      const interval = setInterval(loadClaimHistory, 30000); // Refresh every 30 seconds
      return () => clearInterval(interval);
    }
  }, [isOpen, loadClaimHistory]);

  // Calculate total claimed amounts
  const totalClaimed = claimRecords.reduce((sum, record) => {
    return sum + parseFloat(record.amount);
  }, 0);

  const totalUsdtValue = claimRecords.reduce((sum, record) => {
    return sum + parseFloat(record.usdtValue);
  }, 0);

  const totalNormalClaimed = claimRecords.reduce((sum, record) => {
    return sum + parseFloat(record.normalAmount);
  }, 0);

  const totalAcceleratedClaimed = claimRecords.reduce((sum, record) => {
    return sum + parseFloat(record.acceleratedAmount);
  }, 0);

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 z-50 overflow-y-auto">
      <div className="flex items-center justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:items-center sm:p-0">
        {/* Background overlay */}
        <div
          className="fixed inset-0 bg-black bg-opacity-50 transition-opacity"
          onClick={onClose}
        />

        {/* Modal panel */}
        <div className="inline-block align-bottom bg-white rounded-t-lg lg:rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-6xl sm:w-full w-full max-h-screen">
          <div className="bg-white h-full flex flex-col max-h-screen">
            {/* Modal header */}
            <div className="flex items-center justify-between p-4 lg:p-6 border-b border-gray-200 flex-shrink-0">
              <div className="flex items-center">
                <div className="w-8 h-8 lg:w-10 lg:h-10 rounded-full flex items-center justify-center mr-3 bg-blue-100">
                  <svg className="w-4 h-4 lg:w-5 lg:h-5 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 7h6m0 10v-3m-3 3h.01M9 17h.01M9 14h.01M12 14h.01M15 11h.01M12 11h.01M9 11h.01M7 21h10a2 2 0 002-2V5a2 2 0 00-2-2H7a2 2 0 00-2 2v14a2 2 0 002 2z" />
                  </svg>
                </div>
                <div>
                  <h2 className="text-xl lg:text-2xl font-bold text-gray-900">
                    {t("claim.history.title")}
                  </h2>
                  <p className="text-sm text-gray-500">{t("claim.history.subtitle")}</p>
                </div>
              </div>
              <button
                onClick={onClose}
                className="text-gray-400 hover:text-gray-600 transition-colors p-1"
              >
                <svg className="w-5 h-5 lg:w-6 lg:h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M6 18L18 6M6 6l12 12" />
                </svg>
              </button>
            </div>

            {/* Modal content */}
            <div className="flex-1 p-4 lg:p-6 overflow-y-auto min-h-0">
              {isLoading ? (
                <div className="flex items-center justify-center py-12">
                  <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mr-3"></div>
                  <span className="text-gray-600">{t("claim.history.loading")}</span>
                </div>
              ) : error ? (
                <div className="text-center py-12">
                  <div className="w-16 h-16 bg-red-100 rounded-full flex items-center justify-center mx-auto mb-4">
                    <svg className="w-8 h-8 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                    </svg>
                  </div>
                  <h3 className="text-lg font-semibold text-gray-900 mb-2">{t("claim.history.loadError")}</h3>
                  <p className="text-gray-600 mb-4">{error}</p>
                  <button
                    onClick={loadClaimHistory}
                    className="secondary-btn px-4 py-2 rounded-lg"
                  >
                    {t("common.retry")}
                  </button>
                </div>
              ) : (
                <>
                  {/* Summary cards */}
                  <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-6">
                    <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
                      <div className="text-sm text-blue-700 mb-1">{t("claim.history.totalClaimed")}</div>
                      <div className="text-2xl font-bold text-blue-600">
                        {totalClaimed.toFixed(4)}
                      </div>
                      <div className="text-xs text-gray-500">TIPS</div>
                    </div>
                    
                    <div className="bg-green-50 border border-green-200 rounded-lg p-4">
                      <div className="text-sm text-green-700 mb-1">{t("claim.history.totalValue")}</div>
                      <div className="text-2xl font-bold text-green-600">
                        {totalUsdtValue.toFixed(4)}
                      </div>
                      <div className="text-xs text-gray-500">USDT</div>
                    </div>
                    
                    <div className="bg-purple-50 border border-purple-200 rounded-lg p-4">
                      <div className="text-sm text-purple-700 mb-1">{t("claim.history.normalClaimed")}</div>
                      <div className="text-2xl font-bold text-purple-600">
                        {totalNormalClaimed.toFixed(4)}
                      </div>
                      <div className="text-xs text-gray-500">USDT</div>
                    </div>
                    
                    <div className="bg-orange-50 border border-orange-200 rounded-lg p-4">
                      <div className="text-sm text-orange-700 mb-1">{t("claim.history.acceleratedClaimed")}</div>
                      <div className="text-2xl font-bold text-orange-600">
                        {totalAcceleratedClaimed.toFixed(4)}
                      </div>
                      <div className="text-xs text-gray-500">USDT</div>
                    </div>
                  </div>

                  {/* Accelerated release info */}
                  <div className="bg-gray-50 rounded-lg p-4 mb-6">
                    <h3 className="text-lg font-semibold text-gray-900 mb-3">{t("claim.history.acceleratedInfo")}</h3>
                    <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                      <div>
                        <div className="text-sm text-gray-600 mb-1">{t("claim.history.currentUnused")}</div>
                        <div className="text-lg font-bold text-green-600">
                          {parseFloat(acceleratedInfo.currentUnused).toFixed(4)} USDT
                        </div>
                      </div>
                      <div>
                        <div className="text-sm text-gray-600 mb-1">{t("claim.history.totalReceived")}</div>
                        <div className="text-lg font-bold text-blue-600">
                          {parseFloat(acceleratedInfo.totalReceived).toFixed(4)} USDT
                        </div>
                      </div>
                      <div>
                        <div className="text-sm text-gray-600 mb-1">{t("claim.history.totalUsed")}</div>
                        <div className="text-lg font-bold text-orange-600">
                          {parseFloat(acceleratedInfo.totalUsed).toFixed(4)} USDT
                        </div>
                      </div>
                    </div>
                  </div>

                  {/* Records list */}
                  {claimRecords.length === 0 ? (
                    <div className="text-center py-12">
                      <div className="w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-4">
                        <svg className="w-8 h-8 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2" />
                        </svg>
                      </div>
                      <h3 className="text-lg font-semibold text-gray-900 mb-2">{t("claim.history.noRecords")}</h3>
                      <p className="text-gray-600">{t("claim.history.startClaiming")}</p>
                    </div>
                  ) : (
                    <div className="space-y-4">
                      <h3 className="text-lg font-semibold text-gray-900 mb-3">
                        {t("claim.history.recentRecords")} ({claimRecords.length})
                      </h3>
                      
                      {claimRecords.map((record, index) => (
                        <div
                          key={index}
                          className="bg-white rounded-lg p-4 border border-gray-200 shadow-sm"
                        >
                          <div className="flex justify-between items-start mb-3">
                            <div className="flex items-center">
                              <div className={`w-8 h-8 rounded-full flex items-center justify-center mr-3 ${
                                record.mechanism === 1 ? 'bg-violet-100' : 'bg-green-100'
                              }`}>
                                <span className={`text-xs font-bold ${
                                  record.mechanism === 1 ? 'text-violet-600' : 'text-green-600'
                                }`}>
                                  {record.mechanism === 1 ? 'G→T' : 'T→T'}
                                </span>
                              </div>
                              <div>
                                <div className="text-sm font-medium text-gray-900">
                                  {formatTimestamp(record.timestamp, true)}
                                </div>
                                <div className="text-xs text-gray-500">
                                  {record.mechanism === 1 ? 'GRAT → TIPS' : 'TIPS → TIPS'}
                                </div>
                              </div>
                            </div>
                            
                            <div className="text-right">
                              <div className="text-lg font-bold text-blue-600">
                                +{parseFloat(record.amount).toFixed(4)} TIPS
                              </div>
                              <div className="text-sm text-gray-500">
                                ≈ {parseFloat(record.usdtValue).toFixed(4)} USDT
                              </div>
                            </div>
                          </div>

                          {/* Breakdown */}
                          <div className="grid grid-cols-2 gap-4 pt-3 border-t border-gray-100">
                            <div>
                              <div className="text-xs text-gray-500">{t("claim.history.normalRelease")}</div>
                              <div className="text-sm font-medium text-gray-900">
                                {parseFloat(record.normalAmount).toFixed(4)} USDT
                              </div>
                            </div>
                            <div>
                              <div className="text-xs text-gray-500">{t("claim.history.acceleratedRelease")}</div>
                              <div className="text-sm font-medium text-orange-600">
                                {parseFloat(record.acceleratedAmount).toFixed(4)} USDT
                              </div>
                            </div>
                          </div>
                        </div>
                      ))}
                    </div>
                  )}
                </>
              )}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ClaimHistoryModal;