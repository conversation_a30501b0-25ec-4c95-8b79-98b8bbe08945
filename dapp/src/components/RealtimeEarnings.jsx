import React, { useState, useEffect, useCallback } from "react";
import { ethers } from "ethers";
import { useTranslation } from "react-i18next";

const RealtimeEarnings = ({
  contracts,
  userAddress,
  provider,
  showNotification,
  onClaimRewards,
}) => {
  const { t } = useTranslation();

  const [earningsData, setEarningsData] = useState({
    totalClaimableValue: "0",
    totalClaimableTokenB: "0",
    currentEarningRate: "0",
    dailyEarningRate: "0",
    totalValueLocked: "0",
    totalValueReleased: "0",
    estimatedTimeToComplete: "0",
    nextUpdateTime: "0",
    isLoading: true,
    error: null,
  });

  const [refreshInterval, setRefreshInterval] = useState(null);

  // Load real-time earnings data
  const loadEarningsData = useCallback(async () => {
    if (!contracts.miningContract || !userAddress) {
      return;
    }

    try {
      setEarningsData(prev => ({ ...prev, isLoading: true, error: null }));

      // Call the real-time earnings function
      const result = await contracts.miningContract.getUserRealtimeEarnings(userAddress);
      
      // Parse the returned values
      const [
        totalClaimableValue,
        totalClaimableTokenB,
        currentEarningRate,
        dailyEarningRate,
        totalValueLocked,
        totalValueReleased,
        estimatedTimeToComplete,
        nextUpdateTime
      ] = result;

      setEarningsData({
        totalClaimableValue: ethers.formatEther(totalClaimableValue),
        totalClaimableTokenB: ethers.formatEther(totalClaimableTokenB),
        currentEarningRate: ethers.formatEther(currentEarningRate),
        dailyEarningRate: ethers.formatEther(dailyEarningRate),
        totalValueLocked: ethers.formatEther(totalValueLocked),
        totalValueReleased: ethers.formatEther(totalValueReleased),
        estimatedTimeToComplete: estimatedTimeToComplete.toString(),
        nextUpdateTime: nextUpdateTime.toString(),
        isLoading: false,
        error: null,
      });
    } catch (error) {
      console.error("Failed to load real-time earnings:", error);
      setEarningsData(prev => ({
        ...prev,
        isLoading: false,
        error: error.message,
      }));
    }
  }, [contracts.miningContract, userAddress]);

  // Format time duration
  const formatDuration = useCallback((seconds) => {
    const days = Math.floor(seconds / 86400);
    const hours = Math.floor((seconds % 86400) / 3600);
    const mins = Math.floor((seconds % 3600) / 60);
    
    if (days > 0) return `${days}${t("common.days")} ${hours}${t("common.hours")}`;
    if (hours > 0) return `${hours}${t("common.hours")} ${mins}${t("common.minutes")}`;
    return `${mins}${t("common.minutes")}`;
  }, [t]);

  // Calculate release progress percentage
  const releaseProgress = useCallback(() => {
    const locked = parseFloat(earningsData.totalValueLocked);
    const released = parseFloat(earningsData.totalValueReleased);
    
    if (locked === 0) return 0;
    return Math.min((released / locked) * 100, 100);
  }, [earningsData.totalValueLocked, earningsData.totalValueReleased]);

  // Load data on component mount and user change
  useEffect(() => {
    loadEarningsData();
  }, [loadEarningsData]);

  // Set up auto-refresh (every 30 seconds)
  useEffect(() => {
    if (refreshInterval) clearInterval(refreshInterval);
    
    const interval = setInterval(() => {
      loadEarningsData();
    }, 30000); // Refresh every 30 seconds
    
    setRefreshInterval(interval);
    
    return () => {
      if (interval) clearInterval(interval);
    };
  }, [loadEarningsData]);

  // Clean up interval on unmount
  useEffect(() => {
    return () => {
      if (refreshInterval) clearInterval(refreshInterval);
    };
  }, [refreshInterval]);

  if (earningsData.isLoading) {
    return (
      <div className="card p-6">
        <div className="flex items-center justify-center py-8">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-violet-600 mr-3"></div>
          <span className="text-gray-600">{t("earnings.loading")}</span>
        </div>
      </div>
    );
  }

  if (earningsData.error) {
    return (
      <div className="card p-6">
        <div className="text-center py-8">
          <div className="w-16 h-16 bg-red-100 rounded-full flex items-center justify-center mx-auto mb-4">
            <svg className="w-8 h-8 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
            </svg>
          </div>
          <h3 className="text-lg font-semibold text-gray-900 mb-2">{t("earnings.loadError")}</h3>
          <p className="text-gray-600 mb-4">{earningsData.error}</p>
          <button
            onClick={loadEarningsData}
            className="secondary-btn px-4 py-2 rounded-lg"
          >
            {t("common.retry")}
          </button>
        </div>
      </div>
    );
  }

  const progress = releaseProgress();
  const hasClaimable = parseFloat(earningsData.totalClaimableValue) > 0;

  return (
    <div className="card p-6">
      {/* Header */}
      <div className="flex items-center justify-between mb-6">
        <div className="flex items-center">
          <div className="w-12 h-12 bg-gradient-to-r from-violet-100 to-purple-100 rounded-xl flex items-center justify-center mr-4">
            <svg className="w-6 h-6 text-violet-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M13 10V3L4 14h7v7l9-11h-7z" />
            </svg>
          </div>
          <div>
            <h3 className="text-xl font-bold text-gray-900">{t("earnings.realtimeTitle")}</h3>
            <p className="text-sm text-gray-500">{t("earnings.lastUpdate")}: {t("earnings.realtime")}</p>
          </div>
        </div>
        
        <button
          onClick={loadEarningsData}
          className="p-2 hover:bg-gray-100 rounded-lg transition-colors"
          title={t("earnings.refresh")}
        >
          <svg className="w-5 h-5 text-gray-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
          </svg>
        </button>
      </div>

      {/* Main Stats Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-6">
        {/* Total Claimable */}
        <div className="bg-green-50 border border-green-200 rounded-lg p-4">
          <div className="text-sm text-green-700 mb-1">{t("earnings.totalClaimable")}</div>
          <div className="text-2xl font-bold text-green-600 mb-1">
            {parseFloat(earningsData.totalClaimableTokenB).toFixed(4)}
          </div>
          <div className="text-xs text-green-600">TIPS</div>
          <div className="text-xs text-gray-500 mt-1">
            ≈ {parseFloat(earningsData.totalClaimableValue).toFixed(4)} USDT
          </div>
        </div>

        {/* Daily Earning Rate */}
        <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
          <div className="text-sm text-blue-700 mb-1">{t("earnings.dailyRate")}</div>
          <div className="text-2xl font-bold text-blue-600 mb-1">
            {parseFloat(earningsData.dailyEarningRate).toFixed(4)}
          </div>
          <div className="text-xs text-blue-600">USDT/day</div>
        </div>

        {/* Total Locked */}
        <div className="bg-orange-50 border border-orange-200 rounded-lg p-4">
          <div className="text-sm text-orange-700 mb-1">{t("earnings.totalLocked")}</div>
          <div className="text-2xl font-bold text-orange-600 mb-1">
            {parseFloat(earningsData.totalValueLocked).toFixed(2)}
          </div>
          <div className="text-xs text-orange-600">USDT</div>
        </div>

        {/* Progress */}
        <div className="bg-purple-50 border border-purple-200 rounded-lg p-4">
          <div className="text-sm text-purple-700 mb-1">{t("earnings.progress")}</div>
          <div className="text-2xl font-bold text-purple-600 mb-1">
            {progress.toFixed(1)}%
          </div>
          <div className="text-xs text-purple-600">{t("earnings.released")}</div>
        </div>
      </div>

      {/* Progress Bar */}
      <div className="mb-6">
        <div className="flex items-center justify-between text-sm text-gray-600 mb-2">
          <span>{t("earnings.releaseProgress")}</span>
          <span>{parseFloat(earningsData.totalValueReleased).toFixed(2)} / {parseFloat(earningsData.totalValueLocked).toFixed(2)} USDT</span>
        </div>
        <div className="w-full bg-gray-200 rounded-full h-3">
          <div 
            className="bg-gradient-to-r from-violet-500 to-purple-600 h-3 rounded-full transition-all duration-500 ease-out"
            style={{ width: `${progress}%` }}
          ></div>
        </div>
      </div>

      {/* Estimated Time to Complete */}
      {parseFloat(earningsData.estimatedTimeToComplete) > 0 && (
        <div className="bg-amber-50 border border-amber-200 rounded-lg p-4 mb-6">
          <div className="flex items-center">
            <svg className="w-5 h-5 text-amber-600 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
            </svg>
            <div>
              <div className="text-sm font-medium text-amber-800">
                {t("earnings.estimatedCompletion")}
              </div>
              <div className="text-lg font-bold text-amber-700">
                {formatDuration(parseInt(earningsData.estimatedTimeToComplete))}
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Action Buttons */}
      {hasClaimable && (
        <div className="flex flex-col sm:flex-row gap-3">
          <button
            onClick={() => onClaimRewards && onClaimRewards(1)}
            className="flex-1 primary-btn py-3 px-4 rounded-lg font-semibold flex items-center justify-center"
          >
            <svg className="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1" />
            </svg>
            {t("earnings.claimMechanism1")}
          </button>
          
          <button
            onClick={() => onClaimRewards && onClaimRewards(2)}
            className="flex-1 secondary-btn py-3 px-4 rounded-lg font-semibold flex items-center justify-center"
          >
            <svg className="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1" />
            </svg>
            {t("earnings.claimMechanism2")}
          </button>
        </div>
      )}

      {/* Info Notice */}
      <div className="mt-6 bg-gray-50 border border-gray-200 rounded-lg p-4">
        <div className="flex items-start">
          <svg className="w-5 h-5 text-gray-500 mt-0.5 mr-3 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
          </svg>
          <div className="text-sm text-gray-700">
            <p className="font-medium mb-1">{t("earnings.infoTitle")}</p>
            <p>{t("earnings.infoDesc")}</p>
          </div>
        </div>
      </div>
    </div>
  );
};

export default RealtimeEarnings;