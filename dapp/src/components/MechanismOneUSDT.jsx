import React, { useState, useEffect, useCallback } from "react";
import { ethers } from "ethers";
import { useTranslation } from "react-i18next";
import { CONTRACT_ADDRESSES, ERC20_ABI } from "../utils/constants";

const MechanismOneUSDT = ({
  contracts,
  provider,
  signer,
  userAddress,
  showNotification,
  showLoading,
  hideLoading,
  onTransactionComplete,
  inviterFromUrl = null,
}) => {
  const { t } = useTranslation();

  // State for USDT mechanism
  const [usdtBalance, setUsdtBalance] = useState("0");
  const [usdtInput, setUsdtInput] = useState("");
  const [previewData, setPreviewData] = useState({
    tokenAAmount: "0",
    expectedTokenB: "0",
    isLoading: false,
    error: null,
  });

  // Load USDT balance
  const loadUsdtBalance = useCallback(async () => {
    if (!userAddress || !provider) return;

    try {
      const usdtContract = new ethers.Contract(
        CONTRACT_ADDRESSES.USDT,
        ERC20_ABI,
        provider
      );
      const balance = await usdtContract.balanceOf(userAddress);
      setUsdtBalance(ethers.formatUnits(balance, 18)); // USDT uses 18 decimals
    } catch (error) {
      console.error("Failed to load USDT balance:", error);
    }
  }, [userAddress, provider]);

  // Preview USDT to TokenA conversion
  const previewConversion = useCallback(async (usdtAmount) => {
    if (!contracts.miningContract || !usdtAmount || parseFloat(usdtAmount) <= 0) {
      setPreviewData({
        tokenAAmount: "0",
        expectedTokenB: "0",
        isLoading: false,
        error: null,
      });
      return;
    }

    setPreviewData(prev => ({ ...prev, isLoading: true, error: null }));

    try {
      const usdtAmountWei = ethers.parseUnits(usdtAmount, 18);
      console.log("Preview calculation for USDT amount:", usdtAmount, "Wei:", usdtAmountWei.toString());
      
      // Check minimum amount (1 USDT minimum)
      const minAmount = ethers.parseUnits("1", 18);
      if (usdtAmountWei < minAmount) {
        throw new Error("Minimum amount is 1 USDT");
      }
      
      let tokenAAmount, expectedTokenB;
      
      try {
        // Try the new preview method first
        console.log("Calling previewUSDTToTokenA with amount:", usdtAmountWei.toString());
        const tokenAAmountWei = await contracts.miningContract.previewUSDTToTokenA(usdtAmountWei);
        tokenAAmount = ethers.formatEther(tokenAAmountWei);
        console.log("Preview result - TokenA amount:", tokenAAmount);
        
        // Try to get reward multiplier
        let rewardMultiplier;
        try {
          console.log("Calling REWARD_MULTIPLIER()...");
          rewardMultiplier = await contracts.miningContract.REWARD_MULTIPLIER();
          console.log("REWARD_MULTIPLIER result:", rewardMultiplier.toString());
        } catch (multiplierError) {
          console.warn("REWARD_MULTIPLIER failed, using default value 2:", multiplierError);
          rewardMultiplier = 2n; // Use default value
        }
        
        // Try to get TokenB price
        let tokenBPrice;
        try {
          console.log("Calling getTokenPrice for TokenB...");
          tokenBPrice = await contracts.miningContract.getTokenPrice(CONTRACT_ADDRESSES.TokenB);
          console.log("TokenB price:", ethers.formatEther(tokenBPrice));
        } catch (priceError) {
          console.warn("getTokenPrice failed, using simple 2x calculation:", priceError);
          // Simple fallback: 2x USDT value
          expectedTokenB = ethers.formatEther(usdtAmountWei * BigInt(rewardMultiplier));
          console.log("Using simple 2x calculation - Expected TokenB:", expectedTokenB);
          // Set the preview data and exit this try block
          setPreviewData({
            tokenAAmount,
            expectedTokenB,
            isLoading: false,
            error: null,
          });
          return;
        }
        
        // Calculate expected TokenB using price
        const expectedTokenBWei = (usdtAmountWei * BigInt(rewardMultiplier) * ethers.parseEther("1")) / tokenBPrice;
        expectedTokenB = ethers.formatEther(expectedTokenBWei);
        console.log("Calculated expected TokenB:", expectedTokenB);
        
      } catch (previewError) {
        console.warn("Preview method failed, using simple estimations:", previewError);
        
        // Simple fallback: assume reasonable values
        // Estimate TokenA based on typical price (e.g., 1 USDT = ~1 TokenA)
        tokenAAmount = usdtAmount; // Simple 1:1 estimation
        
        // Simple 2x USDT value for TokenB
        expectedTokenB = (parseFloat(usdtAmount) * 2).toString();
        
        console.log("Using simple estimation - TokenA:", tokenAAmount, "TokenB:", expectedTokenB);
      }

      setPreviewData({
        tokenAAmount,
        expectedTokenB,
        isLoading: false,
        error: null,
      });
    } catch (error) {
      console.error("Preview failed:", error);
      
      // Provide more user-friendly error messages
      let errorMessage = error.message;
      if (error.message.includes("execution reverted")) {
        errorMessage = "Unable to calculate preview. Please check if there is sufficient liquidity in the DEX or try a smaller amount.";
      } else if (error.message.includes("require(false)")) {
        errorMessage = "Transaction requirements not met. This might be due to insufficient DEX liquidity or price protection limits.";
      }
      
      setPreviewData({
        tokenAAmount: "0",
        expectedTokenB: "0",
        isLoading: false,
        error: errorMessage,
      });
    }
  }, [contracts.miningContract]);

  // Execute USDT purchase and burn
  const executeUsdtPurchase = useCallback(async () => {
    console.log("=== USDT Purchase Debug Info ===");
    console.log("Input amount:", usdtInput);
    console.log("USDT balance:", usdtBalance);
    console.log("User address:", userAddress);
    console.log("Signer available:", !!signer);
    
    if (!contracts.miningContract || !usdtInput || parseFloat(usdtInput) <= 0) {
      showNotification(t("mining.mechanism1.enterValidAmount"), "error");
      return;
    }

    const inputAmount = parseFloat(usdtInput);
    const balanceAmount = parseFloat(usdtBalance);
    
    console.log("Input amount (parsed):", inputAmount);
    console.log("Balance amount (parsed):", balanceAmount);
    
    if (inputAmount > balanceAmount) {
      showNotification(t("mining.mechanism1.insufficientBalance"), "error");
      console.log("❌ Insufficient balance check failed");
      return;
    }
    

    try {
      showLoading(t("mining.mechanism1.processing"));
      const usdtAmountWei = ethers.parseUnits(usdtInput, 18);
      console.log("USDT amount in Wei:", usdtAmountWei.toString());

      // Get USDT contract with signer for transactions
      const usdtContract = new ethers.Contract(CONTRACT_ADDRESSES.USDT, ERC20_ABI, signer);
      console.log("USDT contract address (frontend):", CONTRACT_ADDRESSES.USDT);
      console.log("Mining contract address:", await contracts.miningContract.getAddress());
      
      // Check what USDT address the mining contract is actually using
      try {
        const contractUSDTAddress = await contracts.miningContract.USDT();
        console.log("USDT contract address (mining contract):", contractUSDTAddress);
        
        if (contractUSDTAddress.toLowerCase() !== CONTRACT_ADDRESSES.USDT.toLowerCase()) {
          console.error("❌ USDT address mismatch!");
          console.error("Frontend USDT:", CONTRACT_ADDRESSES.USDT);
          console.error("Contract USDT:", contractUSDTAddress);
          throw new Error(`USDT address mismatch. Frontend: ${CONTRACT_ADDRESSES.USDT}, Contract: ${contractUSDTAddress}`);
        }
      } catch (usdtCheckError) {
        console.warn("Could not verify USDT address from contract:", usdtCheckError);
      }

      // Double-check actual USDT balance on-chain
      const actualBalance = await usdtContract.balanceOf(userAddress);
      console.log("Actual on-chain USDT balance:", ethers.formatUnits(actualBalance, 18));
      
      if (actualBalance < usdtAmountWei) {
        throw new Error(`Insufficient USDT balance. Required: ${ethers.formatUnits(usdtAmountWei, 18)}, Available: ${ethers.formatUnits(actualBalance, 18)}`);
      }

      // Check and approve USDT
      const allowance = await usdtContract.allowance(
        userAddress,
        await contracts.miningContract.getAddress()
      );
      console.log("Current allowance:", ethers.formatUnits(allowance, 18));
      
      if (allowance < usdtAmountWei) {
        console.log("Approving USDT...");
        showLoading(t("mining.mechanism1.approvingUSDT"));
        const approveTx = await usdtContract.approve(
          await contracts.miningContract.getAddress(),
          usdtAmountWei
        );
        console.log("Approval transaction:", approveTx.hash);
        await approveTx.wait();
        console.log("Approval confirmed");
      } else {
        console.log("Sufficient allowance already exists");
      }

      // Execute purchase
      showLoading(t("mining.mechanism1.purchasing"));
      const inviter = inviterFromUrl || ethers.ZeroAddress;
      console.log("About to call buyAndBurnTokenAWithUSDT with:");
      console.log("- USDT amount (Wei):", usdtAmountWei.toString());
      console.log("- Inviter address:", inviter);
      console.log("- Contract address:", await contracts.miningContract.getAddress());
      console.log("- Signer address:", await signer.getAddress());
      
      // Check contract's TokenA balance before transaction
      try {
        const tokenAContract = new ethers.Contract(CONTRACT_ADDRESSES.TokenA, ERC20_ABI, provider);
        const contractTokenABalance = await tokenAContract.balanceOf(await contracts.miningContract.getAddress());
        console.log("Contract's TokenA balance:", ethers.formatEther(contractTokenABalance));
      } catch (balanceError) {
        console.warn("Could not check contract TokenA balance:", balanceError);
      }
      
      // Check DEX liquidity before transaction
      try {
        console.log("Checking TokenA price and liquidity...");
        const tokenAPrice = await contracts.miningContract.getTokenPrice(CONTRACT_ADDRESSES.TokenA);
        console.log("TokenA price (USDT):", ethers.formatEther(tokenAPrice));
        
        // Check if there's sufficient liquidity
        try {
          const isReliable = await contracts.miningContract.isTokenPriceReliable(CONTRACT_ADDRESSES.TokenA);
          console.log("Is TokenA price reliable (has min liquidity):", isReliable);
          if (!isReliable) {
            throw new Error("Insufficient DEX liquidity in USDT-TokenA pair. At least 1000 USDT liquidity is required.");
          }
        } catch (liquidityError) {
          console.warn("Could not check liquidity:", liquidityError);
          if (liquidityError.message.includes("Insufficient DEX liquidity")) {
            throw liquidityError; // Re-throw our specific error
          }
        }
      } catch (priceError) {
        console.error("❌ Failed to get TokenA price or liquidity info:", priceError);
        throw new Error(`Cannot get TokenA price: ${priceError.message}. This might indicate insufficient DEX liquidity.`);
      }

      // Check contract's current USDT balance before transaction
      try {
        const contractUsdtBalance = await usdtContract.balanceOf(await contracts.miningContract.getAddress());
        console.log("Contract's current USDT balance:", ethers.formatUnits(contractUsdtBalance, 18));
      } catch (balanceError) {
        console.warn("Could not check contract USDT balance:", balanceError);
      }

      // Skip gas estimation as it might fail due to state simulation issues
      // The "Insufficient balance" error during gas estimation likely occurs because:
      // 1. Gas estimation simulates the transaction but doesn't actually transfer USDT to contract first
      // 2. PancakeRouter checks if contract has USDT balance for the swap
      // 3. Since contract balance is still 0 during simulation, it fails
      console.log("Skipping gas estimation and proceeding with transaction directly...");
      
      console.log("Executing buyAndBurnTokenAWithUSDT transaction...");
      const tx = await contracts.miningContract.buyAndBurnTokenAWithUSDT(
        usdtAmountWei,
        inviter
      );
      console.log("Transaction sent:", tx.hash);
      console.log("Waiting for confirmation...");
      await tx.wait();
      console.log("✅ Transaction confirmed!");

      showNotification(t("mining.mechanism1.success"), "success");

      // Reset form and reload data
      setUsdtInput("");
      setPreviewData({
        tokenAAmount: "0",
        expectedTokenB: "0",
        isLoading: false,
        error: null,
      });

      // Notify parent component
      if (onTransactionComplete) {
        onTransactionComplete();
      }

      // Reload balance
      await loadUsdtBalance();
    } catch (error) {
      console.error("USDT purchase failed:", error);
      showNotification(
        `${t("mining.mechanism1.failed")}: ${error.message}`,
        "error"
      );
    } finally {
      hideLoading();
    }
  }, [
    contracts.miningContract,
    signer,
    usdtInput,
    usdtBalance,
    userAddress,
    inviterFromUrl,
    showNotification,
    showLoading,
    hideLoading,
    onTransactionComplete,
    loadUsdtBalance,
    t,
  ]);

  // Effect to load USDT balance
  useEffect(() => {
    loadUsdtBalance();
  }, [loadUsdtBalance]);

  // Effect to preview conversion when input changes
  useEffect(() => {
    const timeoutId = setTimeout(() => {
      previewConversion(usdtInput);
    }, 500); // Debounce for 500ms

    return () => clearTimeout(timeoutId);
  }, [usdtInput, previewConversion]);

  return (
    <div className="card p-4 sm:p-6 lg:p-8">
      <div className="flex items-center justify-between mb-4 sm:mb-6">
        <h3 className="text-lg sm:text-xl font-bold text-gray-900">
          {t("mining.mechanism1.title")}
        </h3>
        <div className="w-10 h-10 sm:w-12 sm:h-12 bg-blue-100 rounded-full flex items-center justify-center">
          <span className="text-blue-600 font-bold text-sm sm:text-base">💰</span>
        </div>
      </div>

      <p className="text-gray-600 mb-4 sm:mb-6 text-sm sm:text-base">
        {t("mining.mechanism1.description")}
      </p>

      {/* USDT Balance Display */}
      <div className="bg-blue-50 border border-blue-200 rounded-lg p-4 mb-4">
        <div className="flex items-center justify-between">
          <span className="text-sm text-blue-700 font-medium">
            {t("mining.mechanism1.balance")}
          </span>
          <span className="text-lg font-bold text-blue-800">
            {parseFloat(usdtBalance).toFixed(4)} USDT
          </span>
        </div>
      </div>

      {/* Input Section */}
      <div className="space-y-3 sm:space-y-4 mb-4 sm:mb-6">
        <div className="flex flex-col sm:flex-row gap-2">
          <input
            type="number"
            value={usdtInput}
            onChange={(e) => setUsdtInput(e.target.value)}
            placeholder={t("mining.mechanism1.placeholder")}
            className="flex-1 bg-gray-100 rounded-lg p-3 text-gray-800 placeholder-gray-400 border border-gray-200 focus:border-blue-500 focus:ring-blue-500 focus:outline-none text-sm sm:text-base"
            min="0"
            step="0.01"
          />
          <button
            onClick={() => setUsdtInput(usdtBalance)}
            className="secondary-btn px-4 py-3 rounded-lg text-sm font-semibold whitespace-nowrap"
          >
            MAX
          </button>
        </div>

        {/* Preview Section */}
        <div className="space-y-3">
          {previewData.isLoading ? (
            <div className="bg-gray-50 rounded-lg p-4 flex items-center justify-center">
              <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-blue-600 mr-2"></div>
              <span className="text-sm text-gray-600">{t("mining.mechanism1.calculating")}</span>
            </div>
          ) : previewData.error ? (
            <div className="bg-red-50 border border-red-200 rounded-lg p-4">
              <div className="text-sm text-red-700">
                {t("mining.mechanism1.previewError")}: {previewData.error}
              </div>
            </div>
          ) : (
            <>
              {/* TokenA Preview */}
              <div className="bg-gray-50 rounded-lg p-4">
                <div className="text-sm text-gray-600 mb-1">
                  {t("mining.mechanism1.willPurchase")}
                </div>
                <div className="text-lg font-bold text-violet-600 flex items-center">
                  <img
                    src="/icons/TokenA-GRAT.svg"
                    alt="GRAT Token"
                    className="w-5 h-5 mr-1"
                  />
                  {parseFloat(previewData.tokenAAmount).toFixed(4)} GRAT
                </div>
              </div>

              {/* Expected TokenB */}
              <div className="bg-green-50 border border-green-200 rounded-lg p-4">
                <div className="text-sm text-green-700 mb-1">
                  {t("mining.mechanism1.expectedReceive")}
                </div>
                <div className="text-lg font-bold text-green-600 flex items-center">
                  <img
                    src="/icons/TokenB-TIPS.svg"
                    alt="TIPS Token"
                    className="w-5 h-5 mr-1"
                  />
                  {parseFloat(previewData.expectedTokenB).toFixed(4)} TIPS
                </div>
                <div className="text-xs text-green-600 mt-1">
                  {t("mining.mechanism1.releasedIn6Periods")}
                </div>
              </div>
            </>
          )}
        </div>
      </div>

      {/* Warning Notice */}
      {parseFloat(usdtInput) > 0 && (
        <div className="bg-amber-50 border border-amber-200 rounded-lg p-4 mb-6">
          <div className="flex items-start">
            <svg
              className="w-4 h-4 text-amber-500 mt-0.5 mr-2 flex-shrink-0"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth="2"
                d="M12 9v2m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"
              />
            </svg>
            <div className="text-sm text-amber-800">
              <p className="font-semibold mb-1">{t("mining.mechanism1.notice")}</p>
              <p>{t("mining.mechanism1.noticeDesc")}</p>
            </div>
          </div>
        </div>
      )}

      {/* Action Button */}
      <button
        onClick={executeUsdtPurchase}
        disabled={
          !usdtInput ||
          parseFloat(usdtInput) <= 0 ||
          parseFloat(usdtInput) > parseFloat(usdtBalance) ||
          previewData.isLoading ||
          previewData.error
        }
        className="primary-btn w-full py-3 rounded-lg font-bold flex items-center justify-center disabled:opacity-50"
      >
        <span className="mr-2">💰</span>
        {t("mining.mechanism1.purchaseButton")}
        <svg
          className="w-4 h-4 ml-2"
          fill="none"
          stroke="currentColor"
          viewBox="0 0 24 24"
        >
          <path
            strokeLinecap="round"
            strokeLinejoin="round"
            strokeWidth="2"
            d="M17 8l4 4m0 0l-4 4m4-4H3"
          />
        </svg>
        <img
          src="/icons/TokenB-TIPS.svg"
          alt="TIPS Token"
          className="w-5 h-5 ml-2"
        />
      </button>

      {/* Information Footer */}
      <div className="mt-6 text-xs text-gray-500 text-center">
        <p>{t("mining.mechanism1.info")}</p>
      </div>
    </div>
  );
};

export default MechanismOneUSDT;