const fs = require('fs');
const path = require('path');

// 语言文件路径
const localesDir = path.join(__dirname, '..', 'i18n', 'locales');
const languages = ['en', 'es', 'fr', 'de', 'ja', 'ko']; // 中文已更新，跳过

// 推荐系统翻译
const referralTranslations = {
  en: {
    referral: {
      title: "Referral System",
      yourLink: "Your Referral Link",
      copy: "Copy",
      copied: "Copied",
      statistics: "Referral Statistics",
      totalReferees: "Total Referees",
      totalPurchased: "Total Purchased",
      averagePurchase: "Average Purchase",
      totalCommission: "Total Commission (USDT)",
      refereesList: "Referees List",
      purchasedAmount: "Purchased {{amount}} NFTs",
      noReferees: "No referees yet",
      howItWorks: "How it Works",
      instruction1: "Share your referral link with friends",
      instruction2: "Friends purchase NFTs through your link",
      instruction3: "View your referral statistics here"
    },
    common: {
      loading: "Loading...",
      previous: "Previous",
      next: "Next",
      page: "Page {{current}} of {{total}}"
    }
  },
  es: {
    referral: {
      title: "Sistema de Referidos",
      yourLink: "Tu Enlace de Referido",
      copy: "Copiar",
      copied: "Copiado",
      statistics: "Estadísticas de Referidos",
      totalReferees: "Total de Referidos",
      totalPurchased: "Total Comprado",
      averagePurchase: "Compra Promedio",
      totalCommission: "Comisión Total (USDT)",
      refereesList: "Lista de Referidos",
      purchasedAmount: "Compró {{amount}} NFTs",
      noReferees: "Aún no hay referidos",
      howItWorks: "Cómo Funciona",
      instruction1: "Comparte tu enlace de referido con amigos",
      instruction2: "Los amigos compran NFTs a través de tu enlace",
      instruction3: "Ve tus estadísticas de referido aquí"
    },
    common: {
      loading: "Cargando...",
      previous: "Anterior",
      next: "Siguiente",
      page: "Página {{current}} de {{total}}"
    }
  },
  fr: {
    referral: {
      title: "Système de Parrainage",
      yourLink: "Votre Lien de Parrainage",
      copy: "Copier",
      copied: "Copié",
      statistics: "Statistiques de Parrainage",
      totalReferees: "Total des Parrainés",
      totalPurchased: "Total Acheté",
      averagePurchase: "Achat Moyen",
      totalCommission: "Commission Totale (USDT)",
      refereesList: "Liste des Parrainés",
      purchasedAmount: "A acheté {{amount}} NFTs",
      noReferees: "Pas encore de parrainés",
      howItWorks: "Comment ça Marche",
      instruction1: "Partagez votre lien de parrainage avec vos amis",
      instruction2: "Les amis achètent des NFTs via votre lien",
      instruction3: "Consultez vos statistiques de parrainage ici"
    },
    common: {
      loading: "Chargement...",
      previous: "Précédent",
      next: "Suivant",
      page: "Page {{current}} sur {{total}}"
    }
  },
  de: {
    referral: {
      title: "Empfehlungssystem",
      yourLink: "Ihr Empfehlungslink",
      copy: "Kopieren",
      copied: "Kopiert",
      statistics: "Empfehlungsstatistiken",
      totalReferees: "Gesamt Empfohlene",
      totalPurchased: "Gesamt Gekauft",
      averagePurchase: "Durchschnittlicher Kauf",
      totalCommission: "Gesamtprovision (USDT)",
      refereesList: "Empfohlene Liste",
      purchasedAmount: "Kaufte {{amount}} NFTs",
      noReferees: "Noch keine Empfohlenen",
      howItWorks: "Wie es Funktioniert",
      instruction1: "Teilen Sie Ihren Empfehlungslink mit Freunden",
      instruction2: "Freunde kaufen NFTs über Ihren Link",
      instruction3: "Sehen Sie Ihre Empfehlungsstatistiken hier"
    },
    common: {
      loading: "Wird geladen...",
      previous: "Vorherige",
      next: "Nächste",
      page: "Seite {{current}} von {{total}}"
    }
  },
  ja: {
    referral: {
      title: "紹介システム",
      yourLink: "あなたの紹介リンク",
      copy: "コピー",
      copied: "コピーしました",
      statistics: "紹介統計",
      totalReferees: "紹介者総数",
      totalPurchased: "総購入数",
      averagePurchase: "平均購入数",
      totalCommission: "総手数料 (USDT)",
      refereesList: "紹介者リスト",
      purchasedAmount: "{{amount}}個のNFTを購入",
      noReferees: "まだ紹介者はいません",
      howItWorks: "仕組み",
      instruction1: "友達と紹介リンクを共有する",
      instruction2: "友達があなたのリンクからNFTを購入する",
      instruction3: "ここで紹介統計を確認する"
    },
    common: {
      loading: "読み込み中...",
      previous: "前へ",
      next: "次へ",
      page: "{{current}}ページ / {{total}}ページ"
    }
  },
  ko: {
    referral: {
      title: "추천 시스템",
      yourLink: "귀하의 추천 링크",
      copy: "복사",
      copied: "복사됨",
      statistics: "추천 통계",
      totalReferees: "총 추천인 수",
      totalPurchased: "총 구매량",
      averagePurchase: "평균 구매량",
      totalCommission: "총 수수료 (USDT)",
      refereesList: "추천인 목록",
      purchasedAmount: "{{amount}}개의 NFT를 구매",
      noReferees: "아직 추천인이 없습니다",
      howItWorks: "작동 방식",
      instruction1: "친구들과 추천 링크를 공유하세요",
      instruction2: "친구들이 귀하의 링크를 통해 NFT를 구매합니다",
      instruction3: "여기에서 추천 통계를 확인하세요"
    },
    common: {
      loading: "로딩 중...",
      previous: "이전",
      next: "다음",
      page: "{{current}}페이지 / {{total}}페이지"
    }
  }
};

// NFT 预售批量购买翻译
const nftPresaleUpdates = {
  en: {
    userPurchased: "Purchased Quantity",
    pricePerNFT: "Price per NFT",
    selectQuantity: "Select Quantity",
    totalCost: "Total Cost"
  },
  es: {
    userPurchased: "Cantidad Comprada",
    pricePerNFT: "Precio por NFT",
    selectQuantity: "Seleccionar Cantidad",
    totalCost: "Costo Total"
  },
  fr: {
    userPurchased: "Quantité Achetée",
    pricePerNFT: "Prix par NFT",
    selectQuantity: "Sélectionner la Quantité",
    totalCost: "Coût Total"
  },
  de: {
    userPurchased: "Gekaufte Menge",
    pricePerNFT: "Preis pro NFT",
    selectQuantity: "Menge Auswählen",
    totalCost: "Gesamtkosten"
  },
  ja: {
    userPurchased: "購入数量",
    pricePerNFT: "NFT1個あたりの価格",
    selectQuantity: "数量を選択",
    totalCost: "総額"
  },
  ko: {
    userPurchased: "구매 수량",
    pricePerNFT: "NFT당 가격",
    selectQuantity: "수량 선택",
    totalCost: "총 비용"
  }
};

// 更新语言文件
languages.forEach(lang => {
  const filePath = path.join(localesDir, `${lang}.json`);
  
  if (fs.existsSync(filePath)) {
    try {
      const data = JSON.parse(fs.readFileSync(filePath, 'utf8'));
      
      // 添加推荐系统翻译
      if (referralTranslations[lang]) {
        data.referral = referralTranslations[lang].referral;
        data.common = referralTranslations[lang].common;
      }
      
      // 更新 NFT 预售翻译
      if (nftPresaleUpdates[lang] && data.nftPresale) {
        Object.assign(data.nftPresale, nftPresaleUpdates[lang]);
        
        // 更新 info 对象，移除 onePerAddress
        if (data.nftPresale.info) {
          delete data.nftPresale.info.onePerAddress;
        }
      }
      
      // 写回文件
      fs.writeFileSync(filePath, JSON.stringify(data, null, 2), 'utf8');
      console.log(`✅ Updated ${lang}.json`);
    } catch (error) {
      console.error(`❌ Failed to update ${lang}.json:`, error);
    }
  } else {
    console.log(`⚠️  ${lang}.json not found`);
  }
});

console.log('\n🎉 Translation update completed!');