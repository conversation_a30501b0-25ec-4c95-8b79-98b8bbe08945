// Environment variable helper - safe access to process.env in React
const getEnvVar = (name, defaultValue = "") => {
  try {
    // In React build process, environment variables are injected at build time
    if (typeof process !== "undefined" && process.env) {
      return process.env[name] || defaultValue;
    }

    // Fallback for runtime or if process is not available
    if (typeof window !== "undefined" && window[name]) {
      return window[name];
    }

    return defaultValue;
  } catch (error) {
    console.warn(`Failed to access environment variable ${name}:`, error);
    return defaultValue;
  }
};

// AI Service for handling AI requests via Cloudflare Workers proxy (Groq-powered)
class AIService {
  constructor() {
    // Use Cloudflare Workers endpoint with Groq API (ultra-fast AI responses)
    this.workerURL = getEnvVar(
      "REACT_APP_AI_WORKER_URL",
      "https://your-worker-subdomain.your-username.workers.dev"
    );
    this.isGroqEnabled = true;

    // Development mode detection
    this.isDevelopment = this.workerURL.includes("your-worker-subdomain");

    // Debug logging
    console.log("AI Service initialization:", {
      workerURL: this.workerURL,
      isDevelopment: this.isDevelopment,
      envAvailable: typeof process !== "undefined" && !!process.env,
      reactAppEnv:
        typeof process !== "undefined"
          ? process.env.REACT_APP_AI_WORKER_URL
          : "undefined",
    });

    if (this.isDevelopment) {
      console.log(
        "AI Service running in development mode - using fallback responses"
      );
    }
  }

  /**
   * Generate thank you note using Groq-powered AI
   * @param {string} recipient - Who to thank (e.g., "artist", "developer")
   * @param {string} reason - Why to thank them (e.g., "for drawing that cool avatar")
   * @param {string} language - Language for the response
   * @returns {Promise<string>} Generated thank you note
   */
  async generateThankYouNote(recipient, reason, language = "en") {
    // In development mode, return fallback immediately
    if (this.isDevelopment) {
      console.log("Development mode: Using fallback thank you response");
      return this.getFallbackThankYou(recipient, reason, language);
    }

    const prompt = this.buildThankYouPrompt(recipient, reason, language);

    try {
      const response = await this.callGroqWorker(prompt);
      return response;
    } catch (error) {
      console.error("AI Service Error:", error);
      // Return fallback response if AI service fails
      return this.getFallbackThankYou(recipient, reason, language);
    }
  }

  /**
   * Generate meme idea using Groq-powered AI
   * @param {string} topic - The topic for the meme (e.g., "crypto", "cats")
   * @param {string} style - The style of meme ("funny", "wholesome", "sarcastic", "inspirational")
   * @param {string} language - Language for the response
   * @returns {Promise<object>} Generated meme concept with title and description
   */
  async generateMemeIdea(topic, style, language = "en") {
    // In development mode, return fallback immediately
    if (this.isDevelopment) {
      console.log("Development mode: Using fallback meme response");
      return this.getFallbackMeme(topic, style, language);
    }

    const prompt = this.buildMemePrompt(topic, style, language);

    try {
      const response = await this.callGroqWorker(prompt);
      return this.parseMemeResponse(response, topic, style);
    } catch (error) {
      console.error("AI Service Error:", error);
      // Return fallback response if AI service fails
      return this.getFallbackMeme(topic, style, language);
    }
  }

  /**
   * Core method to call Groq Worker with optimized format
   * @param {string} prompt - The user prompt
   * @returns {Promise<string>} AI generated response
   */
  async callGroqWorker(prompt) {
    const response = await fetch(this.workerURL, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify({
        contents: [
          {
            parts: [{ text: prompt }],
          },
        ],
      }),
    });

    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(
        errorData.message || `Worker request failed: ${response.status}`
      );
    }

    const data = await response.json();

    // Handle both success response and fallback response
    if (data.candidates && data.candidates[0] && data.candidates[0].content) {
      return data.candidates[0].content.parts[0].text;
    } else if (data.fallback && data.fallback.text) {
      return data.fallback.text;
    } else {
      throw new Error("Invalid response format from AI service");
    }
  }

  /**
   * Build optimized prompt for thank you note generation
   */
  buildThankYouPrompt(recipient, reason, language) {
    const isZh = language === "zh";

    if (isZh) {
      return `请为我生成一条感谢信息：感谢 ${recipient}，${reason}。要求温暖真诚，适合在社交媒体分享，包含emoji和#TipCoin标签。`;
    } else {
      return `Generate a thank you message: Thank ${recipient} for ${reason}. Make it warm, sincere, suitable for social media sharing, include emojis and #TipCoin hashtags.`;
    }
  }

  /**
   * Build optimized prompt for meme generation
   */
  buildMemePrompt(topic, style, language) {
    const isZh = language === "zh";

    if (isZh) {
      return `请创作一个关于"${topic}"的${style}风格meme创意，包含标题和详细描述，适合TipCoin社区分享。`;
    } else {
      return `Create a ${style} meme idea about "${topic}" with title and detailed description, suitable for TipCoin community sharing.`;
    }
  }

  /**
   * Parse meme response into structured format
   */
  parseMemeResponse(response, topic, style) {
    const lines = response.split("\n").filter((line) => line.trim());

    // Try to extract title and description
    let title = `${
      style.charAt(0).toUpperCase() + style.slice(1)
    } ${topic} Meme`;
    let description = response;

    // Look for title patterns
    const titleMatch = response.match(
      /(?:标题|Title|主题)[:：]\s*(.+?)(?:\n|$)/i
    );
    if (titleMatch) {
      title = titleMatch[1].trim();
    }

    return {
      title,
      description: description
        .replace(/(?:标题|Title|主题)[:：]\s*.+?(?:\n|$)/i, "")
        .trim(),
    };
  }

  /**
   * Fallback thank you responses when AI service is unavailable
   */
  getFallbackThankYou(recipient, reason, language) {
    const responses = {
      en: `Thank you so much, ${recipient}, ${reason}! Your contribution means the world to us. Here's some $TIPS as a token of our appreciation! 🎉 #TipCoin #Gratitude`,
      zh: `非常感谢您，${recipient}，${reason}！您的贡献对我们意义重大。这里有一些 $TIPS 作为我们感谢的象征！🎉 #TipCoin #感恩`,
      ja: `${recipient}様、${reason}本当にありがとうございます！🎉 #TipCoin #感謝`,
      fr: `Merci beaucoup, ${recipient}, ${reason}! 🎉 #TipCoin #Reconnaissance`,
      de: `Vielen Dank, ${recipient}, ${reason}! 🎉 #TipCoin #Dankbarkeit`,
      es: `¡Muchas gracias, ${recipient}, ${reason}! 🎉 #TipCoin #Gratitud`,
      ko: `${recipient}님, ${reason} 정말 감사합니다! 🎉 #TipCoin #감사`,
    };

    return responses[language] || responses.en;
  }

  /**
   * Fallback meme responses when AI service is unavailable
   */
  getFallbackMeme(topic, style, language) {
    const responses = {
      en: {
        title: `${
          style.charAt(0).toUpperCase() + style.slice(1)
        } ${topic} Meme`,
        description: `A ${style} meme about ${topic}.\n\nTop text: "When you finally understand ${topic}"\nBottom text: "But then realize you need to explain it to everyone else"\n\nPerfect for sharing and definitely worth some $TIPS! #TipCoin #MemeWorkshop`,
      },
      zh: {
        title: `${style} ${topic} 梗图`,
        description: `一个关于 ${topic} 的${style}梗图。\n\n上方文字："当你终于理解了 ${topic}"\n下方文字："但是然后意识到你需要向其他人解释"\n\n完美的分享内容，绝对值得一些 $TIPS！#TipCoin #梗图工坊`,
      },
    };

    return responses[language] || responses.en;
  }

  /**
   * Check if AI service is available
   * @returns {boolean} True if worker URL is configured
   */
  isAvailable() {
    return !!this.workerURL;
  }

  /**
   * Get service status and model information
   * @returns {object} Service status information
   */
  getServiceInfo() {
    return {
      provider: "Gmini",
      speed: "Ultra-fast",
      available: this.isAvailable(),
      workerURL: this.workerURL,
      features: [
        "Multi-language support",
        "Contextual prompts",
        "Fallback responses",
      ],
    };
  }

  /**
   * Generate general AI response for any prompt
   * @param {string} prompt - User input prompt
   * @param {string} language - Response language
   * @returns {Promise<string>} AI generated response
   */
  async generateResponse(prompt, language = "en") {
    // In development mode, return fallback immediately
    if (this.isDevelopment) {
      console.log("Development mode: Using fallback general response");
      const fallbacks = {
        en: `Thanks for your input! 🌟\n\nI appreciate your engagement with our TipCoin community. Every interaction makes us stronger!\n\n#TipCoin #Community`,
        zh: `感谢您的输入！🌟\n\n感谢您参与TipCoin社区的互动。每一次交流都让我们变得更强大！\n\n#TipCoin #社区`,
      };
      return fallbacks[language] || fallbacks.en;
    }

    try {
      const response = await this.callGroqWorker(prompt);
      return response;
    } catch (error) {
      console.error("AI Service Error:", error);
      // Return language-appropriate fallback
      const fallbacks = {
        en: `Thanks for your input! 🌟\n\nI appreciate your engagement with our TipCoin community. Every interaction makes us stronger!\n\n#TipCoin #Community`,
        zh: `感谢您的输入！🌟\n\n感谢您参与TipCoin社区的互动。每一次交流都让我们变得更强大！\n\n#TipCoin #社区`,
      };
      return fallbacks[language] || fallbacks.en;
    }
  }
}

// Create singleton instance
export const aiService = new AIService();
export default aiService;
