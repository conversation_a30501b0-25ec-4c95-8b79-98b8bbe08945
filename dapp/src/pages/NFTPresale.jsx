import React, { useState, useEffect } from "react";
import { Link } from "react-router-dom";
import { useTranslation } from "react-i18next";
import LanguageSwitcher from "../components/LanguageSwitcher";
import WalletModal from "../components/WalletModal";
import ReferralModal from "../components/ReferralModal";
import Notification from "../components/Notification";
import { useWallet } from "../hooks/useWallet";
import { usePresale } from "../hooks/usePresale";
import { PRESALE_CONSTANTS } from "../utils/constants";

const NFTPresale = () => {
  const { t } = useTranslation();

  // Wallet connection
  const wallet = useWallet();
  const presale = usePresale(wallet.contracts, wallet.userAddress);

  // State management
  const [showWalletModal, setShowWalletModal] = useState(false);
  const [showReferralModal, setShowReferralModal] = useState(false);
  const [notification, setNotification] = useState(null);
  const [purchaseQuantity, setPurchaseQuantity] = useState(1);
  const [showReferrerConfirmModal, setShowReferrerConfirmModal] =
    useState(false);
  const [pendingReferrer, setPendingReferrer] = useState(null);

  // Presale configuration
  const presaleNetwork =
    process.env.REACT_APP_PRESALE_NETWORK || "BSC (Binance Smart Chain)";
  const presaleTelegramUrl = process.env.REACT_APP_PRESALE_TELEGRAM_URL || "#";
  const presaleSupportEmail =
    process.env.REACT_APP_PRESALE_SUPPORT_EMAIL || "<EMAIL>";

  // Show notification
  const showNotification = (message, type = "info") => {
    setNotification({ message, type });
    setTimeout(() => setNotification(null), 5000);
  };

  // Listen for wallet connection status changes, automatically close modal
  useEffect(() => {
    if (wallet.isConnected && showWalletModal) {
      setShowWalletModal(false);
      showNotification(t("wallet.connectionSuccess"), "success");
    }
  }, [wallet.isConnected, showWalletModal, t]);

  // Handle NFT purchase - supports batch purchase
  const handlePurchase = async () => {
    if (!wallet.isConnected) {
      setShowWalletModal(true);
      return;
    }

    const referrer = presale.getReferrerFromURL();

    // If first purchase and URL contains referrer address, confirmation needed
    if (
      presale.userInfo.purchased === 0 &&
      referrer !== "******************************************"
    ) {
      setPendingReferrer(referrer);
      setShowReferrerConfirmModal(true);
      return;
    }

    // Direct purchase
    await executePurchase(referrer);
  };

  // Execute purchase
  const executePurchase = async (referrer) => {
    const success = await presale.buyNFT(purchaseQuantity, referrer);

    if (success) {
      showNotification(t("notifications.purchaseSuccess"), "success");
      setPurchaseQuantity(1); // Reset quantity
    } else if (presale.error) {
      showNotification(presale.error, "error");
    }
  };

  // Confirm referrer address purchase
  const handleConfirmReferrerPurchase = async () => {
    setShowReferrerConfirmModal(false);
    await executePurchase(pendingReferrer);
    setPendingReferrer(null);
  };

  // Cancel referrer address purchase
  const handleCancelReferrerPurchase = async () => {
    setShowReferrerConfirmModal(false);
    await executePurchase("******************************************");
    setPendingReferrer(null);
  };

  // Calculate total price
  const calculateTotalPrice = () => {
    const price = parseFloat(presale.presaleInfo.nftPrice || "0");
    return (price * purchaseQuantity).toFixed(1);
  };

  // Check if purchase is possible (for button state)
  const checkCanPurchase = () => {
    if (!wallet.isConnected)
      return { canPurchase: false, reason: t("wallet.notConnected") };
    if (!presale.presaleInfo.active)
      return { canPurchase: false, reason: t("presale.notActive") };

    const totalPrice = calculateTotalPrice();
    const userBalance = parseFloat(presale.balances.usdt || "0");

    if (userBalance < parseFloat(totalPrice)) {
      return {
        canPurchase: false,
        reason:
          t("errors.insufficientBalance", {
            required: totalPrice,
            current: userBalance.toFixed(2),
          }) ||
          `Insufficient USDT. Required: ${totalPrice}, Current: ${userBalance.toFixed(
            2
          )}`,
      };
    }

    return { canPurchase: true, reason: "" };
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-violet-50 via-white to-amber-50">
      {/* Navigation header */}
      <header className="bg-white/80 backdrop-blur-md border-b border-gray-200/80 sticky top-0 z-50">
        <div className="container mx-auto px-4 sm:px-6 py-3 sm:py-4 flex justify-between items-center">
          {/* Logo */}
          <Link
            to="/"
            className="flex items-center space-x-1 sm:space-x-2 text-lg sm:text-2xl font-bold font-poppins"
          >
            <img
              src="/icons/MinerNFT-Rabbit.svg"
              alt="Gratitude Protocol NFT"
              className="w-6 h-6 sm:w-8 sm:h-8"
            />
            <span className="text-violet-700">Gratitude Protocol</span>
            <span className="text-gray-800 hidden sm:inline">NFT</span>
          </Link>

          {/* Right controls */}
          <div className="flex items-center gap-2 sm:gap-4">
            <LanguageSwitcher className="" />
            <Link
              to="/"
              className="secondary-btn font-bold py-2 px-3 sm:py-2.5 sm:px-6 rounded-lg text-sm sm:text-base flex items-center gap-1"
            >
              <span className="hidden sm:inline">
                {t("nftPresale.backToHome")}
              </span>
              <span className="sm:hidden flex items-center gap-1">
                <svg
                  className="w-4 h-4"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M15 19l-7-7 7-7"
                  />
                </svg>
                {t("nav.home")}
              </span>
            </Link>
          </div>
        </div>
      </header>

      {/* Main content */}
      <main className="container mx-auto px-4 sm:px-6 py-8 sm:py-16">
        <div className="max-w-4xl mx-auto">
          {/* Page title */}
          <div className="text-center mb-8 sm:mb-16">
            <div className="flex flex-col sm:flex-row items-center justify-center mb-4 sm:mb-6">
              <img
                src="/icons/MinerNFT-Rabbit.svg"
                alt="Gratitude Protocol NFT"
                className="w-16 h-16 sm:w-20 sm:h-20 mb-3 sm:mb-0 sm:mr-4"
              />
              <h1 className="text-2xl sm:text-4xl md:text-6xl font-black text-gray-900 leading-tight text-center sm:text-left">
                {t("nftPresale.title")}
              </h1>
            </div>
            <p className="text-base sm:text-xl text-gray-600 max-w-3xl mx-auto px-4 sm:px-0">
              {t("nftPresale.subtitle")}
            </p>
          </div>

          {/* Presale information cards */}
          <div className="grid sm:grid-cols-2 lg:grid-cols-3 gap-4 sm:gap-8 mb-8 sm:mb-12">
            {/* Price information */}
            <div className="card p-4 sm:p-8">
              <div className="flex items-center mb-3 sm:mb-4">
                <div className="w-10 h-10 sm:w-12 sm:h-12 bg-violet-100 rounded-lg flex items-center justify-center mr-3 sm:mr-4">
                  <svg
                    className="w-5 h-5 sm:w-6 sm:h-6 text-violet-600"
                    fill="none"
                    stroke="currentColor"
                    viewBox="0 0 24 24"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={2}
                      d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1M21 12a9 9 0 11-18 0 9 9 0 0118 0z"
                    />
                  </svg>
                </div>
                <h3 className="text-lg sm:text-2xl font-bold text-gray-900">
                  {t("nftPresale.priceTitle")}
                </h3>
              </div>
              <div className="text-2xl sm:text-4xl font-black text-violet-700 mb-2">
                {presale.presaleInfo.nftPrice || "0"} USDT
              </div>
              <p className="text-sm sm:text-base text-gray-600">
                {t("nftPresale.priceDescription")}
              </p>
            </div>

            {/* Network information */}
            <div className="card p-4 sm:p-8">
              <div className="flex items-center mb-3 sm:mb-4">
                <div className="w-10 h-10 sm:w-12 sm:h-12 bg-amber-100 rounded-lg flex items-center justify-center mr-3 sm:mr-4">
                  <svg
                    className="w-5 h-5 sm:w-6 sm:h-6 text-amber-600"
                    fill="none"
                    stroke="currentColor"
                    viewBox="0 0 24 24"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={2}
                      d="M21 12a9 9 0 01-9 9m9-9a9 9 0 00-9-9m9 9H3m9 9v-9m0-9v9m0 9c-5 0-9-4-9-9s4-9 9-9"
                    />
                  </svg>
                </div>
                <h3 className="text-lg sm:text-2xl font-bold text-gray-900">
                  {t("nftPresale.networkTitle")}
                </h3>
              </div>
              <div className="text-lg sm:text-2xl font-bold text-amber-700 mb-2">
                {presaleNetwork}
              </div>
              <p className="text-sm sm:text-base text-gray-600">
                {t("nftPresale.networkDescription")}
              </p>
            </div>

            {/* Presale quantity */}
            <div className="card p-4 sm:p-8">
              <div className="flex items-center mb-3 sm:mb-4">
                <div className="w-10 h-10 sm:w-12 sm:h-12 bg-green-100 rounded-lg flex items-center justify-center mr-3 sm:mr-4">
                  <svg
                    className="w-5 h-5 sm:w-6 sm:h-6 text-green-600"
                    fill="none"
                    stroke="currentColor"
                    viewBox="0 0 24 24"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={2}
                      d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10"
                    />
                  </svg>
                </div>
                <h3 className="text-lg sm:text-2xl font-bold text-gray-900">
                  {t("nftPresale.supplyTitle")}
                </h3>
              </div>
              <div className="text-lg sm:text-2xl font-bold text-green-700 mb-2">
                {parseInt(presale.presaleInfo.maxSupply || 0).toLocaleString()}
              </div>
              <p className="text-sm sm:text-base text-gray-600">
                {t("nftPresale.supplyDescription")}
              </p>
            </div>
          </div>

          {/* Purchase interface */}
          <div className="card p-4 sm:p-8 mb-8 sm:mb-12">
            <div className="flex items-center mb-4 sm:mb-6">
              <div className="w-10 h-10 sm:w-12 sm:h-12 bg-violet-100 rounded-lg flex items-center justify-center mr-3 sm:mr-4">
                <svg
                  className="w-5 h-5 sm:w-6 sm:h-6 text-violet-600"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1M21 12a9 9 0 11-18 0 9 9 0 0118 0z"
                  />
                </svg>
              </div>
              <h3 className="text-xl sm:text-2xl font-bold text-gray-900">
                {t("nftPresale.buyNFTButton", {
                  price: presale.presaleInfo.nftPrice || "0",
                })}
              </h3>
            </div>

            {/* Wallet connection status */}
            {!wallet.isConnected ? (
              <div className="bg-blue-50 border border-blue-200 rounded-lg p-4 sm:p-6 mb-4 sm:mb-6">
                <div className="flex items-center justify-between">
                  <div className="flex items-center">
                    <svg
                      className="w-5 h-5 text-blue-600 mr-2"
                      fill="none"
                      stroke="currentColor"
                      viewBox="0 0 24 24"
                    >
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth={2}
                        d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"
                      />
                    </svg>
                    <p className="text-blue-800 font-medium">
                      {t("nftPresale.connectWalletPrompt")}
                    </p>
                  </div>
                  <button
                    onClick={() => setShowWalletModal(true)}
                    className="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg font-medium transition-colors duration-200"
                  >
                    {t("wallet.connectWallet")}
                  </button>
                </div>
              </div>
            ) : (
              <div className="bg-green-50 border border-green-200 rounded-lg p-4 sm:p-6 mb-4 sm:mb-6">
                <div className="flex items-center justify-between">
                  <div className="flex items-center">
                    <svg
                      className="w-5 h-5 text-green-600 mr-2"
                      fill="none"
                      stroke="currentColor"
                      viewBox="0 0 24 24"
                    >
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth={2}
                        d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"
                      />
                    </svg>
                    <div>
                      <p className="text-green-800 font-medium">
                        {t("nftPresale.walletConnected")}:{" "}
                        {wallet.formatAddress(wallet.userAddress)}
                      </p>
                      <div className="flex flex-col sm:flex-row sm:items-center gap-1 sm:gap-2">
                        <p className="text-green-600 text-sm">
                          {t("nftPresale.usdtBalance")}: {presale.balances.usdt}
                        </p>
                        {(() => {
                          const totalPrice = calculateTotalPrice();
                          const userBalance = parseFloat(
                            presale.balances.usdt || "0"
                          );
                          const hasSufficientBalance =
                            userBalance >= parseFloat(totalPrice);

                          return (
                            <span
                              className={`text-xs px-2 py-1 rounded-full inline-block ${
                                hasSufficientBalance
                                  ? "bg-green-100 text-green-800"
                                  : "bg-red-100 text-red-800"
                              }`}
                            >
                              {hasSufficientBalance
                                ? "✓ Sufficient"
                                : "⚠ Insufficient"}
                            </span>
                          );
                        })()}
                      </div>
                      {presale.userInfo.purchased > 0 && (
                        <p className="text-green-600 text-sm">
                          {t("nftPresale.userPurchased")}:{" "}
                          {presale.userInfo.purchased}
                        </p>
                      )}
                    </div>
                  </div>
                  <button
                    onClick={wallet.handleDisconnect}
                    className="bg-gray-500 hover:bg-gray-600 text-white px-3 py-1 rounded text-sm transition-colors duration-200"
                  >
                    {t("wallet.disconnect")}
                  </button>
                </div>
              </div>
            )}

            {/* NFT price display and quantity selection */}
            <div className="space-y-4">
              <div className="bg-violet-50 border border-violet-200 rounded-lg p-4 sm:p-6">
                <div className="text-center">
                  <h4 className="text-lg font-bold text-violet-900 mb-2">
                    {t("nftPresale.fixedPrice")}
                  </h4>
                  <div className="text-2xl sm:text-3xl font-bold text-violet-600">
                    {presale.presaleInfo.nftPrice || "0"} USDT
                  </div>
                  <p className="text-sm text-violet-700 mt-2">
                    {t("nftPresale.pricePerNFT")}
                  </p>
                </div>
              </div>

              {/* Quantity selector */}
              <div className="bg-gray-50 border border-gray-200 rounded-lg p-4 sm:p-6">
                <div className="flex items-center justify-between mb-4">
                  <h4 className="text-lg font-bold text-gray-900">
                    {t("nftPresale.selectQuantity")}
                  </h4>
                  <div className="flex items-center gap-3">
                    <button
                      onClick={() =>
                        setPurchaseQuantity(Math.max(1, purchaseQuantity - 1))
                      }
                      disabled={purchaseQuantity <= 1}
                      className="w-8 h-8 bg-gray-200 hover:bg-gray-300 disabled:opacity-50 rounded-lg flex items-center justify-center font-bold"
                    >
                      -
                    </button>
                    <span className="text-xl font-bold px-4 py-2 bg-white border rounded-lg min-w-[60px] text-center">
                      {purchaseQuantity}
                    </span>
                    <button
                      onClick={() => setPurchaseQuantity(purchaseQuantity + 1)}
                      disabled={purchaseQuantity >= 100}
                      className="w-8 h-8 bg-gray-200 hover:bg-gray-300 disabled:opacity-50 rounded-lg flex items-center justify-center font-bold"
                    >
                      +
                    </button>
                  </div>
                </div>
                <div className="text-center">
                  <div className="text-lg text-gray-700 mb-2">
                    {t("nftPresale.totalCost")}
                  </div>
                  <div className="text-2xl sm:text-3xl font-bold text-violet-600">
                    {calculateTotalPrice()} USDT
                  </div>
                </div>
              </div>

              {/* Purchase button */}
              {(() => {
                const purchaseCheck = checkCanPurchase();
                const isDisabled =
                  presale.loading || !purchaseCheck.canPurchase;

                return (
                  <>
                    <button
                      onClick={handlePurchase}
                      disabled={isDisabled}
                      className={`w-full py-3 sm:py-4 px-4 sm:px-6 rounded-lg font-bold text-base sm:text-lg transition-all duration-200 ${
                        isDisabled
                          ? "bg-gray-300 text-gray-500 cursor-not-allowed"
                          : "bg-gradient-to-r from-violet-600 to-purple-600 hover:from-violet-700 hover:to-purple-700 text-white shadow-lg hover:shadow-xl"
                      }`}
                    >
                      {presale.loading ? (
                        <div className="flex items-center justify-center">
                          <svg
                            className="animate-spin -ml-1 mr-3 h-5 w-5 text-white"
                            xmlns="http://www.w3.org/2000/svg"
                            fill="none"
                            viewBox="0 0 24 24"
                          >
                            <circle
                              className="opacity-25"
                              cx="12"
                              cy="12"
                              r="10"
                              stroke="currentColor"
                              strokeWidth="4"
                            ></circle>
                            <path
                              className="opacity-75"
                              fill="currentColor"
                              d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
                            ></path>
                          </svg>
                          {t("nftPresale.processing")}
                        </div>
                      ) : !presale.presaleInfo.active ? (
                        t("nftPresale.presaleNotStarted")
                      ) : !wallet.isConnected ? (
                        t("wallet.connectWallet")
                      ) : (
                        t("nftPresale.buyNFTButton", {
                          price: calculateTotalPrice(),
                        })
                      )}
                    </button>

                    {/* Display balance check error */}
                    {!purchaseCheck.canPurchase &&
                      purchaseCheck.reason &&
                      wallet.isConnected && (
                        <div className="mt-2 p-2 sm:p-3 bg-red-50 border border-red-200 rounded-lg">
                          <div className="flex items-start sm:items-center">
                            <svg
                              className="w-4 h-4 sm:w-5 sm:h-5 text-red-500 mr-2 flex-shrink-0 mt-0.5 sm:mt-0"
                              fill="currentColor"
                              viewBox="0 0 20 20"
                            >
                              <path
                                fillRule="evenodd"
                                d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z"
                                clipRule="evenodd"
                              />
                            </svg>
                            <span className="text-red-700 text-xs sm:text-sm font-medium leading-relaxed">
                              {purchaseCheck.reason}
                            </span>
                          </div>
                        </div>
                      )}
                  </>
                );
              })()}

              {/* Purchase instructions */}
              <div className="text-xs text-gray-500 space-y-1">
                <p>
                  •{" "}
                  {t("nftPresale.info.price", {
                    price: presale.presaleInfo.nftPrice || "0",
                  })}
                </p>
                <p>
                  •{" "}
                  {t("nftPresale.info.maxSupply", {
                    supply: presale.presaleInfo.maxSupply || 0,
                  })}
                </p>
                <p>• {t("nftPresale.info.airdropInfo")}</p>
                <p>• {t("nftPresale.info.networkInfo")}</p>
              </div>
            </div>
          </div>

          {/* Referral system */}
          {wallet.isConnected && (
            <div className="card p-4 sm:p-8 mb-8 sm:mb-12">
              <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between mb-4 sm:mb-6">
                <div className="flex items-center mb-4 sm:mb-0">
                  <div className="w-10 h-10 sm:w-12 sm:h-12 bg-blue-100 rounded-lg flex items-center justify-center mr-3 sm:mr-4">
                    <svg
                      className="w-5 h-5 sm:w-6 sm:h-6 text-blue-600"
                      fill="none"
                      stroke="currentColor"
                      viewBox="0 0 24 24"
                    >
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth={2}
                        d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z"
                      />
                    </svg>
                  </div>
                  <div>
                    <h3 className="text-lg sm:text-xl md:text-2xl font-bold text-gray-900">
                      {t("referral.title")}
                    </h3>
                    <p className="text-xs sm:text-sm text-gray-600">
                      {t("referral.description")}
                    </p>
                  </div>
                </div>
                <button
                  onClick={() => setShowReferralModal(true)}
                  className="w-full sm:w-auto bg-blue-500 hover:bg-blue-600 text-white px-4 sm:px-6 py-2 sm:py-3 rounded-lg font-medium transition-colors duration-200 text-sm sm:text-base"
                >
                  {t("referral.manageReferrals")}
                </button>
              </div>

              {/* Referral system quick info */}
              <div className="bg-blue-50 border border-blue-200 rounded-lg p-4 sm:p-6">
                <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
                  <div className="text-center">
                    <div className="text-lg sm:text-xl font-bold text-blue-600">
                      {presale.referralInfo?.referralDetails?.totalReferees ||
                        "0"}
                    </div>
                    <div className="text-sm text-blue-700">
                      {t("referral.totalReferees")}
                    </div>
                  </div>
                  <div className="text-center">
                    <div className="text-lg sm:text-xl font-bold text-blue-600">
                      {presale.referralInfo?.referralDetails
                        ?.totalPurchasedByReferees || "0"}
                    </div>
                    <div className="text-sm text-blue-700">
                      {t("referral.totalPurchased")}
                    </div>
                  </div>
                </div>
              </div>
            </div>
          )}

          {/* Important notices */}
          <div className="grid sm:grid-cols-1 md:grid-cols-2 gap-4 sm:gap-6 mb-8 sm:mb-12">
            {/* Notice 2: Airdrop explanation */}
            <div className="bg-blue-50 border border-blue-200 rounded-lg p-4 sm:p-6">
              <div className="flex items-center mb-3">
                <svg
                  className="w-6 h-6 text-blue-600 mr-2"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"
                  />
                </svg>
                <h4 className="font-bold text-blue-800 text-sm sm:text-base">
                  {t("nftPresale.info1Title")}
                </h4>
              </div>
              <p className="text-blue-700 text-xs sm:text-sm">
                {t("nftPresale.info1Description")}
              </p>
            </div>

            {/* Notice 3: Personal wallet */}
            <div className="bg-green-50 border border-green-200 rounded-lg p-4 sm:p-6">
              <div className="flex items-center mb-3">
                <svg
                  className="w-6 h-6 text-green-600 mr-2"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"
                  />
                </svg>
                <h4 className="font-bold text-green-800 text-sm sm:text-base">
                  {t("nftPresale.tip1Title")}
                </h4>
              </div>
              <p className="text-green-700 text-xs sm:text-sm">
                {t("nftPresale.tip1Description")}
              </p>
            </div>
          </div>

          {/* NFT features introduction */}
          <div className="card p-4 sm:p-8 mb-8 sm:mb-12">
            <h3 className="text-xl sm:text-2xl md:text-3xl font-bold text-gray-900 mb-4 sm:mb-6 text-center">
              {t("nftPresale.featuresTitle")}
            </h3>
            <div className="grid sm:grid-cols-1 md:grid-cols-2 gap-4 sm:gap-6 md:gap-8">
              <div className="flex items-start space-x-3 sm:space-x-4">
                <div className="w-10 h-10 sm:w-12 sm:h-12 bg-violet-100 rounded-lg flex items-center justify-center flex-shrink-0">
                  <svg
                    className="w-5 h-5 sm:w-6 sm:h-6 text-violet-600"
                    fill="none"
                    stroke="currentColor"
                    viewBox="0 0 24 24"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={2}
                      d="M11.049 2.927c.3-.921 1.603-.921 1.902 0l1.519 4.674a1 1 0 00.95.69h4.915c.969 0 1.371 1.24.588 1.81l-3.976 2.888a1 1 0 00-.363 1.118l1.518 4.674c.3.922-.755 1.688-1.538 1.118l-3.976-2.888a1 1 0 00-1.176 0l-3.976 2.888c-.783.57-1.838-.197-1.538-1.118l1.518-4.674a1 1 0 00-.363-1.118l-3.976-2.888c-.784-.57-.38-1.81.588-1.81h4.914a1 1 0 00.951-.69l1.519-4.674z"
                    />
                  </svg>
                </div>
                <div>
                  <h4 className="text-lg sm:text-xl font-bold text-gray-900 mb-1 sm:mb-2">
                    {t("nftPresale.feature1Title")}
                  </h4>
                  <p className="text-sm sm:text-base text-gray-600">
                    {t("nftPresale.feature1Description")}
                  </p>
                </div>
              </div>

              <div className="flex items-start space-x-3 sm:space-x-4">
                <div className="w-10 h-10 sm:w-12 sm:h-12 bg-amber-100 rounded-lg flex items-center justify-center flex-shrink-0">
                  <svg
                    className="w-5 h-5 sm:w-6 sm:h-6 text-amber-600"
                    fill="none"
                    stroke="currentColor"
                    viewBox="0 0 24 24"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={2}
                      d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1M21 12a9 9 0 11-18 0 9 9 0 0118 0z"
                    />
                  </svg>
                </div>
                <div>
                  <h4 className="text-lg sm:text-xl font-bold text-gray-900 mb-1 sm:mb-2">
                    {t("nftPresale.feature2Title")}
                  </h4>
                  <p className="text-sm sm:text-base text-gray-600">
                    {t("nftPresale.feature2Description")}
                  </p>
                </div>
              </div>

              <div className="flex items-start space-x-3 sm:space-x-4">
                <div className="w-10 h-10 sm:w-12 sm:h-12 bg-green-100 rounded-lg flex items-center justify-center flex-shrink-0">
                  <svg
                    className="w-5 h-5 sm:w-6 sm:h-6 text-green-600"
                    fill="none"
                    stroke="currentColor"
                    viewBox="0 0 24 24"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={2}
                      d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z"
                    />
                  </svg>
                </div>
                <div>
                  <h4 className="text-lg sm:text-xl font-bold text-gray-900 mb-1 sm:mb-2">
                    {t("nftPresale.feature3Title")}
                  </h4>
                  <p className="text-sm sm:text-base text-gray-600">
                    {t("nftPresale.feature3Description")}
                  </p>
                </div>
              </div>

              <div className="flex items-start space-x-3 sm:space-x-4">
                <div className="w-10 h-10 sm:w-12 sm:h-12 bg-blue-100 rounded-lg flex items-center justify-center flex-shrink-0">
                  <svg
                    className="w-5 h-5 sm:w-6 sm:h-6 text-blue-600"
                    fill="none"
                    stroke="currentColor"
                    viewBox="0 0 24 24"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={2}
                      d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"
                    />
                  </svg>
                </div>
                <div>
                  <h4 className="text-lg sm:text-xl font-bold text-gray-900 mb-1 sm:mb-2">
                    {t("nftPresale.feature4Title")}
                  </h4>
                  <p className="text-sm sm:text-base text-gray-600">
                    {t("nftPresale.feature4Description")}
                  </p>
                </div>
              </div>
            </div>
          </div>

          {/* Contact support */}
          <div className="card p-4 sm:p-8 text-center">
            <h3 className="text-xl sm:text-2xl font-bold text-gray-900 mb-3 sm:mb-4">
              {t("nftPresale.supportTitle")}
            </h3>
            <p className="text-sm sm:text-base text-gray-600 mb-4 sm:mb-6">
              {t("nftPresale.supportDescription")}
            </p>
            <div className="flex flex-col sm:flex-row justify-center gap-3 sm:gap-4">
              <a
                href={`mailto:${presaleSupportEmail}`}
                className="secondary-btn font-bold py-2 sm:py-3 px-4 sm:px-6 rounded-lg text-sm sm:text-base"
              >
                📧 {t("nftPresale.contactEmail")}
              </a>
              <a
                href={presaleTelegramUrl}
                target="_blank"
                rel="noopener noreferrer"
                className="primary-btn font-bold py-2 sm:py-3 px-4 sm:px-6 rounded-lg text-sm sm:text-base"
              >
                💬 {t("nftPresale.joinTelegram")}
              </a>
            </div>
          </div>
        </div>
      </main>

      {/* Footer */}
      <footer className="bg-white border-t border-gray-200">
        <div className="container mx-auto px-4 sm:px-6 py-6 sm:py-8 text-center">
          <p className="text-gray-600 text-xs sm:text-sm">
            {t("nftPresale.footer")}
          </p>
        </div>
      </footer>

      {/* Wallet connection modal */}
      {showWalletModal && (
        <WalletModal
          isOpen={showWalletModal}
          onClose={() => setShowWalletModal(false)}
          walletConfigs={wallet.walletConfigs}
          onWalletSelect={wallet.connectWalletWithType}
          selectedWallet={wallet.selectedWallet}
          isConnecting={wallet.isConnecting}
          getWalletAvailability={wallet.getWalletAvailability}
        />
      )}

      {/* Referral system modal */}
      {showReferralModal && (
        <ReferralModal
          isOpen={showReferralModal}
          onClose={() => setShowReferralModal(false)}
          presaleHook={presale}
        />
      )}

      {/* Referrer confirmation dialog */}
      {showReferrerConfirmModal && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
          <div className="bg-white rounded-lg max-w-md w-full p-6">
            <div className="flex items-center mb-4">
              <div className="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center mr-4">
                <svg
                  className="w-6 h-6 text-blue-600"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z"
                  />
                </svg>
              </div>
              <h3 className="text-lg font-bold text-gray-900">
                {t("referral.confirmReferrer")}
              </h3>
            </div>

            <div className="mb-6">
              <p className="text-gray-600 mb-4">
                {t("referral.confirmReferrerDesc")}
              </p>
              <div className="bg-gray-50 rounded-lg p-3">
                <p className="text-sm text-gray-500 mb-1">
                  {t("referral.referrerAddress")}
                </p>
                <p className="font-mono text-sm text-gray-900 break-all">
                  {pendingReferrer}
                </p>
              </div>
            </div>

            <div className="flex gap-3">
              <button
                onClick={handleCancelReferrerPurchase}
                className="flex-1 bg-gray-500 hover:bg-gray-600 text-white px-4 py-2 rounded-lg font-medium transition-colors duration-200"
              >
                {t("referral.purchaseWithoutReferrer")}
              </button>
              <button
                onClick={handleConfirmReferrerPurchase}
                className="flex-1 bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded-lg font-medium transition-colors duration-200"
              >
                {t("referral.confirmAndPurchase")}
              </button>
            </div>
          </div>
        </div>
      )}

      {/* Notification component */}
      {notification && (
        <Notification
          message={notification.message}
          type={notification.type}
          onClose={() => setNotification(null)}
        />
      )}
    </div>
  );
};

export default NFTPresale;
