import React, { useState, useEffect, useCallback } from "react";
import { Link } from "react-router-dom";
import { ethers } from "ethers";
import { useTranslation } from "react-i18next";
import { useWallet } from "../hooks/useWallet";
import { CONTRACT_ADDRESSES, ERC20_ABI } from "../utils/constants";
import WalletModal from "../components/WalletModal";
import Notification from "../components/Notification";
import LoadingOverlay from "../components/LoadingOverlay";
import LanguageSwitcher from "../components/LanguageSwitcher";
import GratTipsHistoryModal from "../components/GratTipsHistoryModal";
import TipsTipsHistoryModal from "../components/TipsTipsHistoryModal";
import NFTModal from "../components/NFTModal";
import InviteModal from "../components/InviteModal";
import InviteRewardsModal from "../components/InviteRewardsModal";
import MobileNavigation from "../components/MobileNavigation";
import MechanismOneUSDT from "../components/MechanismOneUSDT";
import RealtimeEarnings from "../components/RealtimeEarnings";
import EnhancedMiningHistory from "../components/EnhancedMiningHistory";
import ClaimHistoryModal from "../components/ClaimHistoryModal";
import AcceleratedReleaseInfo from "../components/AcceleratedReleaseInfo";

const LaunchApp = () => {
  const { t } = useTranslation();
  const {
    userAddress,
    provider,
    signer,
    contracts,
    selectedWallet,
    isConnecting,
    isConnected,
    connectWalletWithType,
    handleDisconnect,
    formatAddress,
    getWalletAvailability,
    walletConfigs,
  } = useWallet();

  // UI state
  const [showWalletModal, setShowWalletModal] = useState(false);
  const [showGratTipsHistoryModal, setShowGratTipsHistoryModal] =
    useState(false);
  const [showTipsTipsHistoryModal, setShowTipsTipsHistoryModal] =
    useState(false);
  const [showNFTModal, setShowNFTModal] = useState(false);
  const [showInviteModal, setShowInviteModal] = useState(false);
  const [showInviteRewardsModal, setShowInviteRewardsModal] = useState(false);
  const [showEnhancedHistoryModal, setShowEnhancedHistoryModal] =
    useState(false);
  const [enhancedHistoryMechanism, setEnhancedHistoryMechanism] = useState(1);
  const [showClaimHistoryModal, setShowClaimHistoryModal] = useState(false);
  const [notification, setNotification] = useState({
    message: "",
    type: "success",
    isVisible: false,
  });
  const [loading, setLoading] = useState({ isVisible: false, text: "" });

  // User data state
  const [balances, setBalances] = useState({
    tokenA: "0",
    tokenB: "0",
    nftCount: "0",
    usdt: "0",
  });

  // Mining input state
  const [miningInputs, setMiningInputs] = useState({
    burnTokenB: "",
    expectedReleaseValue: "0",
    tokenBUsdtValue: "0",
    priceReliable: true,
  });

  // Mining records state
  const [miningRecords, setMiningRecords] = useState({
    mechanism1: [],
    mechanism2: [],
  });

  // NFT data state
  const [nftData, setNftData] = useState([]);

  // User invite info state
  const [userInviteInfo, setUserInviteInfo] = useState({
    inviter: null,
    totalInvited: 0,
    acceleratedReleaseAmount: "0",
    totalAcceleratedReceived: "0",
    totalAcceleratedUsed: "0",
    inviteeList: [],
  });

  // Invitation URL parameter state
  const [inviterFromUrl, setInviterFromUrl] = useState(null);

  // Show notification
  const showNotification = useCallback((message, type = "success") => {
    setNotification({ message, type, isVisible: true });
  }, []);

  // Hide notification
  const hideNotification = useCallback(() => {
    setNotification((prev) => ({ ...prev, isVisible: false }));
  }, []);

  // Show loading
  const showLoading = useCallback(
    (text) => {
      setLoading({
        isVisible: true,
        text: text || t("notifications.processing"),
      });
    },
    [t]
  );

  // Hide loading
  const hideLoading = useCallback(() => {
    setLoading({ isVisible: false, text: "" });
  }, []);

  // Handle wallet selection
  const handleWalletSelect = useCallback(
    async (walletType) => {
      try {
        showLoading(
          `${t("wallet.connecting")} ${walletConfigs[walletType].name}...`
        );
        await connectWalletWithType(walletType);
        setShowWalletModal(false);
        showNotification(
          `${walletConfigs[walletType].name} ${t("wallet.connectionSuccess")}`,
          "success"
        );
      } catch (error) {
        console.error("Wallet connection failed:", error);
        showNotification(
          `${t("wallet.connectionFailed")}: ${error.message}`,
          "error"
        );
      } finally {
        hideLoading();
      }
    },
    [
      connectWalletWithType,
      walletConfigs,
      showLoading,
      hideLoading,
      showNotification,
    ]
  );

  // Load user balances
  const loadBalances = useCallback(async () => {
    if (
      !contracts.tokenA ||
      !contracts.tokenB ||
      !contracts.minerNFT ||
      !userAddress ||
      !provider
    ) {
      return;
    }

    try {
      // Create USDT contract instance
      const usdtContract = new ethers.Contract(
        CONTRACT_ADDRESSES.USDT,
        ERC20_ABI,
        provider
      );

      const [tokenABalance, tokenBBalance, nftBalance, usdtBalance] =
        await Promise.all([
          contracts.tokenA.balanceOf(userAddress),
          contracts.tokenB.balanceOf(userAddress),
          contracts.minerNFT.balanceOf(userAddress),
          usdtContract.balanceOf(userAddress),
        ]);

      setBalances({
        tokenA: ethers.formatEther(tokenABalance),
        tokenB: ethers.formatEther(tokenBBalance),
        nftCount: nftBalance.toString(),
        usdt: ethers.formatUnits(usdtBalance, 18), // USDT uses 18 decimals
      });
    } catch (error) {
      console.error("Failed to load balances:", error);
      showNotification(t("notifications.loadBalancesFailed"), "error");
    }
  }, [contracts, provider, userAddress, showNotification, t]);

  // Load NFT data
  const loadNFTData = useCallback(async () => {
    if (!contracts.minerNFT || !userAddress) {
      return;
    }

    try {
      const nftCount = await contracts.minerNFT.balanceOf(userAddress);
      const nftList = [];

      for (let i = 0; i < nftCount; i++) {
        const tokenId = await contracts.minerNFT.tokenOfOwnerByIndex(
          userAddress,
          i
        );
        const [releasableAmount, isMinering] = await Promise.all([
          contracts.minerNFT.getReleasableAmount(tokenId),
          contracts.minerNFT.isMinering(tokenId),
        ]);

        nftList.push({
          tokenId: tokenId.toString(),
          releasableAmount: ethers.formatEther(releasableAmount),
          isMinering: isMinering,
        });
      }

      setNftData(nftList);
    } catch (error) {
      console.error("Failed to load NFT data:", error);
      showNotification(t("notifications.loadNFTDataFailed"), "error");
    }
  }, [contracts, userAddress, showNotification]);

  // Load mining records
  const loadMiningRecords = useCallback(async () => {
    if (!contracts.miningContract || !userAddress) {
      return;
    }

    try {
      const [count1, count2] =
        await contracts.miningContract.getUserMiningRecordCount(userAddress);

      const records1 = [];
      const records2 = [];

      // Load mechanism 1 records with detailed accelerated release info
      for (let i = 0; i < count1; i++) {
        const record =
          await contracts.miningContract.getUserMiningRecord1Detail(
            userAddress,
            i
          );
        records1.push({
          index: i,
          burnedTokenAAmount: ethers.formatEther(record[0]),
          burnedTokenAValue: ethers.formatEther(record[1]),
          expectedTokenBAmount: ethers.formatEther(record[2]),
          totalReleaseValue: ethers.formatEther(record[3]),
          releasedValue: ethers.formatEther(record[4]),
          acceleratedReleasedValue: ethers.formatEther(record[5]),
          normalReleasedValue: ethers.formatEther(record[6]),
          acceleratedTime: Number(record[7]),
          startTime: new Date(Number(record[8]) * 1000),
          lastReleaseTime: new Date(Number(record[9]) * 1000),
          active: record[10],
        });
      }

      // Load mechanism 2 records with detailed accelerated release info
      for (let i = 0; i < count2; i++) {
        const record =
          await contracts.miningContract.getUserMiningRecord2Detail(
            userAddress,
            i
          );
        records2.push({
          index: i,
          burnedTokenBAmount: ethers.formatEther(record[0]),
          burnedTokenBValue: ethers.formatEther(record[1]),
          totalReleaseValue: ethers.formatEther(record[2]),
          releasedValue: ethers.formatEther(record[3]),
          acceleratedReleasedValue: ethers.formatEther(record[4]),
          normalReleasedValue: ethers.formatEther(record[5]),
          acceleratedTime: Number(record[6]),
          startTime: new Date(Number(record[7]) * 1000),
          lastReleaseTime: new Date(Number(record[8]) * 1000),
          active: record[9],
        });
      }

      setMiningRecords({ mechanism1: records1, mechanism2: records2 });
    } catch (error) {
      console.error("Failed to load mining records:", error);
      showNotification(t("notifications.loadMiningRecordsFailed"), "error");
    }
  }, [contracts, userAddress, showNotification]);

  // Load user invite info
  const loadUserInviteInfo = useCallback(async () => {
    if (!contracts.miningContract || !userAddress) {
      return;
    }

    try {
      // Try the new full info function first, fallback to old one
      let inviter,
        totalInvited,
        currentUnused,
        totalReceived,
        totalUsed,
        inviteeList;

      try {
        [
          inviter,
          totalInvited,
          currentUnused,
          totalReceived,
          totalUsed,
          inviteeList,
        ] = await contracts.miningContract.getUserInviteInfoFull(userAddress);
      } catch (fullInfoError) {
        // Fallback to old function for backward compatibility
        console.warn("Using fallback getUserInviteInfo:", fullInfoError);
        [inviter, totalInvited, currentUnused, inviteeList] =
          await contracts.miningContract.getUserInviteInfo(userAddress);
        totalReceived = currentUnused; // Approximate
        totalUsed = "0";
      }

      setUserInviteInfo({
        inviter:
          inviter === "******************************************"
            ? null
            : inviter,
        totalInvited: totalInvited.toString(),
        acceleratedReleaseAmount: ethers.formatEther(currentUnused),
        totalAcceleratedReceived: ethers.formatEther(totalReceived),
        totalAcceleratedUsed: ethers.formatEther(totalUsed),
        inviteeList: inviteeList,
      });
    } catch (error) {
      console.error("Failed to load user invite info:", error);
      showNotification(t("notifications.loadInviteInfoFailed"), "error");
    }
  }, [contracts, userAddress, showNotification, t]);

  // Calculate TokenB value in USDT and expected release value
  const calculateTokenBValue = useCallback(
    async (tokenBAmount) => {
      if (!contracts.miningContract || !tokenBAmount || tokenBAmount === "0") {
        return { usdtValue: "0", expectedReleaseValue: "0" };
      }

      try {
        // Get TokenB address
        const tokenBAddress = await contracts.tokenB.getAddress();
        console.log("TokenB address:", tokenBAddress);

        // Try to get TokenB price from DEX
        let tokenBPrice;
        let priceReliable = false;

        try {
          tokenBPrice = await contracts.miningContract.getTokenPrice(
            tokenBAddress
          );
          console.log("TokenB price from DEX:", tokenBPrice.toString());
          priceReliable = true;
        } catch (priceError) {
          console.log("Failed to get TokenB price from DEX, using fallback");
          // Use fallback price: 0.001 USDT per TokenB
          tokenBPrice = ethers.parseEther("0.001");
          priceReliable = false;
        }

        const tokenBAmountWei = ethers.parseEther(tokenBAmount);

        // Calculate USDT value: (tokenBAmount * tokenBPrice) / 1e18
        const usdtValueWei =
          (tokenBAmountWei * tokenBPrice) / ethers.parseEther("1");
        const usdtValue = ethers.formatEther(usdtValueWei);

        // Use hardcoded reward multiplier (2x) since it's private constant in contract
        const rewardMultiplier = 2;
        console.log("Reward multiplier:", rewardMultiplier);

        // Calculate expected release value: usdtValue * rewardMultiplier
        const expectedReleaseValue = (
          parseFloat(usdtValue) * rewardMultiplier
        ).toString();

        return {
          usdtValue: parseFloat(usdtValue).toFixed(6),
          expectedReleaseValue: parseFloat(expectedReleaseValue).toFixed(6),
          priceReliable: priceReliable,
        };
      } catch (error) {
        console.error("Failed to calculate TokenB value:", error);
        console.error("Error details:", error.message);

        // Fallback calculation using simple 2x multiplier
        const fallbackValue = (parseFloat(tokenBAmount) * 0.001).toString(); // Assume 0.001 USDT per TokenB
        const fallbackRelease = (parseFloat(fallbackValue) * 2).toString();

        return {
          usdtValue: parseFloat(fallbackValue).toFixed(6),
          expectedReleaseValue: parseFloat(fallbackRelease).toFixed(6),
          priceReliable: false,
        };
      }
    },
    [contracts]
  );

  // Burn TokenB mining
  const burnTokenB = useCallback(async () => {
    if (!contracts.miningContract || !miningInputs.burnTokenB) {
      showNotification(
        `${t("notifications.enterValidAmount")} TokenB`,
        "error"
      );
      return;
    }

    try {
      // Check NFT count
      if (balances.nftCount === "0") {
        showNotification(t("notifications.mechanism2RequiresNFT"), "error");
        return;
      }

      showLoading(t("notifications.executingTransaction"));
      const amount = ethers.parseEther(miningInputs.burnTokenB);

      // Check authorization
      const allowance = await contracts.tokenB.allowance(
        userAddress,
        await contracts.miningContract.getAddress()
      );
      if (allowance < amount) {
        showLoading(`${t("notifications.approving")} TokenB...`);
        const approveTx = await contracts.tokenB.approve(
          await contracts.miningContract.getAddress(),
          amount
        );
        await approveTx.wait();
      }

      showLoading(t("notifications.executingTransaction"));
      const tx = await contracts.miningContract.burnTokenBWithNFT(
        amount,
        ethers.ZeroAddress
      );
      await tx.wait();

      showNotification(t("notifications.miningSuccess"), "success");

      // Reload data
      setMiningInputs((prev) => ({
        ...prev,
        burnTokenB: "",
        expectedReleaseValue: "0",
        tokenBUsdtValue: "0",
        priceReliable: true,
      }));
      await Promise.all([loadBalances(), loadMiningRecords()]);
    } catch (error) {
      console.error("Mining failed:", error);
      showNotification(
        `${t("notifications.miningFailed")}: ${error.message}`,
        "error"
      );
    } finally {
      hideLoading();
    }
  }, [
    contracts,
    miningInputs.burnTokenB,
    userAddress,
    balances.nftCount,
    showLoading,
    hideLoading,
    showNotification,
    loadBalances,
    loadMiningRecords,
  ]);

  // Release NFT TokenA
  const releaseNFTTokens = useCallback(
    async (tokenId) => {
      if (!contracts.minerNFT) {
        return;
      }

      try {
        showLoading(t("notifications.releasingTokenA"));
        const tx = await contracts.minerNFT.releaseTokens(tokenId);
        await tx.wait();

        showNotification(t("notifications.releaseSuccess"), "success");

        // Reload data
        await Promise.all([loadBalances(), loadNFTData()]);
      } catch (error) {
        console.error("Release failed:", error);
        showNotification(
          `${t("notifications.releaseFailed")}: ${error.message}`,
          "error"
        );
      } finally {
        hideLoading();
      }
    },
    [
      contracts,
      showLoading,
      hideLoading,
      showNotification,
      loadBalances,
      loadNFTData,
    ]
  );

  // Claim mining rewards from mechanism 1 (GRAT → TIPS)
  const claimMechanism1Rewards = useCallback(async () => {
    if (!contracts.miningContract) {
      return;
    }

    try {
      showLoading(t("notifications.claimingRewards") || "Claiming rewards...");
      const tx = await contracts.miningContract.claimTokenBFromMechanism1();
      await tx.wait();

      showNotification(
        t("notifications.claimSuccess") || "Rewards claimed successfully!",
        "success"
      );

      // Reload data
      await Promise.all([loadBalances(), loadMiningRecords()]);
    } catch (error) {
      console.error("Claim failed:", error);
      showNotification(
        `${t("notifications.claimFailed") || "Claim failed"}: ${error.message}`,
        "error"
      );
    } finally {
      hideLoading();
    }
  }, [
    contracts,
    showLoading,
    hideLoading,
    showNotification,
    loadBalances,
    loadMiningRecords,
  ]);

  // Claim mining rewards from mechanism 2 (TIPS → GRAT)
  const claimMechanism2Rewards = useCallback(async () => {
    if (!contracts.miningContract) {
      return;
    }

    try {
      showLoading(t("notifications.claimingRewards") || "Claiming rewards...");
      const tx = await contracts.miningContract.claimTokenBFromMechanism2();
      await tx.wait();

      showNotification(
        t("notifications.claimSuccess") || "Rewards claimed successfully!",
        "success"
      );

      // Reload data
      await Promise.all([loadBalances(), loadMiningRecords()]);
    } catch (error) {
      console.error("Claim failed:", error);
      showNotification(
        `${t("notifications.claimFailed") || "Claim failed"}: ${error.message}`,
        "error"
      );
    } finally {
      hideLoading();
    }
  }, [
    contracts,
    showLoading,
    hideLoading,
    showNotification,
    loadBalances,
    loadMiningRecords,
  ]);

  // Load user data
  const loadUserData = useCallback(async () => {
    if (isConnected) {
      await Promise.all([
        loadBalances(),
        loadNFTData(),
        loadMiningRecords(),
        loadUserInviteInfo(),
      ]);
    }
  }, [
    isConnected,
    loadBalances,
    loadNFTData,
    loadMiningRecords,
    loadUserInviteInfo,
  ]);

  // When wallet connection state changes, load data
  useEffect(() => {
    loadUserData();
  }, [loadUserData]);

  // Check for invitation parameter in URL on page load
  useEffect(() => {
    const urlParams = new URLSearchParams(window.location.search);
    const inviterParam = urlParams.get("inviter");

    if (inviterParam && ethers.isAddress(inviterParam)) {
      setInviterFromUrl(inviterParam);
      showNotification(t("invite.inviterDetected"), "info");

      // If user is not connected, show a different message encouraging them to connect
      if (!isConnected) {
        setTimeout(() => {
          showNotification(t("wallet.connectToStart"), "info");
        }, 3000);
      }
    }
  }, []); // Run only once on component mount

  // Handle invitation when user connects wallet
  useEffect(() => {
    if (
      isConnected &&
      inviterFromUrl &&
      userAddress &&
      inviterFromUrl !== userAddress
    ) {
      // Check if user already has an inviter and show modal
      const checkAndShowInviteModal = async () => {
        try {
          const hasInviterResult = await contracts.miningContract.hasInviter(
            userAddress
          );
          if (!hasInviterResult) {
            setTimeout(() => {
              setShowInviteModal(true);
            }, 2000);
          }
        } catch (error) {
          console.error("Failed to check inviter status:", error);
        }
      };

      checkAndShowInviteModal();
    }
  }, [isConnected, inviterFromUrl, userAddress, contracts.miningContract]);

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header navigation */}
      <header className="fixed top-0 left-0 right-0 z-40 bg-white/80 backdrop-blur-md border-b border-gray-200/80">
        <div className="container mx-auto px-6 py-4 flex justify-between items-center">
          {/* Logo */}
          <Link
            to="/"
            className="flex items-center space-x-2 text-2xl font-bold font-poppins"
          >
            <img
              src="/icons/TokenA-GRAT.svg"
              alt="GRAT Token"
              className="w-8 h-8"
            />
            <span className="text-violet-700">$</span>
            <span className="text-gray-800">GRAT</span>
          </Link>

          {/* Desktop navigation */}
          <nav className="hidden md:flex items-center space-x-4 text-gray-600 font-semibold">
            <Link
              to="/"
              className="flex items-center gap-2 hover:text-violet-700 transition-colors px-3 py-2 rounded-lg hover:bg-violet-50"
              aria-label={t("nav.homeDesc")}
            >
              <svg
                className="w-4 h-4"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth="2"
                  d="M3 12l2-2m0 0l7-7 7 7M5 10v10a1 1 0 001 1h3m10-11l2 2m-2-2v10a1 1 0 01-1 1h-3m-6 0a1 1 0 001-1v-4a1 1 0 011-1h2a1 1 0 011 1v4a1 1 0 001 1m-6 0h6"
                />
              </svg>
              <span>{t("nav.home")}</span>
            </Link>
            <div className="flex items-center gap-2 text-violet-700 bg-violet-50 px-3 py-2 rounded-lg border border-violet-200">
              <svg
                className="w-4 h-4"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth="2"
                  d="M13 10V3L4 14h7v7l9-11h-7z"
                />
              </svg>
              <span className="font-semibold">{t("nav.launchApp")}</span>
              <div className="w-2 h-2 bg-violet-500 rounded-full animate-pulse"></div>
            </div>
          </nav>

          {/* Right side - Desktop wallet connection area or Mobile menu button */}
          <div className="flex items-center gap-3 lg:gap-4">
            {/* Desktop content - hidden on mobile */}
            <div className="hidden md:flex items-center gap-3 lg:gap-4">
              {/* Language switcher with enhanced styling */}
              <div className="relative">
                <LanguageSwitcher />
              </div>

              {/* Enhanced address display */}
              {isConnected && (
                <div className="flex items-center gap-3 bg-gradient-to-r from-green-50 to-emerald-50 border border-green-200 rounded-xl px-4 py-2.5 shadow-sm hover:shadow-md transition-all duration-200">
                  <div className="flex items-center gap-2">
                    <div className="relative">
                      <div className="w-2.5 h-2.5 bg-green-500 rounded-full animate-pulse"></div>
                      <div className="absolute inset-0 w-2.5 h-2.5 bg-green-400 rounded-full animate-ping"></div>
                    </div>
                    <span className="text-xs text-green-700 font-semibold uppercase tracking-wide">
                      {t("wallet.connected")}
                    </span>
                  </div>
                  <div className="bg-white bg-opacity-70 rounded-lg px-2 py-1 border border-green-100">
                    <span className="text-sm text-gray-700 font-mono font-medium">
                      {formatAddress(userAddress)}
                    </span>
                  </div>
                  <button
                    onClick={async (event) => {
                      try {
                        await navigator.clipboard.writeText(userAddress);
                        // Simple success notification - could be enhanced with a toast component
                        console.log("Address copied to clipboard");
                      } catch (err) {
                        console.log("Copy failed:", err);
                      }
                    }}
                    className="p-2 hover:bg-green-100 rounded-lg transition-all duration-200 hover:scale-105 focus:outline-none focus:ring-2 focus:ring-green-500 focus:ring-opacity-50"
                    title={t("wallet.copyAddress")}
                    aria-label={`${t("wallet.copyAddress")}: ${formatAddress(
                      userAddress
                    )}`}
                  >
                    <svg
                      className="w-4 h-4 text-green-600"
                      fill="none"
                      stroke="currentColor"
                      viewBox="0 0 24 24"
                    >
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth="2"
                        d="M8 16H6a2 2 0 01-2-2V6a2 2 0 012-2h8a2 2 0 012 2v2m-6 12h8a2 2 0 002-2v-8a2 2 0 00-2-2h-8a2 2 0 00-2 2v8a2 2 0 002 2z"
                      />
                    </svg>
                  </button>
                </div>
              )}

              {/* Enhanced action buttons */}
              {isConnected ? (
                <button
                  onClick={handleDisconnect}
                  className="secondary-btn font-semibold py-2.5 px-5 rounded-xl text-sm transition-all duration-200 hover:scale-105 flex items-center gap-2 focus:outline-none focus:ring-2 focus:ring-violet-500 focus:ring-opacity-50"
                  aria-label={`${t("wallet.disconnect")} ${formatAddress(
                    userAddress
                  )}`}
                >
                  <svg
                    className="w-4 h-4"
                    fill="none"
                    stroke="currentColor"
                    viewBox="0 0 24 24"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth="2"
                      d="M17 16l4-4m0 0l-4-4m4 4H7m6 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h4a3 3 0 013 3v1"
                    />
                  </svg>
                  <span>{t("wallet.disconnect")}</span>
                </button>
              ) : (
                <button
                  onClick={() => setShowWalletModal(true)}
                  className="primary-btn font-semibold py-2.5 px-6 rounded-xl text-sm transition-all duration-200 hover:scale-105 shadow-lg hover:shadow-xl flex items-center gap-2 focus:outline-none focus:ring-2 focus:ring-violet-500 focus:ring-opacity-50"
                  disabled={isConnecting}
                  aria-label={t("wallet.connectToStart")}
                >
                  {isConnecting ? (
                    <>
                      <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
                      <span>{t("wallet.connecting")}</span>
                    </>
                  ) : (
                    <>
                      <svg
                        className="w-4 h-4"
                        fill="none"
                        stroke="currentColor"
                        viewBox="0 0 24 24"
                      >
                        <path
                          strokeLinecap="round"
                          strokeLinejoin="round"
                          strokeWidth="2"
                          d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z"
                        />
                      </svg>
                      <span>{t("wallet.connectWallet")}</span>
                    </>
                  )}
                </button>
              )}
            </div>

            {/* Mobile menu button - only visible on mobile */}
            <MobileNavigation
              isConnected={isConnected}
              userAddress={userAddress}
              formatAddress={formatAddress}
              handleDisconnect={handleDisconnect}
              onConnectWallet={() => setShowWalletModal(true)}
              isConnecting={isConnecting}
              // LaunchApp specific props
              balances={balances}
              onMiningClick={() => {
                // Scroll to mining section or open mining modal
                const miningSection = document.getElementById("mining-section");
                if (miningSection) {
                  miningSection.scrollIntoView({ behavior: "smooth" });
                }
              }}
              onNFTClick={() => setShowNFTModal(true)}
              onInviteClick={() => setShowInviteModal(true)}
              showQuickActions={true}
            />
          </div>
        </div>
      </header>

      {/* Main content */}
      <main className="pt-24">
        {!isConnected ? (
          /* Wallet connection prompt */
          <div className="container mx-auto px-4 sm:px-6 py-12 sm:py-24 text-center">
            <div className="max-w-2xl mx-auto">
              <div className="w-16 h-16 sm:w-24 sm:h-24 bg-violet-100 rounded-2xl flex items-center justify-center mx-auto mb-6 sm:mb-8">
                <svg
                  className="w-8 h-8 sm:w-12 sm:h-12 text-violet-600"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth="2"
                    d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z"
                  />
                </svg>
              </div>

              <h1 className="text-xl sm:text-2xl md:text-3xl lg:text-5xl font-black text-gray-900 leading-tight mb-4 sm:mb-6">
                {t("dashboard.connectToStart")}
              </h1>

              <p className="text-sm sm:text-base lg:text-lg text-gray-600 mb-6 sm:mb-8">
                {t("dashboard.supportedWallets")}
              </p>

              <button
                onClick={() => setShowWalletModal(true)}
                className="primary-btn font-bold py-3 px-6 sm:py-4 sm:px-8 rounded-lg sm:rounded-xl text-base sm:text-lg inline-flex items-center"
                disabled={isConnecting}
              >
                <svg
                  className="w-4 h-4 sm:w-5 sm:h-5 mr-2"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth="2"
                    d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z"
                  />
                </svg>
                {isConnecting
                  ? t("wallet.connecting")
                  : t("wallet.selectWallet")}
              </button>

              <div className="bg-amber-50 border border-amber-200 rounded-lg p-4 mt-8">
                <div className="flex items-start">
                  <svg
                    className="w-5 h-5 text-amber-500 mt-0.5 mr-3 flex-shrink-0"
                    fill="none"
                    stroke="currentColor"
                    viewBox="0 0 24 24"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth="2"
                      d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"
                    />
                  </svg>
                  <div className="text-sm text-amber-800">
                    <p className="font-semibold mb-1">{t("dashboard.tip")}</p>
                    <p>{t("dashboard.tipDesc")}</p>
                  </div>
                </div>
              </div>
            </div>
          </div>
        ) : (
          /* Dashboard content */
          <div className="container mx-auto px-4 sm:px-6 py-6 sm:py-8">
            {/* User overview */}
            <div className="mb-8 sm:mb-12">
              <h1 className="text-2xl sm:text-3xl md:text-5xl font-black text-gray-900 leading-tight mb-6 sm:mb-8 text-center">
                {t("dashboard.title")}
              </h1>

              <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4 sm:gap-6 lg:gap-8 mb-6 sm:mb-8">
                {/* USDT Balance */}
                <div className="card p-6 lg:p-8 hover:shadow-lg transition-all duration-300 transform hover:-translate-y-1">
                  <div className="flex items-center justify-between mb-4 lg:mb-6">
                    <h3 className="text-lg lg:text-xl xl:text-2xl font-bold text-gray-900">
                      {t("dashboard.usdtBalance")}
                    </h3>
                    <div className="w-12 h-12 lg:w-14 lg:h-14 xl:w-16 xl:h-16 bg-blue-100 rounded-full flex items-center justify-center">
                      <span className="text-blue-600 font-bold text-sm lg:text-base xl:text-lg">
                        💰
                      </span>
                    </div>
                  </div>
                  <div className="text-3xl lg:text-4xl xl:text-5xl font-bold text-blue-600 mb-2 lg:mb-4">
                    {parseFloat(balances.usdt).toFixed(2)}
                  </div>
                  <div className="text-sm lg:text-base text-gray-500">
                    {t("dashboard.usdtBalanceDesc")}
                  </div>
                </div>

                {/* TokenA balance */}
                <div className="card p-6 lg:p-8 hover:shadow-lg transition-all duration-300 transform hover:-translate-y-1">
                  <div className="flex items-center justify-between mb-4 lg:mb-6">
                    <h3 className="text-lg lg:text-xl xl:text-2xl font-bold text-gray-900">
                      {t("dashboard.tokenABalance")}
                    </h3>
                    <div className="w-12 h-12 lg:w-14 lg:h-14 xl:w-16 xl:h-16 bg-violet-100 rounded-full flex items-center justify-center">
                      <img
                        src="/icons/TokenA-GRAT.svg"
                        alt="GRAT Token"
                        className="w-6 h-6 lg:w-7 lg:h-7 xl:w-8 xl:h-8"
                      />
                    </div>
                  </div>
                  <div className="text-3xl lg:text-4xl xl:text-5xl font-bold text-violet-700 mb-2 lg:mb-4">
                    {parseFloat(balances.tokenA).toFixed(2)}
                  </div>
                  <div className="text-sm lg:text-base text-gray-500">
                    {t("dashboard.tokenABalanceDesc")}
                  </div>
                </div>

                {/* TokenB balance */}
                <div className="card p-6 lg:p-8 hover:shadow-lg transition-all duration-300 transform hover:-translate-y-1">
                  <div className="flex items-center justify-between mb-4 lg:mb-6">
                    <h3 className="text-lg lg:text-xl xl:text-2xl font-bold text-gray-900">
                      {t("dashboard.tokenBBalance")}
                    </h3>
                    <div className="w-12 h-12 lg:w-14 lg:h-14 xl:w-16 xl:h-16 bg-green-100 rounded-full flex items-center justify-center">
                      <img
                        src="/icons/TokenB-TIPS.svg"
                        alt="TIPS Token"
                        className="w-6 h-6 lg:w-7 lg:h-7 xl:w-8 xl:h-8"
                      />
                    </div>
                  </div>
                  <div className="text-3xl lg:text-4xl xl:text-5xl font-bold text-green-600 mb-2 lg:mb-4">
                    {parseFloat(balances.tokenB).toFixed(2)}
                  </div>
                  <div className="text-sm lg:text-base text-gray-500">
                    {t("dashboard.tokenBBalanceDesc")}
                  </div>
                </div>

                {/* NFT count */}
                <div className="card p-6 lg:p-8 hover:shadow-lg transition-all duration-300 transform hover:-translate-y-1">
                  <div className="flex items-center justify-between mb-4 lg:mb-6">
                    <h3 className="text-lg lg:text-xl xl:text-2xl font-bold text-gray-900">
                      {t("dashboard.nftCount")}
                    </h3>
                    <div className="w-12 h-12 lg:w-14 lg:h-14 xl:w-16 xl:h-16 bg-gradient-to-br from-purple-100 to-purple-200 rounded-full flex items-center justify-center">
                      <img
                        src="/icons/MinerNFT-Rabbit.svg"
                        alt="Miner NFT"
                        className="w-6 h-6 lg:w-7 lg:h-7 xl:w-8 xl:h-8"
                      />
                    </div>
                  </div>
                  <div className="text-3xl lg:text-4xl xl:text-5xl font-bold text-purple-600 mb-2 lg:mb-4">
                    {balances.nftCount}
                  </div>
                  <div className="text-sm lg:text-base text-gray-500">
                    {t("dashboard.nftCountDesc")}
                  </div>
                </div>
              </div>
            </div>

            {/* Real-time Earnings Display */}
            <div className="mb-8 sm:mb-12">
              <RealtimeEarnings
                contracts={contracts}
                userAddress={userAddress}
                provider={provider}
                showNotification={showNotification}
                onClaimRewards={(mechanism) => {
                  if (mechanism === 1) {
                    claimMechanism1Rewards();
                  } else {
                    claimMechanism2Rewards();
                  }
                }}
              />
            </div>

            {/* Accelerated Release Info */}
            <div className="mb-8 sm:mb-12">
              <AcceleratedReleaseInfo userInviteInfo={userInviteInfo} />
            </div>

            {/* Mining operations area */}
            <div id="mining-section" className="mb-8 sm:mb-12">
              <h2 className="text-2xl sm:text-3xl font-bold text-gray-900 mb-6 sm:mb-8 text-center">
                {t("mining.title")}
              </h2>

              <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 sm:gap-8">
                {/* Mechanism 1: USDT Purchase & Burn */}
                <MechanismOneUSDT
                  contracts={contracts}
                  provider={provider}
                  signer={signer}
                  userAddress={userAddress}
                  showNotification={showNotification}
                  showLoading={showLoading}
                  hideLoading={hideLoading}
                  onTransactionComplete={loadUserData}
                  inviterFromUrl={inviterFromUrl}
                />

                {/* Mechanism 2: Burn TokenB to get release value */}
                <div className="card p-8">
                  <div className="flex items-center justify-between mb-6">
                    <h3 className="text-xl font-bold text-gray-900">
                      {t("mining.mechanism2.title")}
                    </h3>
                    <div className="w-12 h-12 bg-amber-100 rounded-full flex items-center justify-center">
                      <span className="text-amber-600 font-bold">2</span>
                    </div>
                  </div>

                  <p className="text-gray-600 mb-6">
                    {t("mining.mechanism2.description")}
                    {balances.nftCount === "0" && (
                      <span className="text-red-500">
                        {" "}
                        {t("mining.mechanism2.needsNFT")}
                      </span>
                    )}
                  </p>

                  <div className="space-y-4 mb-6">
                    <div className="flex gap-2">
                      <input
                        type="number"
                        value={miningInputs.burnTokenB}
                        onChange={async (e) => {
                          const value = e.target.value;
                          setMiningInputs((prev) => ({
                            ...prev,
                            burnTokenB: value,
                          }));

                          // Calculate TokenB value in USDT and expected release value
                          if (value && parseFloat(value) > 0) {
                            const {
                              usdtValue,
                              expectedReleaseValue,
                              priceReliable,
                            } = await calculateTokenBValue(value);
                            setMiningInputs((prev) => ({
                              ...prev,
                              tokenBUsdtValue: usdtValue,
                              expectedReleaseValue: expectedReleaseValue,
                              priceReliable:
                                priceReliable !== undefined
                                  ? priceReliable
                                  : true,
                            }));
                          } else {
                            setMiningInputs((prev) => ({
                              ...prev,
                              tokenBUsdtValue: "0",
                              expectedReleaseValue: "0",
                              priceReliable: true,
                            }));
                          }
                        }}
                        placeholder={t("mining.mechanism2.placeholder")}
                        disabled={balances.nftCount === "0"}
                        className="flex-1 bg-gray-100 rounded-lg p-3 text-gray-800 placeholder-gray-400 border border-gray-200 focus:border-amber-500 focus:ring-amber-500 focus:outline-none disabled:opacity-50"
                      />
                      <button
                        onClick={async () => {
                          const maxValue = balances.tokenB;
                          setMiningInputs((prev) => ({
                            ...prev,
                            burnTokenB: maxValue,
                          }));

                          // Calculate TokenB value in USDT and expected release value
                          if (maxValue && parseFloat(maxValue) > 0) {
                            const {
                              usdtValue,
                              expectedReleaseValue,
                              priceReliable,
                            } = await calculateTokenBValue(maxValue);
                            setMiningInputs((prev) => ({
                              ...prev,
                              tokenBUsdtValue: usdtValue,
                              expectedReleaseValue: expectedReleaseValue,
                              priceReliable:
                                priceReliable !== undefined
                                  ? priceReliable
                                  : true,
                            }));
                          }
                        }}
                        disabled={balances.nftCount === "0"}
                        className="secondary-btn px-4 py-3 rounded-lg text-sm font-semibold disabled:opacity-50"
                      >
                        MAX
                      </button>
                    </div>

                    <div className="bg-gray-50 rounded-lg p-4 space-y-3">
                      <div>
                        <div className="text-sm text-gray-600 mb-1">
                          TokenB USDT Value
                          {!miningInputs.priceReliable && (
                            <span className="ml-2 text-xs text-orange-600 font-medium">
                              (Estimated Price)
                            </span>
                          )}
                        </div>
                        <div className="text-lg font-bold text-blue-600">
                          {miningInputs.tokenBUsdtValue} USDT
                        </div>
                      </div>
                      <div>
                        <div className="text-sm text-gray-600 mb-1">
                          {t("mining.mechanism2.expectedReleaseValue")}
                        </div>
                        <div className="text-lg font-bold text-violet-600">
                          {miningInputs.expectedReleaseValue} TIPS
                        </div>
                      </div>
                      {!miningInputs.priceReliable && (
                        <div className="text-xs text-orange-600 bg-orange-50 p-2 rounded">
                          Due to insufficient TokenB/USDT pair liquidity, price
                          is estimated. Actual mining rewards are based on
                          contract calculations.
                        </div>
                      )}
                    </div>
                  </div>

                  <button
                    onClick={burnTokenB}
                    disabled={
                      !miningInputs.burnTokenB ||
                      parseFloat(miningInputs.burnTokenB) <= 0 ||
                      balances.nftCount === "0"
                    }
                    className="amber-btn w-full py-3 rounded-lg font-bold disabled:opacity-50"
                  >
                    {t("mining.mechanism2.burnButton")}
                  </button>
                </div>
              </div>
            </div>

            {/* Invite Information */}
            <div className="mb-8 sm:mb-12">
              <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between mb-6 lg:mb-8 space-y-4 lg:space-y-0">
                <h2 className="text-2xl sm:text-3xl lg:text-4xl xl:text-5xl font-bold text-gray-900">
                  {t("dashboard.inviteInfo") || "Invitation Information"}
                </h2>
                <button
                  onClick={() => setShowInviteModal(true)}
                  className="primary-btn px-4 py-3 sm:px-6 sm:py-3 lg:px-8 lg:py-4 xl:px-10 xl:py-5 rounded-lg lg:rounded-xl font-semibold flex items-center text-sm sm:text-base lg:text-lg xl:text-xl shadow-lg hover:shadow-xl transition-all duration-300 self-start lg:self-auto whitespace-nowrap"
                >
                  <svg
                    className="w-4 h-4 sm:w-5 sm:h-5 lg:w-6 lg:h-6 xl:w-7 xl:h-7 mr-2 sm:mr-3"
                    fill="none"
                    stroke="currentColor"
                    viewBox="0 0 24 24"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth="2"
                      d="M12 4v16m8-8H4"
                    />
                  </svg>
                  <span className="hidden sm:hidden lg:inline">{t("invite.title")}</span>
                  <span className="inline lg:hidden">
                    {t("invite.inviteOthersTitle")}
                  </span>
                </button>
              </div>

              <div className="card p-4 sm:p-6 lg:p-8 xl:p-10 hover:shadow-lg transition-all duration-300">
                <div className="flex flex-col xl:flex-row xl:items-center xl:justify-between mb-6 lg:mb-8">
                  <div className="flex items-start sm:items-center mb-6 xl:mb-0">
                    <div className="w-12 h-12 sm:w-14 sm:h-14 lg:w-16 lg:h-16 xl:w-20 xl:h-20 bg-gradient-to-br from-gray-100 to-gray-200 rounded-xl flex items-center justify-center mr-4 lg:mr-6 flex-shrink-0">
                      <svg
                        className="w-6 h-6 sm:w-7 sm:h-7 lg:w-8 lg:h-8 xl:w-10 xl:h-10 text-gray-500"
                        fill="none"
                        stroke="currentColor"
                        viewBox="0 0 24 24"
                      >
                        <path
                          strokeLinecap="round"
                          strokeLinejoin="round"
                          strokeWidth="2"
                          d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z"
                        />
                      </svg>
                    </div>
                    <div className="flex-1 min-w-0">
                      <h3 className="text-lg sm:text-xl lg:text-2xl xl:text-3xl font-semibold text-gray-900 mb-2">
                        {t("invite.title")}
                      </h3>
                      <p className="text-sm sm:text-base lg:text-lg text-gray-500 break-words">
                        {userInviteInfo.totalInvited > 0
                          ? `${t("dashboard.totalInvited")}: ${
                              userInviteInfo.totalInvited
                            } | ${t("dashboard.activeInvitees")}: ${
                              userInviteInfo.inviteeList.length
                            }`
                          : t("invite.noInvitees")}
                      </p>
                    </div>
                  </div>
                  <div className="flex flex-col sm:flex-row gap-3 lg:gap-4 xl:gap-5 xl:flex-shrink-0">
                    <button
                      onClick={() => setShowInviteModal(true)}
                      className="primary-btn px-4 py-3 sm:px-6 sm:py-3 lg:px-8 lg:py-4 xl:px-10 xl:py-5 rounded-lg lg:rounded-xl font-semibold text-sm sm:text-base lg:text-lg xl:text-xl shadow-lg hover:shadow-xl transition-all duration-300 whitespace-nowrap flex items-center justify-center"
                    >
                      <svg
                        className="w-4 h-4 sm:w-5 sm:h-5 lg:w-6 lg:h-6 mr-2 sm:mr-3"
                        fill="none"
                        stroke="currentColor"
                        viewBox="0 0 24 24"
                      >
                        <path
                          strokeLinecap="round"
                          strokeLinejoin="round"
                          strokeWidth="2"
                          d="M12 4v16m8-8H4"
                        />
                      </svg>
                      <span className="hidden sm:inline">{t("invite.inviteOthersTitle")}</span>
                      <span className="sm:hidden">{t("invite.title")}</span>
                    </button>

                    <button
                      onClick={() => setShowInviteRewardsModal(true)}
                      className="secondary-btn px-4 py-3 sm:px-6 sm:py-3 lg:px-8 lg:py-4 xl:px-10 xl:py-5 rounded-lg lg:rounded-xl font-semibold text-sm sm:text-base lg:text-lg xl:text-xl shadow-lg hover:shadow-xl transition-all duration-300 whitespace-nowrap flex items-center justify-center"
                    >
                      <svg
                        className="w-4 h-4 sm:w-5 sm:h-5 lg:w-6 lg:h-6 mr-2 sm:mr-3"
                        fill="none"
                        stroke="currentColor"
                        viewBox="0 0 24 24"
                      >
                        <path
                          strokeLinecap="round"
                          strokeLinejoin="round"
                          strokeWidth="2"
                          d="M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2"
                        />
                      </svg>
                      <span className="hidden sm:inline">{t("invite.viewRewards")}</span>
                      <span className="sm:hidden">Rewards</span>
                    </button>
                  </div>
                </div>
              </div>
            </div>

            {/* Mining History & NFT Quick Actions */}
            <div className="mb-8 lg:mb-12">
              <h2 className="text-2xl lg:text-3xl xl:text-4xl font-bold text-gray-900 mb-6 lg:mb-8 text-center">
                {t("dashboard.quickActions") || "Quick Actions"}
              </h2>

              <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 lg:gap-8 xl:gap-10">
                {/* GRAT → TIPS History */}
                <div
                  className="card p-4 sm:p-6 lg:p-8 hover:shadow-xl transition-all duration-300 cursor-pointer transform hover:scale-105"
                  onClick={() => {
                    setEnhancedHistoryMechanism(1);
                    setShowEnhancedHistoryModal(true);
                  }}
                >
                  <div className="flex items-center justify-between mb-4 sm:mb-6">
                    <div className="flex items-center">
                      <div className="w-10 h-10 sm:w-12 sm:h-12 lg:w-16 lg:h-16 bg-violet-100 rounded-xl flex items-center justify-center mr-3 sm:mr-4">
                        <svg
                          className="w-5 h-5 sm:w-6 sm:h-6 lg:w-8 lg:h-8 text-violet-600"
                          fill="none"
                          stroke="currentColor"
                          viewBox="0 0 24 24"
                        >
                          <path
                            strokeLinecap="round"
                            strokeLinejoin="round"
                            strokeWidth="2"
                            d="M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2"
                          />
                        </svg>
                      </div>
                      <div>
                        <h3 className="text-base sm:text-lg lg:text-xl font-bold text-gray-900">
                          {t("mining.history.mechanism1")}
                        </h3>
                        <p className="text-xs sm:text-sm text-gray-500 mt-1">
                          GRAT → TIPS
                        </p>
                      </div>
                    </div>
                    <svg
                      className="w-4 h-4 sm:w-5 sm:h-5 lg:w-6 lg:h-6 text-gray-400"
                      fill="none"
                      stroke="currentColor"
                      viewBox="0 0 24 24"
                    >
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth="2"
                        d="M9 5l7 7-7 7"
                      />
                    </svg>
                  </div>

                  <div className="bg-violet-50 rounded-lg p-3 sm:p-4 mb-4 sm:mb-6 space-y-3">
                    <div className="flex items-center justify-between">
                      <span className="text-sm sm:text-base text-gray-700 font-medium">
                        {t("dashboard.activeRecords")}
                      </span>
                      <span className="bg-violet-100 text-violet-800 px-2 py-1 sm:px-3 sm:py-1 rounded-full text-sm sm:text-base font-bold">
                        {
                          miningRecords.mechanism1.filter(
                            (record) => record.active
                          ).length
                        }
                      </span>
                    </div>

                    {/* Claim History Link */}
                    <div className="border-t border-violet-200 pt-3">
                      <button
                        onClick={(e) => {
                          e.stopPropagation();
                          setShowClaimHistoryModal(true);
                        }}
                        className="w-full text-left text-xs sm:text-sm text-violet-600 hover:text-violet-800 font-medium flex items-center justify-between hover:bg-violet-100 rounded-lg px-2 py-1 transition-all duration-200"
                      >
                        <span className="flex items-center">
                          <svg
                            className="w-3 h-3 sm:w-4 sm:h-4 mr-2"
                            fill="none"
                            stroke="currentColor"
                            viewBox="0 0 24 24"
                          >
                            <path
                              strokeLinecap="round"
                              strokeLinejoin="round"
                              strokeWidth="2"
                              d="M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2"
                            />
                          </svg>
                          {t("claim.history.viewHistory")}
                        </span>
                        <svg
                          className="w-3 h-3 sm:w-4 sm:h-4"
                          fill="none"
                          stroke="currentColor"
                          viewBox="0 0 24 24"
                        >
                          <path
                            strokeLinecap="round"
                            strokeLinejoin="round"
                            strokeWidth="2"
                            d="M9 5l7 7-7 7"
                          />
                        </svg>
                      </button>
                    </div>
                  </div>

                  <div className="text-xs sm:text-sm text-gray-500 flex items-center justify-center p-2 sm:p-3 bg-violet-50 rounded-lg">
                    <svg
                      className="w-3 h-3 sm:w-4 sm:h-4 mr-2"
                      fill="none"
                      stroke="currentColor"
                      viewBox="0 0 24 24"
                    >
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth="2"
                        d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"
                      />
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth="2"
                        d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"
                      />
                    </svg>
                    {t("dashboard.clickToViewHistory")}
                  </div>
                </div>

                {/* TIPS → TIPS History */}
                <div
                  className="card p-4 sm:p-6 lg:p-8 hover:shadow-xl transition-all duration-300 cursor-pointer transform hover:scale-105"
                  onClick={() => {
                    setEnhancedHistoryMechanism(2);
                    setShowEnhancedHistoryModal(true);
                  }}
                >
                  <div className="flex items-center justify-between mb-4 sm:mb-6">
                    <div className="flex items-center">
                      <div className="w-10 h-10 sm:w-12 sm:h-12 lg:w-16 lg:h-16 bg-green-100 rounded-xl flex items-center justify-center mr-3 sm:mr-4">
                        <svg
                          className="w-5 h-5 sm:w-6 sm:h-6 lg:w-8 lg:h-8 text-green-600"
                          fill="none"
                          stroke="currentColor"
                          viewBox="0 0 24 24"
                        >
                          <path
                            strokeLinecap="round"
                            strokeLinejoin="round"
                            strokeWidth="2"
                            d="M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2"
                          />
                        </svg>
                      </div>
                      <div>
                        <h3 className="text-base sm:text-lg lg:text-xl font-bold text-gray-900">
                          {t("mining.history.mechanism2")}
                        </h3>
                        <p className="text-xs sm:text-sm text-gray-500 mt-1">
                          TIPS → TIPS
                        </p>
                      </div>
                    </div>
                    <svg
                      className="w-4 h-4 sm:w-5 sm:h-5 lg:w-6 lg:h-6 text-gray-400"
                      fill="none"
                      stroke="currentColor"
                      viewBox="0 0 24 24"
                    >
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth="2"
                        d="M9 5l7 7-7 7"
                      />
                    </svg>
                  </div>

                  <div className="bg-green-50 rounded-lg p-3 sm:p-4 mb-4 sm:mb-6">
                    <div className="flex items-center justify-between">
                      <span className="text-sm sm:text-base text-gray-700 font-medium">
                        {t("dashboard.activeRecords")}
                      </span>
                      <span className="bg-green-100 text-green-800 px-2 py-1 sm:px-3 sm:py-1 rounded-full text-sm sm:text-base font-bold">
                        {
                          miningRecords.mechanism2.filter(
                            (record) => record.active
                          ).length
                        }
                      </span>
                    </div>
                  </div>

                  <div className="text-xs sm:text-sm text-gray-500 flex items-center justify-center p-2 sm:p-3 bg-green-50 rounded-lg">
                    <svg
                      className="w-3 h-3 sm:w-4 sm:h-4 mr-2"
                      fill="none"
                      stroke="currentColor"
                      viewBox="0 0 24 24"
                    >
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth="2"
                        d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"
                      />
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth="2"
                        d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"
                      />
                    </svg>
                    {t("dashboard.clickToViewHistory")}
                  </div>
                </div>

                {/* NFT Management Quick View */}
                <div
                  className="card p-6 lg:p-8 xl:p-10 hover:shadow-xl transition-all duration-300 cursor-pointer transform hover:scale-105"
                  onClick={() => setShowNFTModal(true)}
                >
                  <div className="flex items-center justify-between mb-6">
                    <div className="flex items-center">
                      <div className="w-12 h-12 lg:w-16 lg:h-16 bg-purple-100 rounded-xl flex items-center justify-center mr-4">
                        <img
                          src="/icons/MinerNFT-Rabbit.svg"
                          alt="Miner NFT"
                          className="w-6 h-6 lg:w-8 lg:h-8"
                        />
                      </div>
                      <h3 className="text-lg lg:text-xl xl:text-2xl font-bold text-gray-900">
                        {t("nft.title")}
                      </h3>
                    </div>
                    <svg
                      className="w-5 h-5 lg:w-6 lg:h-6 text-gray-400"
                      fill="none"
                      stroke="currentColor"
                      viewBox="0 0 24 24"
                    >
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth="2"
                        d="M9 5l7 7-7 7"
                      />
                    </svg>
                  </div>

                  <div className="space-y-4 lg:space-y-5">
                    <div className="flex items-center justify-between p-3 lg:p-4 bg-gray-50 rounded-lg">
                      <span className="text-base lg:text-lg text-gray-700 font-medium">
                        {t("dashboard.nftHoldings")}
                      </span>
                      <span className="bg-purple-100 text-purple-800 px-3 py-1 lg:px-4 lg:py-2 rounded-full text-sm lg:text-base font-semibold">
                        {nftData.length}
                      </span>
                    </div>
                    {nftData.length > 0 && (
                      <div className="flex items-center justify-between p-3 lg:p-4 bg-gray-50 rounded-lg">
                        <span className="text-base lg:text-lg text-gray-700 font-medium">
                          {t("dashboard.totalReleasable")}
                        </span>
                        <span className="text-base lg:text-lg text-violet-600 font-bold">
                          {nftData
                            .reduce(
                              (total, nft) =>
                                total + parseFloat(nft.releasableAmount),
                              0
                            )
                            .toFixed(4)}{" "}
                          <span className="text-sm text-gray-500">GRAT</span>
                        </span>
                      </div>
                    )}
                  </div>

                  <div className="mt-6 text-sm lg:text-base text-gray-500 flex items-center justify-center p-3 bg-purple-50 rounded-lg">
                    <svg
                      className="w-4 h-4 lg:w-5 lg:h-5 mr-2"
                      fill="none"
                      stroke="currentColor"
                      viewBox="0 0 24 24"
                    >
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth="2"
                        d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"
                      />
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth="2"
                        d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"
                      />
                    </svg>
                    {t("dashboard.clickToManageNFT")}
                  </div>
                </div>
              </div>
            </div>
          </div>
        )}
      </main>

      {/* Wallet selection modal */}
      <WalletModal
        isOpen={showWalletModal}
        onClose={() => setShowWalletModal(false)}
        walletConfigs={walletConfigs}
        onWalletSelect={handleWalletSelect}
        selectedWallet={selectedWallet}
        isConnecting={isConnecting}
        getWalletAvailability={getWalletAvailability}
      />

      {/* Notification component */}
      <Notification
        message={notification.message}
        type={notification.type}
        isVisible={notification.isVisible}
        onClose={hideNotification}
      />

      {/* Loading overlay */}
      <LoadingOverlay isVisible={loading.isVisible} text={loading.text} />

      {/* NFT Modal */}
      <NFTModal
        isOpen={showNFTModal}
        onClose={() => setShowNFTModal(false)}
        nftData={nftData}
        releaseNFTTokens={releaseNFTTokens}
        contracts={contracts}
        userAddress={userAddress}
      />

      {/* Invite Modal */}
      <InviteModal
        isOpen={showInviteModal}
        onClose={() => setShowInviteModal(false)}
        userInviteInfo={userInviteInfo}
        contracts={contracts}
        userAddress={userAddress}
        showNotification={showNotification}
        showLoading={showLoading}
        hideLoading={hideLoading}
        onInviteInfoUpdate={loadUserInviteInfo}
        onOpenRewardsHistory={() => setShowInviteRewardsModal(true)}
        inviterFromUrl={inviterFromUrl}
      />

      {/* Invite Rewards History Modal */}
      <InviteRewardsModal
        isOpen={showInviteRewardsModal}
        onClose={() => setShowInviteRewardsModal(false)}
        userInviteInfo={userInviteInfo}
      />

      {/* GRAT → TIPS History Modal */}
      <GratTipsHistoryModal
        isOpen={showGratTipsHistoryModal}
        onClose={() => setShowGratTipsHistoryModal(false)}
        miningRecords={{ mechanism1: miningRecords.mechanism1 }}
        claimMechanism1Rewards={claimMechanism1Rewards}
      />

      {/* TIPS → TIPS History Modal */}
      <TipsTipsHistoryModal
        isOpen={showTipsTipsHistoryModal}
        onClose={() => setShowTipsTipsHistoryModal(false)}
        miningRecords={{ mechanism2: miningRecords.mechanism2 }}
        claimMechanism2Rewards={claimMechanism2Rewards}
      />

      {/* Enhanced Mining History Modal */}
      <EnhancedMiningHistory
        isOpen={showEnhancedHistoryModal}
        onClose={() => setShowEnhancedHistoryModal(false)}
        contracts={contracts}
        userAddress={userAddress}
        mechanism={enhancedHistoryMechanism}
        onClaimRewards={
          enhancedHistoryMechanism === 1
            ? claimMechanism1Rewards
            : claimMechanism2Rewards
        }
        showNotification={showNotification}
      />

      {/* Claim History Modal */}
      <ClaimHistoryModal
        isOpen={showClaimHistoryModal}
        onClose={() => setShowClaimHistoryModal(false)}
        contracts={contracts}
        userAddress={userAddress}
        showNotification={showNotification}
      />
    </div>
  );
};

export default LaunchApp;
