import React, { useState, useEffect } from "react";
import { Link } from "react-router-dom";
import { useTranslation } from "react-i18next";
import LanguageSwitcher from "../components/LanguageSwitcher";
import AIToolsFixed from "../components/AIToolsFixed";

const HomePage = () => {
  const { t } = useTranslation();
  const [mobileMenuOpen, setMobileMenuOpen] = useState(false);
  
  // Check if NFT presale is enabled
  const isNFTPresaleEnabled = process.env.REACT_APP_ENABLE_NFT_PRESALE === 'true';

  // Scroll to specified section
  const scrollToSection = (sectionId) => {
    const element = document.getElementById(sectionId);
    if (element) {
      element.scrollIntoView({ behavior: "smooth" });
    }
    setMobileMenuOpen(false);
  };

  return (
    <div className="min-h-screen">
      {/* Fixed header navigation */}
      <header className="fixed top-0 left-0 right-0 z-50 bg-white/80 backdrop-blur-md border-b border-gray-200/80">
        <div className="container mx-auto px-6 py-4 flex justify-between items-center">
          {/* Logo */}
          <a
            href="#"
            className="flex items-center space-x-2 text-2xl font-bold font-poppins"
          >
            <img
              src="/icons/TokenA-GRAT.svg"
              alt="GRAT Token"
              className="w-8 h-8"
            />
            <span className="text-violet-700">$</span>
            <span className="text-gray-800">GRAT</span>
          </a>

          {/* Desktop navigation menu */}
          <nav className="hidden md:flex items-center space-x-6 lg:space-x-8 text-gray-600 font-semibold">
            <button
              onClick={() => scrollToSection("grat")}
              className="hover:text-violet-700 transition-colors"
            >
              {t("nav.protocol")}
            </button>
            <button
              onClick={() => scrollToSection("tips")}
              className="hover:text-violet-700 transition-colors"
            >
              {t("nav.coin")}
            </button>
            <button
              onClick={() => scrollToSection("tokenomics")}
              className="hover:text-violet-700 transition-colors"
            >
              {t("nav.tokenomics")}
            </button>
            <button
              onClick={() => scrollToSection("how")}
              className="hover:text-violet-700 transition-colors"
            >
              {t("nav.howItWorks")}
            </button>
            <button
              onClick={() => scrollToSection("ai-tools")}
              className="hover:text-violet-700 transition-colors"
            >
              {t("nav.aiTools")}
            </button>
            <button
              onClick={() => scrollToSection("community")}
              className="hover:text-violet-700 transition-colors"
            >
              {t("nav.community")}
            </button>
          </nav>

          {/* Right side button group */}
          <div className="flex items-center gap-4">
            {/* Language switcher */}
            <LanguageSwitcher className="hidden md:inline-block" />

            {/* NFT Presale button - conditionally rendered */}
            {isNFTPresaleEnabled && (
              <Link
                to="/presale"
                className="bg-gradient-to-r from-violet-600 to-purple-600 hover:from-violet-700 hover:to-purple-700 text-white font-bold py-2.5 px-6 rounded-lg hidden md:inline-block transition-all duration-200 shadow-lg hover:shadow-xl"
              >
                🚀 {t("nav.nftPresale")}
              </Link>
            )}

            {/* Launch app button */}
            <Link
              to="/app"
              className="primary-btn font-bold py-2.5 px-6 rounded-lg hidden md:inline-block"
            >
              {t("nav.launchApp")}
            </Link>

            {/* Mobile menu button */}
            <button
              onClick={() => setMobileMenuOpen(!mobileMenuOpen)}
              className="md:hidden text-gray-800"
            >
              <svg
                className="w-6 h-6"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth="2"
                  d="M4 6h16M4 12h16M4 18h16"
                />
              </svg>
            </button>
          </div>
        </div>

        {/* Mobile menu */}
        {mobileMenuOpen && (
          <div className="md:hidden bg-white/95 backdrop-blur-sm border-t">
            <nav className="flex flex-col items-center space-y-4 py-6 px-4 text-gray-600 font-semibold">
              <button
                onClick={() => scrollToSection("grat")}
                className="hover:text-violet-700 transition-colors py-2 px-4 rounded-lg hover:bg-violet-50 w-full max-w-xs text-center"
              >
                {t("nav.protocol")}
              </button>
              <button
                onClick={() => scrollToSection("tips")}
                className="hover:text-violet-700 transition-colors py-2 px-4 rounded-lg hover:bg-violet-50 w-full max-w-xs text-center"
              >
                {t("nav.coin")}
              </button>
              <button
                onClick={() => scrollToSection("tokenomics")}
                className="hover:text-violet-700 transition-colors py-2 px-4 rounded-lg hover:bg-violet-50 w-full max-w-xs text-center"
              >
                {t("nav.tokenomics")}
              </button>
              <button
                onClick={() => scrollToSection("how")}
                className="hover:text-violet-700 transition-colors py-2 px-4 rounded-lg hover:bg-violet-50 w-full max-w-xs text-center"
              >
                {t("nav.howItWorks")}
              </button>
              <button
                onClick={() => scrollToSection("ai-tools")}
                className="hover:text-violet-700 transition-colors py-2 px-4 rounded-lg hover:bg-violet-50 w-full max-w-xs text-center"
              >
                {t("nav.aiTools")}
              </button>
              <button
                onClick={() => scrollToSection("community")}
                className="hover:text-violet-700 transition-colors py-2 px-4 rounded-lg hover:bg-violet-50 w-full max-w-xs text-center"
              >
                {t("nav.community")}
              </button>
              <div className="w-full max-w-xs">
                <LanguageSwitcher className="w-full" />
              </div>
              
              {/* NFT Presale button for mobile - conditionally rendered */}
              {isNFTPresaleEnabled && (
                <Link
                  to="/presale"
                  className="w-full max-w-xs bg-gradient-to-r from-violet-600 to-purple-600 hover:from-violet-700 hover:to-purple-700 text-white font-bold py-2.5 px-6 rounded-lg transition-all duration-200 shadow-lg text-center"
                >
                  🚀 {t("nav.nftPresale")}
                </Link>
              )}
              
              <Link
                to="/app"
                className="primary-btn font-bold py-2.5 px-6 rounded-lg max-w-xs w-full text-center"
              >
                {t("nav.launchApp")}
              </Link>
            </nav>
          </div>
        )}
      </header>

      {/* Main content area */}
      <div id="content-container">
        {/* Hero section */}
        <main id="home" className="hero-bg pt-36 pb-24 overflow-hidden">
          <div className="container mx-auto px-6 text-center">
            {/* NFT Presale Banner - conditionally rendered */}
            {isNFTPresaleEnabled && (
              <div className="mb-8 max-w-4xl mx-auto">
                <div className="bg-gradient-to-r from-violet-600 via-purple-600 to-indigo-600 rounded-2xl p-6 shadow-2xl transform hover:scale-105 transition-all duration-300">
                  <div className="flex items-center justify-center mb-3">
                    <img
                      src="/icons/MinerNFT-Rabbit.svg"
                      alt="Miner NFT"
                      className="w-8 h-8 mr-3"
                    />
                    <h2 className="text-xl md:text-2xl font-bold text-white">
                      🚀 {t("hero.presaleBanner.title")}
                    </h2>
                  </div>
                  <p className="text-white/90 mb-4 text-sm md:text-base">
                    {t("hero.presaleBanner.subtitle")}
                  </p>
                  <Link
                    to="/presale"
                    className="inline-block bg-white text-violet-700 font-bold py-3 px-8 rounded-xl hover:bg-gray-50 transition-colors"
                  >
                    {t("hero.presaleBanner.cta")}
                  </Link>
                </div>
              </div>
            )}
            
            <div className="max-w-4xl mx-auto">
              <h1 className="hero-title text-4xl md:text-7xl font-black text-gray-900 leading-tight mb-6">
                {t("hero.title")}
              </h1>

              <p className="hero-subtitle text-lg md:text-xl text-gray-600 max-w-2xl mx-auto mb-10">
                {t("hero.subtitle")}
              </p>

              <div className="hero-buttons flex flex-col sm:flex-row justify-center items-center gap-4">
                <button
                  onClick={() => scrollToSection("how")}
                  className="w-full sm:w-auto secondary-btn font-bold py-3 px-8 rounded-xl text-lg"
                >
                  {t("hero.learnMore")}
                </button>
                <Link
                  to="/app"
                  className="w-full sm:w-auto primary-btn font-bold py-3 px-8 rounded-xl text-lg text-center"
                >
                  {t("hero.launchApp")}
                </Link>
              </div>
            </div>

            {/* Animation area */}
            <div className="hero-animation-container mt-28 relative flex justify-center items-center h-80">
              <div className="absolute w-80 h-80">
                {/* Orbital circles - Multi-layer gradient */}
                <div
                  className="absolute inset-0 border-2 border-violet-500/20 rounded-full"
                  style={{ animation: "shimmer 3s ease-in-out infinite" }}
                ></div>
                <div
                  className="absolute inset-4 border border-violet-500/15 rounded-full"
                  style={{ animation: "shimmer 3s ease-in-out infinite 0.5s" }}
                ></div>
                <div
                  className="absolute inset-8 border border-amber-400/15 rounded-full"
                  style={{ animation: "shimmer 3s ease-in-out infinite 1s" }}
                ></div>

                {/* Background ripple effects */}
                <div
                  className="absolute top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2 w-32 h-32 bg-violet-500/5 rounded-full"
                  style={{ animation: "ripple 4s ease-out infinite" }}
                ></div>
                <div
                  className="absolute top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2 w-32 h-32 bg-violet-500/5 rounded-full"
                  style={{ animation: "ripple 4s ease-out infinite 2s" }}
                ></div>

                {/* Central $GRAT logo - Enhanced version */}
                <div
                  className="hero-central-logo absolute top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2 w-32 h-32 bg-gradient-to-br from-white/80 to-violet-50/80 rounded-full flex items-center justify-center font-poppins font-bold text-2xl border-2 border-violet-200/50 shadow-2xl backdrop-blur-sm"
                  style={{
                    animation:
                      "pulse-glow 3s ease-in-out infinite, float 4s ease-in-out infinite",
                  }}
                >
                  <img
                    src="/icons/TokenA-GRAT.svg"
                    alt="GRAT Token"
                    className="w-10 h-10 mr-2"
                  />
                  <span className="text-violet-700 drop-shadow-sm">$GRAT</span>
                </div>

                {/* First orbital $TIPS */}
                <div className="absolute top-1/2 left-1/2 w-20 h-20 -mt-10 -ml-10">
                  <div
                    className="w-full h-full bg-gradient-to-br from-amber-400 to-amber-500 rounded-full flex items-center justify-center font-poppins font-bold text-sm text-white shadow-xl border-2 border-amber-300"
                    style={{ animation: "orbit 8s linear infinite" }}
                  >
                    <img
                      src="/icons/TokenB-TIPS.svg"
                      alt="TIPS Token"
                      className="w-4 h-4 mr-1"
                    />
                    $TIPS
                  </div>
                </div>

                {/* Second orbital $TIPS - Reverse */}
                <div className="absolute top-1/2 left-1/2 w-16 h-16 -mt-8 -ml-8">
                  <div
                    className="w-full h-full bg-gradient-to-br from-green-400 to-green-500 rounded-full flex items-center justify-center font-poppins font-bold text-xs text-white shadow-lg border-2 border-green-300"
                    style={{ animation: "orbit-reverse 12s linear infinite" }}
                  >
                    <img
                      src="/icons/TokenB-TIPS.svg"
                      alt="TIPS Token"
                      className="w-3 h-3"
                    />
                  </div>
                </div>

                {/* Third orbital $TIPS - Slow */}
                <div className="absolute top-1/2 left-1/2 w-14 h-14 -mt-7 -ml-7">
                  <div
                    className="w-full h-full bg-gradient-to-br from-blue-400 to-blue-500 rounded-full flex items-center justify-center font-poppins font-bold text-xs text-white shadow-lg border border-blue-300"
                    style={{ animation: "orbit-slow 15s linear infinite" }}
                  >
                    T
                  </div>
                </div>

                {/* Decorative dots - Enhanced version */}
                <div
                  className="absolute top-1/4 left-1/4 w-3 h-3 bg-violet-400 rounded-full"
                  style={{
                    animation:
                      "twinkle 2s ease-in-out infinite, float 2s ease-in-out infinite",
                  }}
                ></div>
                <div
                  className="absolute top-3/4 right-1/4 w-2 h-2 bg-amber-400 rounded-full"
                  style={{
                    animation:
                      "twinkle 3s ease-in-out infinite 1s, float 3s ease-in-out infinite 1s",
                  }}
                ></div>
                <div
                  className="absolute bottom-1/4 left-1/3 w-4 h-4 bg-green-400 rounded-full"
                  style={{
                    animation:
                      "twinkle 2.5s ease-in-out infinite 0.5s, float 2.5s ease-in-out infinite 0.5s",
                  }}
                ></div>
                <div
                  className="absolute top-1/3 right-1/3 w-2 h-2 bg-blue-400 rounded-full"
                  style={{
                    animation:
                      "twinkle 4s ease-in-out infinite 2s, float 4s ease-in-out infinite 2s",
                  }}
                ></div>
                <div
                  className="absolute bottom-1/3 right-1/2 w-3 h-3 bg-pink-400 rounded-full"
                  style={{
                    animation:
                      "twinkle 3.5s ease-in-out infinite 1.5s, float 3.5s ease-in-out infinite 1.5s",
                  }}
                ></div>
                <div
                  className="absolute top-2/3 left-1/2 w-2 h-2 bg-indigo-400 rounded-full"
                  style={{
                    animation:
                      "twinkle 2.8s ease-in-out infinite 0.8s, float 2.8s ease-in-out infinite 0.8s",
                  }}
                ></div>
              </div>
            </div>
          </div>
        </main>

        {/* GRAT Protocol section */}
        <section id="grat" className="py-28">
          <div className="container mx-auto px-6">
            <div className="text-center mb-16">
              <div className="flex items-center justify-center mb-4">
                <img
                  src="/icons/TokenA-GRAT.svg"
                  alt="GRAT Token"
                  className="w-16 h-16 mr-4"
                />
                <h2 className="text-4xl md:text-6xl font-black text-gray-900 leading-tight">
                  {t("protocol.title")}
                </h2>
              </div>
              <p className="text-gray-600 mt-4 text-lg max-w-2xl mx-auto">
                {t("protocol.subtitle")}
              </p>
            </div>

            <div className="grid md:grid-cols-3 gap-8">
              {/* Card 1: Mining Output */}
              <div className="card p-8 text-center">
                <div className="flex items-center justify-center w-16 h-16 rounded-2xl bg-violet-100 text-violet-600 mx-auto mb-6">
                  <svg
                    className="w-8 h-8"
                    fill="none"
                    stroke="currentColor"
                    viewBox="0 0 24 24"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth="2"
                      d="M11.049 2.927c.3-.921 1.603-.921 1.902 0l1.519 4.674a1 1 0 00.95.69h4.915c.969 0 1.371 1.24.588 1.81l-3.976 2.888a1 1 0 00-.363 1.118l1.518 4.674c.3.922-.755 1.688-1.538 1.118l-3.976-2.888a1 1 0 00-1.176 0l-3.976 2.888c-.783.57-1.838-.197-1.538-1.118l1.518-4.674a1 1 0 00-.363-1.118l-3.976-2.888c-.784-.57-.38-1.81.588-1.81h4.914a1 1 0 00.951-.69l1.519-4.674z"
                    />
                  </svg>
                </div>
                <h3 className="text-2xl font-bold text-gray-900 mb-2 font-poppins">
                  {t("protocol.miningOutput.title")}
                </h3>
                <p className="text-gray-600">
                  {t("protocol.miningOutput.description")}
                </p>
              </div>

              {/* Card 2: Governance Weight */}
              <div className="card p-8 text-center">
                <div className="flex items-center justify-center w-16 h-16 rounded-2xl bg-violet-100 text-violet-600 mx-auto mb-6">
                  <svg
                    className="w-8 h-8"
                    fill="none"
                    stroke="currentColor"
                    viewBox="0 0 24 24"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth="2"
                      d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z"
                    />
                  </svg>
                </div>
                <h3 className="text-2xl font-bold text-gray-900 mb-2 font-poppins">
                  {t("protocol.governanceWeight.title")}
                </h3>
                <p className="text-gray-600">
                  {t("protocol.governanceWeight.description")}
                </p>
              </div>

              {/* Card 3: Value Accrual */}
              <div className="card p-8 text-center">
                <div className="flex items-center justify-center w-16 h-16 rounded-2xl bg-violet-100 text-violet-600 mx-auto mb-6">
                  <svg
                    className="w-8 h-8"
                    fill="none"
                    stroke="currentColor"
                    viewBox="0 0 24 24"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth="2"
                      d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"
                    />
                  </svg>
                </div>
                <h3 className="text-2xl font-bold text-gray-900 mb-2 font-poppins">
                  {t("protocol.valueAccrual.title")}
                </h3>
                <p className="text-gray-600">
                  {t("protocol.valueAccrual.description")}
                </p>
              </div>
            </div>
          </div>
        </section>

        {/* TIPS Token section */}
        <section id="tips" className="py-28 bg-gray-50">
          <div className="container mx-auto px-6">
            <div className="text-center mb-16">
              <div className="flex items-center justify-center mb-4">
                <img
                  src="/icons/TokenB-TIPS.svg"
                  alt="TIPS Token"
                  className="w-16 h-16 mr-4"
                />
                <h2 className="text-4xl md:text-6xl font-black text-gray-900 leading-tight">
                  {t("tips.title")}
                </h2>
              </div>
              <p className="text-gray-600 mt-4 text-lg max-w-2xl mx-auto">
                {t("tips.subtitle")}
              </p>
            </div>

            <div className="grid md:grid-cols-3 gap-8">
              {/* Card 1: P2P Tipping */}
              <div className="card p-8 text-center">
                <div className="flex items-center justify-center w-16 h-16 rounded-2xl bg-amber-100 text-amber-600 mx-auto mb-6">
                  <svg
                    className="w-8 h-8"
                    fill="none"
                    stroke="currentColor"
                    viewBox="0 0 24 24"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth="2"
                      d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1M21 12a9 9 0 11-18 0 9 9 0 0118 0z"
                    />
                  </svg>
                </div>
                <h3 className="text-2xl font-bold text-gray-900 mb-2 font-poppins">
                  {t("tips.p2pTipping.title")}
                </h3>
                <p className="text-gray-600">
                  {t("tips.p2pTipping.description")}
                </p>
              </div>

              {/* Card 2: Meme Creativity */}
              <div className="card p-8 text-center">
                <div className="flex items-center justify-center w-16 h-16 rounded-2xl bg-amber-100 text-amber-600 mx-auto mb-6">
                  <svg
                    className="w-8 h-8"
                    fill="none"
                    stroke="currentColor"
                    viewBox="0 0 24 24"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth="2"
                      d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z"
                    />
                  </svg>
                </div>
                <h3 className="text-2xl font-bold text-gray-900 mb-2 font-poppins">
                  {t("tips.memeCreativity.title")}
                </h3>
                <p className="text-gray-600">
                  {t("tips.memeCreativity.description")}
                </p>
              </div>

              {/* Card 3: Empower Creators */}
              <div className="card p-8 text-center">
                <div className="flex items-center justify-center w-16 h-16 rounded-2xl bg-amber-100 text-amber-600 mx-auto mb-6">
                  <svg
                    className="w-8 h-8"
                    fill="none"
                    stroke="currentColor"
                    viewBox="0 0 24 24"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth="2"
                      d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z"
                    />
                  </svg>
                </div>
                <h3 className="text-2xl font-bold text-gray-900 mb-2 font-poppins">
                  {t("tips.empowerCreators.title")}
                </h3>
                <p className="text-gray-600">
                  {t("tips.empowerCreators.description")}
                </p>
              </div>
            </div>
          </div>
        </section>

        {/* Tokenomics section */}
        <section id="tokenomics" className="py-28">
          <div className="container mx-auto px-6">
            <div className="text-center mb-16">
              <h2 className="text-4xl md:text-6xl font-black text-gray-900 leading-tight">
                {t("tokenomics.title")}
              </h2>
              <p className="text-gray-600 mt-4 text-lg max-w-2xl mx-auto">
                {t("tokenomics.subtitle")}
              </p>
            </div>

            <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-8 max-w-6xl mx-auto">
              {/* 84% Staking Rewards */}
              <div className="card p-8 text-center">
                <div className="w-24 h-24 mx-auto mb-6 relative">
                  <svg
                    className="w-full h-full transform -rotate-90"
                    viewBox="0 0 36 36"
                  >
                    <path
                      d="M18 2.0845 a 15.9155 15.9155 0 0 1 0 31.831 a 15.9155 15.9155 0 0 1 0 -31.831"
                      fill="none"
                      stroke="#e5e7eb"
                      strokeWidth="3"
                    />
                    <path
                      d="M18 2.0845 a 15.9155 15.9155 0 0 1 0 31.831 a 15.9155 15.9155 0 0 1 0 -31.831"
                      fill="none"
                      stroke="#6d28d9"
                      strokeWidth="3"
                      strokeDasharray="84, 16"
                    />
                  </svg>
                  <div className="absolute inset-0 flex items-center justify-center">
                    <span className="text-2xl font-bold text-violet-700">
                      84%
                    </span>
                  </div>
                </div>
                <h3 className="text-xl font-bold text-gray-900 mb-2">
                  {t("tokenomics.stakingRewards")}
                </h3>
                <p className="text-gray-600 text-sm">
                  {t("tokenomics.stakingRewardsDesc")}
                </p>
              </div>

              {/* 6% Liquidity Pool */}
              <div className="card p-8 text-center">
                <div className="w-24 h-24 mx-auto mb-6 relative">
                  <svg
                    className="w-full h-full transform -rotate-90"
                    viewBox="0 0 36 36"
                  >
                    <path
                      d="M18 2.0845 a 15.9155 15.9155 0 0 1 0 31.831 a 15.9155 15.9155 0 0 1 0 -31.831"
                      fill="none"
                      stroke="#e5e7eb"
                      strokeWidth="3"
                    />
                    <path
                      d="M18 2.0845 a 15.9155 15.9155 0 0 1 0 31.831 a 15.9155 15.9155 0 0 1 0 -31.831"
                      fill="none"
                      stroke="#f59e0b"
                      strokeWidth="3"
                      strokeDasharray="6, 94"
                    />
                  </svg>
                  <div className="absolute inset-0 flex items-center justify-center">
                    <span className="text-2xl font-bold text-amber-600">
                      6%
                    </span>
                  </div>
                </div>
                <h3 className="text-xl font-bold text-gray-900 mb-2">
                  {t("tokenomics.liquidityPool")}
                </h3>
                <p className="text-gray-600 text-sm">
                  {t("tokenomics.liquidityPoolDesc")}
                </p>
              </div>

              {/* 5% Ecosystem Fund */}
              <div className="card p-8 text-center">
                <div className="w-24 h-24 mx-auto mb-6 relative">
                  <svg
                    className="w-full h-full transform -rotate-90"
                    viewBox="0 0 36 36"
                  >
                    <path
                      d="M18 2.0845 a 15.9155 15.9155 0 0 1 0 31.831 a 15.9155 15.9155 0 0 1 0 -31.831"
                      fill="none"
                      stroke="#e5e7eb"
                      strokeWidth="3"
                    />
                    <path
                      d="M18 2.0845 a 15.9155 15.9155 0 0 1 0 31.831 a 15.9155 15.9155 0 0 1 0 -31.831"
                      fill="none"
                      stroke="#10b981"
                      strokeWidth="3"
                      strokeDasharray="5, 95"
                    />
                  </svg>
                  <div className="absolute inset-0 flex items-center justify-center">
                    <span className="text-2xl font-bold text-green-600">
                      5%
                    </span>
                  </div>
                </div>
                <h3 className="text-xl font-bold text-gray-900 mb-2">
                  {t("tokenomics.ecosystemFund")}
                </h3>
                <p className="text-gray-600 text-sm">
                  {t("tokenomics.ecosystemFundDesc")}
                </p>
              </div>

              {/* 5% Team & Advisors */}
              <div className="card p-8 text-center">
                <div className="w-24 h-24 mx-auto mb-6 relative">
                  <svg
                    className="w-full h-full transform -rotate-90"
                    viewBox="0 0 36 36"
                  >
                    <path
                      d="M18 2.0845 a 15.9155 15.9155 0 0 1 0 31.831 a 15.9155 15.9155 0 0 1 0 -31.831"
                      fill="none"
                      stroke="#e5e7eb"
                      strokeWidth="3"
                    />
                    <path
                      d="M18 2.0845 a 15.9155 15.9155 0 0 1 0 31.831 a 15.9155 15.9155 0 0 1 0 -31.831"
                      fill="none"
                      stroke="#ef4444"
                      strokeWidth="3"
                      strokeDasharray="5, 95"
                    />
                  </svg>
                  <div className="absolute inset-0 flex items-center justify-center">
                    <span className="text-2xl font-bold text-red-500">5%</span>
                  </div>
                </div>
                <h3 className="text-xl font-bold text-gray-900 mb-2">
                  {t("tokenomics.teamAndAdvisors")}
                </h3>
                <p className="text-gray-600 text-sm">
                  {t("tokenomics.teamAndAdvisorsDesc")}
                </p>
              </div>
            </div>
          </div>
        </section>

        {/* How It Works section */}
        <section id="how" className="py-28 bg-gray-50">
          <div className="container mx-auto px-6">
            <div className="text-center mb-16">
              <h2 className="text-4xl md:text-6xl font-black text-gray-900 leading-tight">
                {t("howItWorks.title")}
              </h2>
              <p className="text-gray-600 mt-4 text-lg max-w-2xl mx-auto">
                {t("howItWorks.subtitle")}
              </p>
            </div>

            <div className="max-w-4xl mx-auto">
              <div className="grid md:grid-cols-3 gap-8">
                {/* Step 1 */}
                <div className="text-center">
                  <div className="w-16 h-16 bg-violet-100 rounded-2xl flex items-center justify-center mx-auto mb-6">
                    <span className="text-2xl font-bold text-violet-700">
                      1
                    </span>
                  </div>
                  <h3 className="text-xl font-bold text-gray-900 mb-4">
                    {t("howItWorks.step1.title")}
                  </h3>
                  <p className="text-gray-600">
                    {t("howItWorks.step1.description")}
                  </p>
                </div>

                {/* Step 2 */}
                <div className="text-center">
                  <div className="w-16 h-16 bg-amber-100 rounded-2xl flex items-center justify-center mx-auto mb-6">
                    <span className="text-2xl font-bold text-amber-600">2</span>
                  </div>
                  <h3 className="text-xl font-bold text-gray-900 mb-4">
                    {t("howItWorks.step2.title")}
                  </h3>
                  <p className="text-gray-600">
                    {t("howItWorks.step2.description")}
                  </p>
                </div>

                {/* Step 3 */}
                <div className="text-center">
                  <div className="w-16 h-16 bg-green-100 rounded-2xl flex items-center justify-center mx-auto mb-6">
                    <span className="text-2xl font-bold text-green-600">3</span>
                  </div>
                  <h3 className="text-xl font-bold text-gray-900 mb-4">
                    {t("howItWorks.step3.title")}
                  </h3>
                  <p className="text-gray-600">
                    {t("howItWorks.step3.description")}
                  </p>
                </div>
              </div>
            </div>
          </div>
        </section>

        {/* AI Tools section */}
        <AIToolsFixed />

        {/* Community section */}
        <section id="community" className="py-28 bg-gray-50">
          <div className="container mx-auto px-6 text-center">
            <h2 className="text-4xl md:text-6xl font-black text-gray-900 leading-tight">
              {t("community.title")}
            </h2>
            <p className="text-gray-600 mt-4 text-lg max-w-2xl mx-auto">
              {t("community.subtitle")}
            </p>

            <div className="mt-10 flex justify-center gap-6">
              <a
                href="#"
                className="bg-gray-200 hover:bg-gray-300 transition-colors p-4 rounded-full"
              >
                <svg
                  className="w-6 h-6 text-gray-600"
                  fill="currentColor"
                  viewBox="0 0 24 24"
                >
                  <path d="M23.953 4.57a10 10 0 01-2.825.775 4.958 4.958 0 002.163-2.723c-.951.555-2.005.959-3.127 1.184a4.92 4.92 0 00-8.384 4.482C7.69 8.095 4.067 6.13 1.64 3.162a4.822 4.822 0 00-.666 2.475c0 1.71.87 3.213 2.188 4.096a4.904 4.904 0 01-2.228-.616v.06a4.923 4.923 0 003.946 4.827 4.996 4.996 0 01-2.212.085 4.936 4.936 0 004.604 3.417 9.867 9.867 0 01-6.102 2.105c-.39 0-.779-.023-1.17-.067a13.995 13.995 0 007.557 2.209c9.053 0 13.998-7.496 13.998-13.985 0-.21 0-.42-.015-.63A9.935 9.935 0 0024 4.59z" />
                </svg>
              </a>
              <a
                href="#"
                className="bg-gray-200 hover:bg-gray-300 transition-colors p-4 rounded-full"
              >
                <svg
                  className="w-6 h-6 text-gray-600"
                  fill="currentColor"
                  viewBox="0 0 24 24"
                >
                  <path d="M20.317 4.492c-1.53-.69-3.17-1.2-4.885-1.49a.075.075 0 00-.079.036c-.211.375-.445.865-.608 1.25a18.566 18.566 0 00-5.487 0 12.36 12.36 0 00-.617-1.25.077.077 0 00-.079-.036A15.946 15.946 0 004.717 4.492a.07.07 0 00-.032.027C.533 9.046-.32 13.498.099 17.9a.082.082 0 00.031.056 16.108 16.108 0 004.729 2.28.077.077 0 00.084-.028c.462-.63.874-1.295 1.226-1.994a.076.076 0 00-.041-.106 10.683 10.683 0 01-1.548-.706.077.077 0 01-.008-.128c.104-.077.208-.158.307-.24a.074.074 0 01.077-.01c3.247 1.424 6.766 1.424 9.99 0a.074.074 0 01.078.01c.099.082.203.163.307.24a.077.077 0 01-.006.128 10.021 10.021 0 01-1.549.706.076.076 0 00-.041.106c.36.699.772 1.365 1.225 1.994a.076.076 0 00.084.028 16.017 16.017 0 004.729-2.28.077.077 0 00.032-.056c.5-5.094-.838-9.52-3.549-13.438a.061.061 0 00-.031-.027zM8.02 15.278c-1.182 0-2.157-1.085-2.157-2.419 0-1.333.956-2.419 2.157-2.419 1.21 0 2.176 1.096 2.157 2.42 0 1.333-.956 2.418-2.157 2.418zm7.975 0c-1.183 0-2.157-1.085-2.157-2.419 0-1.333.955-2.419 2.157-2.419 1.21 0 2.176 1.096 2.157 2.42 0 1.333-.946 2.418-2.157 2.418z" />
                </svg>
              </a>
              <a
                href="#"
                className="bg-gray-200 hover:bg-gray-300 transition-colors p-4 rounded-full"
              >
                <svg
                  className="w-6 h-6 text-gray-600"
                  fill="currentColor"
                  viewBox="0 0 24 24"
                >
                  <path d="M11.944 0A12 12 0 0 0 0 12a12 12 0 0 0 12 12 12 12 0 0 0 12-12A12 12 0 0 0 12 0a12 12 0 0 0-.056 0zm4.962 7.224c.1-.002.321.023.465.14a.506.506 0 0 1 .171.325c.016.093.036.306.02.472-.18 1.898-.962 6.502-1.36 8.627-.168.9-.499 1.201-.82 1.23-.696.065-1.225-.46-1.9-.902-1.056-.693-1.653-1.124-2.678-1.8-1.185-.78-.417-1.21.258-1.91.177-.184 3.247-2.977 3.307-3.23.007-.032.014-.15-.056-.212s-.174-.041-.249-.024c-.106.024-1.793 1.14-5.061 3.345-.48.33-.913.49-1.302.48-.428-.008-1.252-.241-1.865-.44-.752-.245-1.349-.374-1.297-.789.027-.216.325-.437.893-.663 3.498-1.524 5.83-2.529 6.998-3.014 3.332-1.386 4.025-1.627 4.476-1.635z" />
                </svg>
              </a>
            </div>
          </div>
        </section>
      </div>

      {/* Footer */}
      <footer className="bg-white border-t border-gray-200">
        <div className="container mx-auto px-6 py-12">
          <div className="grid md:grid-cols-3 gap-8 mb-8">
            {/* Logo and description */}
            <div className="md:col-span-1">
              <div className="flex items-center space-x-2 text-2xl font-bold font-poppins mb-4">
                <img
                  src="/icons/TokenA-GRAT.svg"
                  alt="GRAT Token"
                  className="w-8 h-8"
                />
                <span className="text-violet-700">$</span>
                <span className="text-gray-800">GRAT</span>
              </div>
              <p className="text-gray-600 text-sm leading-relaxed">
                {t("footer.description")}
              </p>
            </div>

            {/* Quick Links */}
            <div className="md:col-span-1">
              <h4 className="font-bold text-gray-900 mb-4">{t("footer.quickLinks")}</h4>
              <div className="space-y-2">
                <button
                  onClick={() => scrollToSection("grat")}
                  className="block text-gray-600 hover:text-violet-700 transition-colors text-sm"
                >
                  {t("nav.protocol")}
                </button>
                <button
                  onClick={() => scrollToSection("tips")}
                  className="block text-gray-600 hover:text-violet-700 transition-colors text-sm"
                >
                  {t("nav.coin")}
                </button>
                <button
                  onClick={() => scrollToSection("tokenomics")}
                  className="block text-gray-600 hover:text-violet-700 transition-colors text-sm"
                >
                  {t("nav.tokenomics")}
                </button>
                <button
                  onClick={() => scrollToSection("how")}
                  className="block text-gray-600 hover:text-violet-700 transition-colors text-sm"
                >
                  {t("nav.howItWorks")}
                </button>
              </div>
            </div>

            {/* Contact */}
            <div className="md:col-span-1">
              <h4 className="font-bold text-gray-900 mb-4">{t("footer.contact")}</h4>
              <div className="space-y-3">
                <div className="flex items-start space-x-3">
                  <div className="flex-shrink-0 w-8 h-8 bg-violet-100 rounded-lg flex items-center justify-center">
                    <svg className="w-4 h-4 text-violet-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
                    </svg>
                  </div>
                  <div className="min-w-0 flex-1">
                    <p className="text-sm text-gray-600 mb-1">{t("footer.supportEmail")}</p>
                    <a 
                      href="mailto:<EMAIL>" 
                      className="text-sm font-semibold text-violet-700 hover:text-violet-800 transition-colors break-all"
                    >
                      <EMAIL>
                    </a>
                  </div>
                </div>
                
                {/* Social Links */}
                <div className="pt-2">
                  <p className="text-sm text-gray-600 mb-3">{t("footer.followUs")}</p>
                  <div className="flex space-x-3">
                    <a
                      href="#"
                      className="w-8 h-8 bg-gray-100 hover:bg-violet-100 transition-colors rounded-lg flex items-center justify-center"
                    >
                      <svg className="w-4 h-4 text-gray-600 hover:text-violet-600" fill="currentColor" viewBox="0 0 24 24">
                        <path d="M23.953 4.57a10 10 0 01-2.825.775 4.958 4.958 0 002.163-2.723c-.951.555-2.005.959-3.127 1.184a4.92 4.92 0 00-8.384 4.482C7.69 8.095 4.067 6.13 1.64 3.162a4.822 4.822 0 00-.666 2.475c0 1.71.87 3.213 2.188 4.096a4.904 4.904 0 01-2.228-.616v.06a4.923 4.923 0 003.946 4.827 4.996 4.996 0 01-2.212.085 4.936 4.936 0 004.604 3.417 9.867 9.867 0 01-6.102 2.105c-.39 0-.779-.023-1.17-.067a13.995 13.995 0 007.557 2.209c9.053 0 13.998-7.496 13.998-13.985 0-.21 0-.42-.015-.63A9.935 9.935 0 0024 4.59z" />
                      </svg>
                    </a>
                    <a
                      href="#"
                      className="w-8 h-8 bg-gray-100 hover:bg-violet-100 transition-colors rounded-lg flex items-center justify-center"
                    >
                      <svg className="w-4 h-4 text-gray-600 hover:text-violet-600" fill="currentColor" viewBox="0 0 24 24">
                        <path d="M20.317 4.492c-1.53-.69-3.17-1.2-4.885-1.49a.075.075 0 00-.079.036c-.211.375-.445.865-.608 1.25a18.566 18.566 0 00-5.487 0 12.36 12.36 0 00-.617-1.25.077.077 0 00-.079-.036A15.946 15.946 0 004.717 4.492a.07.07 0 00-.032.027C.533 9.046-.32 13.498.099 17.9a.082.082 0 00.031.056 16.108 16.108 0 004.729 2.28.077.077 0 00.084-.028c.462-.63.874-1.295 1.226-1.994a.076.076 0 00-.041-.106 10.683 10.683 0 01-1.548-.706.077.077 0 01-.008-.128c.104-.077.208-.158.307-.24a.074.074 0 01.077-.01c3.247 1.424 6.766 1.424 9.99 0a.074.074 0 01.078.01c.099.082.203.163.307.24a.077.077 0 01-.006.128 10.021 10.021 0 01-1.549.706.076.076 0 00-.041.106c.36.699.772 1.365 1.225 1.994a.076.076 0 00.084.028 16.017 16.017 0 004.729-2.28.077.077 0 00.032-.056c.5-5.094-.838-9.52-3.549-13.438a.061.061 0 00-.031-.027zM8.02 15.278c-1.182 0-2.157-1.085-2.157-2.419 0-1.333.956-2.419 2.157-2.419 1.21 0 2.176 1.096 2.157 2.42 0 1.333-.956 2.418-2.157 2.418zm7.975 0c-1.183 0-2.157-1.085-2.157-2.419 0-1.333.955-2.419 2.157-2.419 1.21 0 2.176 1.096 2.157 2.42 0 1.333-.946 2.418-2.157 2.418z" />
                      </svg>
                    </a>
                    <a
                      href="#"
                      className="w-8 h-8 bg-gray-100 hover:bg-violet-100 transition-colors rounded-lg flex items-center justify-center"
                    >
                      <svg className="w-4 h-4 text-gray-600 hover:text-violet-600" fill="currentColor" viewBox="0 0 24 24">
                        <path d="M11.944 0A12 12 0 0 0 0 12a12 12 0 0 0 12 12 12 12 0 0 0 12-12A12 12 0 0 0 12 0a12 12 0 0 0-.056 0zm4.962 7.224c.1-.002.321.023.465.14a.506.506 0 0 1 .171.325c.016.093.036.306.02.472-.18 1.898-.962 6.502-1.36 8.627-.168.9-.499 1.201-.82 1.23-.696.065-1.225-.46-1.9-.902-1.056-.693-1.653-1.124-2.678-1.8-1.185-.78-.417-1.21.258-1.91.177-.184 3.247-2.977 3.307-3.23.007-.032.014-.15-.056-.212s-.174-.041-.249-.024c-.106.024-1.793 1.14-5.061 3.345-.48.33-.913.49-1.302.48-.428-.008-1.252-.241-1.865-.44-.752-.245-1.349-.374-1.297-.789.027-.216.325-.437.893-.663 3.498-1.524 5.83-2.529 6.998-3.014 3.332-1.386 4.025-1.627 4.476-1.635z" />
                      </svg>
                    </a>
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* Bottom copyright */}
          <div className="border-t border-gray-200 pt-6 text-center text-gray-500">
            <p className="text-sm">{t("footer.copyright")}</p>
            <p className="text-xs mt-2">{t("footer.disclaimer")}</p>
          </div>
        </div>
      </footer>
    </div>
  );
};

export default HomePage;
