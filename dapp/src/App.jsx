import React from 'react'
import { Routes, Route } from 'react-router-dom'
import HomePage from './pages/HomePage'
import LaunchApp from './pages/LaunchApp'
import NFTPresale from './pages/NFTPresale'
import ComingSoonModal from './components/ComingSoonModal'

function App() {
  const envValue = process.env.REACT_APP_ENABLE_LAUNCH_APP
  const isLaunchAppEnabled = envValue === 'true'

  return (
    <div className="App">
      <Routes>
        <Route path="/" element={<HomePage />} />
        <Route path="/app" element={isLaunchAppEnabled ? <LaunchApp /> : <ComingSoonPage />} />
        <Route path="/presale" element={<NFTPresale />} />
      </Routes>
    </div>
  )
}

// Simplified coming soon page component
const ComingSoonPage = () => {
  const [showModal, setShowModal] = React.useState(true);
  
  const handleClose = () => {
    setShowModal(false);
    // Return to homepage after closing modal
    window.location.href = '/';
  };

  return (
    <div className="min-h-screen bg-gray-50 flex items-center justify-center">
      <ComingSoonModal 
        isOpen={showModal} 
        onClose={handleClose}
      />
    </div>
  );
};

export default App