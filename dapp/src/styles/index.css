@tailwind base;
@tailwind components;
@tailwind utilities;

/* Global styles */
body {
  font-family: "Poppins", "Noto Sans SC", "Noto Sans JP", sans-serif;
  background-color: #f9fafb;
  color: #1f2937;
  /* Ensure page scrolls normally */
  touch-action: auto;
  -webkit-overflow-scrolling: touch;
}

html {
  /* Ensure HTML element scrolls normally */
  touch-action: auto;
  -webkit-overflow-scrolling: touch;
}

.font-poppins {
  font-family: "Poppins", sans-serif;
}

/* Card styles */
.card {
  background-color: white;
  border-radius: 1.5rem;
  box-shadow: 0 10px 15px -3px rgb(0 0 0 / 0.05), 0 4px 6px -4px rgb(0 0 0 / 0.05);
  transition: all 0.3s ease-in-out;
}

.card:hover {
  box-shadow: 0 25px 50px -12px rgb(0 0 0 / 0.12), 0 20px 25px -5px rgb(0 0 0 / 0.08);
  transform: translateY(-4px) scale(1.02);
}

/* Modal styles optimization */
.modal-backdrop {
  backdrop-filter: blur(4px);
  background-color: rgba(0, 0, 0, 0.5);
}

.modal-panel {
  max-height: 90vh;
  animation: modalSlideIn 0.3s ease-out;
}

@keyframes modalSlideIn {
  from {
    opacity: 0;
    transform: translateY(-20px) scale(0.95);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

/* Mobile optimization */
@media (max-width: 640px) {
  .modal-panel {
    animation: modalSlideUp 0.3s ease-out;
  }
  
  @keyframes modalSlideUp {
    from {
      opacity: 0;
      transform: translateY(100%);
    }
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }
}

/* Mobile menu slide animation */
@keyframes slideInRight {
  from {
    opacity: 0;
    transform: translateX(100%);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

.mobile-menu-slide {
  animation: slideInRight 0.3s ease-out;
}

/* Mobile menu item animation */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.mobile-menu-item {
  animation: fadeInUp 0.4s ease-out;
}

.mobile-menu-item:nth-child(1) { animation-delay: 0.1s; }
.mobile-menu-item:nth-child(2) { animation-delay: 0.2s; }
.mobile-menu-item:nth-child(3) { animation-delay: 0.3s; }
.mobile-menu-item:nth-child(4) { animation-delay: 0.4s; }

/* Touch scroll optimization */
.modal-panel {
  -webkit-overflow-scrolling: touch;
}

/* Prevent background scroll when modal is open */
.modal-open {
  overflow: hidden;
  position: fixed;
  width: 100%;
}

/* Ensure modal internal scrolling works properly */
.modal-panel {
  display: flex;
  flex-direction: column;
}

/* Optimize scroll experience */
.modal-panel > div {
  min-height: 0;
}

/* Mobile scroll optimization */
@media (max-width: 640px) {
  .modal-panel {
    height: 90vh;
    max-height: 90vh;
  }
  
  /* Mobile optimized scroll area */
  .modal-panel .overflow-y-auto {
    -webkit-overflow-scrolling: touch;
    overscroll-behavior: contain;
  }
}

/* Language switcher enhanced animation */
@keyframes languageDropdownIn {
  from {
    opacity: 0;
    transform: translateY(-10px) scale(0.95);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

.language-dropdown {
  animation: languageDropdownIn 0.2s ease-out;
}

/* Language switcher item animation */
@keyframes languageItemFadeIn {
  from {
    opacity: 0;
    transform: translateX(-10px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

.language-item {
  animation: languageItemFadeIn 0.3s ease-out;
}

.language-item:nth-child(1) { animation-delay: 0.05s; }
.language-item:nth-child(2) { animation-delay: 0.1s; }
.language-item:nth-child(3) { animation-delay: 0.15s; }
.language-item:nth-child(4) { animation-delay: 0.2s; }
.language-item:nth-child(5) { animation-delay: 0.25s; }
.language-item:nth-child(6) { animation-delay: 0.3s; }
.language-item:nth-child(7) { animation-delay: 0.35s; }

/* Current page indicator pulse animation */
@keyframes currentPagePulse {
  0%, 100% {
    opacity: 1;
    transform: scale(1);
  }
  50% {
    opacity: 0.7;
    transform: scale(1.1);
  }
}

.current-page-indicator {
  animation: currentPagePulse 2s ease-in-out infinite;
}

/* Mobile menu touch optimization */
.mobile-menu-item, .language-item {
  -webkit-tap-highlight-color: transparent;
  user-select: none;
}

/* Mobile menu safe area adaptation */
@supports (padding: max(0px)) {
  .mobile-menu-slide {
    padding-left: max(0px, env(safe-area-inset-left));
    padding-right: max(0px, env(safe-area-inset-right));
    padding-bottom: max(0px, env(safe-area-inset-bottom));
  }
}

/* Mobile menu specific optimization */
@media (max-width: 640px) {
  .mobile-menu-slide {
    width: 100vw;
    max-width: 100vw;
  }
  
  /* Ensure menu content area scrolls correctly */
  .mobile-menu-slide .overflow-y-auto {
    max-height: calc(100vh - 180px); /* Leave space for header and footer */
  }
}

/* Extra small screen mobile menu optimization */
@media (max-width: 375px) {
  .mobile-menu-slide {
    width: 100vw;
  }
  
  .mobile-menu-slide .overflow-y-auto {
    max-height: calc(100vh - 160px);
  }
}

/* Optimize mobile click feedback */
@media (hover: none) and (pointer: coarse) {
  .mobile-menu-item:active {
    background-color: rgb(243, 244, 246);
    transform: scale(0.98);
  }
  
  .language-item:active {
    background-color: rgb(249, 250, 251);
    transform: scale(0.98);
  }
}

/* General scroll optimization */
.overflow-y-auto {
  scroll-behavior: smooth;
}

/* NFT Release Schedule scroll */
.nft-release-schedule-scroll {
  overflow-x: auto;
  -webkit-overflow-scrolling: touch;
  scroll-behavior: smooth;
}

.nft-release-schedule-scroll::-webkit-scrollbar {
  display: none;
}

.nft-release-schedule-scroll {
  scrollbar-width: none;
  -ms-overflow-style: none;
}

/* NFT card mobile optimization */
@media (max-width: 640px) {
  .nft-card {
    transform: none;
  }
  
  .nft-card:hover {
    transform: none;
    box-shadow: 0 10px 15px -3px rgb(0 0 0 / 0.08), 0 4px 6px -4px rgb(0 0 0 / 0.08);
  }
}

/* Scroll indicator animation */
@keyframes pulse-dot {
  0%, 100% {
    opacity: 0.3;
  }
  50% {
    opacity: 1;
  }
}

.scroll-indicator-active {
  animation: pulse-dot 2s infinite;
}

/* Period card hover effect */
.period-card {
  transition: all 0.2s ease-in-out;
}

.period-card:hover {
  transform: translateY(-2px);
}

/* Special effect for completed status */
.period-completed {
  position: relative;
  overflow: hidden;
}

.period-completed::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255,255,255,0.4), transparent);
  animation: shimmer 2s infinite;
}

@keyframes shimmer {
  0% {
    left: -100%;
  }
  100% {
    left: 100%;
  }
}

.modal-content-scroll {
  scrollbar-width: thin;
  scrollbar-color: #d1d5db #f3f4f6;
}

.modal-content-scroll::-webkit-scrollbar {
  width: 6px;
}

.modal-content-scroll::-webkit-scrollbar-track {
  background: #f3f4f6;
  border-radius: 3px;
}

.modal-content-scroll::-webkit-scrollbar-thumb {
  background: #d1d5db;
  border-radius: 3px;
}

.modal-content-scroll::-webkit-scrollbar-thumb:hover {
  background: #9ca3af;
}

/* Button styles */
.primary-btn {
  background-color: #6d28d9;
  color: white;
  transition: all 0.3s ease;
  box-shadow: 0 4px 15px rgba(109, 40, 217, 0.2);
  font-weight: 600;
}

.primary-btn:hover:not(:disabled) {
  background-color: #5b21b6;
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(109, 40, 217, 0.3);
}

.primary-btn:disabled {
  background-color: #d1d5db;
  cursor: not-allowed;
  box-shadow: none;
  color: #6b7280;
}

.secondary-btn {
  background-color: #f5f3ff;
  color: #6d28d9;
  border: 1px solid #ddd6fe;
  transition: all 0.3s ease;
  font-weight: 600;
}

.secondary-btn:hover:not(:disabled) {
  background-color: #ede9fe;
  border-color: #c4b5fd;
  transform: translateY(-2px);
}

.secondary-btn:disabled {
  background-color: #e5e7eb;
  color: #9ca3af;
  border-color: transparent;
  cursor: not-allowed;
}

.amber-btn {
  background-color: #f59e0b;
  color: white;
  transition: all 0.3s ease;
  box-shadow: 0 4px 15px rgba(245, 158, 11, 0.2);
  font-weight: 600;
}

.amber-btn:hover:not(:disabled) {
  background-color: #d97706;
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(245, 158, 11, 0.3);
}

.amber-btn:disabled {
  background-color: #d1d5db;
  cursor: not-allowed;
  box-shadow: none;
  color: #6b7280;
}

/* Hero section background */
.hero-bg {
  background-color: #f9fafb;
  background-image: 
    radial-gradient(circle at 20% 80%, rgba(109, 40, 217, 0.08), transparent 50%),
    radial-gradient(circle at 80% 20%, rgba(245, 158, 11, 0.08), transparent 50%),
    radial-gradient(circle at 40% 40%, rgba(16, 185, 129, 0.05), transparent 50%),
    linear-gradient(135deg, transparent 0%, rgba(109, 40, 217, 0.02) 50%, transparent 100%);
  background-size: 100% 100%;
  animation: background-shift 20s ease-in-out infinite;
}

@keyframes background-shift {
  0%, 100% {
    background-position: 0% 0%, 100% 100%, 50% 50%, 0% 0%;
  }
  33% {
    background-position: 100% 20%, 20% 80%, 80% 20%, 100% 0%;
  }
  66% {
    background-position: 20% 100%, 80% 0%, 20% 80%, 50% 100%;
  }
}

/* Loading animation */
.spinner {
  border: 2px solid #e5e7eb;
  border-top: 2px solid #6d28d9;
  border-radius: 50%;
  width: 16px;
  height: 16px;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Hero animation effects */
@keyframes orbit {
  0% { 
    transform: rotate(0deg) translateX(120px) rotate(0deg);
  }
  100% { 
    transform: rotate(360deg) translateX(120px) rotate(-360deg);
  }
}

@keyframes orbit-reverse {
  0% { 
    transform: rotate(0deg) translateX(100px) rotate(0deg);
  }
  100% { 
    transform: rotate(-360deg) translateX(100px) rotate(360deg);
  }
}

@keyframes orbit-slow {
  0% { 
    transform: rotate(0deg) translateX(140px) rotate(0deg);
  }
  100% { 
    transform: rotate(360deg) translateX(140px) rotate(-360deg);
  }
}

@keyframes pulse-glow {
  0%, 100% { 
    box-shadow: 0 0 20px rgba(109, 40, 217, 0.3);
    transform: scale(1);
  }
  50% { 
    box-shadow: 0 0 40px rgba(109, 40, 217, 0.6);
    transform: scale(1.05);
  }
}

@keyframes float {
  0%, 100% { transform: translateY(0px); }
  50% { transform: translateY(-10px); }
}

@keyframes ripple {
  0% {
    transform: scale(0.8);
    opacity: 1;
  }
  100% {
    transform: scale(2.4);
    opacity: 0;
  }
}

@keyframes shimmer {
  0% { opacity: 0.5; }
  50% { opacity: 1; }
  100% { opacity: 0.5; }
}

@keyframes twinkle {
  0%, 100% { opacity: 0.3; transform: scale(1); }
  50% { opacity: 1; transform: scale(1.2); }
}

@keyframes glow-pulse {
  0%, 100% { 
    box-shadow: 0 0 5px rgba(109, 40, 217, 0.5);
  }
  50% { 
    box-shadow: 0 0 20px rgba(109, 40, 217, 0.8), 0 0 40px rgba(109, 40, 217, 0.6);
  }
}

@keyframes slide-in-up {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes bounce-in {
  0% {
    opacity: 0;
    transform: scale(0.3);
  }
  50% {
    opacity: 1;
    transform: scale(1.05);
  }
  70% {
    transform: scale(0.9);
  }
  100% {
    opacity: 1;
    transform: scale(1);
  }
}

/* Hero area enhanced animation */
.hero-animation-container {
  animation: slide-in-up 1s ease-out;
}

.hero-title {
  animation: slide-in-up 1s ease-out 0.2s both;
}

.hero-subtitle {
  animation: slide-in-up 1s ease-out 0.4s both;
}

.hero-buttons {
  animation: slide-in-up 1s ease-out 0.6s both;
}

.hero-central-logo {
  animation: bounce-in 1.2s ease-out 0.8s both;
}

/* NFT card styles */
.nft-card {
  position: relative;
  overflow: hidden;
}

.nft-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(90deg, #6d28d9, #f59e0b);
}

/* Status indicator */
.status-dot {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  display: inline-block;
  margin-right: 6px;
}

.status-connected { background-color: #10b981; }
.status-disconnected { background-color: #ef4444; }

/* Mining progress bar */
.mining-progress {
  background: linear-gradient(90deg, #6d28d9 0%, #8b5cf6 50%, #f59e0b 100%);
  animation: progress-shine 2s ease-in-out infinite;
}

@keyframes progress-shine {
  0%, 100% { opacity: 0.8; }
  50% { opacity: 1; }
}

/* Wallet popup styles */
.wallet-modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  backdrop-filter: blur(4px);
  z-index: 1000;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 1rem;
}

.wallet-modal.hidden {
  display: none;
}

.wallet-modal-content {
  background: white;
  border-radius: 1.5rem;
  max-width: 600px;
  width: 100%;
  max-height: 90vh;
  overflow-y: auto;
  box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
  animation: modalSlideIn 0.3s ease-out;
}

@keyframes modalSlideIn {
  from {
    opacity: 0;
    transform: scale(0.9) translateY(-20px);
  }
  to {
    opacity: 1;
    transform: scale(1) translateY(0);
  }
}

.wallet-close-btn {
  position: absolute;
  top: 1rem;
  right: 1rem;
  background: transparent;
  border: none;
  cursor: pointer;
  padding: 0.5rem;
  border-radius: 0.5rem;
  transition: background-color 0.2s;
}

.wallet-close-btn:hover {
  background-color: #f3f4f6;
}

.wallet-option-modal {
  transition: all 0.2s;
  border: 2px solid transparent;
}

.wallet-option-modal:hover:not(.opacity-50) {
  border-color: #e5e7eb;
  transform: translateY(-2px);
}

.wallet-option-modal.connecting {
  border-color: #f59e0b;
  background-color: #fffbeb;
}

.wallet-option-modal.selected {
  border-color: #6d28d9;
  background-color: #f5f3ff;
}

/* Mobile optimization */
@media (max-width: 768px) {
  .wallet-modal {
    padding: 0.5rem;
  }
  .wallet-modal-content {
    max-height: 95vh;
    border-radius: 1rem;
  }
  .wallet-option-modal {
    padding: 1rem;
  }
}

/* Coming Soon Modal mobile special optimization */
@media (max-width: 640px) {
  .modal-panel {
    margin: 1rem;
    max-width: calc(100vw - 2rem);
    max-height: 90vh;
    padding: 1.5rem !important;
  }
  
  /* Mobile touch optimization */
  .touch-manipulation {
    touch-action: manipulation;
    -webkit-tap-highlight-color: transparent;
  }
  
  /* Email link mobile optimization */
  .modal-panel a[href^="mailto:"] {
    word-break: break-all;
    -webkit-user-select: all;
    -moz-user-select: all;
    -ms-user-select: all;
    user-select: all;
  }
}

/* Extra small screen optimization */
@media (max-width: 375px) {
  .modal-panel {
    margin: 0.75rem;
    max-width: calc(100vw - 1.5rem);
    padding: 1.25rem !important;
  }
  
  .modal-panel h2 {
    font-size: 1.375rem !important;
    line-height: 1.75rem !important;
  }
  
  .modal-panel p {
    font-size: 0.875rem !important;
    line-height: 1.5rem !important;
  }
}

/* Footer mobile optimization */
@media (max-width: 768px) {
  footer .grid {
    grid-template-columns: 1fr;
    gap: 1.5rem;
  }
  
  footer .space-x-3 {
    justify-content: center;
  }
  
  /* Mobile email link optimization */
  footer a[href^="mailto:"] {
    word-break: break-all;
    -webkit-user-select: all;
    -moz-user-select: all;
    -ms-user-select: all;
    user-select: all;
  }
}