{"nav": {"home": "Home", "protocol": "Protocol ($GRAT)", "coin": "Coin ($TIPS)", "tokenomics": "Tokenomics", "howItWorks": "How It Works", "aiTools": "✨AI Tools", "community": "Community", "nftPresale": "NFT Presale", "launchApp": "Launch App", "menu": "<PERSON><PERSON>", "navigation": "Navigation", "homeDesc": "Back to homepage", "currentPage": "Current page"}, "hero": {"title": "Forget Likes, It's Time to Give Some $TIPS!", "subtitle": "Burn $GRAT, mine $TIPS. Join a global digital tipping culture revival.", "learnMore": "Learn More", "launchApp": "Launch App", "presaleBanner": {"title": "Gratitude Protocol NFT Limited Presale", "subtitle": "1.5 WUSDT Limited Presale! Get your Gratitude Protocol NFT and enjoy mining rewards and dividends", "cta": "Join <PERSON>e <PERSON>"}}, "protocol": {"title": "Gratitude Protocol ($GRAT)", "subtitle": "The value heart of the ecosystem, the source of tipping culture.", "miningOutput": {"title": "Mining Output", "description": "Burning $GRAT is the only way to produce $TIPS. You're not speculating, you're minting culture."}, "governanceWeight": {"title": "Governance Weight", "description": "Hold $GRAT, and you own the protocol. Vote together to decide the future of the ecosystem."}, "valueAccrual": {"title": "Value Accrual", "description": "The prosperity of $TIPS will directly drive the demand for $GRAT. Investing in $GRAT is investing in the entire ecosystem."}}, "tips": {"title": "TipCoin ($TIPS)", "subtitle": "Make Tipping Great Again, cheer for every creation.", "p2pTipping": {"title": "P2P Tipping", "description": "Direct peer-to-peer tipping without intermediaries. Pure appreciation, instant delivery."}, "memeCreativity": {"title": "Meme Creativity", "description": "Ignite meme creativity explosion. Every good idea deserves to be rewarded."}, "empowerCreators": {"title": "Empower Creators", "description": "Empower all creators. From artists to developers, from writers to memers."}}, "tokenomics": {"title": "Tokenomics", "subtitle": "A fair and transparent distribution for community growth.", "stakingRewards": "Burning Rewards", "stakingRewardsDesc": "Community mining and ecosystem growth", "liquidityPool": "Liquidity Pool", "liquidityPoolDesc": "DEX liquidity and trading support", "ecosystemFund": "Ecosystem Fund", "ecosystemFundDesc": "Development and partnerships", "teamAndAdvisors": "Team & Advisors", "teamAndAdvisorsDesc": "Core team and strategic advisors"}, "howItWorks": {"title": "How It Works", "subtitle": "Simple steps to join the tipping revolution.", "step1": {"title": "Burn $GRAT", "description": "Burn your $GRAT tokens to start mining $TIPS and earn rewards."}, "step2": {"title": "Mine $TIPS", "description": "Your burned $GRAT automatically generates $TIPS tokens that you can use for tipping."}, "step3": {"title": "Tip & Earn", "description": "Use $TIPS to tip creators and earn rewards through the appreciation economy."}}, "aiTools": {"title": "✨ AI-Powered Tools", "subtitle": "Powered by <PERSON>, unleash your community's creativity.", "thankYouGenerator": {"title": "AI Thank-You Note Generator", "description": "Don't know how to express gratitude? Let AI write a sincere and fun thank-you note for you to send with your $TIPS!", "placeholder1": "e.g., an artist, a developer...", "placeholder2": "e.g., for drawing that cool avatar...", "generateBtn": "Generate Note"}, "memeWorkshop": {"title": "AI Meme Workshop", "description": "Stuck for meme ideas? AI helps you create viral-worthy meme concepts that are worth tipping!", "placeholder1": "e.g., crypto, cats, coffee...", "generateBtn": "Generate Meme Idea", "styles": {"funny": "Funny", "wholesome": "Wholesome", "sarcastic": "Sarcastic", "inspirational": "Inspirational"}}, "generating": "Generating...", "copyToClipboard": "Copy to Clipboard", "apiKeyNotConfigured": "AI features require API configuration. Please contact support for assistance.", "errors": {"fillAllFields": "Please fill in all fields", "enterTopic": "Please enter a topic for the meme", "generateFailed": "Failed to generate content. Please try again."}}, "nftPresale": {"title": "Gratitude Protocol NFT Presale", "subtitle": "Limited time presale! Get your Gratitude Protocol NFT and start your mining and dividend journey.", "backToHome": "Back to Home", "priceTitle": "Presale Price", "priceDescription": "Price per Gratitude Protocol NFT during presale", "networkTitle": "Payment Network", "networkDescription": "Please ensure you use the correct network for transfer", "supplyTitle": "Presale Supply", "supplyDescription": "Limited quantity for this presale", "info1Title": "📦 Airdrop Info", "info1Description": "NFTs will be airdropped to transfer addresses after presale ends. Please be patient.", "tip1Title": "✅ Security Tip", "tip1Description": "Please ensure you use personal wallet addresses for transfer, such as MetaMask, Trust Wallet, etc.", "featuresTitle": "Gratitude Protocol NFT Features", "feature1Title": "Mining Rewards", "feature1Description": "Hold NFT to participate in mining and earn $GRAT and $TIPS rewards", "feature2Title": "Dividend Income", "feature2Description": "Enjoy 30% dividend from TokenA transaction fees", "feature3Title": "Governance Weight", "feature3Description": "Participate in ecosystem governance and vote on protocol development", "feature4Title": "Token Release", "feature4Description": "Built-in 100,000 TokenA with 12-period linear release", "supportTitle": "Need Help?", "supportDescription": "If you have any questions, please contact our support team.", "contactEmail": "Email Support", "joinTelegram": "Join <PERSON>", "footer": "This presale is officially organized by the $GRAT ecosystem. Beware of scams.", "connectWalletPrompt": "Please connect your wallet to participate in the presale", "walletConnected": "Wallet Connected", "usdtBalance": "USDT Balance", "fixedPrice": "NFT Fixed Price", "processing": "Processing...", "presaleNotStarted": "Presale Not Started", "buyNFTButton": "Buy NFT ({{price}} USDT)", "info": {"price": "Fixed price: {{price}} USDT", "maxSupply": "Max supply: {{supply}} NFT", "airdropInfo": "NFTs will be airdropped based on purchase records after presale ends", "networkInfo": "Please ensure wallet network is BSC (Binance Smart Chain)"}, "userPurchased": "Purchased Quantity", "pricePerNFT": "Price per NFT", "selectQuantity": "Select Quantity", "totalCost": "Total Cost"}, "community": {"title": "Join Us", "subtitle": "Tippers are the new whales. Become a pioneer of the digital tipping culture, a community full of goodwill awaits you."}, "footer": {"copyright": "© 2025 Gratitude Protocol. All Rights Reserved.", "disclaimer": "Disclaimer: Digital currency investment involves risks. Please do your own research before participating. This project is a cultural experiment and does not constitute financial advice.", "description": "Global revival of digital tipping culture, rewarding creativity as it deserves.", "quickLinks": "Quick Links", "contact": "Contact Us", "supportEmail": "Support Email", "followUs": "Follow Us"}, "wallet": {"connectWallet": "Connect Wallet", "disconnect": "Disconnect", "connecting": "Connecting...", "connected": "Connected", "notConnected": "Wallet Not Connected", "connectToStart": "Connect wallet to get started", "copyAddress": "Copy address", "copied": "Copied!", "status": "Status", "active": "Active", "network": "Network", "selectWallet": "Select Wallet", "selectWalletDesc": "Choose your favorite wallet to connect to the $GRAT ecosystem", "connectionInstructions": "Connection Instructions:", "greenIndicator": "Green indicates wallet is installed and available", "redIndicator": "Red indicates wallet is not installed", "autoSwitchNetwork": "Will automatically switch to BSC network after connection", "installMetaMask": "Install MetaMask", "cancel": "Cancel", "connectionSuccess": "Connection successful", "connectionFailed": "Connection failed", "switchToBSC": "Please switch to BSC network", "wallets": {"metamask": {"description": "Most popular Ethereum wallet"}, "okx": {"description": "Secure and convenient multi-chain wallet"}, "tokenpocket": {"description": "Professional DeFi wallet"}, "trust": {"description": "Secure mobile wallet"}}, "errors": {"pleaseInstallMetaMask": "Please install MetaMask wallet", "pleaseInstallOKX": "Please install OKX wallet", "pleaseInstallTokenPocket": "Please install TokenPocket wallet", "pleaseInstallTrust": "Please install Trust wallet", "noAccountSelected": "No account selected", "cannotGetProvider": "Cannot get wallet provider", "cannotGetWalletProvider": "Cannot get wallet provider"}}, "errors": {"contractNotConnected": "Contract not connected", "insufficientBalance": "Insufficient USDT balance. Required: {{required}} USDT, Current: {{current}} USDT", "cannotPurchase": "Cannot purchase at this time", "presaleNotActive": "Presale is not active", "exceedsMaxSupply": "Purchase would exceed maximum supply"}, "dashboard": {"title": "My $GRAT Dashboard", "usdtBalance": "USDT Balance", "usdtBalanceDesc": "Ready for mining with USDT", "tokenABalance": "TokenA Balance", "tokenABalanceDesc": "Available for mining", "tokenBBalance": "TokenB Balance", "tokenBBalanceDesc": "Tip token", "nftCount": "MinerNFT", "nftCountDesc": "Holdings", "acceleratedRelease": "Accelerated Release", "acceleratedReleaseDesc": "Available for accelerated mining", "connectToStart": "Connect your wallet to get started", "supportedWallets": "Supports MetaMask, OKX Wallet, TokenPocket and other mainstream wallets", "tip": "Tip:", "tipDesc": "Please ensure you have installed the wallet extension and switched to BSC network. The app will automatically help you add BSC network configuration.", "quickActions": "Quick Actions", "clickToViewHistory": "Click to view detailed history", "clickToManageNFT": "Click to manage NFT", "nftHoldings": "NFT Holdings", "totalReleasable": "Total Releasable", "totalRecords": "Total Records", "activeRecords": "Active Records", "inviteInfo": "Invitation Information", "acceleratedReleaseAvailable": "Accelerated Release Available", "availableForAcceleration": "Available for Acceleration", "totalInvited": "Total Invited", "activeInvitees": "Active Invitees", "accelerationInfo": "Acceleration Information", "accelerationDesc": "Use your accelerated release amount to claim future mining rewards immediately. This will consume your acceleration credits.", "totalReceived": "Total Received", "totalUsed": "Total Used"}, "claim": {"history": {"title": "Claim History", "subtitle": "View all your claim records and accelerated release information", "viewHistory": "View Claim History", "loading": "Loading claim records...", "loadError": "Failed to load claim records", "noRecords": "No claim records yet", "startClaiming": "Start mining and claiming rewards to create your first record", "recentRecords": "Recent Claim Records", "totalClaimed": "Total Claimed", "totalValue": "Total Value", "normalClaimed": "Normal Release Claimed", "acceleratedClaimed": "Accelerated Release Claimed", "acceleratedInfo": "Accelerated Release Information", "currentUnused": "Current Unused", "totalReceived": "Total Received", "totalUsed": "Total Used", "normalRelease": "Normal Release", "acceleratedRelease": "Accelerated Release"}}, "mining": {"title": "Mining Operations", "mechanism1": {"title": "Mechanism 1: USDT Purchase & Burn", "description": "Purchase TokenA with USDT and burn for 2x TIPS rewards", "placeholder": "Enter USDT amount", "balance": "USDT Balance", "willPurchase": "<PERSON>", "expectedReceive": "Expected to Receive", "releasedIn6Periods": "Released over 6 periods (6 months)", "calculating": "Calculating...", "processing": "Processing transaction...", "approvingUSDT": "Approving USDT...", "purchasing": "Purchasing and burning...", "success": "USDT purchase and burn successful!", "failed": "Transaction failed", "enterValidAmount": "Please enter a valid USDT amount", "insufficientBalance": "Insufficient USDT balance", "previewError": "Preview calculation error", "purchaseButton": "Purchase with USDT", "notice": "Important Notice", "noticeDesc": "USDT will be used to purchase GRAT from DEX and burn immediately. You will receive 2x value in TIPS tokens.", "info": "USDT → GRAT → 2x TIPS rewards over 6 periods"}, "mechanism2": {"title": "Mechanism 2: <PERSON>", "description": "Hold NFT to burn TokenB and get 2x value release", "needsNFT": "(NFT required)", "placeholder": "Enter the amount of TokenB to burn", "expectedReleaseValue": "Expected release value", "burnButton": "Burn TokenB Mining"}, "history": {"title": "Mining History", "records": "Records", "mechanism1": "GRAT → TIPS Records", "mechanism2": "TIPS → TIPS Records", "noRecords": "No mining records yet", "claimed": "Claimed", "pending": "Pending", "burned": "Burned", "totalRelease": "Total Release", "totalUsdtValue": "Total USDT Value", "normalReleased": "Normal Released", "acceleratedReleased": "Accelerated Released", "totalReleased": "Total Released", "remaining": "Remaining", "claimRewards": "<PERSON><PERSON><PERSON>", "totalRecords": "Total Records", "clickToView": "Click to view details", "releaseProgress": "Release Progress", "realtimeData": "Real-time Data", "loading": "Loading mining records...", "loadError": "Failed to load mining records", "startMining": "Start mining to create your first record", "totalClaimable": "Total Claimable", "activeRecords": "Active Records", "burnedGRAT": "Burned GRAT", "burnedTIPS": "Burned TIPS", "released": "Released", "currentClaimable": "Current Claimable", "timeRemaining": "Time Remaining", "progress": "Progress", "active": "Active", "completed": "Completed", "claimable": "Claimable", "started": "Started", "releaseBreakdown": "Release Breakdown", "normalRelease": "Normal Release", "acceleratedRelease": "Accelerated Release", "claimAll": "Claim All"}}, "nft": {"title": "My Miner NFTs", "nftList": "NFT List", "noNFTs": "No Miner NFTs Yet", "releasableTokenA": "Releasable TokenA", "releaseTokenA": "Release TokenA", "mineToEarnNFT": "Mine some GRAT tokens to earn your first Miner NFT!", "viewDetails": "View Details", "loading": "Loading...", "totalTokens": "Total Tokens", "releasedTokens": "Released", "releasableNow": "Releasable Now", "released": "Released", "releaseNow": "Release Now", "releaseProgress": "Release Progress", "releaseSchedule": "Release Schedule", "currentPeriod": "Current Period", "nextReleaseTime": "Next Release Time", "month": "Month", "swipeToViewMore": "Swipe left and right to view more", "miningStatus": "Mining Status", "mining": "Mining", "idle": "Idle", "nonTransferable": "Non-transferable", "transferable": "Transferable"}, "notifications": {"processing": "Processing...", "enterValidAmount": "Please enter a valid amount", "miningSuccess": "Mining successful!", "miningFailed": "Mining failed", "approving": "Approving...", "executingTransaction": "Executing mining transaction...", "releasingTokenA": "Releasing TokenA...", "releaseSuccess": "TokenA release successful!", "releaseFailed": "Release failed", "mechanism2RequiresNFT": "Mechanism 2 requires holding at least 1 Miner NFT", "loadBalancesFailed": "Failed to load balances", "loadNFTDataFailed": "Failed to load NFT data", "loadMiningRecordsFailed": "Failed to load mining records", "loadInviteInfoFailed": "Failed to load invitation information", "claimingRewards": "Claiming rewards...", "claimSuccess": "<PERSON><PERSON><PERSON> claimed successfully!", "claimFailed": "<PERSON><PERSON><PERSON> failed", "purchaseSuccess": "Purchase successful!", "purchaseFailed": "Purchase failed", "approveFailed": "Approval failed", "loadPresaleInfoFailed": "Failed to load presale information"}, "languages": {"title": "Language", "en": "English", "zh": "简体中文", "ja": "日本語", "fr": "Français", "de": "De<PERSON>ch", "es": "Español", "ko": "한국어"}, "invite": {"title": "Invitation System", "setInviterTitle": "Set Your Inviter", "inviteOthersTitle": "Invite <PERSON>", "inviterNote": "One-time Setup", "inviterDesc": "You can only set an inviter once. Choose carefully as this cannot be changed later.", "inviterAddress": "Inviter Address", "setInviter": "Set Inviter", "myAddress": "My Invitation Address", "inviteLink": "Invitation Link", "rewardInfo": "Invitation Rewards", "rewardDesc": "You earn 10% accelerated release credits when your invitees participate in mining.", "totalInvited": "Total Invited", "activeInvitees": "Active Invitees", "totalRewards": "Total Rewards", "inviteeList": "My Invitees", "invitee": "Invitee", "copy": "Copy", "copied": "Copied!", "share": "Share", "shareTitle": "Join the GRAT Mining Ecosystem", "shareText": "Join me in the GRAT mining ecosystem and earn rewards together!", "enterValidAddress": "Please enter a valid inviter address", "invalidAddress": "Invalid address format", "cannotInviteSelf": "You cannot invite yourself", "inviterDetected": "Invitation link detected! You can set an inviter to earn rewards.", "settingInviter": "Setting inviter...", "inviterSetSuccess": "Inviter set successfully!", "setInviterFailed": "Failed to set inviter", "linkCopied": "Invitation link copied to clipboard!", "addressCopied": "Address copied to clipboard!", "copyFailed": "Failed to copy to clipboard", "rewardHistory": "Invitation Reward History", "myInviter": "My Inviter", "inviterRole": "Inviter", "activeStatus": "Active", "joinedOn": "Joined on", "noInvitees": "No Invitees Yet", "startInviting": "Start inviting friends to earn rewards!", "rewardMechanism": "Reward Mechanism", "rewardExplanation": "You receive 10% accelerated release credits from the total value burned by your invitees. These credits can be used to claim future mining rewards immediately.", "viewRewards": "View Reward Details"}, "acceleratedRelease": {"title": "Accelerated Release Quota", "currentAvailable": "Current Available", "totalReceived": "Total Received", "totalUsed": "Total Used", "usageRate": "Usage Rate", "used": "Used", "available": "Available", "howItWorks": "How Accelerated Release Works", "explanation1": "Invite others to participate in mining to get 10% accelerated release quota", "explanation2": "Accelerated quota can be used to immediately release locked mining rewards", "explanation3": "Available accelerated quota is used first when claiming", "explanation4": "Using accelerated quota does not affect normal time-based release", "noQuota": "No Accelerated Release Quota", "inviteToEarn": "Invite friends to participate in mining to earn accelerated release quota"}, "comingSoon": {"title": "Coming Soon", "description": "We're working hard to bring you an amazing new experience!", "stayTuned": "Stay tuned for our latest updates", "understand": "I Understand", "contactUs": "Contact Us"}, "referral": {"title": "Referral System", "description": "Invite friends to buy NFTs and earn referral rewards", "manageReferrals": "Manage Referrals", "yourLink": "Your Referral Link", "copy": "Copy", "copied": "<PERSON>pied", "statistics": "Referral Statistics", "totalReferees": "Total Referees", "totalPurchased": "Total Purchased", "averagePurchase": "Average Purchase", "totalCommission": "Total Commission (USDT)", "refereesList": "Referees List", "purchasedAmount": "Purchased {{amount}} NFTs", "noReferees": "No referees yet", "linkReady": "Ready", "linkNotReady": "Not Ready", "linkStatus": "Referral Link Status", "howItWorks": "How it Works", "instruction1": "Share your referral link with friends", "instruction2": "Friends purchase NFTs through your link", "instruction3": "View your referral statistics here", "confirmReferrer": "Con<PERSON>rm <PERSON>rer", "confirmReferrerDesc": "We detected you're using a referral link. Would you like to use the following address as your referrer?", "referrerAddress": "<PERSON><PERSON><PERSON> Address", "purchaseWithoutReferrer": "Purchase Without Referrer", "confirmAndPurchase": "Confirm and Purchase", "myReferrer": "<PERSON> Referrer", "viewOnBscscan": "View on BSC Explorer"}, "common": {"loading": "Loading...", "previous": "Previous", "next": "Next", "retry": "Retry", "cancel": "Cancel", "processing": "Processing...", "calculating": "Calculating...", "copied": "Copied!", "active": "Active", "usdtBalance": "USDT Balance", "releaseProgress": "Release Progress", "days": "d", "hours": "h", "minutes": "m", "seconds": "s"}, "earnings": {"realtimeTitle": "Real-time Earnings", "lastUpdate": "Last Update", "realtime": "Real-time", "refresh": "Refresh", "loading": "Loading earnings data...", "loadError": "Failed to load earnings data", "totalClaimable": "Total Claimable", "dailyRate": "Daily Rate", "totalLocked": "Total Locked Value", "progress": "Release Progress", "released": "Released", "releaseProgress": "Release Progress", "estimatedCompletion": "Estimated Completion", "claimMechanism1": "Claim Mechanism 1 Rewards", "claimMechanism2": "Claim Mechanism 2 Rewards", "infoTitle": "Real-time Earnings Info", "infoDesc": "Earnings data refreshes every 30 seconds. You can claim released rewards anytime, calculated with real-time linear release algorithm."}, "accelerated": {"title": "Accelerated Release", "subtitle": "Use invitation rewards to accelerate mining release", "loading": "Loading accelerated release data...", "loadError": "Load failed", "noBalance": "No available credits", "noBalanceDesc": "You currently have no available accelerated release credits. Invite friends to participate in mining to earn 10% accelerated release rewards.", "availableBalance": "Available Credits", "maxAccelerated": "Max Accelerated", "enterAmount": "Enter accelerated release amount", "maxAmount": "Max amount", "preview": "Preview", "releaseValue": "Release Value", "willReceive": "Will Receive", "estimatedGas": "Estimated Gas", "warning": "Important Notice", "warningDesc": "Accelerated release will immediately consume your invitation reward credits and release mining rewards early. This action is irreversible.", "confirm": "Confirm Accelerated Release", "enterValidAmount": "Please enter a valid release amount", "insufficientBalance": "Insufficient available credits", "exceedsMaximum": "Exceeds maximum accelerated amount", "processing": "Processing accelerated release...", "success": "Accelerated release successful!", "failed": "Accelerated release failed"}}