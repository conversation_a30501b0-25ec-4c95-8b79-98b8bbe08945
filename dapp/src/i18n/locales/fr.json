{"nav": {"home": "Accueil", "protocol": "Protocole ($GRAT)", "coin": "Pièce ($TIPS)", "tokenomics": "Tokenomics", "howItWorks": "Comment ça marche", "aiTools": "✨Outils IA", "community": "Communauté", "nftPresale": "Prévente NFT", "launchApp": "Lancer l'App", "menu": "<PERSON><PERSON>", "navigation": "Navigation", "homeDesc": "Retour à l'accueil", "currentPage": "Page actuelle"}, "hero": {"title": "O<PERSON><PERSON><PERSON> les Likes, il est temps de donner des $TIPS !", "subtitle": "Brûlez $GRAT, minez $TIPS. Rejoignez la renaissance de la culture du pourboire numérique.", "learnMore": "En savoir plus", "launchApp": "Lancer l'App", "presaleBanner": {"title": "Prévente Limitée Gratitude Protocol NFT", "subtitle": "Prévente limitée 1.5 WUSDT ! Obtenez votre Gratitude Protocol NFT et profitez des récompenses de minage et dividendes", "cta": "Rejoindre la Prévente Maintenant"}}, "protocol": {"title": "Protocole Gratitude ($GRAT)", "subtitle": "Le cœur de valeur de l'écosystème, la source de la culture du pourboire.", "miningOutput": {"title": "Production de Minage", "description": "Brûler $GRAT est le seul moyen de produire $TIPS. Vous ne spéculez pas, vous créez de la culture."}, "governanceWeight": {"title": "Poids de Gouvernance", "description": "Possédez $GRAT, et vous possédez le protocole. Votez ensemble pour décider de l'avenir de l'écosystème."}, "valueAccrual": {"title": "Accumulation de Valeur", "description": "La prospérité de $TIPS stimulera directement la demande pour $GRAT. Investir dans $GRAT, c'est investir dans tout l'écosystème."}}, "tips": {"title": "TipCoin ($TIPS)", "subtitle": "Rendons les Pourboires Géniaux Encore, applaudissons chaque création.", "p2pTipping": {"title": "Pourboire P2P", "description": "Pourboire peer-to-peer direct sans intermédiaires. Pure appréciation, livraison instantanée."}, "memeCreativity": {"title": "Créativité Mème", "description": "Déclenchez une explosion de créativité mème. Chaque bonne idée mérite d'être récompensée."}, "empowerCreators": {"title": "Autonomiser les Créateurs", "description": "Autonomisez tous les créateurs. Des artistes aux développeurs, des écrivains aux memers."}}, "tokenomics": {"title": "Tokenomics", "subtitle": "Une distribution juste et transparente pour la croissance communautaire.", "stakingRewards": "Récompenses de Brûlage", "stakingRewardsDesc": "Minage communautaire et croissance de l'écosystème", "liquidityPool": "Pool de Liquidité", "liquidityPoolDesc": "Liquidité DEX et support de trading", "ecosystemFund": "Fonds Écosystème", "ecosystemFundDesc": "Développement et partenariats", "teamAndAdvisors": "Équipe & Conseillers", "teamAndAdvisorsDesc": "Équipe principale et conseillers stratégiques"}, "howItWorks": {"title": "Comment ça marche", "subtitle": "Étapes simples pour rejoindre la révolution du pourboire.", "step1": {"title": "Brûlez $GRAT", "description": "Brûlez vos tokens $GRAT pour commencer à miner $TIPS et gagner des récompenses."}, "step2": {"title": "Minez $TIPS", "description": "Vos $GRAT brûlés génèrent automatiquement des tokens $TIPS que vous pouvez utiliser pour les pourboires."}, "step3": {"title": "Donnez & Gagnez", "description": "Utilisez $TIPS pour donner des pourboires aux créateurs et gagnez des récompenses grâce à l'économie de l'appréciation."}}, "aiTools": {"title": "✨ Outils alimentés par l'IA", "subtitle": "<PERSON><PERSON><PERSON> par Gemini, lib<PERSON>rez la créativité de votre communauté.", "thankYouGenerator": {"title": "Générateur de Notes de Remerciement IA", "description": "Vous ne savez pas comment exprimer votre gratitude ? Laissez l'IA écrire une note de remerciement sincère et amusante à envoyer avec vos $TIPS !", "placeholder1": "ex: un artiste, un développeur...", "placeholder2": "ex: pour avoir dessiné cet avatar cool...", "generateBtn": "<PERSON><PERSON><PERSON><PERSON>"}, "memeWorkshop": {"title": "Atelier Mème IA", "description": "À court d'idées de mèmes ? L'IA vous aide à créer des concepts de mèmes dignes de devenir viraux et méritant des pourboires !", "placeholder1": "ex: crypto, chats, café...", "generateBtn": "<PERSON><PERSON><PERSON>rer une Idée de Mème", "styles": {"funny": "Dr<PERSON>le", "wholesome": "<PERSON><PERSON>", "sarcastic": "Sarcastique", "inspirational": "Inspirant"}}, "generating": "Génération...", "copyToClipboard": "Copier dans le presse-papiers", "apiKeyNotConfigured": "Les fonctionnalités IA nécessitent une configuration API. Veuillez contacter le support pour assistance.", "errors": {"fillAllFields": "<PERSON><PERSON><PERSON>z remplir tous les champs", "enterTopic": "Veuillez entrer un sujet pour le mème", "generateFailed": "Échec de la génération de contenu. Veuillez réessayer."}}, "nftPresale": {"title": "Prévente Gratitude Protocol NFT", "subtitle": "Prévente à durée limitée ! Obtenez votre Gratitude Protocol NFT et commencez votre voyage de minage et de dividendes.", "backToHome": "Retour à l'Accueil", "priceTitle": "Prix de Prévente", "priceDescription": "Prix par Gratitude Protocol NFT pendant la prévente", "networkTitle": "Réseau de Paiement", "networkDescription": "Veuillez vous assurer d'utiliser le bon réseau pour le transfert", "supplyTitle": "<PERSON>re de Prévente", "supplyDescription": "Quantité limitée pour cette prévente", "info1Title": "📦 Info Airdrop", "info1Description": "Les NFTs seront largués par airdrop aux adresses de transfert après la fin de la prévente. Veuillez être patient.", "tip1Title": "✅ Conseil de Sécurité", "tip1Description": "Veuillez vous assurer d'utiliser des adresses de portefeuille personnel pour les transferts, comme MetaMask, Trust Wallet, etc.", "featuresTitle": "Fonctionnalités du Gratitude Protocol NFT", "feature1Title": "Récompenses de Minage", "feature1Description": "Détenez un NFT pour participer au minage et gagner des récompenses $GRAT et $TIPS", "feature2Title": "<PERSON><PERSON><PERSON>", "feature2Description": "Profitez de 30% de dividendes des frais de transaction TokenA", "feature3Title": "Poids de Gouvernance", "feature3Description": "Participez à la gouvernance de l'écosystème et votez sur le développement du protocole", "feature4Title": "Libération de Tokens", "feature4Description": "100 000 TokenA intégrés avec libération linéaire sur 12 périodes", "supportTitle": "Beso<PERSON> d'Aide ?", "supportDescription": "Si vous avez des questions, ve<PERSON><PERSON>z contacter notre équipe de support.", "contactEmail": "Support Email", "joinTelegram": "Rejoindre Telegram", "footer": "Cette prévente est officiellement organisée par l'écosystème $GRAT. Méfiez-vous des arnaques.", "connectWalletPrompt": "Veuillez connecter votre portefeuille pour participer à la prévente", "walletConnected": "Portefeuille Connecté", "usdtBalance": "Solde USDT", "fixedPrice": "Prix Fixe NFT", "processing": "Traitement...", "presaleNotStarted": "Prévente Pas Encore Commencée", "buyNFTButton": "Acheter NFT ({{price}} USDT)", "info": {"price": "Prix fixe : {{price}} USDT", "maxSupply": "Approvisionnement max : {{supply}} NFT", "airdropInfo": "Les NFT seront largués par airdrop selon les enregistrements d'achat après la fin de la prévente", "networkInfo": "Veuillez vous assurer que le réseau de votre portefeuille est BSC (Binance Smart Chain)"}, "userPurchased": "Quantité Achetée", "pricePerNFT": "Prix par NFT", "selectQuantity": "Sélectionner la Quantité", "totalCost": "Coût Total"}, "community": {"title": "Rejoignez-nous", "subtitle": "Les tippers sont les nouvelles baleines. Devenez un pionnier de la culture du pourboire numérique, une communauté pleine de bienveillance vous attend."}, "footer": {"copyright": "© 2025 Protocole Gratitude. Tous droits réservés.", "disclaimer": "Avertissement : L'investissement en monnaie numérique comporte des risques. Veuillez faire vos propres recherches avant de participer. Ce projet est une expérience culturelle et ne constitue pas un conseil financier.", "description": "Renaissance mondiale de la culture du pourboire numérique, récompensant la créativité comme elle le mérite.", "quickLinks": "Liens Rapides", "contact": "<PERSON><PERSON>", "supportEmail": "Email de Support", "followUs": "Suivez-nous"}, "wallet": {"connectWallet": "Con<PERSON><PERSON> le Portefeuille", "disconnect": "Déconnecter", "connecting": "Connexion...", "connected": "Connecté", "notConnected": "Portefeuille non connecté", "connectToStart": "Connectez le portefeuille pour commencer", "copyAddress": "Co<PERSON>r l'adresse", "copied": "Copié !", "status": "Statut", "active": "Actif", "network": "<PERSON><PERSON><PERSON>", "selectWallet": "Sélectionner le Portefeuille", "selectWalletDesc": "Choisissez votre portefeuille préféré pour vous connecter à l'écosystème $GRAT", "connectionInstructions": "Instructions de connexion :", "greenIndicator": "Le vert indique que le portefeuille est installé et disponible", "redIndicator": "Le rouge indique que le portefeuille n'est pas installé", "autoSwitchNetwork": "Basculera automatiquement vers le réseau BSC après la connexion", "installMetaMask": "Installer MetaMask", "cancel": "Annuler", "connectionSuccess": "Connexion réussie", "connectionFailed": "Échec de la connexion", "switchToBSC": "Veuillez basculer vers le réseau BSC", "wallets": {"metamask": {"description": "Le portefeuille Ethereum le plus populaire"}, "okx": {"description": "Portefeuille multi-chaînes sécurisé et pratique"}, "tokenpocket": {"description": "Portefeuille DeFi professionnel"}, "trust": {"description": "Portefeuille mobile sécurisé"}}, "errors": {"pleaseInstallMetaMask": "Veuillez installer le portefeuille MetaMask", "pleaseInstallOKX": "Veuillez installer le portefeuille OKX", "pleaseInstallTokenPocket": "Veuillez installer le portefeuille TokenPocket", "pleaseInstallTrust": "Veuillez installer le portefeuille Trust", "noAccountSelected": "Aucun compte sélectionné", "cannotGetProvider": "Impossible d'obtenir le fournisseur de portefeuille", "cannotGetWalletProvider": "Impossible d'obtenir le fournisseur de portefeuille"}}, "dashboard": {"title": "Mon Tableau de Bord $GRAT", "usdtBalance": "Solde USDT", "usdtBalanceDesc": "Prêt pour le minage avec USDT", "tokenABalance": "Solde TokenA", "tokenABalanceDesc": "Disponible pour le minage", "tokenBBalance": "Solde TokenB", "tokenBBalanceDesc": "Token de pourbo<PERSON>", "nftCount": "MinerNFT", "nftCountDesc": "Possessions", "acceleratedRelease": "Libération Accélérée", "acceleratedReleaseDesc": "Disponible pour le minage accéléré", "connectToStart": "Connectez votre portefeuille pour commencer", "supportedWallets": "Prend en charge MetaMask, OKX Wallet, TokenPocket et autres portefeuilles grand public", "tip": "Conseil :", "tipDesc": "Veuillez vous assurer d'avoir installé l'extension de portefeuille et basculé vers le réseau BSC. L'application vous aidera automatiquement à ajouter la configuration du réseau BSC.", "quickActions": "Actions Rapides", "clickToViewHistory": "Cliquez pour voir les enregistrements d'historique détaillés", "nftHoldings": "Possessions NFT", "totalReleasable": "Total Libérable", "totalRecords": "Total des Enregistrements", "activeRecords": "Enregistrements actifs", "clickToManageNFT": "Cliquez pour gérer vos NFT", "inviteInfo": "Informations d'Invitation", "acceleratedReleaseAvailable": "Libération Accélérée Disponible", "availableForAcceleration": "Disponible pour l'Accélération", "totalInvited": "Total Invité", "activeInvitees": "Invités Actifs", "accelerationInfo": "Informations d'Accélération", "accelerationDesc": "Utilisez votre montant de libération accélérée pour réclamer immédiatement les futures récompenses de minage. Cela consommera vos crédits d'accélération.", "totalReceived": "Total Reçu", "totalUsed": "Total Utilisé"}, "claim": {"history": {"title": "Historique des réclamations", "subtitle": "Consultez tous vos enregistrements de réclamation et les informations de libération accélérée", "viewHistory": "Voir l'historique des réclamations", "loading": "Chargement des enregistrements de réclamation...", "loadError": "Échec du chargement des enregistrements de réclamation", "noRecords": "Aucun enregistrement de réclamation pour le moment", "startClaiming": "Commencez à miner et à réclamer des récompenses pour créer votre premier enregistrement", "recentRecords": "Enregistrements de réclamation récents", "totalClaimed": "Total réclamé", "totalValue": "Valeur totale", "normalClaimed": "Libération normale réclamée", "acceleratedClaimed": "Libération accélérée réclamée", "acceleratedInfo": "Informations de libération accélérée", "currentUnused": "Actuellement inutilisé", "totalReceived": "Total reçu", "totalUsed": "Total utilisé", "normalRelease": "Libération normale", "acceleratedRelease": "Libération accélérée"}}, "mining": {"title": "Opérations de Minage", "mechanism1": {"title": "Mécanisme 1 : Achat et Combustion USDT", "description": "Achetez TokenA avec USDT et brûlez pour obtenir 2x récompenses TIPS", "placeholder": "Entrez le montant USDT", "balance": "Solde USDT", "willPurchase": "Achètera", "expectedReceive": "Attendu de recevoir", "releasedIn6Periods": "Libéré sur 6 périodes (6 mois)", "calculating": "Calcul en cours...", "processing": "Traitement de la transaction...", "approvingUSDT": "Approbation USDT...", "purchasing": "<PERSON><PERSON><PERSON> et combustion...", "success": "Achat et combustion USDT réussis !", "failed": "Transaction échouée", "enterValidAmount": "Veuillez entrer un montant USDT valide", "insufficientBalance": "Solde USDT insuffisant", "previewError": "E<PERSON>ur de calcul d'aperçu", "purchaseButton": "Acheter avec USDT", "notice": "<PERSON><PERSON>", "noticeDesc": "USDT sera utilisé pour acheter GRAT sur DEX et brûler immédiatement. Vous recevrez 2x la valeur en tokens TIPS.", "info": "USDT → GRAT → récompenses TIPS 2x sur 6 périodes"}, "mechanism2": {"title": "Mécanisme 2 : <PERSON><PERSON><PERSON><PERSON>", "description": "Possédez un NFT pour brûler TokenB et obtenir une libération de 2x la valeur", "needsNFT": "(NFT requis)", "placeholder": "Entrez la quantité de TokenB à brûler", "expectedReleaseValue": "Valeur de libération attendue", "burnButton": "Minage Brûler TokenB"}, "history": {"title": "Historique de Minage", "records": "Enregistrements", "mechanism1": "Enregistrements GRAT → TIPS", "mechanism2": "Enregistrements TIPS → TIPS", "noRecords": "Aucun enregistrement de minage pour le moment", "claimed": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "pending": "En attente", "burned": "B<PERSON><PERSON><PERSON>", "totalRelease": "Valeur de Libération Totale", "totalUsdtValue": "Valeur USDT Totale", "normalReleased": "Libération Normale", "acceleratedReleased": "Libération Accélérée", "totalReleased": "Total Libéré", "remaining": "Restant", "claimRewards": "R<PERSON><PERSON><PERSON>er les Récompenses", "totalRecords": "Total des Enregistrements", "clickToView": "Cliquez pour voir les détails", "releaseProgress": "Progrès de Libération", "realtimeData": "<PERSON><PERSON><PERSON>", "loading": "Chargement des enregistrements de minage...", "loadError": "Échec du chargement des enregistrements de minage", "startMining": "Commencez le minage pour créer votre premier enregistrement", "totalClaimable": "Total Réclamable", "activeRecords": "Enregistrements Actifs", "burnedGRAT": "GRAT Brûlé", "burnedTIPS": "TIPS Brûlé", "released": "<PERSON><PERSON><PERSON><PERSON>", "currentClaimable": "Actuellement <PERSON>", "timeRemaining": "<PERSON><PERSON>", "progress": "Progrès", "active": "Actif", "completed": "<PERSON><PERSON><PERSON><PERSON>", "claimable": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "started": "<PERSON><PERSON><PERSON><PERSON>", "releaseBreakdown": "Détail de Libération", "normalRelease": "Libération Normale", "acceleratedRelease": "Libération Accélérée", "claimAll": "<PERSON><PERSON>"}}, "nft": {"title": "Mon MinerNFT", "nftList": "Liste NFT", "noNFTs": "Aucun NFT Mineur pour le moment", "releasableTokenA": "TokenA Libérable", "releaseTokenA": "Libérer TokenA", "mineToEarnNFT": "Minez quelques tokens GRAT pour gagner votre premier NFT Mineur !", "viewDetails": "Voir les détails", "loading": "Chargement...", "totalTokens": "Total des tokens", "releasedTokens": "<PERSON><PERSON><PERSON><PERSON>", "releasableNow": "Libérable maintenant", "released": "<PERSON><PERSON><PERSON><PERSON>", "releaseNow": "Lib<PERSON>rer maintenant", "releaseProgress": "Progression de libération", "releaseSchedule": "Calendrier de libération", "currentPeriod": "Période actuelle", "nextReleaseTime": "Prochaine libération", "month": "<PERSON><PERSON>", "swipeToViewMore": "Glissez à gauche et à droite pour voir plus", "miningStatus": "Statut de Minage", "mining": "En cours de minage", "idle": "Inactif", "nonTransferable": "Non transférable", "transferable": "Transférable"}, "notifications": {"processing": "Traitement...", "enterValidAmount": "Veuillez entrer un montant valide", "miningSuccess": "Minage réussi !", "miningFailed": "Échec du minage", "approving": "Approbation...", "executingTransaction": "Exécution de la transaction de minage...", "releasingTokenA": "Libération de TokenA...", "releaseSuccess": "Libération de TokenA réussie !", "releaseFailed": "Échec de la libération", "mechanism2RequiresNFT": "Le mécanisme 2 nécessite de posséder au moins 1 MinerNFT", "loadBalancesFailed": "Échec du chargement des soldes", "loadNFTDataFailed": "Échec du chargement des données NFT", "loadMiningRecordsFailed": "Échec du chargement des enregistrements de minage", "loadInviteInfoFailed": "Échec du chargement des informations d'invitation", "claimingRewards": "Réclamation des récompenses...", "claimSuccess": "Récompenses réclamées avec succès !", "claimFailed": "Réclamation <PERSON>", "purchaseSuccess": "<PERSON><PERSON><PERSON> !", "purchaseFailed": "<PERSON><PERSON><PERSON>", "approveFailed": "Approbation échouée", "loadPresaleInfoFailed": "Échec du chargement des informations de prévente"}, "languages": {"title": "<PERSON><PERSON>", "en": "English", "zh": "简体中文", "ja": "日本語", "fr": "Français", "de": "De<PERSON>ch", "es": "Español", "ko": "한국어"}, "invite": {"title": "Système d'invitation", "setInviterTitle": "Définir votre parrain", "inviteOthersTitle": "Inviter d'autres personnes", "inviterNote": "Configuration unique", "inviterDesc": "Vous ne pouvez définir un parrain qu'une seule fois. Choi<PERSON><PERSON>z soigneusement car cela ne peut pas être modifié plus tard.", "inviterAddress": "<PERSON><PERSON><PERSON>", "setInviter": "Définir le parrain", "myAddress": "Mon adresse d'invitation", "inviteLink": "Lien d'invitation", "rewardInfo": "Récompenses d'invitation", "rewardDesc": "Vous gagnez 10% de crédits de libération accélérée lorsque vos filleuls participent au minage.", "totalInvited": "Total invité", "activeInvitees": "Filleuls actifs", "totalRewards": "Récompenses totales", "inviteeList": "<PERSON><PERSON>", "invitee": "<PERSON>lle<PERSON>", "copy": "<PERSON><PERSON><PERSON>", "copied": "Copié !", "share": "Partager", "shareTitle": "Rejoignez l'écosystème de minage GRAT", "shareText": "Rejoignez mon écosystème de minage GRAT et gagnons des récompenses ensemble !", "enterValidAddress": "<PERSON><PERSON><PERSON>z entrer une adresse de parrain valide", "invalidAddress": "Format d'adresse invalide", "cannotInviteSelf": "Vous ne pouvez pas vous inviter vous-même", "settingInviter": "Définition du parrain...", "inviterSetSuccess": "Parrain défini avec succès !", "setInviterFailed": "Échec de la définition du parrain", "inviterDetected": "Lien d'invitation détecté ! Vous pouvez définir un parrain pour gagner des récompenses.", "linkCopied": "Lien d'invitation copié dans le presse-papiers !", "addressCopied": "Adresse copiée dans le presse-papiers !", "copyFailed": "Échec de la copie dans le presse-papiers", "rewardHistory": "Historique des récompenses d'invitation", "myInviter": "Mon parrain", "inviterRole": "<PERSON><PERSON><PERSON>", "activeStatus": "Actif", "joinedOn": "Rejoint le", "noInvitees": "Aucun filleul pour le moment", "startInviting": "Commencez à inviter des amis pour gagner des récompenses !", "rewardMechanism": "Mécanisme de récompense", "rewardExplanation": "Vous recevez 10% de crédits de libération accélérée de la valeur totale brûlée par vos filleuls. Ces crédits peuvent être utilisés pour réclamer immédiatement les futures récompenses de minage.", "viewRewards": "Voir les détails des récompenses"}, "acceleratedRelease": {"title": "Quota de Libération Accélérée", "currentAvailable": "Actuellement Disponible", "totalReceived": "Total Reçu", "totalUsed": "Total Utilisé", "usageRate": "Taux d'Utilisation", "used": "<PERSON><PERSON><PERSON><PERSON>", "available": "Disponible", "howItWorks": "Comment Fonctionne la Libération Accélérée", "explanation1": "Invitez d'autres à participer au minage pour obtenir 10% de quota de libération accélérée", "explanation2": "Le quota accéléré peut être utilisé pour libérer immédiatement les récompenses de minage verrouillées", "explanation3": "Le quota accéléré disponible est utilisé en premier lors de la réclamation", "explanation4": "L'utilisation du quota accéléré n'affecte pas la libération normale basée sur le temps", "noQuota": "Aucun Quota de Libération Accélérée", "inviteToEarn": "Invitez des amis à participer au minage pour gagner du quota de libération accélérée"}, "comingSoon": {"title": "Bientôt disponible", "description": "Nous travaillons dur pour vous offrir une expérience extraordinaire !", "stayTuned": "Restez à l'écoute de nos dernières mises à jour", "understand": "Je comprends", "contactUs": "Nous contacter"}, "referral": {"title": "Système de Parrainage", "yourLink": "Votre Lien de Parra<PERSON>ge", "copy": "<PERSON><PERSON><PERSON>", "copied": "<PERSON><PERSON><PERSON>", "statistics": "Statistiques de Parrainage", "totalReferees": "Total des Parrainés", "totalPurchased": "Total Acheté", "averagePurchase": "<PERSON><PERSON><PERSON>", "totalCommission": "Commission Totale (USDT)", "refereesList": "Liste des Parrainés", "purchasedAmount": "A acheté {{amount}} NFTs", "noReferees": "Pas encore de parrainés", "howItWorks": "Comment ça Marche", "instruction1": "Partagez votre lien de parrainage avec vos amis", "instruction2": "Les amis achètent des NFTs via votre lien", "instruction3": "Consultez vos statistiques de parrainage ici", "description": "Invitez vos amis à acheter des NFT et gagnez des récompenses de parrainage", "manageReferrals": "<PERSON><PERSON><PERSON> les Parrainages", "linkReady": "<PERSON><PERSON><PERSON><PERSON>", "linkNotReady": "Non Prêt", "linkStatus": "Statut du Lien de Parrainage", "confirmReferrer": "Con<PERSON>rm<PERSON> le parrain", "confirmReferrerDesc": "Nous avons détecté que vous utilisez un lien de parrainage. Souhaitez-vous utiliser l'adresse suivante comme parrain?", "referrerAddress": "<PERSON><PERSON><PERSON>", "purchaseWithoutReferrer": "Acheter sans parrain", "confirmAndPurchase": "Con<PERSON><PERSON><PERSON> et acheter", "myReferrer": "Mon parrain", "viewOnBscscan": "Voir sur l'explorateur BSC"}, "common": {"loading": "Chargement...", "previous": "Précédent", "next": "Suivant", "retry": "<PERSON><PERSON><PERSON><PERSON>", "cancel": "Annuler", "processing": "Traitement...", "calculating": "Calcul...", "copied": "Copié!", "active": "Actif", "usdtBalance": "Solde USDT", "releaseProgress": "Progrès de Libération", "days": "j", "hours": "h", "minutes": "m", "seconds": "s"}, "earnings": {"realtimeTitle": "Gains en Temps Réel", "lastUpdate": "Dernière Mise à Jour", "realtime": "<PERSON><PERSON>", "refresh": "Actualiser", "loading": "Chargement des données de gains...", "loadError": "Échec du chargement des données de gains", "totalClaimable": "Total Réclamable", "dailyRate": "<PERSON><PERSON>", "totalLocked": "Valeur Totale Bloquée", "progress": "Progrès de Libération", "released": "<PERSON><PERSON><PERSON><PERSON>", "releaseProgress": "Progrès de Libération", "estimatedCompletion": "Achèvement Estimé", "claimMechanism1": "Réclamer Récompenses Mécanisme 1", "claimMechanism2": "Réclamer Récompenses Mécanisme 2", "infoTitle": "Informations G<PERSON><PERSON>", "infoDesc": "Les données de gains se rafraîchissent toutes les 30 secondes. V<PERSON> pouvez réclamer les récompenses libérées à tout moment, calculées avec l'algorithme de libération linéaire en temps réel."}, "accelerated": {"title": "Libération Accélérée", "subtitle": "Utiliser les récompenses d'invitation pour accélérer la libération de minage", "loading": "Chargement des données de libération accélérée...", "loadError": "Échec du chargement", "noBalance": "Aucun crédit disponible", "noBalanceDesc": "Vous n'avez actuellement aucun crédit de libération accélérée disponible. Invitez des amis à participer au minage pour gagner 10% de récompenses de libération accélérée.", "availableBalance": "Crédits Disponibles", "maxAccelerated": "Accélération Maximale", "enterAmount": "Entrer le montant de libération accélérée", "maxAmount": "Montant maximum", "preview": "<PERSON><PERSON><PERSON><PERSON>", "releaseValue": "Valeur de Libération", "willReceive": "<PERSON><PERSON><PERSON><PERSON>", "estimatedGas": "Gas Estimé", "warning": "<PERSON><PERSON>", "warningDesc": "La libération accélérée consommera immédiatement vos crédits de récompense d'invitation et libérera les récompenses de minage plus tôt. Cette action est irréversible.", "confirm": "Confirmer la Libération Accélérée", "enterValidAmount": "Veuillez entrer un montant de libération valide", "insufficientBalance": "Crédits disponibles insuffisants", "exceedsMaximum": "Dépasse le montant d'accélération maximum", "processing": "Traitement de la libération accélérée...", "success": "Libération accélérée réussie!", "failed": "Échec de la libération accélérée"}, "errors": {"contractNotConnected": "Contrat non connecté", "insufficientBalance": "Solde USDT insuffisant. Requis: {{required}} USDT, Actuel: {{current}} USDT", "cannotPurchase": "Impossible d'acheter pour le moment", "presaleNotActive": "La prévente n'est pas active", "exceedsMaxSupply": "L'achat dépasserait l'offre maximale"}}