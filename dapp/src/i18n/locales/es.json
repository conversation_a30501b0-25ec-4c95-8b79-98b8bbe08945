{"nav": {"home": "<PERSON><PERSON>o", "protocol": "Protocolo ($GRAT)", "coin": "Moneda ($TIPS)", "tokenomics": "Tokenomics", "howItWorks": "Cómo funciona", "aiTools": "✨Herramientas IA", "community": "Comunidad", "nftPresale": "Preventa NFT", "launchApp": "Lanzar App", "menu": "Menú", "navigation": "Navegación", "homeDesc": "Volver al inicio", "currentPage": "<PERSON><PERSON>gin<PERSON> actual"}, "hero": {"title": "¡Olvida los Likes, es hora de dar $TIPS!", "subtitle": "Quema $GRAT, minea $TIPS. Únete al renacimiento global de la cultura de propinas digitales.", "learnMore": "Saber más", "launchApp": "Lanzar App", "presaleBanner": {"title": "Preventa Limitada de Gratitude Protocol NFT", "subtitle": "¡Preventa Limitada de 1.5 WUSDT! Obtén tu Gratitude Protocol NFT y disfruta de recompensas de minería y dividendos", "cta": "Unirse a la Preventa Ahora"}}, "protocol": {"title": "Protocolo Gratitud ($GRAT)", "subtitle": "El corazón de valor del ecosistema, la fuente de la cultura de propinas.", "miningOutput": {"title": "Producción de Minería", "description": "Quemar $GRAT es la única forma de producir $TIPS. No estás especulando, estás acuñando cultura."}, "governanceWeight": {"title": "Peso de Gobernanza", "description": "Mantén $GRAT y posees el protocolo. Voten juntos para decidir el futuro del ecosistema."}, "valueAccrual": {"title": "Acumulación de Valor", "description": "La prosperidad de $TIPS impulsará directamente la demanda de $GRAT. Invertir en $GRAT es invertir en todo el ecosistema."}}, "tips": {"title": "TipCoin ($TIPS)", "subtitle": "Hagamos las Propinas Geniales de Nuevo, celebremos cada creación.", "p2pTipping": {"title": "Propinas P2P", "description": "Propinas peer-to-peer directas sin intermediarios. Apreciación pura, entrega instantánea."}, "memeCreativity": {"title": "Creatividad de Memes", "description": "Enciende una explosión de creatividad de memes. Cada buena idea merece ser recompensada."}, "empowerCreators": {"title": "<PERSON><PERSON><PERSON><PERSON>", "description": "Empodera a todos los creadores. Desde artistas hasta desarrolladores, desde escritores hasta memers."}}, "tokenomics": {"title": "Tokenomics", "subtitle": "Una distribución justa y transparente para el crecimiento comunitario.", "stakingRewards": "Recompensas de Quema", "stakingRewardsDesc": "Minería comunitaria y crecimiento del ecosistema", "liquidityPool": "Pool de Liquidez", "liquidityPoolDesc": "Liquidez DEX y soporte de trading", "ecosystemFund": "Fondo del Ecosistema", "ecosystemFundDesc": "Desarrollo y asociaciones", "teamAndAdvisors": "Equipo y Asesores", "teamAndAdvisorsDesc": "Equipo central y asesores estratégicos"}, "howItWorks": {"title": "Cómo funciona", "subtitle": "Pasos simples para unirse a la revolución de las propinas.", "step1": {"title": "Quema $GRAT", "description": "Quema tus tokens $GRAT para comenzar a minar $TIPS y ganar recompensas."}, "step2": {"title": "Minea $TIPS", "description": "Tus $GRAT quemados generan automáticamente tokens $TIPS que puedes usar para propinas."}, "step3": {"title": "Da Propinas y Gana", "description": "Usa $TIPS para dar propinas a los creadores y gana recompensas a través de la economía de la apreciación."}}, "aiTools": {"title": "✨ Herramientas impulsadas por IA", "subtitle": "Impulsad<PERSON> por Gemini, libera la creatividad de tu comunidad.", "thankYouGenerator": {"title": "Generador de Notas de Agradecimiento IA", "description": "¿No sabes cómo expresar gratitud? ¡Deja que la IA escriba una nota de agradecimiento sincera y divertida para enviar con tus $TIPS!", "placeholder1": "ej: un artista, un desarrollador...", "placeholder2": "ej: por dibujar ese avatar genial...", "generateBtn": "<PERSON><PERSON>"}, "memeWorkshop": {"title": "<PERSON>er de Memes IA", "description": "¿Sin ideas para memes? ¡La IA te ayuda a crear conceptos de memes dignos de volverse virales y merecer propinas!", "placeholder1": "ej: crypto, gatos, café...", "generateBtn": "Generar Idea de Me<PERSON>", "styles": {"funny": "Divertido", "wholesome": "Sano", "sarcastic": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "inspirational": "Inspirador"}}, "generating": "Generando...", "copyToClipboard": "Copiar al portapapeles", "apiKeyNotConfigured": "Las funciones de IA requieren configuración de API. Por favor contacte al soporte para asistencia.", "errors": {"fillAllFields": "Por favor llena todos los campos", "enterTopic": "Por favor ingresa un tema para el meme", "generateFailed": "Error al generar contenido. Por favor inténtalo de nuevo."}}, "nftPresale": {"title": "Preventa de Gratitude Protocol NFT", "subtitle": "¡Preventa por tiempo limitado! Obtén tu Gratitude Protocol NFT e inicia tu viaje de minería y dividendos.", "backToHome": "Volver al Inicio", "priceTitle": "Precio de Preventa", "priceDescription": "Precio por Gratitude Protocol NFT durante la preventa", "networkTitle": "Red de Pago", "networkDescription": "Por favor asegúrate de usar la red correcta para la transferencia", "supplyTitle": "Suministro de Preventa", "supplyDescription": "Cantidad limitada para esta preventa", "info1Title": "📦 Información de Airdrop", "info1Description": "Los NFTs serán enviados por airdrop a las direcciones de transferencia después de que termine la preventa. Por favor ten paciencia.", "tip1Title": "✅ Consejo de Seguridad", "tip1Description": "Por favor asegúrate de usar direcciones de billetera personal para transferencias, como MetaMask, Trust Wallet, etc.", "featuresTitle": "Características del Gratitude Protocol NFT", "feature1Title": "Recompensas de Minería", "feature1Description": "Mantén NFT para participar en minería y ganar recompensas de $GRAT y $TIPS", "feature2Title": "Ingresos por Dividendos", "feature2Description": "Disfruta del 30% de dividendos de las tarifas de transacción de TokenA", "feature3Title": "Peso de Gobernanza", "feature3Description": "Participa en la gobernanza del ecosistema y vota sobre el desarrollo del protocolo", "feature4Title": "Liberación de Tokens", "feature4Description": "100,000 TokenA incorporados con liberación lineal de 12 períodos", "supportTitle": "¿Necesitas Ayuda?", "supportDescription": "Si tienes alguna pregunta, por favor contacta a nuestro equipo de soporte.", "contactEmail": "Soporte por Email", "joinTelegram": "Unirse a Telegram", "footer": "Esta preventa está oficialmente organizada por el ecosistema $GRAT. Cuidado con las estafas.", "connectWalletPrompt": "Por favor conecta tu billetera para participar en la preventa", "walletConnected": "Billetera Conectada", "usdtBalance": "Saldo USDT", "fixedPrice": "Precio Fijo del NFT", "processing": "Procesando...", "presaleNotStarted": "La Preventa No Ha Comenzado", "buyNFTButton": "Comprar NFT ({{price}} USDT)", "info": {"price": "Precio fijo: {{price}} USDT", "maxSupply": "Suministro máximo: {{supply}} NFT", "airdropInfo": "Los NFTs serán enviados por airdrop basándose en los registros de compra después de que termine la preventa", "networkInfo": "Por favor asegúrate de que la red de tu billetera sea BSC (Binance Smart Chain)"}, "userPurchased": "Cantidad Comprada", "pricePerNFT": "Precio por NFT", "selectQuantity": "Seleccionar Cantidad", "totalCost": "Costo Total"}, "community": {"title": "Únete a nosotros", "subtitle": "Los que dan propinas son las nuevas ballenas. Conviértete en un pionero de la cultura de propinas digitales, una comunidad llena de buena voluntad te espera."}, "footer": {"copyright": "© 2025 Protocolo Gratitud. Todos los derechos reservados.", "disclaimer": "Descargo de responsabilidad: La inversión en moneda digital conlleva riesgos. Por favor, investiga por tu cuenta antes de participar. Este proyecto es un experimento cultural y no constituye asesoramiento financiero.", "description": "Renacimiento global de la cultura de propinas digitales, recompensando la creatividad como merece.", "quickLinks": "<PERSON><PERSON><PERSON>", "contact": "Contáctanos", "supportEmail": "<PERSON><PERSON> de <PERSON>", "followUs": "Síguenos"}, "wallet": {"connectWallet": "Conectar Billetera", "disconnect": "Desconectar", "connecting": "Conectando...", "connected": "Conectado", "notConnected": "Billetera no conectada", "connectToStart": "Conecta la billetera para comenzar", "copyAddress": "<PERSON><PERSON><PERSON>", "copied": "¡Copiado!", "status": "Estado", "active": "Activo", "network": "Red", "selectWallet": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "selectWalletDesc": "Elige tu billetera favorita para conectarte al ecosistema $GRAT", "connectionInstructions": "Instrucciones de conexión:", "greenIndicator": "El verde indica que la billetera está instalada y disponible", "redIndicator": "El rojo indica que la billetera no está instalada", "autoSwitchNetwork": "Cambiará automáticamente a la red BSC después de la conexión", "installMetaMask": "Instalar MetaMask", "cancel": "<PERSON><PERSON><PERSON>", "connectionSuccess": "Conexión exitosa", "connectionFailed": "Conexión fallida", "switchToBSC": "Por favor cambia a la red BSC", "wallets": {"metamask": {"description": "Billetera Ethereum más popular"}, "okx": {"description": "Billetera multicadena segura y conveniente"}, "tokenpocket": {"description": "Billetera DeFi profesional"}, "trust": {"description": "Billetera móvil segura"}}, "errors": {"pleaseInstallMetaMask": "Por favor instala la billetera MetaMask", "pleaseInstallOKX": "Por favor instala la billetera OKX", "pleaseInstallTokenPocket": "Por favor instala la billetera TokenPocket", "pleaseInstallTrust": "Por favor instala la billetera Trust", "noAccountSelected": "Ninguna cuenta seleccionada", "cannotGetProvider": "No se puede obtener el proveedor de billetera", "cannotGetWalletProvider": "No se puede obtener el proveedor de billetera"}}, "dashboard": {"title": "Mi Panel $GRAT", "usdtBalance": "Saldo USDT", "usdtBalanceDesc": "Listo para minería con USDT", "tokenABalance": "Saldo <PERSON>", "tokenABalanceDesc": "Disponible para minería", "tokenBBalance": "Saldo <PERSON>B", "tokenBBalanceDesc": "Token de propina", "nftCount": "MinerNFT", "nftCountDesc": "<PERSON><PERSON><PERSON><PERSON>", "acceleratedRelease": "Liberación Acelerada", "acceleratedReleaseDesc": "Disponible para minería acelerada", "connectToStart": "Conecta tu billetera para comenzar", "supportedWallets": "Soporta MetaMask, OKX Wallet, TokenPocket y otras billeteras principales", "tip": "Consejo:", "tipDesc": "Por favor asegúrate de haber instalado la extensión de billetera y cambiado a la red BSC. La aplicación te ayudará automáticamente a agregar la configuración de red BSC.", "quickActions": "Acciones Rápidas", "clickToViewHistory": "Haz clic para ver registros de historial detallados", "nftHoldings": "Tenencias NFT", "totalReleasable": "Total Liberable", "totalRecords": "Registros Totales", "activeRecords": "Registros activos", "clickToManageNFT": "Haz clic para gestionar tus NFTs", "inviteInfo": "Información de Invitación", "acceleratedReleaseAvailable": "Liberación Acelerada Disponible", "availableForAcceleration": "Disponible para Aceleración", "totalInvited": "Total Invitado", "activeInvitees": "Invitados Activos", "accelerationInfo": "Información de Aceleración", "accelerationDesc": "Usa tu cantidad de liberación acelerada para reclamar inmediatamente futuras recompensas de minería. Esto consumirá tus créditos de aceleración.", "totalReceived": "Total Recibido", "totalUsed": "Total Usado"}, "claim": {"history": {"title": "Historial de reclamos", "subtitle": "Ver todos sus registros de reclamos e información de liberación acelerada", "viewHistory": "Ver historial de reclamos", "loading": "Cargando registros de reclamos...", "loadError": "Error al cargar los registros de reclamos", "noRecords": "Aún no hay registros de reclamos", "startClaiming": "Comience a minar y reclamar recompensas para crear su primer registro", "recentRecords": "Registros de reclamos recientes", "totalClaimed": "Total reclamado", "totalValue": "Valor total", "normalClaimed": "Liberación normal reclamada", "acceleratedClaimed": "Liberación acelerada reclamada", "acceleratedInfo": "Información de liberación acelerada", "currentUnused": "Actualmente sin usar", "totalReceived": "Total recibido", "totalUsed": "Total usado", "normalRelease": "Liberación normal", "acceleratedRelease": "Liberación acelerada"}}, "mining": {"title": "Operaciones de Minería", "mechanism1": {"title": "Mecanismo 1: Compra y Quema USDT", "description": "Compra TokenA con USDT y quémalo para obtener 2x recompensas TIPS", "placeholder": "Ingresa cantidad de USDT", "balance": "Saldo USDT", "willPurchase": "Comprará", "expectedReceive": "Esperado a recibir", "releasedIn6Periods": "Liberado en 6 períodos (6 meses)", "calculating": "Calculando...", "processing": "Procesando transacción...", "approvingUSDT": "Aprobando USDT...", "purchasing": "Comprando y quemando...", "success": "¡Compra y quema de USDT exitosa!", "failed": "Transacción fallida", "enterValidAmount": "Por favor ingresa una cantidad válida de USDT", "insufficientBalance": "Saldo USDT insuficiente", "previewError": "Error de cálculo de vista previa", "purchaseButton": "Comprar con USDT", "notice": "Aviso Importante", "noticeDesc": "USDT se usará para comprar GRAT de DEX y quemar inmediatamente. Recibirás 2x valor en tokens TIPS.", "info": "USDT → GRAT → recompensas TIPS 2x en 6 períodos"}, "mechanism2": {"title": "Mecanismo 2: <PERSON><PERSON>", "description": "Mantén NFT para quemar TokenB y obtener liberación de 2x valor", "needsNFT": "(NFT requerido)", "placeholder": "Ingresa la cantidad de TokenB a quemar", "expectedReleaseValue": "Valor de liberación esperado", "burnButton": "Mine<PERSON><PERSON>"}, "history": {"title": "Historial de Minería", "records": "Registros", "mechanism1": "Registros GRAT → TIPS", "mechanism2": "Registros TIPS → TIPS", "noRecords": "Aún no hay registros de minería", "claimed": "Reclamado", "pending": "Pendiente", "burned": "<PERSON><PERSON><PERSON>", "totalRelease": "Valor de Liberación Total", "totalUsdtValue": "Valor Total USDT", "normalReleased": "Liberación Normal", "acceleratedReleased": "Liberación Acelerada", "totalReleased": "Total Liberado", "remaining": "Restante", "claimRewards": "<PERSON><PERSON><PERSON><PERSON>pen<PERSON>", "totalRecords": "Registros Totales", "clickToView": "Haz clic para ver detalles", "releaseProgress": "Progreso de Liberación", "realtimeData": "Datos en Tiempo Real", "loading": "Cargando registros de minería...", "loadError": "Error al cargar registros de minería", "startMining": "Comience a minar para crear su primer registro", "totalClaimable": "Total Reclamable", "activeRecords": "Registros Activos", "burnedGRAT": "GRAT <PERSON>o", "burnedTIPS": "TIPS Quemado", "released": "Liberado", "currentClaimable": "Actualmente Reclamable", "timeRemaining": "Tiempo Restante", "progress": "Progreso", "active": "Activo", "completed": "Completado", "claimable": "Reclamable", "started": "Iniciado", "releaseBreakdown": "Desglose de Liberación", "normalRelease": "Liberación Normal", "acceleratedRelease": "Liberación Acelerada", "claimAll": "<PERSON><PERSON><PERSON><PERSON>"}}, "nft": {"title": "Mi MinerNFT", "nftList": "Lista NFT", "noNFTs": "Aún no hay NFTs de Minero", "releasableTokenA": "TokenA Liberable", "releaseTokenA": "Liberar TokenA", "mineToEarnNFT": "¡Mina algunos tokens GRAT para ganar tu primer NFT Minero!", "viewDetails": "<PERSON><PERSON> <PERSON><PERSON><PERSON>", "loading": "Cargando...", "totalTokens": "Total de tokens", "releasedTokens": "Liberado", "releasableNow": "Liberable ahora", "released": "Liberado", "releaseNow": "<PERSON>ber<PERSON> ahora", "releaseProgress": "Progreso de liberación", "releaseSchedule": "Calendario de liberación", "currentPeriod": "<PERSON><PERSON><PERSON> actual", "nextReleaseTime": "Próxima liberación", "month": "<PERSON><PERSON>", "swipeToViewMore": "Desliza a izquierda y derecha para ver más", "miningStatus": "Estado de Minería", "mining": "Minando", "idle": "Inactivo", "nonTransferable": "No transferible", "transferable": "Transferible"}, "notifications": {"processing": "Procesando...", "enterValidAmount": "Por favor ingresa una cantidad válida", "miningSuccess": "¡Minería exitosa!", "miningFailed": "<PERSON><PERSON><PERSON>", "approving": "Aprobando...", "executingTransaction": "Ejecutando transacción de minería...", "releasingTokenA": "Liberando TokenA...", "releaseSuccess": "¡Liberación de TokenA exitosa!", "releaseFailed": "Liberación fallida", "mechanism2RequiresNFT": "El mecanismo 2 requiere mantener al menos 1 MinerNFT", "loadBalancesFailed": "Fallo al cargar saldos", "loadNFTDataFailed": "Fallo al cargar datos NFT", "loadMiningRecordsFailed": "Fallo al cargar registros de minería", "loadInviteInfoFailed": "Fallo al cargar información de invitación", "claimingRewards": "Reclamando recompensas...", "claimSuccess": "¡Recompensas reclamadas exitosamente!", "claimFailed": "Reclamo fall<PERSON>", "purchaseSuccess": "¡Compra exitosa!", "purchaseFailed": "Compra fallida", "approveFailed": "Aprobación fallida", "loadPresaleInfoFailed": "Error al cargar información de preventa"}, "languages": {"title": "Idioma", "en": "English", "zh": "简体中文", "ja": "日本語", "fr": "Français", "de": "De<PERSON>ch", "es": "Español", "ko": "한국어"}, "invite": {"title": "Sistema de Invitación", "setInviterTitle": "<PERSON><PERSON><PERSON> tu <PERSON>", "inviteOthersTitle": "Invitar a Otros", "inviterNote": "Configuración única", "inviterDesc": "Solo puedes establecer un invitador una vez. Elige cuidadosamente ya que esto no se puede cambiar más tarde.", "inviterAddress": "Dirección del Invitador", "setInviter": "<PERSON><PERSON><PERSON>", "myAddress": "Mi Dirección de Invitación", "inviteLink": "Enlace de Invitación", "rewardInfo": "Recompensas de Invitación", "rewardDesc": "Ganas 10% de créditos de liberación acelerada cuando tus invitados participan en la minería.", "totalInvited": "Total Invitado", "activeInvitees": "Invitados Activos", "totalRewards": "Recompensas Totales", "inviteeList": "<PERSON><PERSON>", "invitee": "<PERSON><PERSON><PERSON><PERSON>", "copy": "Copiar", "copied": "¡Copiado!", "share": "Compartir", "shareTitle": "Únete al Ecosistema de Minería GRAT", "shareText": "¡Únete a mi ecosistema de minería GRAT y ganemos recompensas juntos!", "enterValidAddress": "Por favor ingresa una dirección de invitador válida", "invalidAddress": "Formato de dirección inválido", "cannotInviteSelf": "No puedes invitarte a ti mismo", "settingInviter": "Estableciendo invitador...", "inviterSetSuccess": "¡Invitador establecido exitosamente!", "setInviterFailed": "Error al establecer el invitador", "inviterDetected": "¡Enlace de invitación detectado! Puedes establecer un invitador para ganar recompensas.", "linkCopied": "¡Enlace de invitación copiado al portapapeles!", "addressCopied": "¡Dirección copiada al portapapeles!", "copyFailed": "Error al copiar al portapapeles", "rewardHistory": "Historial de Recompensas de Invitación", "myInviter": "<PERSON>", "inviterRole": "<PERSON><PERSON><PERSON><PERSON>", "activeStatus": "Activo", "joinedOn": "Se unió el", "noInvitees": "Aún no hay invitados", "startInviting": "¡Comienza a invitar amigos para ganar recompensas!", "rewardMechanism": "Mecanismo de Recompensas", "rewardExplanation": "Recibes 10% de créditos de liberación acelerada del valor total quemado por tus invitados. Estos créditos pueden usarse para reclamar inmediatamente futuras recompensas de minería.", "viewRewards": "Ver Detalles de Recompensas"}, "acceleratedRelease": {"title": "Cuota de Liberación Acelerada", "currentAvailable": "Disponible Actualmente", "totalReceived": "Total Recibido", "totalUsed": "Total Usado", "usageRate": "<PERSON><PERSON>", "used": "Usado", "available": "Disponible", "howItWorks": "Cómo Funciona la Liberación Acelerada", "explanation1": "Invita a otros a participar en la minería para obtener 10% de cuota de liberación acelerada", "explanation2": "La cuota acelerada puede usarse para liberar inmediatamente recompensas de minería bloqueadas", "explanation3": "La cuota acelerada disponible se usa primero al reclamar", "explanation4": "Usar cuota acelerada no afecta la liberación normal basada en tiempo", "noQuota": "Sin Cuota de Liberación Acelerada", "inviteToEarn": "Invita amigos a participar en la minería para ganar cuota de liberación acelerada"}, "comingSoon": {"title": "Próximamente", "description": "¡Estamos trabajando duro para traerte una experiencia increíble!", "stayTuned": "Mantente atento a nuestras últimas actualizaciones", "understand": "Entendido", "contactUs": "Contáctanos"}, "referral": {"title": "Sistema de Referidos", "yourLink": "Tu <PERSON>lace de Referido", "copy": "Copiar", "copied": "Copiado", "statistics": "Estadísticas de Referidos", "totalReferees": "Total de Referidos", "totalPurchased": "Total Comprado", "averagePurchase": "Compra Promedio", "totalCommission": "Comisión Total (USDT)", "refereesList": "Lista de Referidos", "purchasedAmount": "Compró {{amount}} NFTs", "noReferees": "Aún no hay referidos", "howItWorks": "Cómo Funciona", "instruction1": "Comparte tu enlace de referido con amigos", "instruction2": "Los amigos compran NFTs a través de tu enlace", "instruction3": "Ve tus estadísticas de referido aquí", "description": "Invita a tus amigos a comprar NFTs y gana recompensas de referidos", "manageReferrals": "Gestionar Referencias", "linkReady": "Listo", "linkNotReady": "No Listo", "linkStatus": "Estado del Enlace de Referencia", "confirmReferrer": "<PERSON><PERSON><PERSON><PERSON>", "confirmReferrerDesc": "Detectamos que estás usando un enlace de referido. ¿Te gustaría usar la siguiente dirección como tu referidor?", "referrerAddress": "Dirección del Referidor", "purchaseWithoutReferrer": "Comprar sin Referidor", "confirmAndPurchase": "<PERSON><PERSON><PERSON><PERSON> <PERSON> Comprar", "myReferrer": "<PERSON>", "viewOnBscscan": "Ver en el explorador BSC"}, "common": {"loading": "Cargando...", "previous": "Anterior", "next": "Siguient<PERSON>", "retry": "Reintentar", "cancel": "<PERSON><PERSON><PERSON>", "processing": "Procesando...", "calculating": "Calculando...", "copied": "Copiado!", "active": "Activo", "usdtBalance": "Saldo USDT", "releaseProgress": "Progreso de Liberación", "days": "d", "hours": "h", "minutes": "m", "seconds": "s"}, "earnings": {"realtimeTitle": "Ganancias en Tiempo Real", "lastUpdate": "Última Actualización", "realtime": "Tiempo Real", "refresh": "Actualizar", "loading": "Cargando datos de ganancias...", "loadError": "Error al cargar datos de ganancias", "totalClaimable": "Total Reclamable", "dailyRate": "Tasa Diaria", "totalLocked": "Valor Total Bloqueado", "progress": "Progreso de Liberación", "released": "Liberado", "releaseProgress": "Progreso de Liberación", "estimatedCompletion": "Finalización Estimada", "claimMechanism1": "Reclamar Recompensas Mecanismo 1", "claimMechanism2": "Reclamar Recompensas Mecanismo 2", "infoTitle": "Información Ganancias Tiempo Real", "infoDesc": "Los datos de ganancias se actualizan cada 30 segundos. Puede reclamar recompensas liberadas en cualquier momento, calculadas con el algoritmo de liberación lineal en tiempo real."}, "accelerated": {"title": "Liberación Acelerada", "subtitle": "Usar recompensas de invitación para acelerar la liberación de minería", "loading": "Cargando datos de liberación acelerada...", "loadError": "Error al cargar", "noBalance": "No hay créditos disponibles", "noBalanceDesc": "Actualmente no tiene créditos de liberación acelerada disponibles. Invite amigos a participar en la minería para ganar 10% de recompensas de liberación acelerada.", "availableBalance": "Créditos Disponibles", "maxAccelerated": "Aceleración Máxima", "enterAmount": "Ingresar cantidad de liberación acelerada", "maxAmount": "Cantidad máxima", "preview": "Vista Previa", "releaseValue": "Valor de Liberación", "willReceive": "Recibirá", "estimatedGas": "Gas Estimado", "warning": "Aviso Importante", "warningDesc": "La liberación acelerada consumirá inmediatamente sus créditos de recompensa de invitación y liberará las recompensas de minería más temprano. Esta acción es irreversible.", "confirm": "Confirmar Liberación Acelerada", "enterValidAmount": "Por favor ingrese una cantidad de liberación válida", "insufficientBalance": "Créditos disponibles insuficientes", "exceedsMaximum": "Excede la cantidad máxima de aceleración", "processing": "Procesando liberación acelerada...", "success": "¡Liberación acelerada exitosa!", "failed": "Error en liberación acelerada"}, "errors": {"contractNotConnected": "Contrato no conectado", "insufficientBalance": "Saldo USDT insuficiente. Requerido: {{required}} USDT, Actual: {{current}} USDT", "cannotPurchase": "No se puede comprar en este momento", "presaleNotActive": "La preventa no está activa", "exceedsMaxSupply": "La compra excedería el suministro máximo"}}