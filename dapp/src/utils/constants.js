// Contract address configuration
// Note: After upgrading to the new 6-period release mechanism, MiningContract needs to be redeployed
export const CONTRACT_ADDRESSES = {
  TokenA: "0x749317fd7321f7A844e70f8096ac2124ABa7C458", // Replace with actual TokenA contract address
  TokenB: "0x433D3cadDb1A4FAbC6F6c36FfC5a67b3C91cE2f4", // Replace with actual TokenB contract address
  MinerNFT: "0x85573EA8402C9B9D1a27c99f1a660ea2055bb024", // Replace with actual MinerNFT contract address
  MiningContract: "0x7FaC44bCa82E54714C7DE8aE63964903424F6Ed1", // Replace with actual MiningContract contract address (UPGRADE REQUIRED)
  NFTPresale:
    process.env.REACT_APP_NFT_PRESALE_CONTRACT ||
    "0x0000000000000000000000000000000000000000", // NFT presale contract address
  USDT:
    process.env.REACT_APP_USDT_CONTRACT ||
    "******************************************", // Actual USDT address when contract is deployed
};

// BSC network configuration
export const BSC_NETWORK = {
  chainId: "0x38", // BSC Mainnet: 56, BSC Testnet: 97
  chainName: "Binance Smart Chain",
  nativeCurrency: {
    name: "BNB",
    symbol: "BNB",
    decimals: 18,
  },
  rpcUrls: ["https://bsc-dataseed.binance.org/"],
  blockExplorerUrls: ["https://bscscan.com/"],
};

// NOTE: Wallet configurations are now in walletConfig.js with i18n support
// Import { createWalletConfigs } from './walletConfig.js' and use createWalletConfigs(t) instead

// Contract ABI configuration
export const ERC20_ABI = [
  "function balanceOf(address owner) view returns (uint256)",
  "function approve(address spender, uint256 amount) returns (bool)",
  "function allowance(address owner, address spender) view returns (uint256)",
  "function transfer(address to, uint256 amount) returns (bool)",
  "function symbol() view returns (string)",
  "function decimals() view returns (uint8)",
];

export const MINING_CONTRACT_ABI = [
  // Mechanism 1: USDT purchase and burn functions
  "function buyAndBurnTokenAWithUSDT(uint256 usdtAmount, address inviter)",
  "function previewUSDTToTokenA(uint256 usdtAmount) view returns (uint256)",

  // Mechanism 2 functions
  "function burnTokenBWithNFT(uint256 amount, address inviter)",

  // Claim reward functions
  "function claimTokenBFromMechanism1()",
  "function claimTokenBFromMechanism2()",

  // Query functions
  "function getUserClaimableAmount1(address user) view returns (uint256)",
  "function getUserClaimableAmount2(address user) view returns (uint256)",
  "function getUserMiningRecordCount(address user) view returns (uint256, uint256)",
  "function getUserMiningRecord1Detail(address user, uint256 recordIndex) view returns (uint256, uint256, uint256, uint256, uint256, uint256, uint256, uint256, uint256, uint256, bool)",
  "function getUserMiningRecord2Detail(address user, uint256 recordIndex) view returns (uint256, uint256, uint256, uint256, uint256, uint256, uint256, uint256, uint256, bool)",
  "function getUserAcceleratedReleaseInfo1(address user) view returns (uint256[], uint256[], uint256[])",
  "function getUserAcceleratedReleaseInfo2(address user) view returns (uint256[], uint256[], uint256[])",
  "function getUserInviteInfo(address user) view returns (address, uint256, uint256, address[])",
  "function getUserInviteInfoFull(address user) view returns (address, uint256, uint256, uint256, uint256, address[])",
  "function getUserAcceleratedReleaseInfo(address user) view returns (uint256, uint256, uint256)",
  "function setInviter(address inviter)",
  "function hasInviter(address user) view returns (bool)",

  // Claim record query functions
  "function getUserClaimRecordCount(address user) view returns (uint256)",
  "function getUserClaimRecordDetail(address user, uint256 recordIndex) view returns (uint256, uint256, uint256, uint8, uint256, uint256)",
  "function getUserRecentClaimRecords(address user, uint256 limit) view returns (uint256[], uint256[], uint256[], uint8[], uint256[], uint256[])",

  // Real-time linear release query functions
  "function getUserRealtimeEarnings(address user) view returns (uint256, uint256, uint256, uint256, uint256, uint256, uint256, uint256)",
  "function getMiningRecordRealtimeStatus1(address user, uint256 recordIndex) view returns (uint256, uint256, uint256, uint256, uint256, uint256, uint256, bool)",
  "function getMiningRecordRealtimeStatus2(address user, uint256 recordIndex) view returns (uint256, uint256, uint256, uint256, uint256, uint256, uint256, bool)",
  "function getUserReleaseProgress(address user) view returns (uint256, uint256, uint256, uint256, uint256, uint256, uint256, uint256)",

  // Price and exchange query functions
  "function getTokenPrice(address token) view returns (uint256)",
  "function isTokenPriceReliable(address token) view returns (bool)",
  "function getPairInfo(address token) view returns (address, uint256, uint256, uint256)",

  // Configuration queries
  "function totalReleaseDuration() view returns (uint256)",
  "function REWARD_MULTIPLIER() view returns (uint256)",
  "function USDT() view returns (address)",
  "function PANCAKE_ROUTER() view returns (address)",
  "function MIN_LIQUIDITY() view returns (uint256)",
  "function MAX_REASONABLE_PRICE() view returns (uint256)",
  "function MIN_REASONABLE_PRICE() view returns (uint256)",
];

export const MINER_NFT_ABI = [
  "function balanceOf(address owner) view returns (uint256)",
  "function tokenOfOwnerByIndex(address owner, uint256 index) view returns (uint256)",
  "function getUserNFTs(address user) view returns (uint256[])",
  "function getReleasableAmount(uint256 tokenId) view returns (uint256)",
  "function releaseTokens(uint256 tokenId)",
  "function batchReleaseTokens()",
  "function ownerOf(uint256 tokenId) view returns (address)",
  "function getReleaseSchedule() view returns (uint256[12], uint256, uint256, uint256)",
  "function getNFTReleaseInfo(uint256 tokenId) view returns (uint256, uint256, uint256, uint256, uint256)",
  "function isMinering(uint256 tokenId) view returns (bool)",
  "function getNFTReleaseStartTime(uint256 tokenId) view returns (uint256)",
];

// NFT presale contract ABI - supports batch purchase and referral system
export const NFT_PRESALE_ABI = [
  // Presale management functions
  "function startPresale(uint256 _startTime, uint256 _endTime)",
  "function endPresale()",
  "function setPaused(bool _paused)",
  "function setTreasuryWallet(address _treasuryWallet)",

  // Purchase functions - supports batch purchase
  "function buyNFT(address referrer, uint256 quantity)",
  "function buyNFT(address referrer)", // Backward compatible single purchase

  // Fund management functions (owner only)
  "function withdrawFunds(uint256 amount)",

  // Query functions
  "function getPresaleInfo() view returns (uint256 _totalSold, uint256 _totalParticipants, uint256 _maxSupply, uint256 _nftPrice, bool _active)",
  "function getUserInfo(address user) view returns (uint256 purchased, address referrer, uint256 refereeCount)",
  "function canPurchase(uint256 quantity) view returns (bool)",
  "function getContractBalance() view returns (uint256)",
  "function getReferees(address referrer) view returns (address[])",
  "function getReferees(address referrer, uint256 start, uint256 limit) view returns (address[])",
  "function getParticipants(uint256 start, uint256 limit) view returns (address[])",
  "function getRemainingSupply() view returns (uint256)",

  // Referral system queries
  "function getReferralDetails(address referrer) view returns (address[] referees, uint256[] purchasedAmounts, uint256 totalReferees, uint256 totalPurchasedByReferees)",

  // Constant queries
  "function NFT_PRICE() view returns (uint256)",
  "function MAX_SUPPLY() view returns (uint256)",
  "function usdtToken() view returns (address)",
  "function treasuryWallet() view returns (address)",

  // Status queries
  "function presaleActive() view returns (bool)",
  "function totalSold() view returns (uint256)",
  "function totalParticipants() view returns (uint256)",
  "function userPurchased(address user) view returns (uint256)",
  "function userReferrer(address user) view returns (address)",
  "function referrerCount(address referrer) view returns (uint256)",
  "function owner() view returns (address)",
  "function paused() view returns (bool)",

  // Events
  "event PresaleStarted(uint256 startTime, uint256 endTime)",
  "event PresaleEnded(uint256 totalSold, uint256 totalParticipants)",
  "event NFTPurchased(address indexed buyer, address indexed referrer, uint256 quantity, uint256 timestamp)",
  "event ReferralRecorded(address indexed referrer, address indexed referee)",
  "event FundsWithdrawn(address indexed owner, uint256 amount)",
];

// Presale contract constants - only keep technical configuration
export const PRESALE_CONSTANTS = {
  USDT_DECIMALS: 18, // USDT on BSC uses 18 decimals
};
