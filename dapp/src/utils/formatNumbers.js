/**
 * Number formatting utility functions
 */

/**
 * Format token amount display
 * @param {string|number} value - The value
 * @param {number} decimals - Decimal places, default 4
 * @param {boolean} showZero - Whether to show zero value, default true
 * @returns {string} Formatted value
 */
export const formatTokenAmount = (value, decimals = 4, showZero = true) => {
  if (!value || value === '0' || value === 0) {
    return showZero ? '0.0000' : '--';
  }
  
  const num = parseFloat(value);
  if (isNaN(num)) {
    return showZero ? '0.0000' : '--';
  }
  
  // For very small values, use scientific notation
  if (num > 0 && num < 0.0001) {
    return num.toExponential(2);
  }
  
  // For large values, add thousand separators
  if (num >= 1000000) {
    return (num / 1000000).toFixed(2) + 'M';
  } else if (num >= 1000) {
    return (num / 1000).toFixed(2) + 'K';
  }
  
  return num.toFixed(decimals);
};

/**
 * Format USDT value display
 * @param {string|number} value - The value
 * @param {boolean} showCurrency - Whether to show currency symbol
 * @returns {string} Formatted value
 */
export const formatUSDTAmount = (value, showCurrency = true) => {
  const formatted = formatTokenAmount(value, 4);
  return showCurrency ? `${formatted} USDT` : formatted;
};

/**
 * Format percentage display
 * @param {string|number} value - The value
 * @param {number} decimals - Decimal places, default 2
 * @returns {string} Formatted percentage
 */
export const formatPercentage = (value, decimals = 2) => {
  if (!value || value === '0' || value === 0) {
    return '0.00%';
  }
  
  const num = parseFloat(value);
  if (isNaN(num)) {
    return '0.00%';
  }
  
  return `${num.toFixed(decimals)}%`;
};

/**
 * Format timestamp display
 * @param {string|number} timestamp - The timestamp
 * @param {boolean} showTime - Whether to show time, default false shows only date
 * @returns {string} Formatted time
 */
export const formatTimestamp = (timestamp, showTime = false) => {
  if (!timestamp) {
    return 'N/A';
  }
  
  // Check if timestamp is already a Date object or a millisecond timestamp
  let date;
  if (timestamp instanceof Date) {
    date = timestamp;
  } else if (typeof timestamp === 'number' || typeof timestamp === 'string') {
    const num = Number(timestamp);
    // If the number is less than 1e12, it's likely in seconds, so convert to milliseconds
    date = new Date(num < 1e12 ? num * 1000 : num);
  } else {
    return 'N/A';
  }
  
  if (showTime) {
    return date.toLocaleString('zh-CN', {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit'
    });
  }
  
  return date.toLocaleDateString('zh-CN');
};

/**
 * Calculate release progress percentage
 * @param {string|number} released - Released amount
 * @param {string|number} total - Total release amount
 * @returns {number} Progress percentage
 */
export const calculateReleaseProgress = (released, total) => {
  if (!released || !total || total === '0' || total === 0) {
    return 0;
  }
  
  const releasedNum = parseFloat(released);
  const totalNum = parseFloat(total);
  
  if (isNaN(releasedNum) || isNaN(totalNum) || totalNum === 0) {
    return 0;
  }
  
  return Math.min((releasedNum / totalNum) * 100, 100);
};