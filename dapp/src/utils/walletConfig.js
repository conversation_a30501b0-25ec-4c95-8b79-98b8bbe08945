// Wallet configuration with i18n support
export const createWalletConfigs = (t) => ({
  metamask: {
    name: 'MetaMask',
    icon: '🦊',
    description: t('wallet.wallets.metamask.description'),
    provider: () => window.ethereum,
    checkAvailable: () => {
      return window.ethereum && window.ethereum.isMetaMask && !window.ethereum.isOkxWallet && !window.ethereum.isTokenPocket;
    },
    connect: async () => {
      const provider = window.ethereum;
      if (!provider || !provider.isMetaMask) throw new Error(t('wallet.errors.pleaseInstallMetaMask'));
      return await provider.request({ method: 'eth_requestAccounts' });
    }
  },
  okx: {
    name: 'OKX Wallet',
    icon: '🟡',
    description: t('wallet.wallets.okx.description'),
    provider: () => window.okxwallet || (window.ethereum && window.ethereum.isOkxWallet ? window.ethereum : null),
    checkAvailable: () => {
      return window.okxwallet || (window.ethereum && window.ethereum.isOkxWallet);
    },
    connect: async () => {
      const provider = window.okxwallet || (window.ethereum && window.ethereum.isOkxWallet ? window.ethereum : null);
      if (!provider) throw new Error(t('wallet.errors.pleaseInstallOKX'));
      return await provider.request({ method: 'eth_requestAccounts' });
    }
  },
  tokenpocket: {
    name: 'TokenPocket',
    icon: '💼',
    description: t('wallet.wallets.tokenpocket.description'),
    provider: () => {
      if (window.tokenpocket?.ethereum) return window.tokenpocket.ethereum;
      if (window.ethereum && window.ethereum.isTokenPocket) return window.ethereum;
      return null;
    },
    checkAvailable: () => {
      return window.tokenpocket || (window.ethereum && window.ethereum.isTokenPocket);
    },
    connect: async () => {
      let provider = null;
      if (window.tokenpocket?.ethereum) {
        provider = window.tokenpocket.ethereum;
      } else if (window.ethereum && window.ethereum.isTokenPocket) {
        provider = window.ethereum;
      }
      if (!provider) throw new Error(t('wallet.errors.pleaseInstallTokenPocket'));
      return await provider.request({ method: 'eth_requestAccounts' });
    }
  },
  trust: {
    name: 'Trust Wallet',
    icon: '🛡️',
    description: t('wallet.wallets.trust.description'),
    provider: () => {
      return window.trustwallet || (window.ethereum && window.ethereum.isTrust ? window.ethereum : null);
    },
    checkAvailable: () => {
      return window.trustwallet || (window.ethereum && window.ethereum.isTrust);
    },
    connect: async () => {
      const provider = window.trustwallet || (window.ethereum && window.ethereum.isTrust ? window.ethereum : null);
      if (!provider) throw new Error(t('wallet.errors.pleaseInstallTrust'));
      return await provider.request({ method: 'eth_requestAccounts' });
    }
  }
})