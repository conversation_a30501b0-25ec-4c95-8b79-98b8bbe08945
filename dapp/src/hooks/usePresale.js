import { useState, useEffect, useCallback } from "react";
import { ethers } from "ethers";
import { useTranslation } from "react-i18next";
import { PRESALE_CONSTANTS } from "../utils/constants";

export const usePresale = (contracts, userAddress) => {
  const { t } = useTranslation();
  
  // Presale state
  const [presaleInfo, setPresaleInfo] = useState({
    totalSold: 0,
    totalParticipants: 0,
    maxSupply: 0,
    nftPrice: "0",
    active: false,
    startTime: 0,
    endTime: 0,
    progress: 0,
  });
  
  // User information
  const [userInfo, setUserInfo] = useState({
    purchased: 0,
    referrer: "******************************************",
    refereeCount: 0,
  });

  // Referral system state
  const [referralInfo, setReferralInfo] = useState({
    referees: [],
    referralDetails: null,
    loading: false,
  });
  
  // Balance information
  const [balances, setBalances] = useState({
    usdt: "0",
    usdtAllowance: "0",
  });
  
  // Transaction state
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);
  
  // Format USDT amount
  const formatUSDT = useCallback((amount) => {
    if (!amount) return "0";
    return ethers.formatUnits(amount.toString(), PRESALE_CONSTANTS.USDT_DECIMALS);
  }, []);
  
  // Parse USDT amount
  const parseUSDT = useCallback((amount) => {
    return ethers.parseUnits(amount.toString(), PRESALE_CONSTANTS.USDT_DECIMALS);
  }, []);
  
  // Load presale information
  const loadPresaleInfo = useCallback(async () => {
    if (!contracts.nftPresale) return;
    
    try {
      // Try using getPresaleInfo function, if failed then fallback to individual function calls
      let presaleData;
      
      try {
        if (typeof contracts.nftPresale.getPresaleInfo === 'function') {
          const info = await contracts.nftPresale.getPresaleInfo();
          
          // Parse according to actual contract return data structure (5 return values)
          presaleData = {
            totalSold: Number(info._totalSold || info[0]),
            totalParticipants: Number(info._totalParticipants || info[1]),
            maxSupply: Number(info._maxSupply || info[2]),
            nftPrice: formatUSDT(info._nftPrice || info[3]),
            active: info._active || info[4],
            startTime: 0, // Cannot get from contract, use default value
            endTime: 0, // Cannot get from contract, use default value
            progress: 0, // Will be calculated later
          };
          
          // Calculate progress
          if (presaleData.maxSupply > 0) {
            presaleData.progress = (presaleData.totalSold / presaleData.maxSupply) * 100;
          }
        } else {
          throw new Error("getPresaleInfo function not available");
        }
      } catch (getPresaleInfoError) {
        console.warn("getPresaleInfo failed, falling back to individual function calls:", getPresaleInfoError);
        
        // Use individual functions to get information
        const totalSold = await contracts.nftPresale.totalSold().catch(() => 0);
        const totalParticipants = await contracts.nftPresale.totalParticipants().catch(() => 0);
        const maxSupply = await contracts.nftPresale.MAX_SUPPLY().catch(() => 0);
        const nftPrice = await contracts.nftPresale.NFT_PRICE().catch(() => 0);
        const active = await contracts.nftPresale.presaleActive().catch(() => false);
        
        presaleData = {
          totalSold: Number(totalSold),
          totalParticipants: Number(totalParticipants),
          maxSupply: Number(maxSupply),
          nftPrice: formatUSDT(nftPrice),
          active: active,
          startTime: 0,
          endTime: 0,
          progress: Number(maxSupply) > 0 ? (Number(totalSold) / Number(maxSupply)) * 100 : 0,
        };
      }
      
      setPresaleInfo(presaleData);
    } catch (err) {
      console.error("Failed to load presale info:", err);
      setError(t("notifications.loadPresaleInfoFailed"));
    }
  }, [contracts.nftPresale, formatUSDT, t]);
  
  // Load user information
  const loadUserInfo = useCallback(async () => {
    if (!contracts.nftPresale || !userAddress) return;
    
    try {
      const info = await contracts.nftPresale.getUserInfo(userAddress);
      
      setUserInfo({
        purchased: Number(info.purchased),
        referrer: info.referrer,
        refereeCount: Number(info.refereeCount),
      });
    } catch (err) {
      console.error("Failed to load user info:", err);
    }
  }, [contracts.nftPresale, userAddress]);
  
  // Load balance information
  const loadBalances = useCallback(async () => {
    if (!contracts.usdt || !contracts.nftPresale || !userAddress) return;
    
    try {
      const usdtBalance = await contracts.usdt.balanceOf(userAddress);
      const allowance = await contracts.usdt.allowance(userAddress, contracts.nftPresale.target);
      
      setBalances({
        usdt: formatUSDT(usdtBalance),
        usdtAllowance: formatUSDT(allowance),
      });
    } catch (err) {
      console.error("Failed to load balances:", err);
    }
  }, [contracts.usdt, contracts.nftPresale, userAddress, formatUSDT]);
  
  // Check if can purchase
  const canPurchase = useCallback(async (quantity = 1) => {
    if (!contracts.nftPresale || !userAddress) return { can: false, message: t("errors.contractNotConnected") };
    
    try {
      // Get NFT price and calculate total cost
      const nftPrice = await contracts.nftPresale.NFT_PRICE();
      const totalCost = nftPrice * BigInt(quantity);
      
      // Check USDT balance
      const userBalance = parseUSDT(balances.usdt);
      if (userBalance < totalCost) {
        const requiredAmount = ethers.formatUnits(totalCost, PRESALE_CONSTANTS.USDT_DECIMALS);
        const userBalanceFormatted = ethers.formatUnits(userBalance, PRESALE_CONSTANTS.USDT_DECIMALS);
        return { 
          can: false, 
          message: t("errors.insufficientBalance", { 
            required: requiredAmount, 
            current: userBalanceFormatted 
          }) || `Insufficient USDT balance. Required: ${requiredAmount} USDT, Current: ${userBalanceFormatted} USDT`
        };
      }
      
      // Check if contract has canPurchase function
      if (typeof contracts.nftPresale.canPurchase === 'function') {
        const result = await contracts.nftPresale.canPurchase(quantity);
        return { can: result, message: result ? "" : t("errors.cannotPurchase") || "Cannot purchase" };
      } else {
        // If no canPurchase function, perform basic checks
        const presaleActive = await contracts.nftPresale.presaleActive();
        const totalSold = await contracts.nftPresale.totalSold();
        const maxSupply = await contracts.nftPresale.MAX_SUPPLY();
        
        if (!presaleActive) {
          return { can: false, message: t("errors.presaleNotActive") || "Presale not active" };
        }
        
        if (Number(totalSold) + quantity > Number(maxSupply)) {
          return { can: false, message: t("errors.exceedsMaxSupply") || "Exceeds maximum supply" };
        }
        
        return { can: true, message: "" };
      }
    } catch (err) {
      console.error("Failed to check purchase:", err);
      return { can: false, message: err.message };
    }
  }, [contracts.nftPresale, userAddress, balances.usdt, parseUSDT, t]);
  
  // Approve USDT - supports batch purchase
  const approveUSDT = useCallback(async (quantity = 1) => {
    if (!contracts.usdt || !contracts.nftPresale) return false;
    
    setLoading(true);
    setError(null);
    
    try {
      // Get NFT price from contract
      const contractPrice = await contracts.nftPresale.NFT_PRICE();
      const totalAmount = contractPrice * BigInt(quantity);
      const tx = await contracts.usdt.approve(contracts.nftPresale.target, totalAmount);
      await tx.wait();
      
      // Reload balances
      await loadBalances();
      return true;
    } catch (err) {
      console.error("Failed to approve USDT:", err);
      setError(t("notifications.approveFailed"));
      return false;
    } finally {
      setLoading(false);
    }
  }, [contracts.usdt, contracts.nftPresale, loadBalances, t]);
  
  // Buy NFT - supports batch purchase
  const buyNFT = useCallback(async (quantity = 1, referrer = "******************************************") => {
    if (!contracts.nftPresale) return false;
    
    setLoading(true);
    setError(null);
    
    try {
      // Get NFT price from contract
      const contractPrice = await contracts.nftPresale.NFT_PRICE();
      const totalAmount = contractPrice * BigInt(quantity);
      
      // Check if can purchase
      const { can, message } = await canPurchase(quantity);
      if (!can) {
        throw new Error(message);
      }
      
      // Check authorization
      const currentAllowance = parseUSDT(balances.usdtAllowance);
      if (currentAllowance < totalAmount) {
        // Need to authorize first
        const approved = await approveUSDT(quantity);
        if (!approved) {
          throw new Error(t("notifications.approveFailed"));
        }
      }
      
      // Execute purchase
      const tx = await contracts.nftPresale["buyNFT(address,uint256)"](referrer, quantity);
      await tx.wait();
      
      // Reload data
      await Promise.all([
        loadPresaleInfo(),
        loadUserInfo(),
        loadBalances(),
      ]);
      
      return true;
    } catch (err) {
      console.error("Failed to buy NFT:", err);
      setError(err.message || t("notifications.purchaseFailed"));
      return false;
    } finally {
      setLoading(false);
    }
  }, [
    contracts.nftPresale,
    parseUSDT,
    canPurchase,
    balances.usdtAllowance,
    approveUSDT,
    loadPresaleInfo,
    loadUserInfo,
    loadBalances,
    t,
  ]);
  
  // Get referrer address from URL
  const getReferrerFromURL = useCallback(() => {
    const urlParams = new URLSearchParams(window.location.search);
    const ref = urlParams.get('ref');
    if (ref && ethers.isAddress(ref)) {
      return ref;
    }
    return "******************************************";
  }, []);

  // Load referral information
  const loadReferralInfo = useCallback(async () => {
    if (!contracts.nftPresale || !userAddress) return;
    
    setReferralInfo(prev => ({ ...prev, loading: true }));
    
    try {
      // Check if contract has getReferralDetails function
      if (typeof contracts.nftPresale.getReferralDetails === 'function') {
        const details = await contracts.nftPresale.getReferralDetails(userAddress);
        
        setReferralInfo({
          referees: details.referees || [],
          referralDetails: {
            referees: details.referees || [],
            purchasedAmounts: details.purchasedAmounts?.map(amount => amount.toString()) || [],
            totalReferees: details.totalReferees?.toString() || "0",
            totalPurchasedByReferees: details.totalPurchasedByReferees?.toString() || "0",
          },
          loading: false,
        });
      } else {
        // If contract doesn't have getReferralDetails function, try using individual functions
        const referees = await contracts.nftPresale.getReferees(userAddress).catch(() => []);
        const refereeCount = await contracts.nftPresale.referrerCount(userAddress).catch(() => 0);
        
        setReferralInfo({
          referees: referees || [],
          referralDetails: {
            referees: referees || [],
            purchasedAmounts: [],
            totalReferees: refereeCount.toString(),
            totalPurchasedByReferees: "0",
          },
          loading: false,
        });
      }
    } catch (err) {
      console.error("Failed to load referral info:", err);
      setReferralInfo({
        referees: [],
        referralDetails: null,
        loading: false,
      });
    }
  }, [contracts.nftPresale, userAddress]);

  // Get referral list with pagination
  const getReferees = useCallback(async (start = 0, limit = 10) => {
    if (!contracts.nftPresale || !userAddress) return [];
    
    try {
      // Try calling paginated version of getReferees
      try {
        const referees = await contracts.nftPresale["getReferees(address,uint256,uint256)"](
          userAddress,
          start,
          limit
        );
        return referees;
      } catch (paginationError) {
        // If paginated version doesn't exist, use basic version
        const allReferees = await contracts.nftPresale["getReferees(address)"](userAddress);
        return allReferees.slice(start, start + limit);
      }
    } catch (err) {
      console.error("Failed to get referees:", err);
      return [];
    }
  }, [contracts.nftPresale, userAddress]);

  // Get referral statistics
  const getReferralStats = useCallback(async () => {
    if (!contracts.nftPresale || !userAddress) return null;
    
    try {
      // Check if contract has getReferralDetails function
      if (typeof contracts.nftPresale.getReferralDetails === 'function') {
        const details = await contracts.nftPresale.getReferralDetails(userAddress);
        const totalReferees = Number(details.totalReferees);
        const totalPurchased = Number(details.totalPurchasedByReferees);
        
        // Get NFT price from contract
        const contractPrice = await contracts.nftPresale.NFT_PRICE();
        const nftPrice = parseFloat(formatUSDT(contractPrice));
        
        return {
          totalReferees,
          totalPurchasedByReferees: totalPurchased,
          averagePurchasePerReferee: totalReferees > 0 ? (totalPurchased / totalReferees).toFixed(2) : "0",
          totalCommission: (totalPurchased * nftPrice).toFixed(2),
        };
      } else {
        // If contract doesn't have getReferralDetails function, use basic statistics
        const referees = await contracts.nftPresale.getReferees(userAddress).catch(() => []);
        const refereeCount = await contracts.nftPresale.referrerCount(userAddress).catch(() => 0);
        
        return {
          totalReferees: Number(refereeCount),
          totalPurchasedByReferees: 0,
          averagePurchasePerReferee: "0",
          totalCommission: "0",
        };
      }
    } catch (err) {
      console.error("Failed to get referral stats:", err);
      return null;
    }
  }, [contracts.nftPresale, userAddress, formatUSDT]);

  // Generate referral link
  const generateReferralLink = useCallback(() => {
    if (!userAddress) return "";
    const baseUrl = window.location.origin + window.location.pathname;
    return `${baseUrl}?ref=${userAddress}`;
  }, [userAddress]);
  
  // Initial data loading
  useEffect(() => {
    if (contracts.nftPresale) {
      loadPresaleInfo();
    }
  }, [contracts.nftPresale, loadPresaleInfo]);
  
  useEffect(() => {
    if (contracts.nftPresale && userAddress) {
      loadUserInfo();
      loadReferralInfo();
    }
  }, [contracts.nftPresale, userAddress, loadUserInfo, loadReferralInfo]);
  
  useEffect(() => {
    if (contracts.usdt && userAddress) {
      loadBalances();
    }
  }, [contracts.usdt, userAddress, loadBalances]);
  
  return {
    // State
    presaleInfo,
    userInfo,
    balances,
    loading,
    error,
    referralInfo,
    
    // Methods
    loadPresaleInfo,
    loadUserInfo,
    loadBalances,
    loadReferralInfo,
    canPurchase,
    approveUSDT,
    buyNFT,
    getReferrerFromURL,
    getReferees,
    getReferralStats,
    generateReferralLink,
    
    // Utility functions
    formatUSDT,
    parseUSDT,
    
    // Clear error
    clearError: () => setError(null),
  };
};