import { useState, useEffect, useCallback } from "react";
import { ethers } from "ethers";
import { useTranslation } from "react-i18next";
import {
  BSC_NETWORK,
  CONTRACT_ADDRESSES,
  ERC20_ABI,
  MINING_CONTRACT_ABI,
  MINER_NFT_ABI,
  NFT_PRESALE_ABI,
} from "../utils/constants";
import { createWalletConfigs } from "../utils/walletConfig";

export const useWallet = () => {
  const { t } = useTranslation();
  const [userAddress, setUserAddress] = useState(null);
  const [provider, setProvider] = useState(null);
  const [signer, setSigner] = useState(null);
  const [contracts, setContracts] = useState({});
  const [selectedWallet, setSelectedWallet] = useState(null);
  const [isConnecting, setIsConnecting] = useState(false);
  const [isConnected, setIsConnected] = useState(false);
  const [manuallyDisconnected, setManuallyDisconnected] = useState(false);

  // Get wallet configs with i18n support
  const WALLET_CONFIGS = createWalletConfigs(t);

  // Connect specific type of wallet
  const connectWalletWithType = useCallback(
    async (walletType) => {
      if (isConnecting) return false;
      setIsConnecting(true);

      const config = WALLET_CONFIGS[walletType];
      try {
        // Execute wallet connection
        const accounts = await config.connect();
        if (accounts.length === 0) {
          throw new Error(t("wallet.errors.noAccountSelected"));
        }

        // Get provider
        const walletProvider = config.provider();
        if (!walletProvider) {
          throw new Error(t("wallet.errors.cannotGetWalletProvider"));
        }

        // Check network
        // const chainId = await walletProvider.request({ method: "eth_chainId" });
        // if (chainId !== BSC_NETWORK.chainId) {
        //   await switchToBSC(walletProvider);
        // }

        // Initialize provider and signer
        const ethProvider = new ethers.BrowserProvider(walletProvider);
        const ethSigner = await ethProvider.getSigner();

        setProvider(ethProvider);
        setSigner(ethSigner);
        setUserAddress(accounts[0]);
        setSelectedWallet(walletType);
        setIsConnected(true);
        setManuallyDisconnected(false);

        // Initialize contract instances
        const contractInstances = {
          tokenA: new ethers.Contract(
            CONTRACT_ADDRESSES.TokenA,
            ERC20_ABI,
            ethSigner
          ),
          tokenB: new ethers.Contract(
            CONTRACT_ADDRESSES.TokenB,
            ERC20_ABI,
            ethSigner
          ),
          minerNFT: new ethers.Contract(
            CONTRACT_ADDRESSES.MinerNFT,
            MINER_NFT_ABI,
            ethSigner
          ),
          miningContract: new ethers.Contract(
            CONTRACT_ADDRESSES.MiningContract,
            MINING_CONTRACT_ABI,
            ethSigner
          ),
          usdt: new ethers.Contract(
            CONTRACT_ADDRESSES.USDT,
            ERC20_ABI,
            ethSigner
          ),
          nftPresale: new ethers.Contract(
            CONTRACT_ADDRESSES.NFTPresale,
            NFT_PRESALE_ABI,
            ethSigner
          ),
        };
        setContracts(contractInstances);

        console.log("contractInstances:", contractInstances);
        // Setup wallet event listeners
        setupWalletEventListeners(walletProvider);

        setIsConnecting(false);
        return true;
      } catch (error) {
        setIsConnecting(false);
        console.error(`Failed to connect ${config.name}:`, error);
        throw error;
      }
    },
    [isConnecting, t, WALLET_CONFIGS]
  );

  // Switch to BSC network
  const switchToBSC = useCallback(async (walletProvider = null) => {
    const provider = walletProvider || window.ethereum;
    if (!provider) {
      throw new Error(t("wallet.errors.cannotGetProvider"));
    }

    try {
      await provider.request({
        method: "wallet_switchEthereumChain",
        params: [{ chainId: BSC_NETWORK.chainId }],
      });
    } catch (switchError) {
      if (switchError.code === 4902) {
        // Network doesn't exist, try to add it
        await provider.request({
          method: "wallet_addEthereumChain",
          params: [BSC_NETWORK],
        });
      } else {
        throw switchError;
      }
    }
  }, []);

  // Setup wallet event listeners
  const setupWalletEventListeners = useCallback((walletProvider) => {
    if (walletProvider && walletProvider.on) {
      walletProvider.on("accountsChanged", handleAccountsChanged);
      walletProvider.on("chainChanged", handleChainChanged);
      walletProvider.on("disconnect", handleDisconnect);
    }
  }, []);

  // Handle wallet disconnect
  const handleDisconnect = useCallback(() => {
    setUserAddress(null);
    setProvider(null);
    setSigner(null);
    setContracts({});
    setSelectedWallet(null);
    setIsConnected(false);
    setManuallyDisconnected(true);
  }, []);

  // Handle account changes
  const handleAccountsChanged = useCallback(
    async (accounts) => {
      if (accounts.length === 0) {
        handleDisconnect();
      } else if (accounts[0] !== userAddress) {
        // Account switched, just update the address without full reconnection
        setUserAddress(accounts[0]);
      }
    },
    [userAddress, handleDisconnect]
  );

  // Handle network changes
  const handleChainChanged = useCallback((chainId) => {
    if (chainId !== BSC_NETWORK.chainId) {
      // Network is incorrect, can choose to auto-switch or prompt user
      console.warn(t("wallet.switchToBSC"));
    }
  }, []);

  // Format address display
  const formatAddress = useCallback((address) => {
    if (!address) return "";
    return `${address.slice(0, 6)}...${address.slice(-4)}`;
  }, []);

  // Check wallet availability
  const getWalletAvailability = useCallback(() => {
    const availability = {};
    for (const [walletType, config] of Object.entries(WALLET_CONFIGS)) {
      availability[walletType] = config.checkAvailable();
    }
    return availability;
  }, []);

  // Check existing connections on initialization
  useEffect(() => {
    let mounted = true;

    const initConnection = async () => {
      // Only run if component is still mounted and not already connected and not manually disconnected
      if (!mounted || isConnected || manuallyDisconnected) return;

      for (const [walletType, config] of Object.entries(WALLET_CONFIGS)) {
        if (!mounted) return; // Check if component is still mounted

        if (config.checkAvailable()) {
          try {
            const provider = config.provider();
            if (provider) {
              const accounts = await provider.request({
                method: "eth_accounts",
              });
              if (accounts.length > 0 && mounted) {
                // Call connectWalletWithType directly without depending on it
                setIsConnecting(true);
                try {
                  const ethProvider = new ethers.BrowserProvider(provider);
                  const ethSigner = await ethProvider.getSigner();

                  setProvider(ethProvider);
                  setSigner(ethSigner);
                  setUserAddress(accounts[0]);
                  setSelectedWallet(walletType);
                  setIsConnected(true);
                  setManuallyDisconnected(false);

                  // Initialize contracts
                  const contractInstances = {
                    tokenA: new ethers.Contract(
                      CONTRACT_ADDRESSES.TokenA,
                      ERC20_ABI,
                      ethSigner
                    ),
                    tokenB: new ethers.Contract(
                      CONTRACT_ADDRESSES.TokenB,
                      ERC20_ABI,
                      ethSigner
                    ),
                    minerNFT: new ethers.Contract(
                      CONTRACT_ADDRESSES.MinerNFT,
                      MINER_NFT_ABI,
                      ethSigner
                    ),
                    miningContract: new ethers.Contract(
                      CONTRACT_ADDRESSES.MiningContract,
                      MINING_CONTRACT_ABI,
                      ethSigner
                    ),
                    usdt: new ethers.Contract(
                      CONTRACT_ADDRESSES.USDT,
                      ERC20_ABI,
                      ethSigner
                    ),
                    nftPresale: new ethers.Contract(
                      CONTRACT_ADDRESSES.NFTPresale,
                      NFT_PRESALE_ABI,
                      ethSigner
                    ),
                  };
                  setContracts(contractInstances);

                  // Setup event listeners
                  if (provider && provider.on) {
                    provider.on("accountsChanged", handleAccountsChanged);
                    provider.on("chainChanged", handleChainChanged);
                    provider.on("disconnect", handleDisconnect);
                  }
                } catch (error) {
                  console.error(`Failed to connect ${config.name}:`, error);
                }
                setIsConnecting(false);
                return;
              }
            }
          } catch (error) {
            console.log(`Failed to check ${config.name} connection:`, error);
          }
        }
      }
    };

    initConnection();

    return () => {
      mounted = false;
    };
  }, []); // Remove all dependencies to prevent infinite loops

  return {
    // State
    userAddress,
    provider,
    signer,
    contracts,
    selectedWallet,
    isConnecting,
    isConnected,

    // Methods
    connectWalletWithType,
    handleDisconnect,
    switchToBSC,
    formatAddress,
    getWalletAvailability,

    // Configuration
    walletConfigs: WALLET_CONFIGS,
    bscNetwork: BSC_NETWORK,
  };
};
