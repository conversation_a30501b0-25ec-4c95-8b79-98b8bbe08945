import { useState, useEffect } from "react";
import { ethers } from "ethers";

export const useContractConstants = (miningContract) => {
  const [constants, setConstants] = useState({
    totalReleasePeriods: 6,
    rewardMultiplier: 2,
    releaseInterval: 30 * 24 * 60 * 60, // 30 days in seconds
    usdtAddress: "******************************************",
    routerAddress: "******************************************",
    minLiquidity: "1000",
    maxPrice: "1000000",
    minPrice: "0.000001",
    isLoading: true,
    error: null,
  });

  useEffect(() => {
    const loadConstants = async () => {
      if (!miningContract) {
        setConstants(prev => ({ ...prev, isLoading: false }));
        return;
      }

      try {
        setConstants(prev => ({ ...prev, isLoading: true, error: null }));

        const [
          totalReleasePeriods,
          rewardMultiplier,
          releaseInterval,
          usdtAddress,
          routerAddress,
          minLiquidity,
          maxPrice,
          minPrice,
        ] = await Promise.all([
          miningContract.TOTAL_RELEASE_PERIODS().catch(() => 6),
          miningContract.REWARD_MULTIPLIER().catch(() => 2),
          miningContract.releaseInterval().catch(() => 30 * 24 * 60 * 60),
          miningContract.USDT().catch(() => "******************************************"),
          miningContract.PANCAKE_ROUTER().catch(() => "******************************************"),
          miningContract.MIN_LIQUIDITY().catch(() => ethers.parseEther("1000")),
          miningContract.MAX_REASONABLE_PRICE().catch(() => ethers.parseEther("1000000")),
          miningContract.MIN_REASONABLE_PRICE().catch(() => ethers.parseUnits("0.000001", 18)),
        ]);

        setConstants({
          totalReleasePeriods: Number(totalReleasePeriods),
          rewardMultiplier: Number(rewardMultiplier),
          releaseInterval: Number(releaseInterval),
          usdtAddress: usdtAddress.toString(),
          routerAddress: routerAddress.toString(),
          minLiquidity: ethers.formatEther(minLiquidity),
          maxPrice: ethers.formatEther(maxPrice),
          minPrice: ethers.formatUnits(minPrice, 18),
          isLoading: false,
          error: null,
        });
      } catch (error) {
        console.error("Failed to load contract constants:", error);
        setConstants(prev => ({
          ...prev,
          isLoading: false,
          error: error.message,
        }));
      }
    };

    loadConstants();
  }, [miningContract]);

  return constants;
};