# NFT预售页面配置指南

## 概述

本项目已集成了一个完整的NFT预售页面，支持响应式设计和多语言（中文/英文/德语等）。预售页面可以通过环境变量进行开关控制，所有关键参数都支持环境变量配置。

## 环境变量控制

### 启用/禁用预售页面

在 `.env` 文件中设置：

```bash
# 启用NFT预售页面
REACT_APP_ENABLE_NFT_PRESALE=true

# 禁用NFT预售页面
REACT_APP_ENABLE_NFT_PRESALE=false
```

当启用时，预售入口会出现在：
- 桌面端导航栏
- 移动端导航菜单
- 主页英雄区域的横幅

## 配置预售参数

所有预售参数都通过环境变量配置，在 `.env` 文件中设置：

### 1. 收款地址
```bash
REACT_APP_PRESALE_ADDRESS=0x1234567890123456789012345678901234567890
```

### 2. 预售价格
```bash
REACT_APP_PRESALE_PRICE=1.5 WUSDT
```

### 3. 支付网络
```bash
REACT_APP_PRESALE_NETWORK=BSC (Binance Smart Chain)
```

### 4. 预售结束时间
```bash
REACT_APP_PRESALE_END_TIME=2024-12-31T23:59:59
```

### 5. 预售最大数量
```bash
REACT_APP_PRESALE_MAX_SUPPLY=10000
```

### 6. 联系方式
```bash
REACT_APP_PRESALE_TELEGRAM_URL=https://t.me/gratprotocol
REACT_APP_PRESALE_SUPPORT_EMAIL=<EMAIL>
```

完整的环境变量配置请参考 `.env.example` 文件。

## 页面功能特性

### 1. 倒计时功能
- 自动计算距离预售结束的时间
- 实时显示天、时、分、秒
- 倒计时结束后显示0

### 2. 一键复制地址
- 点击复制按钮一键复制收款地址
- 复制成功后显示确认提示

### 3. 重要安全提示
- ⚠️ 不要使用交易所地址转账的警告
- 📦 空投说明和流程介绍
- ✅ 使用个人钱包的安全提示

### 4. NFT功能介绍
- 挖矿奖励说明
- 分红收益介绍
- 治理权重说明
- 代币释放机制

### 5. 客服支持
- 邮件联系方式
- 电报群链接
- 详细的购买步骤说明

### 6. 退款政策
- 预售售罄后超出部分自动退回
- 预售结束后转账全额退回
- 24小时内处理退款
- 原路退回到转账地址

## 响应式设计

页面完全支持响应式设计：
- **桌面端**: 完整的卡片布局和多列显示
- **移动端**: 优化的单列布局和触摸友好的按钮
- **平板端**: 自适应的中等屏幕布局

## 多语言支持

### 支持语言
- 中文 (zh)
- 英文 (en)
- 德语 (de)
- 更多语言可根据需要添加

### 添加新语言
1. 在 `src/i18n/locales/` 目录下创建新的语言文件
2. 复制 `en.json` 的结构
3. 翻译 `nftPresale` 部分的所有字段
4. 在 `src/i18n/index.js` 中导入新语言

### 翻译内容包括
- 页面标题和描述
- 倒计时标签
- 价格和网络信息
- 购买步骤说明
- 安全警告提示
- NFT功能介绍
- 客服联系信息

## 样式自定义

### 主色调
预售页面使用了渐变色设计：
- 主色调：紫色渐变 (`from-violet-600 to-purple-600`)
- 辅助色：琥珀色、绿色、蓝色
- 背景：浅色渐变 (`from-violet-50 via-white to-amber-50`)

### 自定义样式
可以在以下位置修改样式：
- `className` 属性中的 Tailwind CSS 类
- 主题色彩配置
- 动画效果和过渡

## 安全注意事项

### 重要配置
1. **收款地址**: 必须使用正确的BSC网络地址
2. **时间设置**: 确保预售结束时间准确
3. **价格显示**: 确保价格信息与实际一致
4. **网络信息**: 明确标注BSC网络要求

### 用户安全提示
页面已包含所有必要的安全警告：
- 禁止使用交易所地址
- 要求使用个人钱包
- 明确空投流程
- 提供客服联系方式

## 部署步骤

1. **配置环境变量**
   ```bash
   REACT_APP_ENABLE_NFT_PRESALE=true
   ```

2. **更新预售参数**
   - 修改收款地址
   - 设置预售结束时间
   - 确认价格信息

3. **测试功能**
   - 验证倒计时正常
   - 测试地址复制功能
   - 检查响应式布局
   - 验证多语言切换

4. **部署到生产环境**
   ```bash
   npm run build
   npm run deploy
   ```

## 页面路由

- 预售页面URL: `/presale`
- 从主页访问: 点击导航栏的"NFT预售"按钮
- 移动端访问: 打开移动菜单后点击"NFT预售"

## 监控和分析

建议添加以下监控：
- 页面访问量统计
- 地址复制次数追踪
- 转化率分析
- 错误日志监控

## 常见问题

### Q: 如何临时关闭预售页面？
A: 在 `.env` 文件中设置 `REACT_APP_ENABLE_NFT_PRESALE=false`

### Q: 如何修改预售价格？
A: 在 `NFTPresale.jsx` 文件中修改 `presalePrice` 常量

### Q: 如何添加新的支付网络？
A: 修改 `presaleNetwork` 常量和相关的说明文字

### Q: 如何自定义倒计时结束时间？
A: 修改 `presaleEndTime` 常量为目标日期时间

---

## 技术支持

如有技术问题，请联系：
- 邮箱: <EMAIL>
- 项目仓库: GitHub Issues

预售页面已经过全面测试，可以直接用于生产环境。