# AI Tools Setup Guide

This document explains how to configure and use the AI-powered tools in the $GRAT DApp.

## Features

The DApp includes two AI-powered tools:

1. **AI Thank-You Note Generator** - Generates personalized thank you notes to send with $TIPS
2. **AI Meme Workshop** - Creates viral-worthy meme concepts based on topics and styles

## Architecture

The AI tools use a **Cloudflare Workers proxy** to securely handle API requests:

- **Frontend** → **Cloudflare Worker** → **Google Gemini API**
- API keys are stored securely on Cloudflare, never exposed to clients
- CORS and security are handled at the edge
- Global performance with low latency

## Quick Setup

### Option 1: Use Cloudflare Workers (Recommended)

1. **Deploy the Worker**
   - See `CLOUDFLARE_WORKERS_SETUP.md` for detailed instructions
   - Deploy `cloudflare-worker.js` to Cloudflare Workers
   - Set your Gemini API key as an environment variable

2. **Configure the DApp**
   ```bash
   cp .env.example .env
   # Edit .env and set:
   REACT_APP_AI_WORKER_URL=https://your-worker.workers.dev
   ```

3. **Restart the development server**
   ```bash
   npm run dev
   ```

### Option 2: Direct API (Development Only)

⚠️ **Not recommended for production** - exposes API keys to clients

1. Get a Google Gemini API key from [Google AI Studio](https://makersuite.google.com/app/apikey)
2. Modify `src/services/aiService.js` to use direct API calls
3. Set `REACT_APP_GEMINI_API_KEY` in your `.env` file

## Usage

### AI Thank-You Note Generator

1. Enter the recipient type (e.g., "artist", "developer", "content creator")
2. Enter the reason for thanks (e.g., "for creating that amazing artwork")
3. Click "Generate Note"
4. Copy the generated thank you note to use with your $TIPS transaction

### AI Meme Workshop

1. Enter a topic for your meme (e.g., "cryptocurrency", "cats", "coffee")
2. Select a style (Funny, Wholesome, Sarcastic, or Inspirational)
3. Click "Generate Meme Idea"
4. Use the generated concept to create your meme content

## Multi-language Support

The AI tools automatically generate content in the user's selected language:
- English
- Chinese (Simplified)
- Japanese
- French
- German
- Spanish
- Korean

## Error Handling

- **API Key Not Configured**: Tools will show a warning message and be disabled
- **Network Errors**: Appropriate error messages will be displayed
- **Invalid Input**: Validation messages guide users to provide required information

## Technical Details

### Files Structure

- `src/services/aiService.js` - Core AI service using Google Gemini API
- `src/components/AITools.jsx` - React component for the AI tools interface
- `src/i18n/locales/*.json` - Translation files for all supported languages

### API Limitations

- Rate limits apply based on your Google AI Studio plan
- Content generation may take 2-10 seconds depending on complexity
- Generated content follows Google's AI safety guidelines

### Security

- API keys are only used client-side for demonstration purposes
- For production use, consider implementing a backend proxy to secure API keys
- Never commit API keys to version control

## Troubleshooting

### Common Issues

1. **"AI features require API configuration"**
   - Ensure your `.env` file contains a valid `REACT_APP_GEMINI_API_KEY`
   - Restart the development server after adding the key

2. **"Failed to generate content"**
   - Check your internet connection
   - Verify your API key is valid and has quota remaining
   - Try with simpler input text

3. **Generated content is in wrong language**
   - The language follows the user's UI language selection
   - Change the language using the language switcher in the top navigation

### Support

For additional help:
- Check the browser console for detailed error messages
- Verify API key permissions in Google AI Studio
- Ensure you're using a supported browser with JavaScript enabled

## Development

To extend the AI functionality:

1. **Add new AI tools**: Create new methods in `aiService.js`
2. **Modify prompts**: Edit the prompt templates in the service methods
3. **Add translations**: Update all language files in `src/i18n/locales/`
4. **Customize UI**: Modify `AITools.jsx` component

### Example Custom AI Tool

```javascript
// In aiService.js
async generateCustomContent(input, language = 'en') {
  const prompt = `Create custom content for: ${input} in ${language}`
  // ... implementation
}

// In AITools.jsx
const handleCustomGeneration = async () => {
  const result = await aiService.generateCustomContent(userInput, i18n.language)
  // ... handle result
}
```

## License

This AI integration is part of the $GRAT Protocol project and follows the same licensing terms.