const { ethers, network } = require("hardhat");
const fs = require("fs");
const path = require("path");

// 部署配置
const DEPLOYMENT_CONFIG = {
  // BSC主网配置
  bsc_mainnet: {
    usdtAddress: "******************************************", // BSC主网USDT地址
    treasuryWallet: process.env.TREASURY_WALLET || "******************************************",
  },
  // BSC测试网配置
  bsc_testnet: {
    usdtAddress: "******************************************", // BSC测试网USDT地址
    treasuryWallet: process.env.TREASURY_WALLET || "******************************************",
  },
  // 本地测试配置
  hardhat: {
    usdtAddress: "", // 将在部署时部署MockUSDT
    treasuryWallet: process.env.TREASURY_WALLET || "******************************************",
  },
};

// 验证配置
function validateConfig(config) {
  if (
    !config.treasuryWallet ||
    config.treasuryWallet === "******************************************"
  ) {
    throw new Error("请在.env文件中设置有效的TREASURY_WALLET地址");
  }
}

// 等待交易确认
async function waitForConfirmation(tx, confirmations = 1) {
  console.log(`等待交易确认: ${tx.hash}`);
  const receipt = await tx.wait(confirmations);
  console.log(`交易已确认, Gas used: ${receipt.gasUsed.toString()}`);
  return receipt;
}

// 保存部署信息
function saveDeploymentInfo(deploymentInfo) {
  const deploymentDir = path.join(__dirname, "../deployments");
  if (!fs.existsSync(deploymentDir)) {
    fs.mkdirSync(deploymentDir, { recursive: true });
  }

  const fileName = `${network.name}-nft-presale-deployment.json`;
  const filePath = path.join(deploymentDir, fileName);

  fs.writeFileSync(filePath, JSON.stringify(deploymentInfo, null, 2));
  console.log(`\n部署信息已保存到: ${filePath}`);
}

async function main() {
  console.log("=".repeat(60));
  console.log("开始部署NFTPresale预售合约");
  console.log("=".repeat(60));

  // 获取部署者账户
  const [deployer] = await ethers.getSigners();
  console.log(`\n部署账户: ${deployer.address}`);
  console.log(
    `账户余额: ${ethers.formatEther(
      await deployer.provider.getBalance(deployer.address)
    )} BNB`
  );

  // 获取网络配置
  const config = DEPLOYMENT_CONFIG[network.name];
  if (!config) {
    throw new Error(`不支持的网络: ${network.name}`);
  }

  validateConfig(config);
  console.log(`\n部署网络: ${network.name}`);
  console.log(`收款钱包: ${config.treasuryWallet}`);

  const deploymentInfo = {
    network: network.name,
    deployer: deployer.address,
    timestamp: new Date().toISOString(),
    contracts: {},
  };

  try {
    let usdtAddress = config.usdtAddress;

    // 如果是本地测试网，需要先部署MockUSDT
    if (network.name === "hardhat" && !usdtAddress) {
      console.log("\n1. 部署MockUSDT合约（用于测试）...");
      const MockUSDT = await ethers.getContractFactory("MockUSDT");
      const mockUSDT = await MockUSDT.deploy();
      await waitForConfirmation(mockUSDT.deploymentTransaction());
      usdtAddress = await mockUSDT.getAddress();
      console.log(`✅ MockUSDT部署成功: ${usdtAddress}`);

      deploymentInfo.contracts.MockUSDT = {
        address: usdtAddress,
        constructorArgs: [],
      };

      // 为部署者铸造一些测试USDT
      console.log("\n1.1 铸造测试USDT...");
      const mintTx = await mockUSDT.mint(deployer.address, ethers.parseUnits("1000000", 6));
      await waitForConfirmation(mintTx);
      console.log("✅ 已为部署者铸造1,000,000 USDT");
    }

    // 2. 部署NFTPresale合约
    console.log("\n2. 部署NFTPresale合约...");
    const NFTPresale = await ethers.getContractFactory("NFTPresale");
    const nftPresale = await NFTPresale.deploy(usdtAddress, config.treasuryWallet);
    await waitForConfirmation(nftPresale.deploymentTransaction());
    const contractAddress = await nftPresale.getAddress();
    console.log(`✅ NFTPresale部署成功: ${contractAddress}`);

    deploymentInfo.contracts.NFTPresale = {
      address: contractAddress,
      constructorArgs: [usdtAddress, config.treasuryWallet],
    };

    // 3. 验证合约部署
    console.log("\n3. 验证合约部署...");
    const presaleInfo = await nftPresale.getPresaleInfo();
    console.log(`最大供应量: ${presaleInfo._maxSupply.toString()}`);
    console.log(`NFT价格: ${ethers.formatUnits(presaleInfo._nftPrice, 6)} USDT`);
    console.log(`当前状态: ${presaleInfo._active ? "活跃" : "未激活"}`);
    console.log(`收款钱包: ${await nftPresale.getTreasuryWallet()}`);

    // 保存部署信息
    saveDeploymentInfo(deploymentInfo);

    console.log("\n" + "=".repeat(60));
    console.log("部署完成总结");
    console.log("=".repeat(60));
    console.log(`USDT地址:        ${usdtAddress}`);
    console.log(`NFTPresale地址:  ${contractAddress}`);
    console.log(`收款钱包:        ${config.treasuryWallet}`);
    console.log(`预售价格:        1.5 USDT`);
    console.log(`最大供应量:      10,000 NFT`);
    console.log(`预售状态:        默认激活（无时间限制）`);

    console.log("\n🎉 NFTPresale合约部署和配置完成!");
    console.log("\n📋 使用说明:");
    console.log("1. 用户需要先approval USDT给NFTPresale合约");
    console.log("2. 调用buyNFT(referrer, quantity)购买NFT");
    console.log("3. USDT会自动转账到指定的收款钱包");
    console.log("4. 每个地址可以购买多个NFT，无数量限制");
    console.log("5. 无时间限制，可通过togglePresale()控制开关");
    console.log("\n请将合约地址添加到前端环境变量 REACT_APP_NFT_PRESALE_CONTRACT");
    
    return {
      contractAddress: contractAddress,
      usdtAddress: usdtAddress,
      treasuryWallet: config.treasuryWallet,
      network: network.name
    };
    
  } catch (error) {
    console.error("\n❌ 部署过程中发生错误:");
    console.error(error);

    // 保存错误信息
    deploymentInfo.error = {
      message: error.message,
      stack: error.stack,
    };
    saveDeploymentInfo(deploymentInfo);

    throw error;
  }
}

if (require.main === module) {
  main()
    .then(() => process.exit(0))
    .catch((error) => {
      console.error(error);
      process.exit(1);
    });
}

module.exports = { main };