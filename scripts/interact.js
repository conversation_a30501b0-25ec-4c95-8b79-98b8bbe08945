const { ethers, network } = require("hardhat");
const fs = require("fs");
const path = require("path");

// 加载部署信息
function loadDeploymentInfo() {
  const deploymentDir = path.join(__dirname, "../deployments");
  const fileName = `${network.name}-deployment.json`;
  const filePath = path.join(deploymentDir, fileName);
  
  if (!fs.existsSync(filePath)) {
    throw new Error(`部署文件不存在: ${filePath}`);
  }
  
  return JSON.parse(fs.readFileSync(filePath, "utf8"));
}

// 获取合约实例
async function getContracts() {
  const deploymentInfo = loadDeploymentInfo();
  const contracts = {};
  
  // TokenA
  const TokenA = await ethers.getContractFactory("TokenA");
  contracts.tokenA = TokenA.attach(deploymentInfo.contracts.TokenA.address);
  
  // TokenB
  const TokenB = await ethers.getContractFactory("TokenB");
  contracts.tokenB = TokenB.attach(deploymentInfo.contracts.TokenB.address);
  
  // MinerNFT
  const MinerNFT = await ethers.getContractFactory("MinerNFT");
  contracts.minerNFT = MinerNFT.attach(deploymentInfo.contracts.MinerNFT.address);
  
  // MiningContract
  const MiningContract = await ethers.getContractFactory("MiningContract");
  contracts.miningContract = MiningContract.attach(deploymentInfo.contracts.MiningContract.address);
  
  // PriceOracle
  const PriceOracle = await ethers.getContractFactory("PriceOracle");
  contracts.priceOracle = PriceOracle.attach(deploymentInfo.contracts.PriceOracle.address);
  
  return contracts;
}

// 显示合约状态
async function showContractStatus() {
  console.log("=".repeat(60));
  console.log("合约状态查询");
  console.log("=".repeat(60));
  
  const contracts = await getContracts();
  const [deployer] = await ethers.getSigners();
  
  try {
    // TokenA状态
    console.log("\n【TokenA状态】");
    const tokenABalance = await contracts.tokenA.balanceOf(deployer.address);
    const tokenATotalSupply = await contracts.tokenA.totalSupply();
    const tokenADecimals = await contracts.tokenA.decimals();
    
    console.log(`总供应量: ${ethers.utils.formatUnits(tokenATotalSupply, tokenADecimals)} TKA`);
    console.log(`部署者余额: ${ethers.utils.formatUnits(tokenABalance, tokenADecimals)} TKA`);
    console.log(`合约余额: ${ethers.utils.formatUnits(await contracts.tokenA.contractBalance(), tokenADecimals)} TKA`);
    
    // TokenB状态
    console.log("\n【TokenB状态】");
    const tokenBBalance = await contracts.tokenB.balanceOf(deployer.address);
    const tokenBTotalSupply = await contracts.tokenB.totalSupply();
    const tokenBDecimals = await contracts.tokenB.decimals();
    
    console.log(`总供应量: ${ethers.utils.formatUnits(tokenBTotalSupply, tokenBDecimals)} TKB`);
    console.log(`部署者余额: ${ethers.utils.formatUnits(tokenBBalance, tokenBDecimals)} TKB`);
    console.log(`已铸造总量: ${ethers.utils.formatUnits(await contracts.tokenB.totalMinted(), tokenBDecimals)} TKB`);
    
    // NFT状态
    console.log("\n【MinerNFT状态】");
    const nextTokenId = await contracts.minerNFT.nextTokenId();
    const maxSupply = await contracts.minerNFT.MAX_SUPPLY();
    const userNFTCount = await contracts.minerNFT.getUserNFTCount(deployer.address);
    
    console.log(`已铸造数量: ${nextTokenId.toNumber() - 1}/${maxSupply.toString()}`);
    console.log(`部署者持有: ${userNFTCount.toString()} 个`);
    
    // 挖矿合约状态
    console.log("\n【挖矿合约状态】");
    const totalBurnedA = await contracts.miningContract.totalBurnedTokenA();
    const totalBurnedB = await contracts.miningContract.totalBurnedTokenB();
    const totalReleasedB = await contracts.miningContract.totalReleasedTokenB();
    
    console.log(`累计销毁TokenA: ${ethers.utils.formatEther(totalBurnedA)} TKA`);
    console.log(`累计销毁TokenB: ${ethers.utils.formatEther(totalBurnedB)} TKB`);
    console.log(`累计释放TokenB: ${ethers.utils.formatEther(totalReleasedB)} TKB`);
    
    // 用户邀请信息
    const inviteInfo = await contracts.miningContract.getUserInviteInfo(deployer.address);
    console.log(`邀请人: ${inviteInfo.inviter}`);
    console.log(`邀请人数: ${inviteInfo.totalInvited.toString()}`);
    console.log(`加速释放额度: ${ethers.utils.formatEther(inviteInfo.acceleratedReleaseAmount)} (价值)`);
    
  } catch (error) {
    console.error("查询合约状态时发生错误:", error.message);
  }
}

// 铸造NFT
async function mintNFT(count = 1) {
  console.log(`\n铸造 ${count} 个NFT...`);
  
  const contracts = await getContracts();
  const [deployer] = await ethers.getSigners();
  
  try {
    if (count === 1) {
      const tx = await contracts.minerNFT.mint(deployer.address);
      await tx.wait();
      console.log(`✅ NFT铸造成功, 交易哈希: ${tx.hash}`);
    } else {
      const recipients = Array(count).fill(deployer.address);
      const tx = await contracts.minerNFT.batchMint(recipients);
      await tx.wait();
      console.log(`✅ 批量NFT铸造成功, 交易哈希: ${tx.hash}`);
    }
  } catch (error) {
    console.error("铸造NFT失败:", error.message);
  }
}

// 释放NFT代币
async function releaseNFTTokens() {
  console.log("\n释放NFT代币...");
  
  const contracts = await getContracts();
  const [deployer] = await ethers.getSigners();
  
  try {
    const releasableAmount = await contracts.minerNFT.getUserTotalReleasableAmount(deployer.address);
    
    if (releasableAmount.gt(0)) {
      console.log(`可释放数量: ${ethers.utils.formatEther(releasableAmount)} TKA`);
      
      const tx = await contracts.minerNFT.batchReleaseTokens();
      await tx.wait();
      console.log(`✅ NFT代币释放成功, 交易哈希: ${tx.hash}`);
    } else {
      console.log("当前没有可释放的代币");
    }
  } catch (error) {
    console.error("释放NFT代币失败:", error.message);
  }
}

// 销毁TokenA换取TokenB
async function burnTokenAForB(amount) {
  console.log(`\n销毁 ${amount} TKA 换取TokenB...`);
  
  const contracts = await getContracts();
  const [deployer] = await ethers.getSigners();
  
  try {
    const amountWei = ethers.utils.parseEther(amount);
    
    // 先授权
    const approveTx = await contracts.tokenA.approve(contracts.miningContract.address, amountWei);
    await approveTx.wait();
    console.log("✅ TokenA授权成功");
    
    // 执行销毁
    const tx = await contracts.miningContract.burnTokenAForTokenB(amountWei);
    await tx.wait();
    console.log(`✅ TokenA销毁成功, 交易哈希: ${tx.hash}`);
  } catch (error) {
    console.error("销毁TokenA失败:", error.message);
  }
}

// 销毁TokenB (需要持有NFT)
async function burnTokenBWithNFT(amount) {
  console.log(`\n销毁 ${amount} TKB (需要持有NFT)...`);
  
  const contracts = await getContracts();
  const [deployer] = await ethers.getSigners();
  
  try {
    const nftBalance = await contracts.minerNFT.balanceOf(deployer.address);
    if (nftBalance.eq(0)) {
      console.log("❌ 必须持有至少1个NFT才能使用此功能");
      return;
    }
    
    const amountWei = ethers.utils.parseEther(amount);
    
    // 先授权
    const approveTx = await contracts.tokenB.approve(contracts.miningContract.address, amountWei);
    await approveTx.wait();
    console.log("✅ TokenB授权成功");
    
    // 执行销毁
    const tx = await contracts.miningContract.burnTokenBWithNFT(amountWei);
    await tx.wait();
    console.log(`✅ TokenB销毁成功, 交易哈希: ${tx.hash}`);
  } catch (error) {
    console.error("销毁TokenB失败:", error.message);
  }
}

// 领取挖矿奖励
async function claimMiningRewards() {
  console.log("\n领取挖矿奖励...");
  
  const contracts = await getContracts();
  const [deployer] = await ethers.getSigners();
  
  try {
    // 查询可领取数量
    const claimable1 = await contracts.miningContract.getUserClaimableAmount1(deployer.address);
    const claimable2 = await contracts.miningContract.getUserClaimableAmount2(deployer.address);
    
    console.log(`机制一可领取: ${ethers.utils.formatEther(claimable1)} (价值)`);
    console.log(`机制二可领取: ${ethers.utils.formatEther(claimable2)} (价值)`);
    
    // 领取机制一奖励
    if (claimable1.gt(0)) {
      const tx1 = await contracts.miningContract.claimTokenBFromMechanism1();
      await tx1.wait();
      console.log(`✅ 机制一奖励领取成功, 交易哈希: ${tx1.hash}`);
    }
    
    // 领取机制二奖励
    if (claimable2.gt(0)) {
      const tx2 = await contracts.miningContract.claimTokenBFromMechanism2();
      await tx2.wait();
      console.log(`✅ 机制二奖励领取成功, 交易哈希: ${tx2.hash}`);
    }
    
    if (claimable1.eq(0) && claimable2.eq(0)) {
      console.log("当前没有可领取的挖矿奖励");
    }
  } catch (error) {
    console.error("领取挖矿奖励失败:", error.message);
  }
}

// 主菜单
async function showMenu() {
  console.log("\n" + "=".repeat(60));
  console.log("DeFi生态系统管理工具");
  console.log("=".repeat(60));
  console.log("1. 查看合约状态");
  console.log("2. 铸造NFT");
  console.log("3. 释放NFT代币");
  console.log("4. 销毁TokenA换取TokenB");
  console.log("5. 销毁TokenB (需要NFT)");
  console.log("6. 领取挖矿奖励");
  console.log("0. 退出");
  console.log("=".repeat(60));
}

async function main() {
  const args = process.argv.slice(2);
  
  if (args.length === 0) {
    // 交互模式
    const readline = require('readline');
    const rl = readline.createInterface({
      input: process.stdin,
      output: process.stdout
    });
    
    const question = (query) => new Promise((resolve) => rl.question(query, resolve));
    
    while (true) {
      await showMenu();
      const choice = await question("请选择操作 (0-6): ");
      
      switch (choice) {
        case "1":
          await showContractStatus();
          break;
        case "2":
          const count = await question("请输入铸造数量 (默认1): ");
          await mintNFT(parseInt(count) || 1);
          break;
        case "3":
          await releaseNFTTokens();
          break;
        case "4":
          const amountA = await question("请输入销毁TokenA数量: ");
          if (amountA) await burnTokenAForB(amountA);
          break;
        case "5":
          const amountB = await question("请输入销毁TokenB数量: ");
          if (amountB) await burnTokenBWithNFT(amountB);
          break;
        case "6":
          await claimMiningRewards();
          break;
        case "0":
          console.log("退出管理工具");
          rl.close();
          return;
        default:
          console.log("无效选择，请重新输入");
      }
      
      await question("\n按回车键继续...");
    }
  } else {
    // 命令行模式
    const command = args[0];
    
    switch (command) {
      case "status":
        await showContractStatus();
        break;
      case "mint":
        const count = parseInt(args[1]) || 1;
        await mintNFT(count);
        break;
      case "release":
        await releaseNFTTokens();
        break;
      case "claim":
        await claimMiningRewards();
        break;
      default:
        console.log("使用方法:");
        console.log("npx hardhat run scripts/interact.js --network <network>         # 交互模式");
        console.log("npx hardhat run scripts/interact.js --network <network> status # 查看状态");
        console.log("npx hardhat run scripts/interact.js --network <network> mint [count] # 铸造NFT");
        console.log("npx hardhat run scripts/interact.js --network <network> release # 释放NFT代币");
        console.log("npx hardhat run scripts/interact.js --network <network> claim # 领取奖励");
    }
  }
}

if (require.main === module) {
  main()
    .then(() => process.exit(0))
    .catch((error) => {
      console.error(error);
      process.exit(1);
    });
}

module.exports = { main, showContractStatus, mintNFT, releaseNFTTokens, burnTokenAForB, burnTokenBWithNFT, claimMiningRewards };