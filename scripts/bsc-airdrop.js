const { ethers } = require("hardhat");
const readline = require('readline');

// BSC Network Configuration
const BSC_CONFIG = {
    testnet: {
        name: "BSC Testnet",
        url: "https://data-seed-prebsc-1-s1.binance.org:8545/",
        chainId: 97,
        currency: "tBNB",
        explorer: "https://testnet.bscscan.com"
    },
    mainnet: {
        name: "BSC Mainnet", 
        url: "https://bsc-dataseed1.binance.org/",
        chainId: 56,
        currency: "BNB",
        explorer: "https://bscscan.com"
    }
};

// Create readline interface
const rl = readline.createInterface({
    input: process.stdin,
    output: process.stdout
});

function askQuestion(question) {
    return new Promise((resolve) => {
        rl.question(question, (answer) => {
            resolve(answer);
        });
    });
}

function generateRandomAddresses(count) {
    console.log(`\n📝 Generating ${count} random addresses for BSC airdrop...`);
    const addresses = [];
    
    for (let i = 0; i < count; i++) {
        const randomWallet = ethers.Wallet.createRandom();
        addresses.push(randomWallet.address);
        console.log(`${i + 1}. ${randomWallet.address}`);
    }
    
    return addresses;
}

async function bscAirdrop() {
    console.log("🌟 BSC NFT Airdrop Script");
    console.log("========================\n");
    
    try {
        // Network selection
        console.log("🌐 Available BSC Networks:");
        console.log("1. BSC Testnet (tBNB)");
        console.log("2. BSC Mainnet (BNB)");
        
        const networkChoice = await askQuestion("\n📝 Select network (1 or 2): ");
        let networkConfig;
        
        switch(networkChoice) {
            case '1':
                networkConfig = BSC_CONFIG.testnet;
                break;
            case '2':
                networkConfig = BSC_CONFIG.mainnet;
                break;
            default:
                console.log("❌ Invalid network selection!");
                rl.close();
                return;
        }
        
        console.log(`\n🌐 Selected Network: ${networkConfig.name}`);
        console.log(`💰 Currency: ${networkConfig.currency}`);
        console.log(`🔍 Explorer: ${networkConfig.explorer}`);
        
        // Get private key
        const privateKey = await askQuestion("\n📝 Please enter your private key (64 characters): ");
        
        if (!privateKey || privateKey.length !== 64) {
            console.log("❌ Invalid private key format!");
            rl.close();
            return;
        }
        
        // Get contract address  
        const contractAddress = await askQuestion("📝 Please enter NFT contract address: ");
        
        if (!ethers.isAddress(contractAddress)) {
            console.log("❌ Invalid contract address format!");
            rl.close();
            return;
        }
        
        // Get airdrop parameters
        const addressCount = await askQuestion("📝 How many addresses to generate? (1-50): ");
        const count = parseInt(addressCount);
        
        if (isNaN(count) || count < 1 || count > 50) {
            console.log("❌ Invalid count! Please enter 1-50.");
            rl.close();
            return;
        }
        
        const quantityInput = await askQuestion("📝 How many NFTs per address? (1-5): ");
        const quantity = parseInt(quantityInput);
        
        if (isNaN(quantity) || quantity < 1 || quantity > 5) {
            console.log("❌ Invalid quantity! Please enter 1-5.");
            rl.close();
            return;
        }
        
        // Configuration summary
        console.log("\n🔧 Airdrop Configuration:");
        console.log(`🌐 Network: ${networkConfig.name}`);
        console.log(`📍 Contract: ${contractAddress}`);
        console.log(`👥 Recipients: ${count}`);
        console.log(`🎁 NFTs per address: ${quantity}`);
        console.log(`📊 Total NFTs: ${count * quantity}`);
        
        const confirm = await askQuestion("\n❓ Proceed with airdrop? (y/N): ");
        if (confirm.toLowerCase() !== 'y') {
            console.log("❌ Airdrop cancelled.");
            rl.close();
            return;
        }
        
        // Generate addresses
        const addresses = generateRandomAddresses(count);
        
        // Connect to BSC
        const provider = new ethers.JsonRpcProvider(networkConfig.url);
        const wallet = new ethers.Wallet(privateKey, provider);
        
        console.log(`\n👤 Airdrop Wallet: ${wallet.address}`);
        
        // Check network connection
        const network = await provider.getNetwork();
        console.log(`🌐 Connected to Chain ID: ${network.chainId}`);
        
        if (Number(network.chainId) !== networkConfig.chainId) {
            console.log(`⚠️  Warning: Expected chain ID ${networkConfig.chainId}, got ${network.chainId}`);
        }
        
        // Check balance
        const balance = await provider.getBalance(wallet.address);
        console.log(`💰 Wallet Balance: ${ethers.formatEther(balance)} ${networkConfig.currency}`);
        
        if (balance === 0n) {
            console.log(`❌ Insufficient ${networkConfig.currency} balance!`);
            rl.close();
            return;
        }
        
        // Contract setup
        const contractABI = [
            "function mintFor(address to, uint256 quantity) external",
            "function batchMintToMultiple(address[] calldata recipients, uint256[] calldata quantities) external",
            "function owner() external view returns (address)",
            "function totalMinted() external view returns (uint256)",
            "function MAX_SUPPLY() external view returns (uint256)",
            "function name() external view returns (string)",
            "function symbol() external view returns (string)"
        ];
        
        const nftContract = new ethers.Contract(contractAddress, contractABI, wallet);
        
        try {
            const contractName = await nftContract.name();
            const contractSymbol = await nftContract.symbol();
            const owner = await nftContract.owner();
            
            console.log(`\n📋 Contract Info:`);
            console.log(`📝 Name: ${contractName}`);
            console.log(`🔤 Symbol: ${contractSymbol}`);
            console.log(`👑 Owner: ${owner}`);
            
            // Verify ownership
            if (wallet.address.toLowerCase() !== owner.toLowerCase()) {
                console.log("❌ You are not the contract owner!");
                rl.close();
                return;
            }
            
        } catch (error) {
            console.log("❌ Contract connection error:", error.message);
            rl.close();
            return;
        }
        
        console.log("\n🚀 Starting BSC airdrop...");
        
        // Batch processing
        const batchSize = 10; // Smaller batches for BSC
        let successCount = 0;
        let failureCount = 0;
        
        for (let i = 0; i < addresses.length; i += batchSize) {
            const batch = addresses.slice(i, i + batchSize);
            const quantities = new Array(batch.length).fill(quantity);
            
            console.log(`\n📦 Processing batch ${Math.floor(i / batchSize) + 1}/${Math.ceil(addresses.length / batchSize)}...`);
            
            try {
                // Gas estimation for BSC
                const gasEstimate = await nftContract.batchMintToMultiple.estimateGas(batch, quantities);
                const feeData = await provider.getFeeData();
                const gasCost = gasEstimate * feeData.gasPrice;
                
                console.log(`⛽ Gas: ${gasEstimate.toString()}`);
                console.log(`💰 Cost: ${ethers.formatEther(gasCost)} ${networkConfig.currency}`);
                
                // Execute transaction
                const tx = await nftContract.batchMintToMultiple(batch, quantities, {
                    gasLimit: gasEstimate * 120n / 100n,
                    gasPrice: feeData.gasPrice
                });
                
                console.log(`📝 TX Hash: ${tx.hash}`);
                console.log(`🔍 View: ${networkConfig.explorer}/tx/${tx.hash}`);
                console.log("⏳ Waiting for confirmation...");
                
                const receipt = await tx.wait();
                
                if (receipt.status === 1) {
                    console.log(`✅ Batch successful! Gas used: ${receipt.gasUsed.toString()}`);
                    successCount += batch.length;
                } else {
                    console.log("❌ Transaction failed!");
                    failureCount += batch.length;
                }
                
            } catch (error) {
                console.log(`❌ Batch failed: ${error.message}`);
                failureCount += batch.length;
            }
            
            // Delay between batches
            if (i + batchSize < addresses.length) {
                console.log("⏳ Waiting 3 seconds...");
                await new Promise(resolve => setTimeout(resolve, 3000));
            }
        }
        
        // Final results
        console.log("\n🎉 BSC Airdrop Completed!");
        console.log("=========================");
        console.log(`🌐 Network: ${networkConfig.name}`);
        console.log(`✅ Successful: ${successCount}/${count}`);
        console.log(`❌ Failed: ${failureCount}/${count}`);
        console.log(`🎁 Total NFTs minted: ${successCount * quantity}`);
        
        if (successCount === count) {
            console.log("🏆 All airdrops completed successfully!");
        }
        
    } catch (error) {
        console.log("💥 Unexpected error:", error.message);
    } finally {
        rl.close();
    }
}

// Run script
if (require.main === module) {
    bscAirdrop()
        .then(() => {
            console.log("\n👋 BSC Airdrop finished!");
            process.exit(0);
        })
        .catch((error) => {
            console.error("💥 Script error:", error);
            process.exit(1);
        });
}

module.exports = { bscAirdrop };