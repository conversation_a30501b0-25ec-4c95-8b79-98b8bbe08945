const { NFTReleaseTimeManager, dateToTimestamp } = require('./setNFTReleaseTime');

async function example() {
  const manager = new NFTReleaseTimeManager();
  
  try {
    // 验证权限
    await manager.verifyOwner();
    
    // 查看当前状态
    await manager.displayNFTStatus();
    
    // 示例1: 将NFT #1-5 的释放时间设置为2024年1月1日
    const batch1 = [1, 2, 3, 4, 5];
    const time1 = dateToTimestamp('2024-01-01 00:00:00');
    await manager.batchSetReleaseTime(batch1, time1);
    
    // 示例2: 将NFT #6-10 的释放时间设置为2024年2月1日
    const batch2 = [6, 7, 8, 9, 10];
    const time2 = dateToTimestamp('2024-02-01 00:00:00');
    await manager.batchSetReleaseTime(batch2, time2);
    
    // 查看更新后的状态
    console.log('\n更新后的状态:');
    await manager.displayNFTStatus();
    
  } catch (error) {
    console.error('示例执行失败:', error);
  }
}

example();