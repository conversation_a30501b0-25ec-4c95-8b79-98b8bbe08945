const { ethers } = require("hardhat");

async function main() {
    console.log("开始部署 Gratitude Protocol Token (GRAT)...");
    
    // 获取部署者账户
    const [deployer] = await ethers.getSigners();
    console.log("部署者地址:", deployer.address);
    
    // 检查部署者余额
    const balance = await ethers.provider.getBalance(deployer.address);
    console.log("部署者余额:", ethers.formatEther(balance), "ETH");
    
    // 部署合约
    console.log("\n正在部署 GratitudeToken 合约...");
    const GratitudeToken = await ethers.getContractFactory("GratitudeToken");
    
    // 使用部署者地址作为初始所有者
    const gratitudeToken = await GratitudeToken.deploy(deployer.address);
    
    // 等待部署完成
    await gratitudeToken.waitForDeployment();
    const contractAddress = await gratitudeToken.getAddress();
    
    console.log("✅ GratitudeToken 合约部署成功!");
    console.log("合约地址:", contractAddress);
    
    // 验证部署结果
    console.log("\n验证部署结果...");
    
    // 获取代币基本信息
    const tokenInfo = await gratitudeToken.getTokenInfo();
    console.log("代币名称:", tokenInfo.name);
    console.log("代币符号:", tokenInfo.symbol);
    console.log("小数位数:", tokenInfo.decimals.toString());
    console.log("总供应量:", ethers.formatEther(tokenInfo.totalSupply), "GRAT");
    console.log("最大供应量:", ethers.formatEther(tokenInfo.maxSupply), "GRAT");
    
    // 检查所有者余额
    const ownerBalance = await gratitudeToken.balanceOf(deployer.address);
    console.log("所有者余额:", ethers.formatEther(ownerBalance), "GRAT");
    
    // 检查所有者权限
    const isOwner = await gratitudeToken.owner();
    const isMinter = await gratitudeToken.isMinter(deployer.address);
    console.log("合约所有者:", isOwner);
    console.log("部署者是否为铸造者:", isMinter);
    
    // 保存部署信息到文件
    const deploymentInfo = {
        network: (await ethers.provider.getNetwork()).name,
        contractAddress: contractAddress,
        deployer: deployer.address,
        deploymentTime: new Date().toISOString(),
        tokenInfo: {
            name: tokenInfo.name,
            symbol: tokenInfo.symbol,
            decimals: tokenInfo.decimals.toString(),
            totalSupply: tokenInfo.totalSupply.toString(),
            maxSupply: tokenInfo.maxSupply.toString()
        },
        transactionHash: gratitudeToken.deploymentTransaction()?.hash
    };
    
    const fs = require('fs');
    const path = require('path');
    
    // 确保 deployments 目录存在
    const deploymentsDir = path.join(__dirname, '..', 'deployments');
    if (!fs.existsSync(deploymentsDir)) {
        fs.mkdirSync(deploymentsDir, { recursive: true });
    }
    
    // 保存部署信息
    const deploymentFile = path.join(deploymentsDir, 'gratitude-token-deployment.json');
    fs.writeFileSync(deploymentFile, JSON.stringify(deploymentInfo, null, 2));
    
    console.log("\n📄 部署信息已保存到:", deploymentFile);
    
    // 输出使用说明
    console.log("\n📋 使用说明:");
    console.log("1. 代币已成功部署，总供应量为 2.5 亿 GRAT");
    console.log("2. 所有代币已铸造给部署者地址");
    console.log("3. 部署者拥有管理员权限，可以:");
    console.log("   - 添加/移除铸造者");
    console.log("   - 管理黑名单");
    console.log("   - 暂停/恢复合约");
    console.log("4. 合约包含以下功能:");
    console.log("   - 基本的 ERC20 转账功能");
    console.log("   - 代币销毁功能");
    console.log("   - 批量转账功能");
    console.log("   - 黑名单管理");
    console.log("   - 紧急暂停功能");
    
    console.log("\n🎉 部署完成!");
    
    return {
        contractAddress,
        deployer: deployer.address,
        deploymentInfo
    };
}

// 错误处理
main()
    .then((result) => {
        console.log("\n✅ 脚本执行成功");
        process.exit(0);
    })
    .catch((error) => {
        console.error("\n❌ 部署失败:");
        console.error(error);
        process.exit(1);
    });

module.exports = main;
