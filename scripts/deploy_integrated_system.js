const { ethers } = require("hardhat");

async function main() {
    console.log("=== 部署防闪电贷挖矿系统 ===");
    
    const [deployer] = await ethers.getSigners();
    console.log("部署账户:", deployer.address);

    // 1. 部署UltraSimplePriceOracle
    console.log("\n1. 部署UltraSimplePriceOracle...");
    const UltraSimplePriceOracle = await ethers.getContractFactory("UltraSimplePriceOracle");
    const priceOracle = await UltraSimplePriceOracle.deploy();
    await priceOracle.deployed();
    console.log("UltraSimplePriceOracle 地址:", priceOracle.address);

    // 2. 部署MiningContract (假设TokenA, TokenB, NFT已存在)
    const tokenAAddress = "******************************************"; // 替换为实际TokenA地址
    const tokenBAddress = "******************************************"; // 替换为实际TokenB地址
    const nftAddress = "******************************************";    // 替换为实际NFT地址

    console.log("\n2. 部署MiningContract...");
    const MiningContract = await ethers.getContractFactory("MiningContract");
    const miningContract = await MiningContract.deploy(
        tokenAAddress,
        tokenBAddress,
        nftAddress,
        priceOracle.address
    );
    await miningContract.deployed();
    console.log("MiningContract 地址:", miningContract.address);

    // 3. 配置价格预言机
    console.log("\n3. 配置价格预言机...");
    
    // 配置TokenA
    console.log("配置TokenA...");
    await priceOracle.configureToken(tokenAAddress);
    console.log("TokenA配置完成");

    // 配置TokenB
    console.log("配置TokenB...");
    await priceOracle.configureToken(tokenBAddress);
    console.log("TokenB配置完成");

    // 设置挖矿合约为授权更新者
    console.log("设置挖矿合约为授权更新者...");
    await priceOracle.setAuthorizedUpdater(miningContract.address, true);
    console.log("授权设置完成");

    // 4. 配置挖矿合约
    console.log("\n4. 配置挖矿合约...");
    await miningContract.setContractAddresses(
        tokenAAddress,
        tokenBAddress,
        nftAddress,
        priceOracle.address
    );
    console.log("挖矿合约配置完成");

    // 5. 验证集成
    console.log("\n5. 验证集成状态...");
    
    // 检查价格预言机状态
    const isTokenAReliable = await priceOracle.isPriceReliable(tokenAAddress);
    const isTokenBReliable = await priceOracle.isPriceReliable(tokenBAddress);
    console.log("TokenA价格可靠性:", isTokenAReliable);
    console.log("TokenB价格可靠性:", isTokenBReliable);

    // 检查授权状态
    const isAuthorized = await priceOracle.authorizedUpdaters(miningContract.address);
    console.log("挖矿合约授权状态:", isAuthorized);

    // 获取价格信息
    try {
        const tokenAPrice = await priceOracle.getTokenPrice(tokenAAddress);
        console.log("TokenA当前价格:", ethers.utils.formatEther(tokenAPrice), "BNB");
    } catch (error) {
        console.log("TokenA价格获取失败:", error.message);
    }

    try {
        const tokenBPrice = await priceOracle.getTokenPrice(tokenBAddress);
        console.log("TokenB当前价格:", ethers.utils.formatEther(tokenBPrice), "BNB");
    } catch (error) {
        console.log("TokenB价格获取失败:", error.message);
    }

    console.log("\n=== 部署完成 ===");
    console.log("合约地址总结:");
    console.log("- UltraSimplePriceOracle:", priceOracle.address);
    console.log("- MiningContract:", miningContract.address);
    
    console.log("\n后续步骤:");
    console.log("1. 在TokenA和TokenB合约中集成价格触发逻辑");
    console.log("2. 验证代币转账时是否正确触发价格记录");
    console.log("3. 测试挖矿功能的防闪电贷效果");

    return {
        priceOracle: priceOracle.address,
        miningContract: miningContract.address
    };
}

// 测试价格记录功能
async function testPriceRecording(priceOracleAddress, tokenAddress) {
    console.log("\n=== 测试价格记录功能 ===");
    
    const priceOracle = await ethers.getContractAt("UltraSimplePriceOracle", priceOracleAddress);
    
    // 手动记录价格
    console.log("手动记录价格...");
    await priceOracle.manualRecordPrice(tokenAddress);
    
    // 获取价格历史
    const history = await priceOracle.getPriceHistory(tokenAddress);
    console.log("价格历史记录数量:", history.prices.length);
    
    if (history.prices.length > 0) {
        console.log("最新价格:", ethers.utils.formatEther(history.prices[history.prices.length - 1]), "BNB");
        console.log("记录时间:", new Date(history.timestamps[history.timestamps.length - 1] * 1000));
    }
    
    // 获取价格信息
    const priceInfo = await priceOracle.getPriceInfo(tokenAddress);
    console.log("加权价格:", ethers.utils.formatEther(priceInfo.weightedPrice), "BNB");
    console.log("即时价格:", ethers.utils.formatEther(priceInfo.spotPrice), "BNB");
    console.log("偏差:", priceInfo.deviation.toString(), "basis points");
}

// 演示挖矿流程
async function demonstrateMining(miningContractAddress, userAddress, tokenAAmount) {
    console.log("\n=== 演示挖矿流程 ===");
    
    const miningContract = await ethers.getContractAt("MiningContract", miningContractAddress);
    
    try {
        // 模拟挖矿（注意：需要先授权TokenA）
        console.log("执行TokenA挖矿...");
        const tx = await miningContract.burnTokenAForTokenB(tokenAAmount);
        await tx.wait();
        console.log("挖矿成功，交易哈希:", tx.hash);
        
        // 检查挖矿记录
        const recordCount = await miningContract.getUserMiningRecordCount(userAddress);
        console.log("用户挖矿记录数量:", recordCount.count1.toString(), recordCount.count2.toString());
        
    } catch (error) {
        console.log("挖矿演示失败:", error.message);
        console.log("请确保:");
        console.log("1. 用户拥有足够的TokenA");
        console.log("2. 用户已授权挖矿合约使用TokenA");
        console.log("3. TokenA合约实现了burnFrom功能");
    }
}

if (require.main === module) {
    main()
        .then(() => process.exit(0))
        .catch((error) => {
            console.error(error);
            process.exit(1);
        });
}

module.exports = { main, testPriceRecording, demonstrateMining };