const { ethers } = require("hardhat");
const readline = require("readline");

// Create readline interface
const rl = readline.createInterface({
  input: process.stdin,
  output: process.stdout,
});

// Function to prompt user input
function askQuestion(question) {
  return new Promise((resolve) => {
    rl.question(question, (answer) => {
      resolve(answer);
    });
  });
}

// Function to validate private key
function isValidPrivateKey(privateKey) {
  try {
    new ethers.Wallet(privateKey);
    return true;
  } catch (error) {
    return false;
  }
}

// Function to get all existing token IDs
async function getAllTokenIds(contract) {
  try {
    const totalSupply = await contract.totalSupply();
    const tokenIds = [];

    console.log(`📊 Total Supply: ${totalSupply.toString()} tokens`);
    console.log("🔍 Scanning for existing tokens...");

    // Get all token IDs using enumerable interface
    for (let i = 0; i < totalSupply; i++) {
      try {
        const tokenId = await contract.tokenByIndex(i);
        tokenIds.push(tokenId.toString());
      } catch (error) {
        console.log(`⚠️  Token at index ${i} not found, skipping...`);
      }
    }

    return tokenIds;
  } catch (error) {
    console.log(
      "⚠️  totalSupply() not available, trying alternative method..."
    );

    // Alternative method: try sequential token IDs
    const tokenIds = [];
    let currentId = 1;
    let consecutiveFailures = 0;
    const maxFailures = 10; // Stop after 10 consecutive failures

    while (consecutiveFailures < maxFailures && currentId <= 10000) {
      try {
        const owner = await contract.ownerOf(currentId);
        if (owner && owner !== ethers.ZeroAddress) {
          tokenIds.push(currentId.toString());
          consecutiveFailures = 0;
          console.log(`✅ Found token ID: ${currentId}`);
        } else {
          consecutiveFailures++;
        }
      } catch (error) {
        consecutiveFailures++;
      }
      currentId++;
    }

    return tokenIds;
  }
}

// Main function to set token URIs
async function setTokenURIs() {
  console.log("🖼️  NFT Token URI Setup Script");
  console.log("==============================\n");

  try {
    // Get network selection
    console.log("🌐 Network Selection:");
    console.log("1. BSC Testnet");
    console.log("2. BSC Mainnet");
    console.log("3. Current/Default Network");

    const networkChoice = await askQuestion("\n📝 Select network (1-3): ");
    let provider;
    let networkName;

    switch (networkChoice) {
      case "1":
        provider = new ethers.JsonRpcProvider(
          "https://data-seed-prebsc-1-s1.binance.org:8545/"
        );
        networkName = "BSC Testnet";
        break;
      case "2":
        provider = new ethers.JsonRpcProvider(
          "https://bsc-dataseed1.binance.org/"
        );
        networkName = "BSC Mainnet";
        break;
      default:
        provider = ethers.provider;
        networkName = "Current Network";
    }

    console.log(`🌐 Selected: ${networkName}`);

    // Get private key
    const privateKey = await askQuestion(
      "\n📝 Enter your private key (64 characters): "
    );

    if (
      !privateKey ||
      privateKey.length !== 64 ||
      !isValidPrivateKey(privateKey)
    ) {
      console.log("❌ Invalid private key format!");
      rl.close();
      return;
    }

    // Get contract address
    const contractAddress = await askQuestion(
      "📝 Enter NFT contract address: "
    );

    if (!ethers.isAddress(contractAddress)) {
      console.log("❌ Invalid contract address!");
      rl.close();
      return;
    }

    // Get base URI
    const baseURI = await askQuestion(
      "📝 Enter base URI (e.g., https://api.example.com/metadata/): "
    );

    if (!baseURI.trim()) {
      console.log("❌ Base URI cannot be empty!");
      rl.close();
      return;
    }

    // Ensure base URI ends with /
    const formattedBaseURI = baseURI.endsWith("/") ? baseURI : baseURI + "/";

    // Create wallet and contract instance
    const wallet = new ethers.Wallet(privateKey, provider);
    console.log(`\n👤 Wallet: ${wallet.address}`);

    // Check balance
    const balance = await provider.getBalance(wallet.address);
    console.log(`💰 Balance: ${ethers.formatEther(balance)} ETH`);

    if (balance === 0n) {
      console.log("❌ Insufficient balance for gas fees!");
      rl.close();
      return;
    }

    // Contract ABI
    const contractABI = [
      "function setTokenURI(uint256 tokenId, string memory uri) external",
      "function tokenURI(uint256 tokenId) external view returns (string)",
      "function ownerOf(uint256 tokenId) external view returns (address)",
      "function totalSupply() external view returns (uint256)",
      "function tokenByIndex(uint256 index) external view returns (uint256)",
      "function owner() external view returns (address)",
      "function name() external view returns (string)",
      "function symbol() external view returns (string)",
    ];

    const nftContract = new ethers.Contract(
      contractAddress,
      contractABI,
      wallet
    );

    // Verify contract and ownership
    try {
      const contractName = await nftContract.name();
      const contractSymbol = await nftContract.symbol();
      const owner = await nftContract.owner();

      console.log(`\n📋 Contract Info:`);
      console.log(`📝 Name: ${contractName}`);
      console.log(`🔤 Symbol: ${contractSymbol}`);
      console.log(`👑 Owner: ${owner}`);

      if (wallet.address.toLowerCase() !== owner.toLowerCase()) {
        console.log("❌ You are not the contract owner!");
        rl.close();
        return;
      }
    } catch (error) {
      console.log("❌ Error connecting to contract:", error.message);
      rl.close();
      return;
    }

    // Get all existing token IDs
    console.log("\n🔍 Finding all existing tokens...");
    const tokenIds = await getAllTokenIds(nftContract);

    if (tokenIds.length === 0) {
      console.log("❌ No tokens found in this contract!");
      rl.close();
      return;
    }

    console.log(`\n📊 Found ${tokenIds.length} tokens:`);
    tokenIds.forEach((id, index) => {
      if (index < 10) {
        console.log(`  Token ID: ${id}`);
      } else if (index === 10) {
        console.log(`  ... and ${tokenIds.length - 10} more tokens`);
      }
    });

    // Show URI format examples
    console.log(`\n🔗 URI Format Examples:`);
    console.log(`  Token 1: ${formattedBaseURI}1.json`);
    console.log(`  Token 2: ${formattedBaseURI}2.json`);
    console.log(`  Token N: ${formattedBaseURI}N.json`);

    // Confirm operation
    const confirm = await askQuestion(
      `\n❓ Set URI for all ${tokenIds.length} tokens? (y/N): `
    );
    if (confirm.toLowerCase() !== "y" && confirm.toLowerCase() !== "yes") {
      console.log("❌ Operation cancelled.");
      rl.close();
      return;
    }

    // Check which tokens already have URIs
    console.log("\n🔍 Checking existing URIs...");
    const tokensNeedingUpdate = [];
    const tokensWithURI = [];

    for (const tokenId of tokenIds) {
      try {
        const currentURI = await nftContract.tokenURI(tokenId);
        const expectedURI = `meta_data.json`;

        if (currentURI === expectedURI) {
          tokensWithURI.push(tokenId);
        } else {
          tokensNeedingUpdate.push({
            tokenId,
            currentURI: currentURI || "none",
            newURI: expectedURI,
          });
        }
      } catch (error) {
        tokensNeedingUpdate.push({
          tokenId,
          currentURI: "error",
          newURI: `${formattedBaseURI}${tokenId}.json`,
        });
      }
    }

    console.log(`✅ Tokens with correct URI: ${tokensWithURI.length}`);
    console.log(`🔄 Tokens needing update: ${tokensNeedingUpdate.length}`);

    if (tokensNeedingUpdate.length === 0) {
      console.log("\n🎉 All tokens already have correct URIs!");
      rl.close();
      return;
    }

    // Show some examples of updates
    console.log(`\n📝 Updates to be made (showing first 5):`);
    tokensNeedingUpdate.slice(0, 5).forEach((token) => {
      console.log(`  Token ${token.tokenId}:`);
      console.log(`    Current: ${token.currentURI}`);
      console.log(`    New: ${token.newURI}`);
    });

    // Start batch processing
    console.log(`\n🚀 Starting URI updates...`);

    const batchSize = 10; // Process 10 tokens at a time
    let successCount = 0;
    let failureCount = 0;
    const failedTokens = [];

    for (let i = 0; i < tokensNeedingUpdate.length; i += batchSize) {
      const batch = tokensNeedingUpdate.slice(i, i + batchSize);

      console.log(
        `\n📦 Processing batch ${Math.floor(i / batchSize) + 1}/${Math.ceil(
          tokensNeedingUpdate.length / batchSize
        )}...`
      );
      console.log(
        `🎯 Tokens in batch: ${batch.map((t) => t.tokenId).join(", ")}`
      );

      // Process each token in the batch
      for (const token of batch) {
        try {
          console.log(`\n⚡ Setting URI for Token ${token.tokenId}...`);

          // Estimate gas
          const gasEstimate = await nftContract.setTokenURI.estimateGas(
            token.tokenId,
            token.newURI
          );
          const feeData = await provider.getFeeData();
          const gasCost = gasEstimate * feeData.gasPrice;

          console.log(`  ⛽ Gas: ${gasEstimate.toString()}`);
          console.log(`  💰 Cost: ${ethers.formatEther(gasCost)} ETH`);

          // Execute transaction
          const tx = await nftContract.setTokenURI(
            token.tokenId,
            token.newURI,
            {
              gasLimit: (gasEstimate * 120n) / 100n, // 20% buffer
            }
          );

          console.log(`  📝 TX: ${tx.hash}`);
          console.log(`  ⏳ Confirming...`);

          const receipt = await tx.wait();

          if (receipt.status === 1) {
            console.log(
              `  ✅ Success! Gas used: ${receipt.gasUsed.toString()}`
            );
            successCount++;
          } else {
            console.log(`  ❌ Transaction failed`);
            failureCount++;
            failedTokens.push(token.tokenId);
          }
        } catch (error) {
          console.log(`  ❌ Error: ${error.message}`);
          failureCount++;
          failedTokens.push(token.tokenId);
        }

        // Small delay between transactions
        if (batch.indexOf(token) < batch.length - 1) {
          console.log(`  ⏳ Waiting 1 second...`);
          await new Promise((resolve) => setTimeout(resolve, 1000));
        }
      }

      // Delay between batches
      if (i + batchSize < tokensNeedingUpdate.length) {
        console.log(
          `\n⏳ Batch complete. Waiting 3 seconds before next batch...`
        );
        await new Promise((resolve) => setTimeout(resolve, 3000));
      }
    }

    // Final summary
    console.log("\n🎉 URI Update Complete!");
    console.log("========================");
    console.log(`✅ Successful updates: ${successCount}`);
    console.log(`❌ Failed updates: ${failureCount}`);
    console.log(`📊 Total processed: ${successCount + failureCount}`);
    console.log(`🔗 Base URI used: ${formattedBaseURI}`);

    if (failedTokens.length > 0) {
      console.log(`\n❌ Failed token IDs: ${failedTokens.join(", ")}`);
      console.log("💡 You may need to retry these manually");
    }

    if (successCount === tokensNeedingUpdate.length) {
      console.log("\n🏆 All URI updates completed successfully!");
    }

    // Verify a few random tokens
    console.log("\n🔍 Verifying random token URIs...");
    const sampleTokens = tokenIds.slice(0, Math.min(3, tokenIds.length));

    for (const tokenId of sampleTokens) {
      try {
        const uri = await nftContract.tokenURI(tokenId);
        console.log(`✅ Token ${tokenId}: ${uri}`);
      } catch (error) {
        console.log(`❌ Token ${tokenId}: Error reading URI`);
      }
    }
  } catch (error) {
    console.log("💥 Unexpected error:", error.message);
  } finally {
    rl.close();
  }
}

// Run script
if (require.main === module) {
  setTokenURIs()
    .then(() => {
      console.log("\n👋 URI setup script finished!");
      process.exit(0);
    })
    .catch((error) => {
      console.error("💥 Script error:", error);
      process.exit(1);
    });
}

module.exports = { setTokenURIs };
