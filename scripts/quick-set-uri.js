const { ethers } = require("hardhat");
const readline = require('readline');

// Quick URI setup for tokenid.json format
const rl = readline.createInterface({
    input: process.stdin,
    output: process.stdout
});

function askQuestion(question) {
    return new Promise((resolve) => {
        rl.question(question, (answer) => {
            resolve(answer);
        });
    });
}

async function quickSetURI() {
    console.log("⚡ Quick TokenURI Setup (tokenid.json format)");
    console.log("==============================================\n");
    
    try {
        // Network selection
        const networkChoice = await askQuestion("📝 Network (1=BSC Testnet, 2=BSC Mainnet, 3=Current): ");
        let provider;
        
        switch(networkChoice) {
            case '1':
                provider = new ethers.JsonRpcProvider("https://data-seed-prebsc-1-s1.binance.org:8545/");
                console.log("🌐 BSC Testnet selected");
                break;
            case '2':
                provider = new ethers.JsonRpcProvider("https://bsc-dataseed1.binance.org/");
                console.log("🌐 BSC Mainnet selected");
                break;
            default:
                provider = ethers.provider;
                console.log("🌐 Current network selected");
        }
        
        // Get inputs
        const privateKey = await askQuestion("🔑 Private key (64 chars): ");
        const contractAddress = await askQuestion("📍 Contract address: ");
        const baseURI = await askQuestion("🔗 Base URI (e.g., https://api.example.com/): ");
        
        // Quick validation
        if (privateKey.length !== 64) {
            console.log("❌ Invalid private key length!");
            rl.close();
            return;
        }
        
        if (!ethers.isAddress(contractAddress)) {
            console.log("❌ Invalid contract address!");
            rl.close();
            return;
        }
        
        const formattedBaseURI = baseURI.endsWith('/') ? baseURI : baseURI + '/';
        console.log(`🔗 Using base URI: ${formattedBaseURI}`);
        
        // Setup contract
        const wallet = new ethers.Wallet(privateKey, provider);
        const contractABI = [
            "function setTokenURI(uint256 tokenId, string memory uri) external",
            "function tokenURI(uint256 tokenId) external view returns (string)",
            "function totalSupply() external view returns (uint256)",
            "function tokenByIndex(uint256 index) external view returns (uint256)",
            "function owner() external view returns (address)"
        ];
        
        const contract = new ethers.Contract(contractAddress, contractABI, wallet);
        
        console.log(`\n👤 Wallet: ${wallet.address}`);
        
        // Quick owner check
        try {
            const owner = await contract.owner();
            if (wallet.address.toLowerCase() !== owner.toLowerCase()) {
                console.log("❌ Not contract owner!");
                rl.close();
                return;
            }
        } catch (error) {
            console.log("❌ Contract connection failed!");
            rl.close();
            return;
        }
        
        // Get token IDs
        console.log("\n🔍 Finding tokens...");
        let tokenIds = [];
        
        try {
            const totalSupply = await contract.totalSupply();
            for (let i = 0; i < totalSupply; i++) {
                try {
                    const tokenId = await contract.tokenByIndex(i);
                    tokenIds.push(tokenId.toString());
                } catch {}
            }
        } catch {
            // Fallback: try sequential IDs
            for (let id = 1; id <= 100; id++) {
                try {
                    const uri = await contract.tokenURI(id);
                    tokenIds.push(id.toString());
                } catch {}
            }
        }
        
        console.log(`📊 Found ${tokenIds.length} tokens`);
        if (tokenIds.length === 0) {
            console.log("❌ No tokens found!");
            rl.close();
            return;
        }
        
        // Show first few examples
        console.log("\n📝 Will set URIs:");
        tokenIds.slice(0, 3).forEach(id => {
            console.log(`  Token ${id}: ${formattedBaseURI}${id}.json`);
        });
        if (tokenIds.length > 3) {
            console.log(`  ... and ${tokenIds.length - 3} more`);
        }
        
        const confirm = await askQuestion(`\n❓ Update ${tokenIds.length} tokens? (y/N): `);
        if (confirm.toLowerCase() !== 'y') {
            console.log("❌ Cancelled");
            rl.close();
            return;
        }
        
        // Process tokens
        console.log("\n🚀 Setting URIs...");
        let success = 0;
        let failed = 0;
        
        for (const tokenId of tokenIds) {
            try {
                const newURI = `${formattedBaseURI}${tokenId}.json`;
                console.log(`⚡ Token ${tokenId}...`);
                
                const tx = await contract.setTokenURI(tokenId, newURI);
                await tx.wait();
                
                console.log(`  ✅ Success`);
                success++;
                
            } catch (error) {
                console.log(`  ❌ Failed: ${error.message.substring(0, 50)}`);
                failed++;
            }
            
            // Small delay
            if (tokenIds.indexOf(tokenId) < tokenIds.length - 1) {
                await new Promise(r => setTimeout(r, 500));
            }
        }
        
        // Summary
        console.log("\n🎉 Complete!");
        console.log(`✅ Success: ${success}`);
        console.log(`❌ Failed: ${failed}`);
        
        if (success > 0) {
            console.log("\n🔍 Sample verification:");
            const sampleId = tokenIds[0];
            try {
                const uri = await contract.tokenURI(sampleId);
                console.log(`Token ${sampleId}: ${uri}`);
            } catch {
                console.log("Verification failed");
            }
        }
        
    } catch (error) {
        console.log(`💥 Error: ${error.message}`);
    } finally {
        rl.close();
    }
}

// Run
if (require.main === module) {
    quickSetURI()
        .then(() => process.exit(0))
        .catch(error => {
            console.error(error);
            process.exit(1);
        });
}

module.exports = { quickSetURI };