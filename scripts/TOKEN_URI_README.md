# NFT Token URI Setup Scripts

Scripts for batch setting TokenURI for all existing NFT tokens in the format `{tokenId}.json`.

## Overview

These scripts help you set metadata URIs for all existing NFT tokens in your contract. The URI format will be:
- Token 1: `{baseURI}/1.json`
- Token 2: `{baseURI}/2.json`
- Token N: `{baseURI}/N.json`

## Scripts Available

### 1. 🚀 `quick-set-uri.js` (Recommended)
Fast and simple script for quick URI updates.

```bash
node scripts/quick-set-uri.js
```

**Features:**
- ✅ Streamlined interface
- ✅ Automatic token discovery
- ✅ Network selection (BSC/Current)
- ✅ Real-time progress
- ✅ Quick validation

### 2. 🔧 `set-token-uri.js` (Advanced)
Full-featured script with detailed options.

```bash
node scripts/set-token-uri.js
```

**Features:**
- ✅ Comprehensive error handling
- ✅ Batch processing (10 tokens per batch)
- ✅ Gas estimation and cost calculation
- ✅ URI verification before/after
- ✅ Detailed progress tracking
- ✅ Failed token retry tracking

### 3. 🧪 `test-uri-setup.js` (Testing)
Test script to validate functionality.

```bash
node scripts/test-uri-setup.js
```

## Quick Start

### Step 1: Prepare Your Metadata
Ensure you have JSON metadata files hosted at:
```
https://yourdomain.com/metadata/1.json
https://yourdomain.com/metadata/2.json
https://yourdomain.com/metadata/3.json
...
```

### Step 2: Run Quick Setup
```bash
node scripts/quick-set-uri.js
```

**Example interaction:**
```
⚡ Quick TokenURI Setup (tokenid.json format)
==============================================

📝 Network (1=BSC Testnet, 2=BSC Mainnet, 3=Current): 1
🌐 BSC Testnet selected
🔑 Private key (64 chars): your_private_key_here
📍 Contract address: ******************************************
🔗 Base URI (e.g., https://api.example.com/): https://yourdomain.com/metadata

🔗 Using base URI: https://yourdomain.com/metadata/

👤 Wallet: 0xYourWalletAddress
🔍 Finding tokens...
📊 Found 50 tokens

📝 Will set URIs:
  Token 1: https://yourdomain.com/metadata/1.json
  Token 2: https://yourdomain.com/metadata/2.json
  Token 3: https://yourdomain.com/metadata/3.json
  ... and 47 more

❓ Update 50 tokens? (y/N): y
```

## Supported Contract Types

### TraditionalNFT Contract
✅ Fully supported (ERC721 + ERC721URIStorage)

### Other ERC721 Contracts
✅ Must implement:
- `setTokenURI(uint256 tokenId, string memory uri)`
- `tokenURI(uint256 tokenId)` 
- `totalSupply()` (preferred)
- `tokenByIndex(uint256 index)` (preferred)
- `owner()`

## Common Base URI Examples

### IPFS
```
ipfs://QmYourHashHere/
```
Result: `ipfs://QmYourHashHere/1.json`

### HTTP/HTTPS
```
https://api.yourdomain.com/metadata/
```
Result: `https://api.yourdomain.com/metadata/1.json`

### Pinata Gateway
```
https://gateway.pinata.cloud/ipfs/QmHash/
```
Result: `https://gateway.pinata.cloud/ipfs/QmHash/1.json`

### Infura IPFS
```
https://yourproject.infura-ipfs.io/ipfs/QmHash/
```

## JSON Metadata Format

Each `{tokenId}.json` should follow this structure:

```json
{
  "name": "Your NFT Name #1",
  "description": "Description of your NFT",
  "image": "https://yourdomain.com/images/1.png",
  "attributes": [
    {
      "trait_type": "Rarity",
      "value": "Common"
    },
    {
      "trait_type": "Level",
      "value": 1
    }
  ]
}
```

## Gas Costs (Approximate)

| Network | Single URI | 10 URIs | 100 URIs |
|---------|------------|---------|-----------|
| BSC Testnet | ~$0.01 | ~$0.08 | ~$0.80 |
| BSC Mainnet | ~$0.05 | ~$0.40 | ~$4.00 |
| Ethereum | ~$5.00 | ~$40.00 | ~$400.00 |

*Costs vary with network congestion*

## Troubleshooting

### "No tokens found"
- Check contract address
- Ensure contract has minted tokens
- Verify contract implements required functions

### "Not contract owner"
- Use the private key of the contract owner
- Check ownership with contract's `owner()` function

### "Invalid contract address"
- Ensure address is valid 42-character hex
- Verify contract is deployed on selected network

### "Gas estimation failed"
- Check wallet has sufficient balance
- Try reducing batch size
- Network may be congested

### "URI already set correctly"
- Script skips tokens with correct URIs
- This saves gas and time
- Use `-f` flag to force update (advanced script only)

## Security Best Practices

⚠️ **Security Warnings:**
- Never share your private key
- Use testnet for initial testing
- Verify contract address before execution
- Start with small batches

✅ **Recommendations:**
- Test on BSC testnet first
- Keep metadata files backed up
- Use HTTPS for metadata URLs
- Monitor gas prices for optimal timing

## Advanced Usage

### Custom URI Patterns
Modify the script for custom patterns:

```javascript
// Instead of: token.newURI = `${formattedBaseURI}${tokenId}.json`
// Use custom pattern:
token.newURI = `${formattedBaseURI}metadata_${tokenId}.json`
token.newURI = `${formattedBaseURI}nft-${tokenId.padStart(4, '0')}.json`
```

### Batch Size Optimization
For large collections, adjust batch size:

```javascript
const batchSize = 5;  // Smaller for complex metadata
const batchSize = 20; // Larger for simple updates
```

### Network-Specific Settings
```javascript
// BSC optimized settings
const batchSize = 10;
const delayBetweenTx = 1000; // 1 second

// Ethereum optimized settings  
const batchSize = 5;
const delayBetweenTx = 3000; // 3 seconds
```

## Support

### Common Commands
```bash
# Quick setup
node scripts/quick-set-uri.js

# Advanced setup
node scripts/set-token-uri.js

# Test functionality
node scripts/test-uri-setup.js

# Compile contracts
npm run compile
```

### Logs and Debugging
Scripts provide detailed logs showing:
- Token discovery progress
- Gas estimation per transaction
- Success/failure status
- Final verification results

For issues, check the console output for specific error messages and refer to the troubleshooting section above.