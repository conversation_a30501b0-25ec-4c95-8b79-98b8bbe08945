const { ethers } = require("hardhat");

async function main() {
    console.log("开始部署优化后的MiningContract...");
    
    const [deployer] = await ethers.getSigners();
    console.log("部署者地址:", deployer.address);
    
    // 获取合约余额
    const balance = await ethers.provider.getBalance(deployer.address);
    console.log("部署者余额:", ethers.formatEther(balance), "BNB");
    
    // 合约地址 (需要根据实际部署的地址进行修改)
    const TOKEN_A_ADDRESS = "0x..."; // 替换为实际的TokenA地址
    const TOKEN_B_ADDRESS = "0x..."; // 替换为实际的TokenB地址
    const NFT_CONTRACT_ADDRESS = "0x..."; // 替换为实际的NFT合约地址
    const PRICE_ORACLE_ADDRESS = "0x..."; // 替换为实际的PriceOracle地址 (备用)
    
    // 部署MiningContract
    console.log("正在部署MiningContract...");
    const MiningContract = await ethers.getContractFactory("MiningContract");
    const miningContract = await MiningContract.deploy(
        TOKEN_A_ADDRESS,
        TOKEN_B_ADDRESS,
        NFT_CONTRACT_ADDRESS,
        PRICE_ORACLE_ADDRESS
    );
    
    await miningContract.waitForDeployment();
    const miningContractAddress = await miningContract.getAddress();
    console.log("MiningContract 部署地址:", miningContractAddress);
    
    // 验证合约常量
    console.log("\n验证合约配置...");
    const wbnb = await miningContract.WBNB();
    const factory = await miningContract.PANCAKE_FACTORY();
    const minLiquidity = await miningContract.MIN_LIQUIDITY();
    const maxPrice = await miningContract.MAX_REASONABLE_PRICE();
    const minPrice = await miningContract.MIN_REASONABLE_PRICE();
    
    console.log("WBNB地址:", wbnb);
    console.log("PancakeSwap Factory:", factory);
    console.log("最小流动性要求:", ethers.formatEther(minLiquidity), "BNB");
    console.log("最大合理价格:", ethers.formatEther(maxPrice), "BNB");
    console.log("最小合理价格:", ethers.formatUnits(minPrice, 18), "BNB");
    
    // 测试价格获取功能
    console.log("\n测试价格获取功能...");
    
    try {
        // 测试WBNB价格
        const wbnbPrice = await miningContract.getTokenPrice(wbnb);
        console.log("WBNB价格:", ethers.formatEther(wbnbPrice), "(应该是1.0)");
        
        // 测试TokenA价格 (如果存在交易对)
        if (TOKEN_A_ADDRESS !== "0x...") {
            try {
                const tokenAPrice = await miningContract.getTokenPrice(TOKEN_A_ADDRESS);
                console.log("TokenA价格:", ethers.formatEther(tokenAPrice), "BNB");
                
                const isReliable = await miningContract.isTokenPriceReliable(TOKEN_A_ADDRESS);
                console.log("TokenA价格可靠性:", isReliable);
            } catch (error) {
                console.log("TokenA价格获取失败:", error.message);
            }
        }
        
        // 测试TokenB价格 (如果存在交易对)
        if (TOKEN_B_ADDRESS !== "0x...") {
            try {
                const tokenBPrice = await miningContract.getTokenPrice(TOKEN_B_ADDRESS);
                console.log("TokenB价格:", ethers.formatEther(tokenBPrice), "BNB");
                
                const isReliable = await miningContract.isTokenPriceReliable(TOKEN_B_ADDRESS);
                console.log("TokenB价格可靠性:", isReliable);
            } catch (error) {
                console.log("TokenB价格获取失败:", error.message);
            }
        }
        
    } catch (error) {
        console.log("价格获取测试失败:", error.message);
    }
    
    // 输出部署总结
    console.log("\n=== 部署总结 ===");
    console.log("MiningContract地址:", miningContractAddress);
    console.log("TokenA地址:", TOKEN_A_ADDRESS);
    console.log("TokenB地址:", TOKEN_B_ADDRESS);
    console.log("NFT合约地址:", NFT_CONTRACT_ADDRESS);
    console.log("PriceOracle地址:", PRICE_ORACLE_ADDRESS);
    
    console.log("\n=== 关键特性 ===");
    console.log("✅ 直接从PancakeSwap DEX获取实时价格");
    console.log("✅ 内置价格合理性检查");
    console.log("✅ 流动性要求验证");
    console.log("✅ 攻击成本高 (需要真实销毁代币)");
    console.log("✅ 时间锁定释放机制 (180天)");
    
    console.log("\n=== 使用说明 ===");
    console.log("1. 用户调用 burnTokenAForTokenB() 销毁TokenA获得TokenB");
    console.log("2. 用户调用 burnTokenBWithNFT() 销毁TokenB获得2倍价值奖励");
    console.log("3. 价格直接从DEX获取，无需预言机更新");
    console.log("4. 奖励分6期释放，每期30天");
    console.log("5. 邀请机制可获得加速释放额度");
    
    console.log("\n部署完成！");
}

main()
    .then(() => process.exit(0))
    .catch((error) => {
        console.error("部署失败:", error);
        process.exit(1);
    });