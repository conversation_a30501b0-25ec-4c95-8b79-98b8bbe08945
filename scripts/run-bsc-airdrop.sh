#!/bin/bash

# BSC空投运行脚本

echo "🌐 BSC Network Airdrop Script"
echo "=============================="

# 检查是否提供了网络参数
if [ "$1" == "testnet" ]; then
    echo "🔥 Running on BSC Testnet"
    NETWORK="bsc_testnet"
elif [ "$1" == "mainnet" ]; then
    echo "🔥 Running on BSC Mainnet"
    NETWORK="bsc_mainnet"  
else
    echo "❓ Please specify network: testnet or mainnet"
    echo "Usage: ./run-bsc-airdrop.sh [testnet|mainnet]"
    echo ""
    echo "Examples:"
    echo "  ./run-bsc-airdrop.sh testnet   # Run on BSC Testnet"
    echo "  ./run-bsc-airdrop.sh mainnet   # Run on BSC Mainnet"
    exit 1
fi

# 检查私钥环境变量
if [ -z "$PRIVATE_KEY" ]; then
    echo "⚠️  Warning: PRIVATE_KEY environment variable not set"
    echo "You will need to input your private key in the script"
fi

echo "🚀 Starting airdrop on $NETWORK..."
echo ""

# 运行空投脚本
npx hardhat run scripts/airdrop-nft.js --network $NETWORK