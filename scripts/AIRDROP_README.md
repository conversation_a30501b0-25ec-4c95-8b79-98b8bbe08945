# NFT Airdrop Script

A comprehensive script for airdropping NFTs to randomly generated addresses or specific recipients.

## Features

- 🎯 Generate random Ethereum addresses for testing
- 🚀 Batch mint NFTs to multiple addresses
- 💰 Private key input for secure wallet management
- ⛽ Gas estimation and cost calculation
- 🔒 Input validation and error handling
- 📊 Real-time progress tracking and statistics

## Files

- `airdrop-nft.js` - Main airdrop script
- `test-airdrop.js` - Test script for validating functions
- `AIRDROP_README.md` - This documentation

## Prerequisites

1. Node.js installed
2. Hardhat environment set up
3. NFT contract deployed (TraditionalNFT or compatible ERC721)
4. Private key with owner permissions for the NFT contract
5. Sufficient ETH balance for gas fees

## Usage

### Method 1: BSC专用空投脚本 (推荐)

```bash
node scripts/bsc-airdrop.js
```

### Method 2: 通用空投脚本 (支持网络选择)

```bash
node scripts/airdrop-nft.js
```

### Method 3: Hardhat网络参数

```bash
# BSC测试网
npx hardhat run scripts/airdrop-nft.js --network bsc_testnet

# BSC主网
npx hardhat run scripts/airdrop-nft.js --network bsc_mainnet
```

### Method 4: 使用脚本快捷方式

```bash
# BSC测试网
./scripts/run-bsc-airdrop.sh testnet

# BSC主网  
./scripts/run-bsc-airdrop.sh mainnet
```

The script will prompt you for:

1. **Private Key** (64 characters): Your wallet's private key
2. **Contract Address**: Deployed NFT contract address
3. **Number of Recipients** (1-100): How many addresses to generate
4. **NFTs per Address** (1-10): Quantity of NFTs for each address

### Example Interaction

```
🚀 NFT Airdrop Script Started
=====================================

📝 Please enter your private key (64 characters): 1234567890123456789012345678901234567890123456789012345678901234
📝 Please enter NFT contract address: ******************************************
📝 How many addresses to generate for airdrop? (1-100): 10
📝 How many NFTs per address? (1-10): 2

🔧 Configuration Summary:
📍 Contract Address: ******************************************
👥 Number of Recipients: 10
🎁 NFTs per Address: 2
📊 Total NFTs to Mint: 20

❓ Do you want to proceed? (y/N): y
```

### Running Tests

```bash
node scripts/test-airdrop.js
```

This will validate:
- Random address generation
- Contract ABI compatibility  
- Gas estimation simulation
- Input validation functions

## Security Features

### Input Validation
- ✅ Private key format validation (64 characters)
- ✅ Ethereum address validation
- ✅ Numeric range validation for quantities
- ✅ Contract ownership verification

### Transaction Safety
- ✅ Gas estimation before execution
- ✅ Balance checking
- ✅ Supply limit verification
- ✅ Batch size limits (prevents gas limit issues)
- ✅ Error handling and retry logic

## How It Works

### 1. Address Generation
```javascript
function generateRandomAddresses(count) {
    const addresses = [];
    for (let i = 0; i < count; i++) {
        const randomWallet = ethers.Wallet.createRandom();
        addresses.push(randomWallet.address);
    }
    return addresses;
}
```

### 2. Batch Processing
- Processes addresses in batches of 20
- Uses `batchMintToMultiple()` for efficiency
- Fallback to individual mints if batch fails
- 2-second delay between batches

### 3. Error Handling
- Contract connection errors
- Insufficient balance detection
- Gas estimation failures
- Transaction failures with retry logic

## Contract Compatibility

The script works with contracts that implement:

```solidity
interface IAirdropCompatible {
    function mintFor(address to, uint256 quantity) external;
    function batchMintToMultiple(
        address[] calldata recipients, 
        uint256[] calldata quantities
    ) external;
    function owner() external view returns (address);
    function totalMinted() external view returns (uint256);
    function MAX_SUPPLY() external view returns (uint256);
}
```

## Configuration Options

### Batch Size
Default: 20 addresses per batch
- Modify `batchSize` variable in script
- Consider gas limits for your network

### Gas Settings  
- Automatic gas estimation
- 20% buffer added to gas limit
- Uses current network gas price

### Limits
- Max recipients: 100 (configurable)
- Max NFTs per address: 10 (configurable)
- These can be modified in the script

## Network Support

Works with any EVM-compatible network configured in Hardhat:
- Ethereum Mainnet/Testnets
- BSC Mainnet/Testnet  
- Polygon
- Arbitrum
- And others

## Troubleshooting

### Common Issues

**"Invalid private key format"**
- Ensure private key is exactly 64 characters
- Remove "0x" prefix if present

**"You are not the contract owner"**
- Use the private key of the contract owner
- Verify contract ownership with `owner()` function

**"Insufficient balance"**
- Fund the wallet with ETH for gas fees
- Check gas estimation before proceeding

**"Exceeds maximum supply"**
- Reduce number of recipients or NFTs per address
- Check remaining supply with contract

### Gas Optimization Tips

1. **Batch Size**: Larger batches are more efficient but may hit gas limits
2. **Network Choice**: Use testnets for testing to save costs
3. **Gas Price**: Monitor network congestion for optimal timing
4. **Contract Design**: Optimize your NFT contract for batch operations

## Example Output

```
🎉 Airdrop Completed!
=====================================
✅ Successful airdrops: 10/10
❌ Failed airdrops: 0/10
🎁 Total NFTs minted: 20
🏆 All airdrops completed successfully!
📊 Total minted after airdrop: 120
```

## Security Warnings

⚠️ **Never commit private keys to version control**
⚠️ **Use test networks for development**
⚠️ **Verify contract addresses before execution**
⚠️ **Test with small amounts first**

## Support

For issues or questions:
1. Check the troubleshooting section
2. Review contract compatibility requirements
3. Test on a testnet first
4. Verify all prerequisites are met