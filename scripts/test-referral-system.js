const { ethers } = require("hardhat");

async function main() {
  console.log("开始测试推荐系统功能...");

  // 获取测试账户
  const [deployer, referrer, ...users] = await ethers.getSigners();
  
  // 部署MockUSDT
  const MockUSDT = await ethers.getContractFactory("MockUSDT");
  const mockUSDT = await MockUSDT.deploy();
  await mockUSDT.waitForDeployment();
  const usdtAddress = await mockUSDT.getAddress();
  console.log(`MockUSDT部署到: ${usdtAddress}`);

  // 部署NFTPresale
  const NFTPresale = await ethers.getContractFactory("NFTPresale");
  const nftPresale = await NFTPresale.deploy(usdtAddress, deployer.address);
  await nftPresale.waitForDeployment();
  const nftPresaleAddress = await nftPresale.getAddress();
  console.log(`NFTPresale部署到: ${nftPresaleAddress}`);

  // 为所有用户铸造USDT
  const mintAmount = ethers.parseUnits("100", 6);
  for (let i = 0; i < 5; i++) {
    await mockUSDT.mint(users[i].address, mintAmount);
  }
  console.log(`为5个用户每人铸造了 ${ethers.formatUnits(mintAmount, 6)} USDT`);

  // 获取NFT价格
  const presaleInfo = await nftPresale.getPresaleInfo();
  const nftPrice = presaleInfo._nftPrice;

  // 测试推荐系统
  console.log("\n=== 测试推荐系统 ===");
  
  // 用户1-5都通过referrer购买NFT
  for (let i = 0; i < 5; i++) {
    const quantity = i + 1; // 用户i购买i+1个NFT
    const totalCost = nftPrice * BigInt(quantity);
    
    await mockUSDT.connect(users[i]).approve(nftPresaleAddress, totalCost);
    await nftPresale.connect(users[i])["buyNFT(address,uint256)"](referrer.address, quantity);
    
    console.log(`用户${i+1}购买了${quantity}个NFT，推荐人: ${referrer.address}`);
  }

  // 检查推荐人信息
  console.log("\n=== 推荐人信息 ===");
  const referrerInfo = await nftPresale.getUserInfo(referrer.address);
  console.log(`推荐人购买数量: ${referrerInfo.purchased}`);
  console.log(`推荐人推荐数量: ${referrerInfo.refereeCount}`);

  // 获取完整推荐列表
  console.log("\n=== 完整推荐列表 ===");
  const allReferees = await nftPresale.getReferees(referrer.address);
  console.log(`推荐人共推荐了${allReferees.length}个用户:`);
  allReferees.forEach((referee, index) => {
    console.log(`  ${index + 1}. ${referee}`);
  });

  // 测试分页功能
  console.log("\n=== 测试分页功能 ===");
  const pageSize = 2;
  const totalPages = Math.ceil(allReferees.length / pageSize);
  
  for (let page = 0; page < totalPages; page++) {
    const start = page * pageSize;
    const pagedReferees = await nftPresale["getReferees(address,uint256,uint256)"](
      referrer.address, 
      start, 
      pageSize
    );
    console.log(`第${page + 1}页 (起始位置${start}，限制${pageSize}个): ${pagedReferees.join(", ")}`);
  }

  // 获取推荐详情
  console.log("\n=== 推荐详情 ===");
  const referralDetails = await nftPresale.getReferralDetails(referrer.address);
  console.log(`推荐总人数: ${referralDetails.totalReferees}`);
  console.log(`被推荐人总购买量: ${referralDetails.totalPurchasedByReferees}`);
  console.log("详细信息:");
  
  for (let i = 0; i < referralDetails.referees.length; i++) {
    const address = referralDetails.referees[i];
    const amount = referralDetails.purchasedAmounts[i];
    const userInfo = await nftPresale.getUserInfo(address);
    console.log(`  ${i + 1}. ${address} - 购买${amount}个NFT, 推荐人: ${userInfo.referrer}`);
  }

  // 检查总销售情况
  console.log("\n=== 总销售情况 ===");
  const finalInfo = await nftPresale.getPresaleInfo();
  console.log(`总售出: ${finalInfo._totalSold}`);
  console.log(`总参与者: ${finalInfo._totalParticipants}`);
  console.log(`剩余供应: ${await nftPresale.getRemainingSupply()}`);

  // 检查收款钱包余额
  const deployerBalance = await mockUSDT.balanceOf(deployer.address);
  console.log(`收款钱包余额: ${ethers.formatUnits(deployerBalance, 6)} USDT`);

  console.log("\n🎉 推荐系统测试完成！");
}

if (require.main === module) {
  main()
    .then(() => process.exit(0))
    .catch((error) => {
      console.error(error);
      process.exit(1);
    });
}

module.exports = { main };