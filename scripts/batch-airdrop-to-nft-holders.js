const { ethers } = require("hardhat");
const readline = require('readline');

// 创建命令行接口
const rl = readline.createInterface({
    input: process.stdin,
    output: process.stdout
});

// 封装问题函数
function question(query) {
    return new Promise(resolve => rl.question(query, resolve));
}

// NFT合约ABI（只需要获取持有者的函数）
const NFT_ABI = [
    "function totalSupply() external view returns (uint256)",
    "function ownerOf(uint256 tokenId) external view returns (address)",
    "function tokenOfOwnerByIndex(address owner, uint256 index) external view returns (uint256)",
    "function balanceOf(address owner) external view returns (uint256)"
];

// ERC20代币ABI
const TOKEN_ABI = [
    "function transfer(address to, uint256 amount) external returns (bool)",
    "function balanceOf(address account) external view returns (uint256)",
    "function decimals() external view returns (uint8)",
    "function symbol() external view returns (string)",
    "function name() external view returns (string)"
];

async function main() {
    console.log("🎯 NFT持有者批量空投脚本");
    console.log("================================");
    
    try {
        // 获取用户输入
        const privateKey = await question("请输入发送者私钥: ");
        if (!privateKey || privateKey.length !== 64) {
            throw new Error("私钥格式不正确，应为64位十六进制字符串");
        }

        const nftAddress = await question("请输入NFT合约地址: ");
        if (!ethers.isAddress(nftAddress)) {
            throw new Error("NFT合约地址格式不正确");
        }

        const tokenAddress = await question("请输入代币合约地址: ");
        if (!ethers.isAddress(tokenAddress)) {
            throw new Error("代币合约地址格式不正确");
        }

        const tokenAmountInput = await question("请输入每个NFT持有者获得的代币数量 (默认3000): ");
        const tokenAmount = tokenAmountInput.trim() === '' ? '3000' : tokenAmountInput;
        
        if (isNaN(parseFloat(tokenAmount)) || parseFloat(tokenAmount) <= 0) {
            throw new Error("代币数量必须是正数");
        }

        console.log("\n📋 配置信息:");
        console.log("NFT合约地址:", nftAddress);
        console.log("代币合约地址:", tokenAddress);
        console.log("每人代币数量:", tokenAmount);

        const confirm = await question("\n确认执行空投? (y/N): ");
        if (confirm.toLowerCase() !== 'y' && confirm.toLowerCase() !== 'yes') {
            console.log("❌ 操作已取消");
            process.exit(0);
        }

        // 创建钱包和provider
        const provider = ethers.provider;
        const wallet = new ethers.Wallet(privateKey, provider);
        
        console.log("\n👤 发送者地址:", wallet.address);
        
        // 检查网络
        const network = await provider.getNetwork();
        console.log("🌐 网络:", network.name, "Chain ID:", network.chainId.toString());
        
        // 检查发送者余额
        const balance = await provider.getBalance(wallet.address);
        console.log("💰 发送者ETH余额:", ethers.formatEther(balance));
        
        // 连接合约
        const nftContract = new ethers.Contract(nftAddress, NFT_ABI, provider);
        const tokenContract = new ethers.Contract(tokenAddress, TOKEN_ABI, wallet);
        
        // 获取代币信息
        console.log("\n📊 获取代币信息...");
        const tokenName = await tokenContract.name();
        const tokenSymbol = await tokenContract.symbol();
        const tokenDecimals = await tokenContract.decimals();
        const tokenAmountWei = ethers.parseUnits(tokenAmount, tokenDecimals);
        
        console.log("代币名称:", tokenName);
        console.log("代币符号:", tokenSymbol);
        console.log("代币精度:", tokenDecimals.toString());
        
        // 检查发送者代币余额
        const senderTokenBalance = await tokenContract.balanceOf(wallet.address);
        console.log("发送者代币余额:", ethers.formatUnits(senderTokenBalance, tokenDecimals), tokenSymbol);
        
        // 获取NFT总供应量
        console.log("\n🔍 分析NFT持有情况...");
        const totalSupply = await nftContract.totalSupply();
        console.log("NFT总供应量:", totalSupply.toString());
        
        if (totalSupply === 0n) {
            throw new Error("NFT总供应量为0，无法执行空投");
        }
        
        // 获取所有NFT持有者（去重）
        const holders = new Set();
        const holderTokens = new Map(); // 记录每个持有者的NFT数量
        
        console.log("正在扫描NFT持有者...");
        for (let i = 1; i <= totalSupply; i++) {
            try {
                const owner = await nftContract.ownerOf(i);
                if (owner !== ethers.ZeroAddress) {
                    holders.add(owner);
                    holderTokens.set(owner, (holderTokens.get(owner) || 0) + 1);
                }
                
                // 显示进度
                if (i % 100 === 0 || i === totalSupply) {
                    process.stdout.write(`\r进度: ${i}/${totalSupply} (${Math.round(i/Number(totalSupply)*100)}%)`);
                }
            } catch (error) {
                console.log(`\n⚠️  NFT #${i} 查询失败:`, error.message);
            }
        }
        
        console.log(`\n✅ 扫描完成！找到 ${holders.size} 个唯一持有者`);
        
        // 显示持有者统计
        console.log("\n📈 持有者统计:");
        const sortedHolders = Array.from(holderTokens.entries()).sort((a, b) => b[1] - a[1]);
        sortedHolders.slice(0, 5).forEach(([holder, count]) => {
            console.log(`  ${holder}: ${count} NFTs`);
        });
        if (sortedHolders.length > 5) {
            console.log(`  ... 还有 ${sortedHolders.length - 5} 个持有者`);
        }
        
        // 计算总需要的代币数量
        const totalTokensNeeded = tokenAmountWei * BigInt(holders.size);
        console.log(`\n💎 总计需要代币: ${ethers.formatUnits(totalTokensNeeded, tokenDecimals)} ${tokenSymbol}`);
        
        // 检查余额是否足够
        if (senderTokenBalance < totalTokensNeeded) {
            throw new Error(`代币余额不足！需要 ${ethers.formatUnits(totalTokensNeeded, tokenDecimals)} ${tokenSymbol}，但只有 ${ethers.formatUnits(senderTokenBalance, tokenDecimals)} ${tokenSymbol}`);
        }
        
        // 最终确认
        const finalConfirm = await question(`\n🚀 即将向 ${holders.size} 个地址发送代币，每个地址 ${tokenAmount} ${tokenSymbol}，总计 ${ethers.formatUnits(totalTokensNeeded, tokenDecimals)} ${tokenSymbol}。\n确认执行? (y/N): `);
        if (finalConfirm.toLowerCase() !== 'y' && finalConfirm.toLowerCase() !== 'yes') {
            console.log("❌ 操作已取消");
            process.exit(0);
        }
        
        // 执行批量转账
        console.log("\n🔄 开始执行批量转账...");
        const holdersArray = Array.from(holders);
        const batchSize = 50; // 每批处理50个地址
        let successCount = 0;
        let failCount = 0;
        const failedAddresses = [];
        
        for (let i = 0; i < holdersArray.length; i += batchSize) {
            const batch = holdersArray.slice(i, i + batchSize);
            console.log(`\n📦 处理批次 ${Math.floor(i/batchSize) + 1}/${Math.ceil(holdersArray.length/batchSize)} (${batch.length} 个地址)`);
            
            // 并行处理当前批次
            const promises = batch.map(async (holder, index) => {
                try {
                    const tx = await tokenContract.transfer(holder, tokenAmountWei);
                    await tx.wait();
                    console.log(`✅ ${i + index + 1}/${holdersArray.length}: ${holder} - ${tokenAmount} ${tokenSymbol}`);
                    return { success: true, address: holder };
                } catch (error) {
                    console.log(`❌ ${i + index + 1}/${holdersArray.length}: ${holder} - 失败: ${error.message}`);
                    return { success: false, address: holder, error: error.message };
                }
            });
            
            const results = await Promise.all(promises);
            
            // 统计结果
            results.forEach(result => {
                if (result.success) {
                    successCount++;
                } else {
                    failCount++;
                    failedAddresses.push({ address: result.address, error: result.error });
                }
            });
            
            // 批次间延迟，避免网络拥堵
            if (i + batchSize < holdersArray.length) {
                console.log("⏳ 等待3秒后处理下一批次...");
                await new Promise(resolve => setTimeout(resolve, 3000));
            }
        }
        
        // 输出最终结果
        console.log("\n🎉 批量转账完成！");
        console.log("================================");
        console.log(`✅ 成功: ${successCount} 个地址`);
        console.log(`❌ 失败: ${failCount} 个地址`);
        console.log(`💰 总计发送: ${ethers.formatUnits(tokenAmountWei * BigInt(successCount), tokenDecimals)} ${tokenSymbol}`);
        
        if (failedAddresses.length > 0) {
            console.log("\n❌ 失败的地址:");
            failedAddresses.forEach(({ address, error }) => {
                console.log(`  ${address}: ${error}`);
            });
        }
        
        // 保存结果到文件
        const fs = require('fs');
        const path = require('path');
        
        const resultData = {
            timestamp: new Date().toISOString(),
            network: network.name,
            chainId: network.chainId.toString(),
            sender: wallet.address,
            nftContract: nftAddress,
            tokenContract: tokenAddress,
            tokenAmount: tokenAmount,
            tokenSymbol: tokenSymbol,
            totalHolders: holders.size,
            successCount: successCount,
            failCount: failCount,
            failedAddresses: failedAddresses,
            totalTokensSent: ethers.formatUnits(tokenAmountWei * BigInt(successCount), tokenDecimals)
        };
        
        const resultsDir = path.join(__dirname, '..', 'airdrop-results');
        if (!fs.existsSync(resultsDir)) {
            fs.mkdirSync(resultsDir, { recursive: true });
        }
        
        const resultFile = path.join(resultsDir, `nft-airdrop-${Date.now()}.json`);
        fs.writeFileSync(resultFile, JSON.stringify(resultData, null, 2));
        
        console.log(`\n📄 结果已保存到: ${resultFile}`);
        
    } catch (error) {
        console.error("\n❌ 执行失败:", error.message);
        process.exit(1);
    } finally {
        rl.close();
    }
}

// 错误处理
main()
    .then(() => {
        console.log("\n✅ 脚本执行完成");
        process.exit(0);
    })
    .catch((error) => {
        console.error("\n❌ 脚本执行失败:", error);
        process.exit(1);
    });

module.exports = main;
