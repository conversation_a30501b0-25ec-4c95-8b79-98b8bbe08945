const { ethers } = require("hardhat");

async function main() {
  console.log("🚀 开始USDT机制验证...\n");

  const [deployer] = await ethers.getSigners();
  console.log("部署账户:", deployer.address);
  console.log(
    "账户余额:",
    ethers.formatEther(await deployer.provider.getBalance(deployer.address)),
    "ETH\n"
  );

  // 部署Mock代币用于测试
  console.log("📦 部署测试代币...");
  const MockERC20 = await ethers.getContractFactory("MockERC20");
  const tokenA = await MockERC20.deploy(
    "TokenA",
    "TKA",
    ethers.parseEther("1000000")
  );
  const tokenB = await MockERC20.deploy(
    "TokenB",
    "TKB",
    ethers.parseEther("1000000")
  );
  console.log("TokenA部署地址:", await tokenA.getAddress());
  console.log("TokenB部署地址:", await tokenB.getAddress());

  // 部署Mock NFT
  const MockERC721 = await ethers.getContractFactory("MockERC721");
  const nftContract = await MockERC721.deploy("TestNFT", "TNFT");
  console.log("NFT合约地址:", await nftContract.getAddress());

  // 部署MiningContract
  console.log("\n🏗️  部署MiningContract...");
  const MiningContract = await ethers.getContractFactory("MiningContract");
  const miningContract = await MiningContract.deploy(
    await tokenA.getAddress(),
    await tokenB.getAddress(),
    await nftContract.getAddress()
  );

  console.log("✅ MiningContract部署地址:", await miningContract.getAddress());

  // 验证USDT配置
  console.log("\n🔍 验证USDT机制配置...");
  const usdtAddress = await miningContract.USDT();
  const routerAddress = await miningContract.PANCAKE_ROUTER();
  const factoryAddress = await miningContract.PANCAKE_FACTORY();

  console.log("USDT地址:", usdtAddress);
  console.log("Router地址:", routerAddress);
  console.log("Factory地址:", factoryAddress);

  // 验证价格保护参数
  const minLiquidity = await miningContract.MIN_LIQUIDITY();
  const maxPrice = await miningContract.MAX_REASONABLE_PRICE();
  const minPrice = await miningContract.MIN_REASONABLE_PRICE();

  console.log("\n💰 价格保护参数:");
  console.log("最小流动性:", ethers.formatEther(minLiquidity), "USDT");
  console.log("最大合理价格:", ethers.formatEther(maxPrice), "USDT");
  console.log("最小合理价格:", ethers.formatUnits(minPrice, 18), "USDT");

  // 验证USDT价格获取
  console.log("\n💱 验证USDT价格功能...");
  try {
    const usdtPrice = await miningContract.getTokenPrice(usdtAddress);
    console.log("USDT基准价格:", ethers.formatEther(usdtPrice));

    const isReliable = await miningContract.isTokenPriceReliable(usdtAddress);
    console.log("USDT价格可靠性:", isReliable);
  } catch (error) {
    console.log("价格功能测试:", error.message);
  }

  // 验证查询功能
  console.log("\n📊 验证查询功能...");
  const [count1, count2] = await miningContract.getUserMiningRecordCount(
    deployer.address
  );
  console.log(
    "用户挖矿记录数量 - 机制一:",
    count1.toString(),
    "机制二:",
    count2.toString()
  );

  const inviteInfo = await miningContract.getUserInviteInfo(deployer.address);
  console.log(
    "邀请信息 - 邀请人:",
    inviteInfo.inviter,
    "总邀请数:",
    inviteInfo.totalInvited.toString()
  );

  // 验证全局统计
  const totalBurnedA = await miningContract.totalBurnedTokenA();
  const totalBurnedB = await miningContract.totalBurnedTokenB();
  const totalReleased = await miningContract.totalReleasedTokenB();

  console.log("\n📈 全局统计:");
  console.log("总销毁TokenA:", ethers.formatEther(totalBurnedA));
  console.log("总销毁TokenB:", ethers.formatEther(totalBurnedB));
  console.log("总释放TokenB:", ethers.formatEther(totalReleased));

  // 检查新函数是否可调用
  console.log("\n🔧 验证新函数可用性...");
  try {
    // 这会失败因为没有USDT余额，但证明函数存在
    await miningContract.buyAndBurnTokenAWithUSDT.staticCall(
      ethers.parseUnits("100", 18),
      ethers.ZeroAddress
    );
  } catch (error) {
    if (error.message.includes("Insufficient USDT balance")) {
      console.log("✅ buyAndBurnTokenAWithUSDT 函数正常 (预期错误: 余额不足)");
    } else {
      console.log("⚠️  buyAndBurnTokenAWithUSDT 函数错误:", error.message);
    }
  }

  try {
    await miningContract.previewUSDTToTokenA.staticCall(
      ethers.parseUnits("100", 18)
    );
    console.log("✅ previewUSDTToTokenA 函数可调用");
  } catch (error) {
    console.log("⚠️  previewUSDTToTokenA 函数错误:", error.message);
  }

  console.log("\n🎉 USDT机制验证完成!");
  console.log("📋 总结:");
  console.log("- ✅ 合约部署成功");
  console.log("- ✅ USDT配置正确");
  console.log("- ✅ 价格保护参数正确");
  console.log("- ✅ 查询功能正常");
  console.log("- ✅ 新函数可用");
  console.log("\n⚠️  注意: DEX交互功能需要在有真实交易对的环境中测试");
}

main()
  .then(() => process.exit(0))
  .catch((error) => {
    console.error("❌ 验证失败:", error);
    process.exit(1);
  });
