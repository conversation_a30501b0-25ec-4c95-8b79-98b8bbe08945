const { ethers } = require("hardhat");

async function main() {
    console.log("开始部署修复后的USDT挖矿合约...");
    
    const [deployer] = await ethers.getSigners();
    console.log("部署者地址:", deployer.address);
    
    // 获取合约余额
    const balance = await ethers.provider.getBalance(deployer.address);
    console.log("部署者余额:", ethers.formatEther(balance), "BNB");
    
    // 实际的合约地址
    const TOKEN_A_ADDRESS = "******************************************"; // GRAT Token
    const TOKEN_B_ADDRESS = "******************************************"; // TIPS Token  
    const NFT_CONTRACT_ADDRESS = "******************************************"; // MinerNFT
    
    // 部署MiningContract
    console.log("正在部署修复后的MiningContract...");
    const MiningContract = await ethers.getContractFactory("MiningContract");
    const miningContract = await MiningContract.deploy(
        TOKEN_A_ADDRESS,
        TOKEN_B_ADDRESS,
        NFT_CONTRACT_ADDRESS
    );
    
    await miningContract.waitForDeployment();
    const miningContractAddress = await miningContract.getAddress();
    console.log("MiningContract 部署地址:", miningContractAddress);
    
    // 验证合约配置
    console.log("\n验证合约配置...");
    const usdtAddress = await miningContract.USDT();
    const routerAddress = await miningContract.PANCAKE_ROUTER();
    const factoryAddress = await miningContract.PANCAKE_FACTORY();
    const minLiquidity = await miningContract.MIN_LIQUIDITY();
    
    console.log("USDT地址:", usdtAddress);
    console.log("PancakeSwap Router:", routerAddress);
    console.log("PancakeSwap Factory:", factoryAddress);
    console.log("最小流动性要求:", ethers.formatEther(minLiquidity), "USDT");
    
    // 测试价格获取功能
    console.log("\n测试价格获取功能...");
    
    try {
        // 测试USDT价格
        const usdtPrice = await miningContract.getTokenPrice(usdtAddress);
        console.log("USDT价格:", ethers.formatEther(usdtPrice), "(应该是1.0)");
        
        // 测试TokenA价格和流动性
        try {
            const tokenAPrice = await miningContract.getTokenPrice(TOKEN_A_ADDRESS);
            console.log("TokenA价格:", ethers.formatEther(tokenAPrice), "USDT");
            
            const isReliable = await miningContract.isTokenPriceReliable(TOKEN_A_ADDRESS);
            console.log("TokenA价格可靠性:", isReliable);
            
            // 测试预览功能
            const previewAmount = ethers.parseUnits("10", 18); // 10 USDT
            const previewResult = await miningContract.previewUSDTToTokenA(previewAmount);
            console.log("10 USDT 预览可购买 TokenA:", ethers.formatEther(previewResult));
            
        } catch (error) {
            console.log("TokenA价格获取失败:", error.message);
        }
        
        // 测试TokenB价格
        try {
            const tokenBPrice = await miningContract.getTokenPrice(TOKEN_B_ADDRESS);
            console.log("TokenB价格:", ethers.formatEther(tokenBPrice), "USDT");
            
            const isReliable = await miningContract.isTokenPriceReliable(TOKEN_B_ADDRESS);
            console.log("TokenB价格可靠性:", isReliable);
        } catch (error) {
            console.log("TokenB价格获取失败:", error.message);
        }
        
    } catch (error) {
        console.log("价格获取测试失败:", error.message);
    }
    
    // 输出部署总结
    console.log("\n=== 部署总结 ===");
    console.log("MiningContract地址:", miningContractAddress);
    console.log("TokenA地址:", TOKEN_A_ADDRESS);
    console.log("TokenB地址:", TOKEN_B_ADDRESS);
    console.log("NFT合约地址:", NFT_CONTRACT_ADDRESS);
    console.log("USDT地址:", usdtAddress);
    
    console.log("\n=== 修复内容 ===");
    console.log("✅ USDT地址已修正为:", usdtAddress);
    console.log("✅ 添加5%滑点保护");
    console.log("✅ 设置5分钟交易超时");
    console.log("✅ DEX兑换逻辑已优化");
    
    console.log("\n=== 功能特性 ===");
    console.log("✅ USDT直接购买TokenA并销毁");
    console.log("✅ 获得2倍USDT价值的TIPS奖励");
    console.log("✅ 分6期释放，每期30天");
    console.log("✅ 邀请机制获得加速释放");
    console.log("✅ 实时DEX价格和流动性检查");
    
    console.log("\n=== 前端配置更新 ===");
    console.log("请更新 dapp/src/utils/constants.js:");
    console.log(`MiningContract: "${miningContractAddress}",`);
    
    console.log("\n部署完成！");
}

main()
    .then(() => process.exit(0))
    .catch((error) => {
        console.error("部署失败:", error);
        process.exit(1);
    });