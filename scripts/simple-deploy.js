const { ethers } = require("hardhat");

async function main() {
  console.log("开始部署DeFi生态系统合约...");
  
  const [deployer] = await ethers.getSigners();
  console.log(`部署账户: ${deployer.address}`);
  
  // 使用deployer地址作为营销钱包（测试用）
  const marketingWallet = deployer.address;
  
  try {
    // 1. 部署PriceOracle
    console.log("\n1. 部署PriceOracle...");
    const PriceOracle = await ethers.getContractFactory("PriceOracle");
    const priceOracle = await PriceOracle.deploy();
    console.log(`✅ PriceOracle: ${await priceOracle.getAddress()}`);
    
    // 2. 部署TokenA
    console.log("\n2. 部署TokenA...");
    const TokenA = await ethers.getContractFactory("TokenA");
    const tokenA = await TokenA.deploy(
      marketingWallet,
      deployer.address, // pancakeSwapRouter占位符
      deployer.address  // wbnbAddress占位符
    );
    console.log(`✅ TokenA: ${await tokenA.getAddress()}`);
    
    // 3. 部署TokenB
    console.log("\n3. 部署TokenB...");
    const TokenB = await ethers.getContractFactory("TokenB");
    const tokenB = await TokenB.deploy(
      marketingWallet,
      deployer.address, // pancakeSwapRouter占位符
      deployer.address  // wbnbAddress占位符
    );
    console.log(`✅ TokenB: ${await tokenB.getAddress()}`);
    
    // 4. 部署MinerNFT
    console.log("\n4. 部署MinerNFT...");
    const MinerNFT = await ethers.getContractFactory("MinerNFT");
    const minerNFT = await MinerNFT.deploy(
      "MinerNFT",
      "MNFT",
      await tokenA.getAddress()
    );
    console.log(`✅ MinerNFT: ${await minerNFT.getAddress()}`);
    
    // 5. 部署MiningContract
    console.log("\n5. 部署MiningContract...");
    const MiningContract = await ethers.getContractFactory("MiningContract");
    const miningContract = await MiningContract.deploy(
      await tokenA.getAddress(),
      await tokenB.getAddress(),
      await minerNFT.getAddress(),
      await priceOracle.getAddress()
    );
    console.log(`✅ MiningContract: ${await miningContract.getAddress()}`);
    
    console.log("\n6. 配置合约关系...");
    
    // 配置TokenA NFT合约
    await tokenA.setNftContract(await minerNFT.getAddress());
    console.log("✅ TokenA NFT合约地址设置完成");
    
    // 配置TokenB铸造权限
    await tokenB.addMinter(await miningContract.getAddress());
    console.log("✅ TokenB铸造权限设置完成");
    
    // 配置税收豁免
    await tokenA.setTaxExempt(await miningContract.getAddress(), true);
    await tokenB.setTaxExempt(await miningContract.getAddress(), true);
    console.log("✅ 税收豁免设置完成");
    
    // 向NFT合约转入TokenA
    const nftTokenAmount = ethers.parseEther("*********"); // 2.1亿
    await tokenA.transfer(await minerNFT.getAddress(), nftTokenAmount);
    console.log("✅ TokenA转入NFT合约完成");
    
    // 设置测试价格
    await priceOracle.setManualPrice(await tokenA.getAddress(), ethers.parseEther("0.001"));
    await priceOracle.setManualPrice(await tokenB.getAddress(), ethers.parseEther("0.0001"));
    console.log("✅ 测试价格设置完成");
    
    console.log("\n🎉 部署完成！");
    console.log("=".repeat(50));
    console.log(`PriceOracle:    ${await priceOracle.getAddress()}`);
    console.log(`TokenA:         ${await tokenA.getAddress()}`);
    console.log(`TokenB:         ${await tokenB.getAddress()}`);
    console.log(`MinerNFT:       ${await minerNFT.getAddress()}`);
    console.log(`MiningContract: ${await miningContract.getAddress()}`);
    console.log("=".repeat(50));
    
    // 验证部署
    console.log("\n📊 验证部署状态...");
    console.log(`TokenA总供应量: ${ethers.formatEther(await tokenA.totalSupply())} TKA`);
    console.log(`TokenB总供应量: ${ethers.formatEther(await tokenB.totalSupply())} TKB`);
    console.log(`NFT最大供应量: ${await minerNFT.MAX_SUPPLY()}`);
    console.log(`部署者TokenA余额: ${ethers.formatEther(await tokenA.balanceOf(deployer.address))} TKA`);
    console.log(`NFT合约TokenA余额: ${ethers.formatEther(await tokenA.balanceOf(await minerNFT.getAddress()))} TKA`);
    
  } catch (error) {
    console.error("\n❌ 部署失败:");
    console.error(error);
    process.exit(1);
  }
}

if (require.main === module) {
  main()
    .then(() => process.exit(0))
    .catch((error) => {
      console.error(error);
      process.exit(1);
    });
}

module.exports = { main };