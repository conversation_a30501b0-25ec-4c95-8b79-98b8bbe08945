const { ethers } = require("hardhat");

async function main() {
  console.log("开始测试NFTPresale合约功能...");

  // 获取部署信息
  const deploymentInfo = require("../deployments/hardhat-nft-presale-deployment.json");
  const usdtAddress = deploymentInfo.contracts.MockUSDT.address;
  const nftPresaleAddress = deploymentInfo.contracts.NFTPresale.address;

  console.log(`USDT地址: ${usdtAddress}`);
  console.log(`NFTPresale地址: ${nftPresaleAddress}`);

  // 获取合约实例
  const usdt = await ethers.getContractAt("MockUSDT", usdtAddress);
  const nftPresale = await ethers.getContractAt("NFTPresale", nftPresaleAddress);

  // 获取测试账户
  const [deployer, user1, user2] = await ethers.getSigners();
  console.log(`部署者: ${deployer.address}`);
  console.log(`用户1: ${user1.address}`);
  console.log(`用户2: ${user2.address}`);

  // 1. 检查预售信息
  console.log("\n=== 预售信息 ===");
  const presaleInfo = await nftPresale.getPresaleInfo();
  console.log(`总售出: ${presaleInfo._totalSold}`);
  console.log(`总参与者: ${presaleInfo._totalParticipants}`);
  console.log(`最大供应量: ${presaleInfo._maxSupply}`);
  console.log(`NFT价格: ${ethers.formatUnits(presaleInfo._nftPrice, 6)} USDT`);
  console.log(`预售状态: ${presaleInfo._active ? "活跃" : "未激活"}`);

  // 2. 为用户铸造USDT
  console.log("\n=== 为用户铸造USDT ===");
  const mintAmount = ethers.parseUnits("1000", 6); // 1000 USDT
  await usdt.mint(user1.address, mintAmount);
  await usdt.mint(user2.address, mintAmount);
  console.log(`为用户1铸造: ${ethers.formatUnits(mintAmount, 6)} USDT`);
  console.log(`为用户2铸造: ${ethers.formatUnits(mintAmount, 6)} USDT`);

  // 3. 用户1授权并购买NFT
  console.log("\n=== 用户1购买NFT ===");
  const quantity1 = 3;
  const totalCost1 = presaleInfo._nftPrice * BigInt(quantity1);
  
  await usdt.connect(user1).approve(nftPresaleAddress, totalCost1);
  console.log(`用户1授权: ${ethers.formatUnits(totalCost1, 6)} USDT`);
  
  // 等待预售开始
  console.log("等待预售开始...");
  await new Promise(resolve => setTimeout(resolve, 5000));
  
  try {
    await nftPresale.connect(user1).buyNFT(ethers.ZeroAddress, quantity1);
    console.log(`用户1成功购买 ${quantity1} 个NFT`);
  } catch (error) {
    console.log(`用户1购买失败: ${error.message}`);
  }

  // 4. 用户2带推荐人购买NFT
  console.log("\n=== 用户2带推荐人购买NFT ===");
  const quantity2 = 2;
  const totalCost2 = presaleInfo._nftPrice * BigInt(quantity2);
  
  await usdt.connect(user2).approve(nftPresaleAddress, totalCost2);
  console.log(`用户2授权: ${ethers.formatUnits(totalCost2, 6)} USDT`);
  
  try {
    await nftPresale.connect(user2).buyNFT(user1.address, quantity2);
    console.log(`用户2成功购买 ${quantity2} 个NFT，推荐人: ${user1.address}`);
  } catch (error) {
    console.log(`用户2购买失败: ${error.message}`);
  }

  // 5. 检查最新状态
  console.log("\n=== 最新状态 ===");
  const updatedInfo = await nftPresale.getPresaleInfo();
  console.log(`总售出: ${updatedInfo._totalSold}`);
  console.log(`总参与者: ${updatedInfo._totalParticipants}`);
  console.log(`剩余供应: ${await nftPresale.getRemainingSupply()}`);

  // 6. 检查用户信息
  console.log("\n=== 用户信息 ===");
  const user1Info = await nftPresale.getUserInfo(user1.address);
  console.log(`用户1购买数量: ${user1Info.purchased}`);
  console.log(`用户1推荐人: ${user1Info.referrer}`);
  console.log(`用户1推荐数量: ${user1Info.refereeCount}`);

  const user2Info = await nftPresale.getUserInfo(user2.address);
  console.log(`用户2购买数量: ${user2Info.purchased}`);
  console.log(`用户2推荐人: ${user2Info.referrer}`);
  console.log(`用户2推荐数量: ${user2Info.refereeCount}`);

  // 7. 检查收款钱包余额
  console.log("\n=== 收款钱包余额 ===");
  const treasuryWallet = await nftPresale.getTreasuryWallet();
  const treasuryBalance = await usdt.balanceOf(treasuryWallet);
  console.log(`收款钱包: ${treasuryWallet}`);
  console.log(`收款钱包余额: ${ethers.formatUnits(treasuryBalance, 6)} USDT`);

  // 8. 检查合约余额（应该为0，因为USDT直接转到收款钱包）
  console.log("\n=== 合约余额 ===");
  const contractBalance = await nftPresale.getContractBalance();
  console.log(`合约余额: ${ethers.formatUnits(contractBalance, 6)} USDT`);

  console.log("\n🎉 NFTPresale合约功能测试完成!");
}

if (require.main === module) {
  main()
    .then(() => process.exit(0))
    .catch((error) => {
      console.error(error);
      process.exit(1);
    });
}

module.exports = { main };