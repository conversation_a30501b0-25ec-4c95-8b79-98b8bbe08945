const { ethers, network } = require("hardhat");
const fs = require("fs");
const path = require("path");

// 部署配置
const DEPLOYMENT_CONFIG = {
  // BSC主网配置
  bsc_mainnet: {
    pancakeSwapRouter: "******************************************",
    wbnbAddress: "******************************************",
    marketingWallet:
      process.env.MARKETING_WALLET ||
      "******************************************",
  },
  // BSC测试网配置
  bsc_testnet: {
    pancakeSwapRouter: "******************************************",
    wbnbAddress: "******************************************",
    marketingWallet:
      process.env.MARKETING_WALLET ||
      "******************************************",
  },
  // 本地测试配置
  hardhat: {
    pancakeSwapRouter: "******************************************", // 占位符
    wbnbAddress: "******************************************", // 占位符
    marketingWallet: "******************************************", // 占位符
  },
};

// 验证配置
function validateConfig(config) {
  if (
    !config.marketingWallet ||
    config.marketingWallet === "******************************************"
  ) {
    throw new Error("请在.env文件中设置有效的MARKETING_WALLET地址");
  }
}

// 等待交易确认
async function waitForConfirmation(tx, confirmations = 2) {
  console.log(`等待交易确认: ${tx.hash}`);
  const receipt = await tx.wait(confirmations);
  console.log(`交易已确认, Gas used: ${receipt.gasUsed.toString()}`);
  return receipt;
}

// 验证合约代码
async function verifyContract(contractAddress, constructorArgs, contractName) {
  if (network.name === "hardhat") {
    console.log("跳过本地网络的合约验证");
    return;
  }

  try {
    console.log(`\n验证合约 ${contractName}: ${contractAddress}`);
    await hre.run("verify:verify", {
      address: contractAddress,
      constructorArguments: constructorArgs,
    });
    console.log(`✅ ${contractName} 验证成功`);
  } catch (error) {
    console.log(`❌ ${contractName} 验证失败:`, error.message);
  }
}

// 保存部署信息
function saveDeploymentInfo(deploymentInfo) {
  const deploymentDir = path.join(__dirname, "../deployments");
  if (!fs.existsSync(deploymentDir)) {
    fs.mkdirSync(deploymentDir, { recursive: true });
  }

  const fileName = `${network.name}-deployment.json`;
  const filePath = path.join(deploymentDir, fileName);

  fs.writeFileSync(filePath, JSON.stringify(deploymentInfo, null, 2));
  console.log(`\n部署信息已保存到: ${filePath}`);
}

// 主部署函数
async function main() {
  console.log("=".repeat(60));
  console.log("开始部署DeFi生态系统合约");
  console.log("=".repeat(60));

  // 获取部署者账户
  const [deployer] = await ethers.getSigners();
  console.log(`\n部署账户: ${deployer.address}`);
  console.log(
    `账户余额: ${ethers.formatEther(
      await deployer.provider.getBalance(deployer.address)
    )} BNB`
  );

  // 获取网络配置
  const config = DEPLOYMENT_CONFIG[network.name];
  if (!config) {
    throw new Error(`不支持的网络: ${network.name}`);
  }

  validateConfig(config);
  console.log(`\n部署网络: ${network.name}`);
  console.log(`营销钱包: ${config.marketingWallet}`);

  const deploymentInfo = {
    network: network.name,
    deployer: deployer.address,
    timestamp: new Date().toISOString(),
    contracts: {},
  };

  try {
    // 1. 部署PriceOracle合约
    console.log("\n1. 部署PriceOracle合约...");
    const PriceOracle = await ethers.getContractFactory("PriceOracle");
    const priceOracle = await PriceOracle.deploy();
    await waitForConfirmation(priceOracle.deployTransaction);
    console.log(`✅ PriceOracle部署成功: ${await priceOracle.getAddress()}`);

    deploymentInfo.contracts.PriceOracle = {
      address: priceOracle.address,
      constructorArgs: [],
    };

    // 2. 部署TokenA合约
    console.log("\n2. 部署TokenA合约...");
    const TokenA = await ethers.getContractFactory("TokenA");
    const tokenA = await TokenA.deploy(
      config.marketingWallet,
      config.pancakeSwapRouter,
      config.wbnbAddress
    );
    await waitForConfirmation(tokenA.deployTransaction);
    console.log(`✅ TokenA部署成功: ${tokenA.address}`);

    deploymentInfo.contracts.TokenA = {
      address: tokenA.address,
      constructorArgs: [
        config.marketingWallet,
        config.pancakeSwapRouter,
        config.wbnbAddress,
      ],
    };

    // 3. 部署TokenB合约
    console.log("\n3. 部署TokenB合约...");
    const TokenB = await ethers.getContractFactory("TokenB");
    const tokenB = await TokenB.deploy(
      config.marketingWallet,
      config.pancakeSwapRouter,
      config.wbnbAddress
    );
    await waitForConfirmation(tokenB.deployTransaction);
    console.log(`✅ TokenB部署成功: ${tokenB.address}`);

    deploymentInfo.contracts.TokenB = {
      address: tokenB.address,
      constructorArgs: [
        config.marketingWallet,
        config.pancakeSwapRouter,
        config.wbnbAddress,
      ],
    };

    // 4. 部署MinerNFT合约
    console.log("\n4. 部署MinerNFT合约...");
    const MinerNFT = await ethers.getContractFactory("MinerNFT");
    const minerNFT = await MinerNFT.deploy("MinerNFT", "MNFT", tokenA.address);
    await waitForConfirmation(minerNFT.deployTransaction);
    console.log(`✅ MinerNFT部署成功: ${minerNFT.address}`);

    deploymentInfo.contracts.MinerNFT = {
      address: minerNFT.address,
      constructorArgs: ["MinerNFT", "MNFT", tokenA.address],
    };

    // 5. 部署MiningContract合约
    console.log("\n5. 部署MiningContract合约...");
    const MiningContract = await ethers.getContractFactory("MiningContract");
    const miningContract = await MiningContract.deploy(
      tokenA.address,
      tokenB.address,
      minerNFT.address,
      priceOracle.address
    );
    await waitForConfirmation(miningContract.deployTransaction);
    console.log(`✅ MiningContract部署成功: ${miningContract.address}`);

    deploymentInfo.contracts.MiningContract = {
      address: miningContract.address,
      constructorArgs: [
        tokenA.address,
        tokenB.address,
        minerNFT.address,
        priceOracle.address,
      ],
    };

    console.log("\n" + "=".repeat(60));
    console.log("配置合约之间的关系...");
    console.log("=".repeat(60));

    // 6. 配置合约之间的关系
    console.log("\n6.1 设置TokenA的NFT合约地址...");
    const setNftTx = await tokenA.setNftContract(minerNFT.address);
    await waitForConfirmation(setNftTx);
    console.log("✅ TokenA NFT合约地址设置成功");

    console.log("\n6.2 为TokenB添加挖矿合约铸造权限...");
    const addMinterTx = await tokenB.addMinter(miningContract.address);
    await waitForConfirmation(addMinterTx);
    console.log("✅ TokenB挖矿合约铸造权限添加成功");

    console.log("\n6.3 设置税收豁免地址...");
    const setTaxExemptTx1 = await tokenA.setTaxExempt(
      miningContract.address,
      true
    );
    await waitForConfirmation(setTaxExemptTx1);

    const setTaxExemptTx2 = await tokenB.setTaxExempt(
      miningContract.address,
      true
    );
    await waitForConfirmation(setTaxExemptTx2);
    console.log("✅ 税收豁免地址设置成功");

    console.log("\n6.4 向NFT合约转入TokenA...");
    // NFT合约需要足够的TokenA用于释放 (2100 * 100,000 = 210,000,000)
    const nftTokenAmount = ethers.utils.parseEther("*********");
    const transferTx = await tokenA.transfer(minerNFT.address, nftTokenAmount);
    await waitForConfirmation(transferTx);
    console.log("✅ TokenA转入NFT合约成功");

    // 保存部署信息
    saveDeploymentInfo(deploymentInfo);

    console.log("\n" + "=".repeat(60));
    console.log("部署完成总结");
    console.log("=".repeat(60));
    console.log(`PriceOracle:    ${priceOracle.address}`);
    console.log(`TokenA:         ${tokenA.address}`);
    console.log(`TokenB:         ${tokenB.address}`);
    console.log(`MinerNFT:       ${minerNFT.address}`);
    console.log(`MiningContract: ${miningContract.address}`);

    // 验证合约
    if (network.name !== "hardhat") {
      console.log("\n" + "=".repeat(60));
      console.log("开始验证合约...");
      console.log("=".repeat(60));

      // 等待一段时间确保合约被索引
      console.log("等待合约被Etherscan索引...");
      await new Promise((resolve) => setTimeout(resolve, 30000));

      await verifyContract(priceOracle.address, [], "PriceOracle");
      await verifyContract(
        tokenA.address,
        [config.marketingWallet, config.pancakeSwapRouter, config.wbnbAddress],
        "TokenA"
      );
      await verifyContract(
        tokenB.address,
        [config.marketingWallet, config.pancakeSwapRouter, config.wbnbAddress],
        "TokenB"
      );
      await verifyContract(
        minerNFT.address,
        ["MinerNFT", "MNFT", tokenA.address],
        "MinerNFT"
      );
      await verifyContract(
        miningContract.address,
        [tokenA.address, tokenB.address, minerNFT.address, priceOracle.address],
        "MiningContract"
      );
    }

    console.log("\n🎉 所有合约部署和配置完成!");
  } catch (error) {
    console.error("\n❌ 部署过程中发生错误:");
    console.error(error);

    // 保存错误信息
    deploymentInfo.error = {
      message: error.message,
      stack: error.stack,
    };
    saveDeploymentInfo(deploymentInfo);

    process.exit(1);
  }
}

// 执行部署
if (require.main === module) {
  main()
    .then(() => process.exit(0))
    .catch((error) => {
      console.error(error);
      process.exit(1);
    });
}

module.exports = { main };
