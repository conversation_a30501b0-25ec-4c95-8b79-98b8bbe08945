const { ethers } = require("hardhat");

async function testAirdropScript() {
    console.log("🧪 Testing Airdrop Script Functions");
    console.log("=====================================\n");
    
    // Import the airdrop functions
    const { generateRandomAddresses } = require('./airdrop-nft');
    
    try {
        // Test 1: Generate random addresses
        console.log("📋 Test 1: Generate Random Addresses");
        console.log("-------------------------------------");
        
        const testAddresses = generateRandomAddresses(5);
        
        // Validate generated addresses
        let validAddressCount = 0;
        testAddresses.forEach((address, index) => {
            const isValid = ethers.isAddress(address);
            console.log(`Address ${index + 1}: ${address} - ${isValid ? '✅ Valid' : '❌ Invalid'}`);
            if (isValid) validAddressCount++;
        });
        
        console.log(`\n📊 Results: ${validAddressCount}/${testAddresses.length} addresses are valid\n`);
        
        // Test 2: Contract interaction simulation (without actual deployment)
        console.log("📋 Test 2: Contract ABI and Function Check");
        console.log("------------------------------------------");
        
        const contractABI = [
            "function mintFor(address to, uint256 quantity) external",
            "function batchMintToMultiple(address[] calldata recipients, uint256[] calldata quantities) external",
            "function owner() external view returns (address)",
            "function totalMinted() external view returns (uint256)",
            "function MAX_SUPPLY() external view returns (uint256)",
            "function name() external view returns (string)",
            "function symbol() external view returns (string)"
        ];
        
        // Create interface to test ABI
        const contractInterface = new ethers.Interface(contractABI);
        
        console.log("✅ Contract ABI loaded successfully");
        console.log("✅ Available functions:");
        contractInterface.fragments.forEach(fragment => {
            if (fragment.type === 'function') {
                console.log(`   - ${fragment.name}`);
            }
        });
        
        // Test 3: Gas estimation simulation
        console.log("\n📋 Test 3: Gas Estimation Simulation");
        console.log("-------------------------------------");
        
        try {
            // Simulate batch parameters
            const batchSize = 5;
            const quantities = new Array(batchSize).fill(1);
            
            console.log(`✅ Batch size: ${batchSize}`);
            console.log(`✅ Quantities per address: ${quantities}`);
            console.log(`✅ Total NFTs in batch: ${quantities.reduce((a, b) => a + b, 0)}`);
            
        } catch (error) {
            console.log(`❌ Gas estimation simulation failed: ${error.message}`);
        }
        
        // Test 4: Input validation
        console.log("\n📋 Test 4: Input Validation Functions");
        console.log("-------------------------------------");
        
        // Test private key validation
        const testPrivateKeys = [
            "1234567890123456789012345678901234567890123456789012345678901234", // Valid length
            "invalid_key", // Invalid
            "", // Empty
            "0x1234567890123456789012345678901234567890123456789012345678901234" // With 0x prefix
        ];
        
        testPrivateKeys.forEach((key, index) => {
            try {
                new ethers.Wallet(key);
                console.log(`Private Key ${index + 1}: ✅ Valid`);
            } catch (error) {
                console.log(`Private Key ${index + 1}: ❌ Invalid`);
            }
        });
        
        // Test address validation  
        const testAddresses2 = [
            "******************************************", // Valid (42 chars)
            "0xinvalid", // Invalid
            "", // Empty
            "not_an_address" // Invalid format
        ];
        
        testAddresses2.forEach((address, index) => {
            const isValid = ethers.isAddress(address);
            console.log(`Test Address ${index + 1}: ${isValid ? '✅ Valid' : '❌ Invalid'}`);
        });
        
        console.log("\n🎉 All tests completed!");
        console.log("📝 The airdrop script is ready to use with actual contract deployment.");
        
    } catch (error) {
        console.log("💥 Test error:", error.message);
    }
}

// Run tests
if (require.main === module) {
    testAirdropScript()
        .then(() => {
            console.log("\n✨ Test script finished successfully!");
            process.exit(0);
        })
        .catch((error) => {
            console.error("💥 Test script error:", error);
            process.exit(1);
        });
}