const { ethers } = require("hardhat");
const readline = require('readline');

// 创建命令行接口
const rl = readline.createInterface({
    input: process.stdin,
    output: process.stdout
});

// 封装问题函数
function question(query) {
    return new Promise(resolve => rl.question(query, resolve));
}

// NFT合约ABI
const NFT_ABI = [
    "function totalSupply() external view returns (uint256)",
    "function ownerOf(uint256 tokenId) external view returns (address)"
];

// GRAT代币ABI（包含批量转账功能）
const GRAT_TOKEN_ABI = [
    "function batchTransfer(address[] calldata recipients, uint256[] calldata amounts) external",
    "function balanceOf(address account) external view returns (uint256)",
    "function decimals() external view returns (uint8)",
    "function symbol() external view returns (string)",
    "function name() external view returns (string)",
    "function transfer(address to, uint256 amount) external returns (bool)"
];

async function main() {
    console.log("🚀 NFT持有者优化批量空投脚本 (使用批量转账)");
    console.log("===============================================");
    
    try {
        // 获取用户输入
        const privateKey = await question("请输入发送者私钥: ");
        if (!privateKey || privateKey.length !== 64) {
            throw new Error("私钥格式不正确，应为64位十六进制字符串");
        }

        const nftAddress = await question("请输入NFT合约地址: ");
        if (!ethers.isAddress(nftAddress)) {
            throw new Error("NFT合约地址格式不正确");
        }

        const tokenAddress = await question("请输入GRAT代币合约地址: ");
        if (!ethers.isAddress(tokenAddress)) {
            throw new Error("代币合约地址格式不正确");
        }

        const tokenAmountInput = await question("请输入每个NFT持有者获得的代币数量 (默认3000): ");
        const tokenAmount = tokenAmountInput.trim() === '' ? '3000' : tokenAmountInput;
        
        if (isNaN(parseFloat(tokenAmount)) || parseFloat(tokenAmount) <= 0) {
            throw new Error("代币数量必须是正数");
        }

        console.log("\n📋 配置信息:");
        console.log("NFT合约地址:", nftAddress);
        console.log("GRAT代币地址:", tokenAddress);
        console.log("每人代币数量:", tokenAmount);

        const confirm = await question("\n确认执行空投? (y/N): ");
        if (confirm.toLowerCase() !== 'y' && confirm.toLowerCase() !== 'yes') {
            console.log("❌ 操作已取消");
            process.exit(0);
        }

        // 创建钱包和provider
        const provider = ethers.provider;
        const wallet = new ethers.Wallet(privateKey, provider);
        
        console.log("\n👤 发送者地址:", wallet.address);
        
        // 检查网络
        const network = await provider.getNetwork();
        console.log("🌐 网络:", network.name, "Chain ID:", network.chainId.toString());
        
        // 检查发送者余额
        const balance = await provider.getBalance(wallet.address);
        console.log("💰 发送者ETH余额:", ethers.formatEther(balance));
        
        if (balance < ethers.parseEther("0.01")) {
            console.log("⚠️  警告: ETH余额较低，可能不足以支付Gas费用");
        }
        
        // 连接合约
        const nftContract = new ethers.Contract(nftAddress, NFT_ABI, provider);
        const tokenContract = new ethers.Contract(tokenAddress, GRAT_TOKEN_ABI, wallet);
        
        // 获取代币信息
        console.log("\n📊 获取代币信息...");
        const tokenName = await tokenContract.name();
        const tokenSymbol = await tokenContract.symbol();
        const tokenDecimals = await tokenContract.decimals();
        const tokenAmountWei = ethers.parseUnits(tokenAmount, tokenDecimals);
        
        console.log("代币名称:", tokenName);
        console.log("代币符号:", tokenSymbol);
        console.log("代币精度:", tokenDecimals.toString());
        
        // 检查发送者代币余额
        const senderTokenBalance = await tokenContract.balanceOf(wallet.address);
        console.log("发送者代币余额:", ethers.formatUnits(senderTokenBalance, tokenDecimals), tokenSymbol);
        
        // 获取NFT总供应量
        console.log("\n🔍 分析NFT持有情况...");
        const totalSupply = await nftContract.totalSupply();
        console.log("NFT总供应量:", totalSupply.toString());
        
        if (totalSupply === 0n) {
            throw new Error("NFT总供应量为0，无法执行空投");
        }
        
        // 获取所有NFT持有者（去重）
        const holders = new Set();
        
        console.log("正在扫描NFT持有者...");
        for (let i = 1; i <= totalSupply; i++) {
            try {
                const owner = await nftContract.ownerOf(i);
                if (owner !== ethers.ZeroAddress) {
                    holders.add(owner);
                }
                
                // 显示进度
                if (i % 50 === 0 || i === totalSupply) {
                    process.stdout.write(`\r进度: ${i}/${totalSupply} (${Math.round(i/Number(totalSupply)*100)}%) - 找到 ${holders.size} 个持有者`);
                }
            } catch (error) {
                console.log(`\n⚠️  NFT #${i} 查询失败:`, error.message);
            }
        }
        
        console.log(`\n✅ 扫描完成！找到 ${holders.size} 个唯一持有者`);
        
        // 计算总需要的代币数量
        const totalTokensNeeded = tokenAmountWei * BigInt(holders.size);
        console.log(`💎 总计需要代币: ${ethers.formatUnits(totalTokensNeeded, tokenDecimals)} ${tokenSymbol}`);
        
        // 检查余额是否足够
        if (senderTokenBalance < totalTokensNeeded) {
            throw new Error(`代币余额不足！需要 ${ethers.formatUnits(totalTokensNeeded, tokenDecimals)} ${tokenSymbol}，但只有 ${ethers.formatUnits(senderTokenBalance, tokenDecimals)} ${tokenSymbol}`);
        }
        
        // 最终确认
        const finalConfirm = await question(`\n🚀 即将使用批量转账向 ${holders.size} 个地址发送代币，每个地址 ${tokenAmount} ${tokenSymbol}。\n这将大大节省Gas费用！确认执行? (y/N): `);
        if (finalConfirm.toLowerCase() !== 'y' && finalConfirm.toLowerCase() !== 'yes') {
            console.log("❌ 操作已取消");
            process.exit(0);
        }
        
        // 准备批量转账数据
        const holdersArray = Array.from(holders);
        const amounts = new Array(holdersArray.length).fill(tokenAmountWei);
        
        console.log("\n🔄 执行批量转账...");
        console.log(`📦 准备向 ${holdersArray.length} 个地址发送代币`);
        
        // 估算Gas费用
        try {
            const gasEstimate = await tokenContract.batchTransfer.estimateGas(holdersArray, amounts);
            const gasPrice = await provider.getFeeData();
            const estimatedCost = gasEstimate * gasPrice.gasPrice;
            console.log(`⛽ 预估Gas费用: ${ethers.formatEther(estimatedCost)} ETH`);
        } catch (error) {
            console.log("⚠️  无法估算Gas费用:", error.message);
        }
        
        // 执行批量转账
        try {
            console.log("🚀 正在执行批量转账交易...");
            const tx = await tokenContract.batchTransfer(holdersArray, amounts);
            console.log("📝 交易哈希:", tx.hash);
            console.log("⏳ 等待交易确认...");
            
            const receipt = await tx.wait();
            console.log("✅ 交易已确认！");
            console.log("📊 Gas使用量:", receipt.gasUsed.toString());
            console.log("💰 实际Gas费用:", ethers.formatEther(receipt.gasUsed * receipt.gasPrice), "ETH");
            
            // 验证转账结果
            console.log("\n🔍 验证转账结果...");
            let successCount = 0;
            const sampleSize = Math.min(5, holdersArray.length);
            
            for (let i = 0; i < sampleSize; i++) {
                const holder = holdersArray[i];
                const balance = await tokenContract.balanceOf(holder);
                if (balance >= tokenAmountWei) {
                    successCount++;
                    console.log(`✅ ${holder}: ${ethers.formatUnits(balance, tokenDecimals)} ${tokenSymbol}`);
                } else {
                    console.log(`❌ ${holder}: ${ethers.formatUnits(balance, tokenDecimals)} ${tokenSymbol}`);
                }
            }
            
            if (sampleSize < holdersArray.length) {
                console.log(`... 还有 ${holdersArray.length - sampleSize} 个地址未显示`);
            }
            
            // 输出最终结果
            console.log("\n🎉 批量空投完成！");
            console.log("================================");
            console.log(`📊 目标地址数量: ${holdersArray.length}`);
            console.log(`💰 每地址代币数量: ${tokenAmount} ${tokenSymbol}`);
            console.log(`🎯 总计发送代币: ${ethers.formatUnits(totalTokensNeeded, tokenDecimals)} ${tokenSymbol}`);
            console.log(`⛽ Gas费用: ${ethers.formatEther(receipt.gasUsed * receipt.gasPrice)} ETH`);
            console.log(`📝 交易哈希: ${tx.hash}`);
            
            // 保存结果到文件
            const fs = require('fs');
            const path = require('path');
            
            const resultData = {
                timestamp: new Date().toISOString(),
                network: network.name,
                chainId: network.chainId.toString(),
                sender: wallet.address,
                nftContract: nftAddress,
                tokenContract: tokenAddress,
                tokenAmount: tokenAmount,
                tokenSymbol: tokenSymbol,
                totalHolders: holdersArray.length,
                totalTokensSent: ethers.formatUnits(totalTokensNeeded, tokenDecimals),
                transactionHash: tx.hash,
                gasUsed: receipt.gasUsed.toString(),
                gasCost: ethers.formatEther(receipt.gasUsed * receipt.gasPrice),
                recipients: holdersArray
            };
            
            const resultsDir = path.join(__dirname, '..', 'airdrop-results');
            if (!fs.existsSync(resultsDir)) {
                fs.mkdirSync(resultsDir, { recursive: true });
            }
            
            const resultFile = path.join(resultsDir, `optimized-nft-airdrop-${Date.now()}.json`);
            fs.writeFileSync(resultFile, JSON.stringify(resultData, null, 2));
            
            console.log(`📄 详细结果已保存到: ${resultFile}`);
            
        } catch (error) {
            console.error("❌ 批量转账失败:", error.message);
            
            // 如果批量转账失败，提供回退选项
            const fallback = await question("\n是否使用单笔转账模式作为备选方案? (y/N): ");
            if (fallback.toLowerCase() === 'y' || fallback.toLowerCase() === 'yes') {
                console.log("🔄 切换到单笔转账模式...");
                await fallbackSingleTransfers(tokenContract, holdersArray, tokenAmountWei, tokenSymbol, tokenDecimals);
            }
        }
        
    } catch (error) {
        console.error("\n❌ 执行失败:", error.message);
        process.exit(1);
    } finally {
        rl.close();
    }
}

// 备选方案：单笔转账
async function fallbackSingleTransfers(tokenContract, holders, amount, symbol, decimals) {
    console.log("⚠️  使用单笔转账模式，这将消耗更多Gas费用...");
    
    let successCount = 0;
    let failCount = 0;
    
    for (let i = 0; i < holders.length; i++) {
        try {
            const tx = await tokenContract.transfer(holders[i], amount);
            await tx.wait();
            successCount++;
            console.log(`✅ ${i + 1}/${holders.length}: ${holders[i]} - ${ethers.formatUnits(amount, decimals)} ${symbol}`);
        } catch (error) {
            failCount++;
            console.log(`❌ ${i + 1}/${holders.length}: ${holders[i]} - 失败: ${error.message}`);
        }
        
        // 每10笔交易后暂停2秒
        if ((i + 1) % 10 === 0) {
            console.log("⏳ 暂停2秒...");
            await new Promise(resolve => setTimeout(resolve, 2000));
        }
    }
    
    console.log(`\n📊 单笔转账完成: 成功 ${successCount}, 失败 ${failCount}`);
}

// 错误处理
main()
    .then(() => {
        console.log("\n✅ 脚本执行完成");
        process.exit(0);
    })
    .catch((error) => {
        console.error("\n❌ 脚本执行失败:", error);
        process.exit(1);
    });

module.exports = main;
