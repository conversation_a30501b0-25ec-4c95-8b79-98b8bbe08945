const { ethers } = require("hardhat");

async function main() {
  console.log("开始简单测试NFTPresale合约功能...");

  // 直接从部署中获取地址
  const [deployer, user1, user2] = await ethers.getSigners();
  
  // 部署MockUSDT
  const MockUSDT = await ethers.getContractFactory("MockUSDT");
  const mockUSDT = await MockUSDT.deploy();
  await mockUSDT.waitForDeployment();
  const usdtAddress = await mockUSDT.getAddress();
  console.log(`MockUSDT部署到: ${usdtAddress}`);

  // 部署NFTPresale
  const NFTPresale = await ethers.getContractFactory("NFTPresale");
  const nftPresale = await NFTPresale.deploy(usdtAddress, deployer.address);
  await nftPresale.waitForDeployment();
  const nftPresaleAddress = await nftPresale.getAddress();
  console.log(`NFTPresale部署到: ${nftPresaleAddress}`);

  // 预售默认已启动，无需额外设置
  console.log("预售默认已启动（无时间限制）");

  // 检查预售信息
  const presaleInfo = await nftPresale.getPresaleInfo();
  console.log(`最大供应量: ${presaleInfo._maxSupply}`);
  console.log(`NFT价格: ${ethers.formatUnits(presaleInfo._nftPrice, 6)} USDT`);
  console.log(`预售状态: ${presaleInfo._active ? "活跃" : "未激活"}`);

  // 为用户1铸造USDT
  const mintAmount = ethers.parseUnits("100", 6); // 100 USDT
  await mockUSDT.mint(user1.address, mintAmount);
  console.log(`为用户1铸造了 ${ethers.formatUnits(mintAmount, 6)} USDT`);

  // 用户1购买NFT
  const quantity = 2;
  const totalCost = presaleInfo._nftPrice * BigInt(quantity);
  
  await mockUSDT.connect(user1).approve(nftPresaleAddress, totalCost);
  console.log(`用户1授权了 ${ethers.formatUnits(totalCost, 6)} USDT`);

  try {
    await nftPresale.connect(user1)["buyNFT(address,uint256)"](ethers.ZeroAddress, quantity);
    console.log(`用户1成功购买了 ${quantity} 个NFT`);
  } catch (error) {
    console.log(`用户1购买失败: ${error.message}`);
  }

  // 为用户2铸造USDT并购买NFT（带推荐人）
  console.log("\n=== 用户2带推荐人购买NFT ===");
  const mintAmount2 = ethers.parseUnits("50", 6); // 50 USDT
  await mockUSDT.mint(user2.address, mintAmount2);
  console.log(`为用户2铸造了 ${ethers.formatUnits(mintAmount2, 6)} USDT`);

  const quantity2 = 1;
  const totalCost2 = presaleInfo._nftPrice * BigInt(quantity2);
  
  await mockUSDT.connect(user2).approve(nftPresaleAddress, totalCost2);
  console.log(`用户2授权了 ${ethers.formatUnits(totalCost2, 6)} USDT`);

  try {
    await nftPresale.connect(user2)["buyNFT(address,uint256)"](user1.address, quantity2);
    console.log(`用户2成功购买了 ${quantity2} 个NFT，推荐人: ${user1.address}`);
  } catch (error) {
    console.log(`用户2购买失败: ${error.message}`);
  }

  // 检查用户信息
  console.log("\n=== 用户信息 ===");
  const user1Info = await nftPresale.getUserInfo(user1.address);
  console.log(`用户1购买数量: ${user1Info.purchased}`);
  console.log(`用户1推荐人数: ${user1Info.refereeCount}`);

  const user2Info = await nftPresale.getUserInfo(user2.address);
  console.log(`用户2购买数量: ${user2Info.purchased}`);
  console.log(`用户2推荐人: ${user2Info.referrer}`);

  // 检查推荐列表
  console.log("\n=== 推荐列表 ===");
  const user1Referees = await nftPresale.getReferees(user1.address);
  console.log(`用户1推荐的地址列表: ${user1Referees}`);

  // 检查推荐详情
  const referralDetails = await nftPresale.getReferralDetails(user1.address);
  console.log(`推荐总人数: ${referralDetails.totalReferees}`);
  console.log(`被推荐人总购买量: ${referralDetails.totalPurchasedByReferees}`);
  
  if (referralDetails.referees.length > 0) {
    console.log("推荐详情:");
    for (let i = 0; i < referralDetails.referees.length; i++) {
      console.log(`  - ${referralDetails.referees[i]}: ${referralDetails.purchasedAmounts[i]} NFT`);
    }
  }

  // 检查总销售情况
  console.log("\n=== 总销售情况 ===");
  const updatedInfo = await nftPresale.getPresaleInfo();
  console.log(`总售出: ${updatedInfo._totalSold}`);
  console.log(`总参与者: ${updatedInfo._totalParticipants}`);

  // 检查收款钱包余额
  const deployerBalance = await mockUSDT.balanceOf(deployer.address);
  console.log(`部署者(收款钱包)余额: ${ethers.formatUnits(deployerBalance, 6)} USDT`);

  console.log("\n🎉 推荐功能测试完成！");
}

if (require.main === module) {
  main()
    .then(() => process.exit(0))
    .catch((error) => {
      console.error(error);
      process.exit(1);
    });
}

module.exports = { main };