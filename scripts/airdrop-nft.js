const { ethers } = require("hardhat");
const readline = require('readline');

// Create readline interface for user input
const rl = readline.createInterface({
    input: process.stdin,
    output: process.stdout
});

// Function to prompt user input
function askQuestion(question) {
    return new Promise((resolve) => {
        rl.question(question, (answer) => {
            resolve(answer);
        });
    });
}

// Function to generate random addresses
function generateRandomAddresses(count) {
    console.log(`\n📝 Generating ${count} random addresses for airdrop...`);
    const addresses = [];
    
    for (let i = 0; i < count; i++) {
        // Generate random wallet
        const randomWallet = ethers.Wallet.createRandom();
        addresses.push(randomWallet.address);
        console.log(`${i + 1}. ${randomWallet.address}`);
    }
    
    return addresses;
}

// Function to validate Ethereum address
function isValidEthereumAddress(address) {
    return ethers.isAddress(address);
}

// Function to validate private key
function isValidPrivateKey(privateKey) {
    try {
        new ethers.Wallet(privateKey);
        return true;
    } catch (error) {
        return false;
    }
}

// Main airdrop function
async function airdropNFT() {
    console.log("🚀 NFT Airdrop Script Started");
    console.log("=====================================\n");
    
    try {
        // Get private key from user
        const privateKey = await askQuestion("📝 Please enter your private key (64 characters): ");
        
        // Validate private key
        if (!privateKey || privateKey.length !== 64 || !isValidPrivateKey(privateKey)) {
            console.log("❌ Invalid private key format! Private key must be 64 characters long.");
            rl.close();
            return;
        }
        
        // Get NFT contract address
        const contractAddress = await askQuestion("📝 Please enter NFT contract address: ");
        
        // Validate contract address
        if (!isValidEthereumAddress(contractAddress)) {
            console.log("❌ Invalid contract address format!");
            rl.close();
            return;
        }
        
        // Get number of addresses to generate
        const addressCount = await askQuestion("📝 How many addresses to generate for airdrop? (1-100): ");
        const count = parseInt(addressCount);
        
        if (isNaN(count) || count < 1 || count > 100) {
            console.log("❌ Invalid count! Please enter a number between 1 and 100.");
            rl.close();
            return;
        }
        
        // Get quantity per address
        const quantityInput = await askQuestion("📝 How many NFTs per address? (1-10): ");
        const quantity = parseInt(quantityInput);
        
        if (isNaN(quantity) || quantity < 1 || quantity > 10) {
            console.log("❌ Invalid quantity! Please enter a number between 1 and 10.");
            rl.close();
            return;
        }
        
        console.log("\n🔧 Configuration Summary:");
        console.log(`📍 Contract Address: ${contractAddress}`);
        console.log(`👥 Number of Recipients: ${count}`);
        console.log(`🎁 NFTs per Address: ${quantity}`);
        console.log(`📊 Total NFTs to Mint: ${count * quantity}`);
        
        // Ask for confirmation
        const confirm = await askQuestion("\n❓ Do you want to proceed? (y/N): ");
        if (confirm.toLowerCase() !== 'y' && confirm.toLowerCase() !== 'yes') {
            console.log("❌ Airdrop cancelled by user.");
            rl.close();
            return;
        }
        
        // Generate random addresses
        const addresses = generateRandomAddresses(count);
        
        // Get network selection
        const networkChoice = await askQuestion("📝 Select network (1: BSC Testnet, 2: BSC Mainnet, 3: Current/Default): ");
        let provider;
        let networkName;
        
        switch(networkChoice) {
            case '1':
                provider = new ethers.JsonRpcProvider("https://data-seed-prebsc-1-s1.binance.org:8545/");
                networkName = "BSC Testnet";
                break;
            case '2':
                provider = new ethers.JsonRpcProvider("https://bsc-dataseed1.binance.org/");
                networkName = "BSC Mainnet";
                break;
            default:
                provider = ethers.provider;
                networkName = "Current Network";
        }
        
        // Create wallet from private key
        const wallet = new ethers.Wallet(privateKey, provider);
        console.log(`\n👤 Airdrop Wallet: ${wallet.address}`);
        console.log(`🌐 Network: ${networkName}`);
        
        // Check wallet balance
        const balance = await wallet.provider.getBalance(wallet.address);
        console.log(`💰 Wallet Balance: ${ethers.formatEther(balance)} ETH`);
        
        if (balance === 0n) {
            console.log("❌ Insufficient balance! Please fund your wallet first.");
            rl.close();
            return;
        }
        
        // Get contract ABI (using TraditionalNFT ABI)
        const contractABI = [
            "function mintFor(address to, uint256 quantity) external",
            "function batchMintToMultiple(address[] calldata recipients, uint256[] calldata quantities) external",
            "function owner() external view returns (address)",
            "function totalMinted() external view returns (uint256)",
            "function MAX_SUPPLY() external view returns (uint256)",
            "function name() external view returns (string)",
            "function symbol() external view returns (string)"
        ];
        
        // Connect to contract
        console.log("\n🔌 Connecting to NFT contract...");
        const nftContract = new ethers.Contract(contractAddress, contractABI, wallet);
        
        try {
            // Get contract info
            const contractName = await nftContract.name();
            const contractSymbol = await nftContract.symbol();
            const totalMinted = await nftContract.totalMinted();
            const maxSupply = await nftContract.MAX_SUPPLY();
            
            console.log(`📋 Contract Name: ${contractName}`);
            console.log(`🔤 Contract Symbol: ${contractSymbol}`);
            console.log(`📊 Total Minted: ${totalMinted}`);
            console.log(`🎯 Max Supply: ${maxSupply}`);
            
            // Check if enough supply remains
            const remainingSupply = maxSupply - totalMinted;
            const totalToMint = BigInt(count * quantity);
            
            if (remainingSupply < totalToMint) {
                console.log(`❌ Insufficient supply! Remaining: ${remainingSupply}, Required: ${totalToMint}`);
                rl.close();
                return;
            }
            
            // Check if wallet is owner
            const owner = await nftContract.owner();
            if (wallet.address.toLowerCase() !== owner.toLowerCase()) {
                console.log("❌ You are not the contract owner! Only owner can mint NFTs.");
                rl.close();
                return;
            }
            
        } catch (error) {
            console.log("❌ Error connecting to contract:", error.message);
            rl.close();
            return;
        }
        
        console.log("\n🚀 Starting airdrop...");
        
        // Batch size for processing
        const batchSize = 20; // Process 20 addresses at a time
        let successCount = 0;
        let failureCount = 0;
        
        // Process addresses in batches
        for (let i = 0; i < addresses.length; i += batchSize) {
            const batch = addresses.slice(i, i + batchSize);
            const quantities = new Array(batch.length).fill(quantity);
            
            console.log(`\n📦 Processing batch ${Math.floor(i / batchSize) + 1}/${Math.ceil(addresses.length / batchSize)}...`);
            console.log(`👥 Addresses in this batch: ${batch.length}`);
            
            try {
                // Estimate gas
                const gasEstimate = await nftContract.batchMintToMultiple.estimateGas(batch, quantities);
                const feeData = await provider.getFeeData();
                const gasCost = gasEstimate * feeData.gasPrice;
                
                console.log(`⛽ Estimated Gas: ${gasEstimate.toString()}`);
                console.log(`💰 Estimated Cost: ${ethers.formatEther(gasCost)} ETH`);
                
                // Execute batch mint
                const tx = await nftContract.batchMintToMultiple(batch, quantities, {
                    gasLimit: gasEstimate * 120n / 100n // Add 20% buffer
                });
                
                console.log(`📝 Transaction Hash: ${tx.hash}`);
                console.log("⏳ Waiting for confirmation...");
                
                const receipt = await tx.wait();
                
                if (receipt.status === 1) {
                    console.log(`✅ Batch successful! Gas used: ${receipt.gasUsed.toString()}`);
                    successCount += batch.length;
                    
                    // Show minted token info from events
                    const mintEvents = receipt.logs.filter(log => {
                        try {
                            return nftContract.interface.parseLog(log).name === 'BatchMinted';
                        } catch {
                            return false;
                        }
                    });
                    
                    mintEvents.forEach(event => {
                        try {
                            const parsed = nftContract.interface.parseLog(event);
                            console.log(`🎁 Minted ${parsed.args.tokenIds.length} NFTs to ${parsed.args.to}`);
                        } catch (error) {
                            console.log("📝 Mint event detected (details unavailable)");
                        }
                    });
                    
                } else {
                    console.log("❌ Transaction failed!");
                    failureCount += batch.length;
                }
                
            } catch (error) {
                console.log(`❌ Batch failed: ${error.message}`);
                failureCount += batch.length;
                
                // Try individual mints for this batch
                console.log("🔄 Attempting individual mints...");
                for (const address of batch) {
                    try {
                        const tx = await nftContract.mintFor(address, quantity);
                        await tx.wait();
                        console.log(`✅ Individual mint successful for ${address}`);
                        successCount++;
                        failureCount--;
                    } catch (individualError) {
                        console.log(`❌ Individual mint failed for ${address}: ${individualError.message}`);
                    }
                }
            }
            
            // Add delay between batches
            if (i + batchSize < addresses.length) {
                console.log("⏳ Waiting 2 seconds before next batch...");
                await new Promise(resolve => setTimeout(resolve, 2000));
            }
        }
        
        // Final summary
        console.log("\n🎉 Airdrop Completed!");
        console.log("=====================================");
        console.log(`✅ Successful airdrops: ${successCount}/${count}`);
        console.log(`❌ Failed airdrops: ${failureCount}/${count}`);
        console.log(`🎁 Total NFTs minted: ${successCount * quantity}`);
        
        if (successCount === count) {
            console.log("🏆 All airdrops completed successfully!");
        } else if (successCount > 0) {
            console.log("⚠️  Partial success - some airdrops failed");
        } else {
            console.log("💥 All airdrops failed - please check configuration");
        }
        
        // Show final contract stats
        try {
            const finalTotalMinted = await nftContract.totalMinted();
            console.log(`📊 Total minted after airdrop: ${finalTotalMinted}`);
        } catch (error) {
            console.log("ℹ️  Could not retrieve final stats");
        }
        
    } catch (error) {
        console.log("💥 Unexpected error:", error.message);
    } finally {
        rl.close();
    }
}

// Handle script execution
if (require.main === module) {
    airdropNFT()
        .then(() => {
            console.log("\n👋 Script finished. Goodbye!");
            process.exit(0);
        })
        .catch((error) => {
            console.error("💥 Script error:", error);
            process.exit(1);
        });
}

module.exports = { airdropNFT, generateRandomAddresses };