const { ethers } = require('ethers');

// 配置信息
const CONFIG = {
  // RPC URL (请替换为实际的RPC地址)
  RPC_URL: 'https://bsc-dataseed.binance.org/',
  
  // 合约地址 (请替换为实际的合约地址)
  MINER_NFT_ADDRESS: '******************************************',
  
  // Owner私钥 (请替换为实际的私钥，注意安全)
  PRIVATE_KEY: 'your_private_key_here',
};

// MinerNFT合约ABI (只包含需要的函数)
const MINER_NFT_ABI = [
  'function batchSetNFTReleaseStartTime(uint256[] tokenIds, uint256[] startTimes)',
  'function setNFTReleaseStartTime(uint256 tokenId, uint256 startTime)',
  'function getNFTReleaseStartTime(uint256 tokenId) view returns (uint256)',
  'function totalSupply() view returns (uint256)',
  'function tokenByIndex(uint256 index) view returns (uint256)',
  'function owner() view returns (address)',
];

class NFTReleaseTimeManager {
  constructor() {
    this.provider = new ethers.JsonRpcProvider(CONFIG.RPC_URL);
    this.wallet = new ethers.Wallet(CONFIG.PRIVATE_KEY, this.provider);
    this.contract = new ethers.Contract(CONFIG.MINER_NFT_ADDRESS, MINER_NFT_ABI, this.wallet);
  }

  // 验证owner权限
  async verifyOwner() {
    try {
      const contractOwner = await this.contract.owner();
      const walletAddress = await this.wallet.getAddress();
      
      if (contractOwner.toLowerCase() !== walletAddress.toLowerCase()) {
        throw new Error(`权限不足: 合约owner是 ${contractOwner}, 当前钱包是 ${walletAddress}`);
      }
      
      console.log(`✅ Owner验证成功: ${walletAddress}`);
      return true;
    } catch (error) {
      console.error('❌ Owner验证失败:', error.message);
      return false;
    }
  }

  // 获取所有NFT的当前释放时间
  async getAllNFTReleaseTimes() {
    try {
      const totalSupply = await this.contract.totalSupply();
      console.log(`📊 总NFT数量: ${totalSupply}`);
      
      const nftList = [];
      
      for (let i = 0; i < totalSupply; i++) {
        const tokenId = await this.contract.tokenByIndex(i);
        const releaseStartTime = await this.contract.getNFTReleaseStartTime(tokenId);
        
        nftList.push({
          tokenId: tokenId.toString(),
          releaseStartTime: Number(releaseStartTime),
          releaseStartTimeFormatted: new Date(Number(releaseStartTime) * 1000).toLocaleString()
        });
      }
      
      return nftList;
    } catch (error) {
      console.error('❌ 获取NFT释放时间失败:', error.message);
      return [];
    }
  }

  // 批量设置NFT释放时间
  async batchSetReleaseTime(tokenIds, timestamp) {
    try {
      console.log(`🔄 批量设置 ${tokenIds.length} 个NFT的释放时间...`);
      console.log(`📅 新的释放时间: ${new Date(timestamp * 1000).toLocaleString()}`);
      
      const startTimes = new Array(tokenIds.length).fill(BigInt(timestamp));
      const tokenIdsBigInt = tokenIds.map(id => BigInt(id));
      
      const tx = await this.contract.batchSetNFTReleaseStartTime(tokenIdsBigInt, startTimes);
      console.log(`📤 交易已发送: ${tx.hash}`);
      
      const receipt = await tx.wait();
      console.log(`✅ 交易确认成功! Gas使用: ${receipt.gasUsed}`);
      
      return receipt;
    } catch (error) {
      console.error('❌ 批量设置失败:', error.message);
      throw error;
    }
  }

  // 设置单个NFT释放时间
  async setSingleReleaseTime(tokenId, timestamp) {
    try {
      console.log(`🔄 设置NFT #${tokenId} 的释放时间...`);
      console.log(`📅 新的释放时间: ${new Date(timestamp * 1000).toLocaleString()}`);
      
      const tx = await this.contract.setNFTReleaseStartTime(BigInt(tokenId), BigInt(timestamp));
      console.log(`📤 交易已发送: ${tx.hash}`);
      
      const receipt = await tx.wait();
      console.log(`✅ 交易确认成功! Gas使用: ${receipt.gasUsed}`);
      
      return receipt;
    } catch (error) {
      console.error('❌ 设置失败:', error.message);
      throw error;
    }
  }

  // 显示NFT释放时间状态
  async displayNFTStatus() {
    console.log('📋 当前NFT释放时间状态:');
    console.log('━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━');
    
    const nftList = await this.getAllNFTReleaseTimes();
    
    if (nftList.length === 0) {
      console.log('❌ 未找到NFT');
      return;
    }
    
    nftList.forEach(nft => {
      console.log(`🎯 NFT #${nft.tokenId.padStart(3, ' ')} | ${nft.releaseStartTimeFormatted}`);
    });
    
    console.log('━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━');
  }
}

// 使用示例
async function main() {
  const manager = new NFTReleaseTimeManager();
  
  try {
    // 1. 验证owner权限
    const isOwner = await manager.verifyOwner();
    if (!isOwner) {
      process.exit(1);
    }
    
    // 2. 显示当前状态
    await manager.displayNFTStatus();
    
    // 示例操作（请根据需要修改）
    
    // 方法1: 批量设置多个NFT的释放时间
    // const tokenIds = [1, 2, 3, 4, 5]; // 要设置的NFT ID数组
    // const newTimestamp = Math.floor(Date.now() / 1000) + 86400; // 24小时后
    // await manager.batchSetReleaseTime(tokenIds, newTimestamp);
    
    // 方法2: 设置单个NFT的释放时间
    // const tokenId = 1;
    // const newTimestamp = Math.floor(Date.now() / 1000) + 3600; // 1小时后
    // await manager.setSingleReleaseTime(tokenId, newTimestamp);
    
    // 方法3: 设置所有NFT为相同时间
    // const allNFTs = await manager.getAllNFTReleaseTimes();
    // const allTokenIds = allNFTs.map(nft => parseInt(nft.tokenId));
    // const uniformTimestamp = Math.floor(new Date('2024-02-01 00:00:00').getTime() / 1000);
    // await manager.batchSetReleaseTime(allTokenIds, uniformTimestamp);
    
    // 方法4: 设置不同批次的NFT为不同时间
    // const batch1 = [1, 2, 3, 4, 5]; // 第一批
    // const batch2 = [6, 7, 8, 9, 10]; // 第二批
    // const time1 = Math.floor(new Date('2024-01-01 00:00:00').getTime() / 1000);
    // const time2 = Math.floor(new Date('2024-02-01 00:00:00').getTime() / 1000);
    // 
    // await manager.batchSetReleaseTime(batch1, time1);
    // await manager.batchSetReleaseTime(batch2, time2);
    
    console.log('🎉 操作完成!');
    
  } catch (error) {
    console.error('💥 脚本执行失败:', error.message);
    process.exit(1);
  }
}

// 工具函数: 将日期字符串转换为时间戳
function dateToTimestamp(dateString) {
  // 示例: dateToTimestamp('2024-01-01 00:00:00')
  return Math.floor(new Date(dateString).getTime() / 1000);
}

// 工具函数: 将时间戳转换为日期字符串
function timestampToDate(timestamp) {
  return new Date(timestamp * 1000).toLocaleString();
}

// 如果直接运行此文件，则执行main函数
if (require.main === module) {
  console.log('🚀 NFT释放时间管理脚本启动...');
  console.log('⚠️  请确保已配置正确的RPC_URL、合约地址和私钥!');
  console.log('');
  
  main().catch(console.error);
}

module.exports = {
  NFTReleaseTimeManager,
  dateToTimestamp,
  timestampToDate
};