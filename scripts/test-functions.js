const { ethers } = require("hardhat");

async function main() {
  console.log("🧪 开始功能测试...");
  
  const [deployer, user1, user2] = await ethers.getSigners();
  
  // 先运行部署
  const deployScript = require("./simple-deploy.js");
  await deployScript.main();
  
  // 获取已部署的合约 (使用最新部署的地址)
  const tokenA = await ethers.getContractAt("TokenA", "******************************************");
  const tokenB = await ethers.getContractAt("TokenB", "******************************************");
  const minerNFT = await ethers.getContractAt("MinerNFT", "******************************************");
  const miningContract = await ethers.getContractAt("MiningContract", "******************************************");
  const priceOracle = await ethers.getContractAt("PriceOracle", "******************************************");
  
  console.log("\n=".repeat(60));
  console.log("🔄 开始功能测试");
  console.log("=".repeat(60));
  
  try {
    // 1. 测试NFT铸造
    console.log("\n1. 测试NFT铸造...");
    await minerNFT.mint(user1.address);
    await minerNFT.mint(user2.address);
    console.log(`✅ User1 NFT余额: ${await minerNFT.balanceOf(user1.address)}`);
    console.log(`✅ User2 NFT余额: ${await minerNFT.balanceOf(user2.address)}`);
    
    // 2. 测试TokenA转账
    console.log("\n2. 测试TokenA转账...");
    const transferAmount = ethers.parseEther("10000");
    await tokenA.transfer(user1.address, transferAmount);
    console.log(`✅ User1 TokenA余额: ${ethers.formatEther(await tokenA.balanceOf(user1.address))} TKA`);
    
    // 3. 测试邀请关系
    console.log("\n3. 测试邀请关系...");
    await miningContract.connect(user2).setInviter(user1.address);
    const inviteInfo = await miningContract.getUserInviteInfo(user2.address);
    console.log(`✅ User2邀请人: ${inviteInfo.inviter}`);
    console.log(`✅ User1邀请人数: ${inviteInfo.totalInvited}`);
    
    // 4. 测试挖矿机制一：销毁TokenA获得TokenB
    console.log("\n4. 测试挖矿机制一...");
    const burnAmount = ethers.parseEther("1000");
    await tokenA.connect(user1).approve(await miningContract.getAddress(), burnAmount);
    await miningContract.connect(user1).burnTokenAForTokenB(burnAmount);
    
    const recordCount = await miningContract.getUserMiningRecordCount(user1.address);
    console.log(`✅ User1挖矿记录数: 机制一=${recordCount.count1}, 机制二=${recordCount.count2}`);
    
    // 5. 测试TokenB铸造给用户2
    console.log("\n5. 测试TokenB铸造...");
    const mintAmount = ethers.parseEther("50000");
    await tokenB.mint(user2.address, mintAmount);
    console.log(`✅ User2 TokenB余额: ${ethers.formatEther(await tokenB.balanceOf(user2.address))} TKB`);
    
    // 6. 测试挖矿机制二：持有NFT并销毁TokenB
    console.log("\n6. 测试挖矿机制二...");
    const burnAmountB = ethers.parseEther("10000");
    await tokenB.connect(user2).approve(await miningContract.getAddress(), burnAmountB);
    await miningContract.connect(user2).burnTokenBWithNFT(burnAmountB);
    
    const recordCount2 = await miningContract.getUserMiningRecordCount(user2.address);
    console.log(`✅ User2挖矿记录数: 机制一=${recordCount2.count1}, 机制二=${recordCount2.count2}`);
    
    // 7. 检查邀请奖励
    console.log("\n7. 检查邀请奖励...");
    const inviteInfo2 = await miningContract.getUserInviteInfo(user1.address);
    console.log(`✅ User1加速释放额度: ${ethers.formatEther(inviteInfo2.acceleratedReleaseAmount)} (价值)`);
    
    // 8. 测试价格查询
    console.log("\n8. 测试价格查询...");
    const tokenAPrice = await priceOracle.getTokenPrice(await tokenA.getAddress());
    const tokenBPrice = await priceOracle.getTokenPrice(await tokenB.getAddress());
    console.log(`✅ TokenA价格: ${ethers.formatEther(tokenAPrice)} BNB`);
    console.log(`✅ TokenB价格: ${ethers.formatEther(tokenBPrice)} BNB`);
    
    // 9. 检查NFT释放机制
    console.log("\n9. 测试NFT释放机制...");
    // 设置释放开始时间为当前时间
    await minerNFT.setReleaseParameters(
      Math.floor(Date.now() / 1000),
      30 * 24 * 3600, // 30天
      10 // 10%
    );
    
    const releasableAmount = await minerNFT.getReleasableAmount(1);
    console.log(`✅ NFT #1可释放数量: ${ethers.formatEther(releasableAmount)} TKA`);
    
    // 10. 统计信息
    console.log("\n10. 最终统计信息...");
    const totalBurnedA = await miningContract.totalBurnedTokenA();
    const totalBurnedB = await miningContract.totalBurnedTokenB();
    console.log(`✅ 累计销毁TokenA: ${ethers.formatEther(totalBurnedA)} TKA`);
    console.log(`✅ 累计销毁TokenB: ${ethers.formatEther(totalBurnedB)} TKB`);
    
    console.log("\n🎉 所有功能测试通过！");
    console.log("=".repeat(60));
    
  } catch (error) {
    console.error("\n❌ 功能测试失败:");
    console.error(error);
    process.exit(1);
  }
}

if (require.main === module) {
  main()
    .then(() => process.exit(0))
    .catch((error) => {
      console.error(error);
      process.exit(1);
    });
}

module.exports = { main };