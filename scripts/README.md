# NFT释放时间管理脚本

用于批量设置MinerNFT合约中每个NFT的个性化开始释放时间。

## 功能特点

- ✅ 批量设置多个NFT的释放时间
- ✅ 设置单个NFT的释放时间
- ✅ 查看所有NFT的当前释放时间状态
- ✅ Owner权限验证
- ✅ 完整的错误处理和日志

## 安装依赖

```bash
cd scripts
npm install
```

## 配置

编辑 `setNFTReleaseTime.js` 文件中的配置:

```javascript
const CONFIG = {
  // RPC URL
  RPC_URL: 'https://bsc-dataseed.binance.org/',
  
  // MinerNFT合约地址
  MINER_NFT_ADDRESS: '0x75fb297F699C350D9bD2bf3b69054b4766432F83',
  
  // Owner私钥 (请替换为实际的私钥)
  PRIVATE_KEY: 'your_private_key_here',
};
```

**⚠️ 安全提醒**: 
- 请妥善保管私钥，不要提交到版本控制系统
- 建议使用环境变量或配置文件存储敏感信息

## 使用方法

### 1. 查看当前状态

```javascript
const manager = new NFTReleaseTimeManager();
await manager.verifyOwner();
await manager.displayNFTStatus();
```

### 2. 批量设置释放时间

```javascript
// 设置NFT #1-5 的释放时间为2024年1月1日
const tokenIds = [1, 2, 3, 4, 5];
const timestamp = Math.floor(new Date('2024-01-01 00:00:00').getTime() / 1000);
await manager.batchSetReleaseTime(tokenIds, timestamp);
```

### 3. 设置单个NFT释放时间

```javascript
// 设置NFT #1 的释放时间为明天
const tokenId = 1;
const timestamp = Math.floor(Date.now() / 1000) + 86400; // 24小时后
await manager.setSingleReleaseTime(tokenId, timestamp);
```

### 4. 常用时间设置示例

```javascript
// 当前时间
const now = Math.floor(Date.now() / 1000);

// 1小时后
const oneHourLater = now + 3600;

// 1天后
const oneDayLater = now + 86400;

// 特定日期
const specificDate = Math.floor(new Date('2024-01-01 00:00:00').getTime() / 1000);

// 使用工具函数
const timestamp = dateToTimestamp('2024-01-01 00:00:00');
```

## 运行示例

### 运行预设示例
```bash
npm run example
```

### 自定义脚本
```bash
node setNFTReleaseTime.js
```

## 常见使用场景

### 场景1: 分批次释放
```javascript
// 第一批NFT (1-100): 立即开始释放
const batch1 = Array.from({length: 100}, (_, i) => i + 1);
const immediateStart = Math.floor(Date.now() / 1000);
await manager.batchSetReleaseTime(batch1, immediateStart);

// 第二批NFT (101-200): 1个月后开始释放
const batch2 = Array.from({length: 100}, (_, i) => i + 101);
const oneMonthLater = immediateStart + 30 * 24 * 3600;
await manager.batchSetReleaseTime(batch2, oneMonthLater);
```

### 场景2: 按等级分类释放
```javascript
// 稀有NFT: 立即释放
const rareNFTs = [1, 5, 10, 25, 50];
const nowTimestamp = Math.floor(Date.now() / 1000);
await manager.batchSetReleaseTime(rareNFTs, nowTimestamp);

// 普通NFT: 延迟释放
const commonNFTs = [2, 3, 4, 6, 7, 8, 9];
const delayedTimestamp = nowTimestamp + 7 * 24 * 3600; // 7天后
await manager.batchSetReleaseTime(commonNFTs, delayedTimestamp);
```

### 场景3: 线性递增释放时间
```javascript
const allNFTs = await manager.getAllNFTReleaseTimes();
const baseTime = Math.floor(new Date('2024-01-01 00:00:00').getTime() / 1000);
const intervalDays = 7; // 每7天释放下一批

for (let i = 0; i < allNFTs.length; i += 10) {
  const batch = allNFTs.slice(i, i + 10).map(nft => parseInt(nft.tokenId));
  const releaseTime = baseTime + Math.floor(i / 10) * intervalDays * 24 * 3600;
  await manager.batchSetReleaseTime(batch, releaseTime);
}
```

## API参考

### NFTReleaseTimeManager

#### 方法

- `verifyOwner()` - 验证当前钱包是否为合约owner
- `getAllNFTReleaseTimes()` - 获取所有NFT的释放时间信息
- `batchSetReleaseTime(tokenIds, timestamp)` - 批量设置NFT释放时间
- `setSingleReleaseTime(tokenId, timestamp)` - 设置单个NFT释放时间
- `displayNFTStatus()` - 显示所有NFT的当前状态

#### 工具函数

- `dateToTimestamp(dateString)` - 将日期字符串转换为Unix时间戳
- `timestampToDate(timestamp)` - 将Unix时间戳转换为日期字符串

## 注意事项

1. **权限要求**: 只有合约owner才能执行设置操作
2. **Gas费用**: 批量操作比单个操作更节省Gas
3. **时间格式**: 使用Unix时间戳（秒）
4. **网络延迟**: 请等待交易确认后再进行下一步操作
5. **备份数据**: 建议在大批量操作前备份当前状态

## 故障排除

### 常见错误

1. **权限不足**: 确保使用的是合约owner的私钥
2. **RPC连接失败**: 检查RPC_URL是否正确
3. **Gas不足**: 确保钱包有足够的BNB支付Gas费
4. **时间戳格式错误**: 确保使用正确的Unix时间戳格式

### 调试方法

```javascript
// 开启详细日志
console.log('当前钱包地址:', await manager.wallet.getAddress());
console.log('合约owner:', await manager.contract.owner());
console.log('设置时间戳:', timestamp);
console.log('设置时间:', new Date(timestamp * 1000).toLocaleString());
```