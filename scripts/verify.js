const { network } = require("hardhat");
const fs = require("fs");
const path = require("path");

// 从部署文件读取合约地址
function loadDeploymentInfo() {
  const deploymentDir = path.join(__dirname, "../deployments");
  const fileName = `${network.name}-deployment.json`;
  const filePath = path.join(deploymentDir, fileName);
  
  if (!fs.existsSync(filePath)) {
    throw new Error(`部署文件不存在: ${filePath}`);
  }
  
  return JSON.parse(fs.readFileSync(filePath, "utf8"));
}

// 验证单个合约
async function verifyContract(contractName, address, constructorArgs) {
  try {
    console.log(`\n验证合约 ${contractName}: ${address}`);
    console.log(`构造函数参数:`, constructorArgs);
    
    await hre.run("verify:verify", {
      address: address,
      constructorArguments: constructorArgs,
    });
    
    console.log(`✅ ${contractName} 验证成功`);
    return true;
  } catch (error) {
    console.log(`❌ ${contractName} 验证失败:`, error.message);
    return false;
  }
}

async function main() {
  console.log("=".repeat(60));
  console.log("开始验证合约");
  console.log("=".repeat(60));
  
  if (network.name === "hardhat") {
    console.log("跳过本地网络的合约验证");
    return;
  }
  
  try {
    // 加载部署信息
    const deploymentInfo = loadDeploymentInfo();
    console.log(`\n验证网络: ${network.name}`);
    console.log(`部署时间: ${deploymentInfo.timestamp}`);
    
    const contracts = deploymentInfo.contracts;
    let successCount = 0;
    let totalCount = 0;
    
    // 验证所有合约
    for (const [contractName, contractInfo] of Object.entries(contracts)) {
      totalCount++;
      const success = await verifyContract(
        contractName,
        contractInfo.address,
        contractInfo.constructorArgs
      );
      
      if (success) {
        successCount++;
      }
      
      // 验证间隔
      await new Promise(resolve => setTimeout(resolve, 5000));
    }
    
    console.log("\n" + "=".repeat(60));
    console.log("验证完成总结");
    console.log("=".repeat(60));
    console.log(`成功验证: ${successCount}/${totalCount} 个合约`);
    
    if (successCount === totalCount) {
      console.log("🎉 所有合约验证成功!");
    } else {
      console.log("⚠️ 部分合约验证失败，请检查错误信息");
    }
    
  } catch (error) {
    console.error("\n❌ 验证过程中发生错误:");
    console.error(error);
    process.exit(1);
  }
}

// 执行验证
if (require.main === module) {
  main()
    .then(() => process.exit(0))
    .catch((error) => {
      console.error(error);
      process.exit(1);
    });
}

module.exports = { main };