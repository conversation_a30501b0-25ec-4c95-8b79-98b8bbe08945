const { expect } = require("chai");
const { ethers } = require("hardhat");

describe("边界条件详细分析测试", function () {
  let tokenA, tokenB, minerNFT;
  let mockWBNB, mockFactory, mockRouter;
  let owner, marketingWallet, user1, user2, user3;
  
  beforeEach(async function () {
    [owner, marketingWallet, user1, user2, user3] = await ethers.getSigners();
    
    // 部署模拟环境
    const MockWBNB = await ethers.getContractFactory("MockWBNB");
    mockWBNB = await MockWBNB.deploy();
    await mockWBNB.waitForDeployment();
    
    const MockPancakeFactory = await ethers.getContractFactory("MockPancakeFactory");
    mockFactory = await MockPancakeFactory.deploy();
    await mockFactory.waitForDeployment();
    
    const MockPancakeRouter = await ethers.getContractFactory("MockPancakeRouter");
    mockRouter = await MockPancakeRouter.deploy(
      await mockFactory.getAddress(),
      await mockWBNB.getAddress()
    );
    await mockRouter.waitForDeployment();
    
    // 部署TokenA
    const TokenA = await ethers.getContractFactory("TokenA");
    tokenA = await TokenA.deploy(
      await marketingWallet.getAddress(),
      await mockRouter.getAddress(),
      await mockWBNB.getAddress()
    );
    await tokenA.waitForDeployment();
    
    // 部署TokenB
    const TokenB = await ethers.getContractFactory("TokenB");
    tokenB = await TokenB.deploy(
      await marketingWallet.getAddress(),
      await mockRouter.getAddress(),
      await mockWBNB.getAddress()
    );
    await tokenB.waitForDeployment();
    
    // 部署NFT
    const MinerNFT = await ethers.getContractFactory("MinerNFT");
    minerNFT = await MinerNFT.deploy("MinerNFT", "MNFT", await tokenA.getAddress());
    await minerNFT.waitForDeployment();
    
    // 配置关系
    await tokenA.setNftContract(await minerNFT.getAddress());
    await tokenA.transfer(await minerNFT.getAddress(), ethers.parseEther("21000000"));
  });

  describe("1. 税收计算精度分析", function () {
    it("应该分析税收计算中的精度损失", async function () {
      console.log("=== 税收计算精度分析 ===");
      
      // 创建交易对
      const pairAddress = await createTradingPair();
      
      // 测试不同金额的税收计算精度
      const testAmounts = [
        1n,                           // 1 wei - 极小值
        1000n,                        // 1000 wei - 小值
        ethers.parseEther("0.001"),   // 0.001 ETH - 小数点
        ethers.parseEther("1"),       // 1 ETH - 整数
        ethers.parseEther("100"),     // 100 ETH - 中等
        ethers.parseEther("10000"),   // 10000 ETH - 大额
      ];
      
      for (let i = 0; i < testAmounts.length; i++) {
        const amount = testAmounts[i];
        
        // 给用户足够的代币
        await tokenA.transfer(await user1.getAddress(), amount + ethers.parseEther("1000"));
        
        // 记录转账前状态
        const initialMarketingBalance = await tokenA.balanceOf(await marketingWallet.getAddress());
        const initialTotalSupply = await tokenA.totalSupply();
        const initialContractBalance = await tokenA.balanceOf(await tokenA.getAddress());
        
        // 执行转账
        await tokenA.connect(user1).transfer(pairAddress, amount);
        
        // 记录转账后状态
        const finalMarketingBalance = await tokenA.balanceOf(await marketingWallet.getAddress());
        const finalTotalSupply = await tokenA.totalSupply();
        const finalContractBalance = await tokenA.balanceOf(await tokenA.getAddress());
        
        // 计算实际变化
        const marketingIncrease = finalMarketingBalance - initialMarketingBalance;
        const burnedAmount = initialTotalSupply - finalTotalSupply;
        const dividendIncrease = finalContractBalance - initialContractBalance;
        const totalTaxCollected = marketingIncrease + burnedAmount + dividendIncrease;
        
        // 计算理论税收
        const theoreticalTax = amount * BigInt(3) / BigInt(100);
        const theoreticalMarketing = theoreticalTax * BigInt(20) / BigInt(100);
        const theoreticalBurn = theoreticalTax * BigInt(50) / BigInt(100);
        const theoreticalDividend = theoreticalTax * BigInt(30) / BigInt(100);
        
        console.log(`\n测试金额: ${ethers.formatEther(amount)} ETH`);
        console.log(`理论税收: ${ethers.formatEther(theoreticalTax)} ETH`);
        console.log(`实际税收: ${ethers.formatEther(totalTaxCollected)} ETH`);
        console.log(`营销分配 - 理论: ${ethers.formatEther(theoreticalMarketing)}, 实际: ${ethers.formatEther(marketingIncrease)}`);
        console.log(`销毁金额 - 理论: ${ethers.formatEther(theoreticalBurn)}, 实际: ${ethers.formatEther(burnedAmount)}`);
        console.log(`分红金额 - 理论: ${ethers.formatEther(theoreticalDividend)}, 实际: ${ethers.formatEther(dividendIncrease)}`);
        
        // 计算精度损失
        const taxPrecisionLoss = theoreticalTax > totalTaxCollected ? 
          theoreticalTax - totalTaxCollected : totalTaxCollected - theoreticalTax;
        const precisionLossPercentage = Number(taxPrecisionLoss * BigInt(10000) / theoreticalTax) / 100;
        
        console.log(`精度损失: ${ethers.formatEther(taxPrecisionLoss)} ETH (${precisionLossPercentage}%)`);
        
        // 对于大金额，精度损失应该很小
        if (amount >= ethers.parseEther("1")) {
          expect(precisionLossPercentage).to.be.lt(5); // 小于5%
        }
      }
      
      console.log("\n✓ 税收计算精度分析完成");
    });
    
    it("应该分析分红计算的精度", async function () {
      console.log("=== 分红计算精度分析 ===");
      
      // 创建不同数量的NFT持有者
      const nftCounts = [1, 2, 5, 10, 50];
      
      for (let nftCount of nftCounts) {
        console.log(`\n--- 测试 ${nftCount} 个NFT的分红精度 ---`);
        
        // 重新部署合约以获得干净状态
        const TokenA = await ethers.getContractFactory("TokenA");
        const testTokenA = await TokenA.deploy(
          await marketingWallet.getAddress(),
          await mockRouter.getAddress(),
          await mockWBNB.getAddress()
        );
        await testTokenA.waitForDeployment();
        
        const MinerNFT = await ethers.getContractFactory("MinerNFT");
        const testNFT = await MinerNFT.deploy("TestNFT", "TNFT", await testTokenA.getAddress());
        await testNFT.waitForDeployment();
        
        await testTokenA.setNftContract(await testNFT.getAddress());
        await testTokenA.transfer(await testNFT.getAddress(), ethers.parseEther("21000000"));
        
        // 创建NFT持有者
        for (let i = 0; i < nftCount; i++) {
          await testNFT.mint(await user1.getAddress());
        }
        
        // 创建交易对
        const liquidityTokenA = ethers.parseEther("50000");
        const liquidityWBNB = ethers.parseEther("50");
        
        await mockWBNB.deposit({ value: liquidityWBNB });
        await testTokenA.approve(await mockRouter.getAddress(), liquidityTokenA);
        await mockWBNB.approve(await mockRouter.getAddress(), liquidityWBNB);
        
        await mockRouter.addLiquidity(
          await testTokenA.getAddress(),
          await mockWBNB.getAddress(),
          liquidityTokenA,
          liquidityWBNB,
          0, 0,
          await owner.getAddress(),
          Math.floor(Date.now() / 1000) + 3600
        );
        
        const pairAddress = await mockFactory.getPair(await testTokenA.getAddress(), await mockWBNB.getAddress());
        await testTokenA.setPair(pairAddress, true);
        
        // 产生分红
        const transferAmount = ethers.parseEther("10000");
        await testTokenA.transfer(await user2.getAddress(), transferAmount);
        await testTokenA.connect(user2).transfer(pairAddress, transferAmount);
        
        // 计算分红精度
        const totalTax = transferAmount * BigInt(3) / BigInt(100);
        const totalDividend = totalTax * BigInt(30) / BigInt(100);
        const expectedPerNFT = totalDividend / BigInt(nftCount);
        
        const actualUserDividend = await testTokenA.calculateDividend(await user1.getAddress());
        const actualPerNFT = actualUserDividend / BigInt(nftCount);
        
        console.log(`总分红池: ${ethers.formatEther(totalDividend)} ETH`);
        console.log(`理论每NFT分红: ${ethers.formatEther(expectedPerNFT)} ETH`);
        console.log(`实际每NFT分红: ${ethers.formatEther(actualPerNFT)} ETH`);
        
        const dividendPrecisionLoss = expectedPerNFT > actualPerNFT ? 
          expectedPerNFT - actualPerNFT : actualPerNFT - expectedPerNFT;
        const dividendPrecisionLossPercentage = Number(dividendPrecisionLoss * BigInt(10000) / expectedPerNFT) / 100;
        
        console.log(`分红精度损失: ${ethers.formatEther(dividendPrecisionLoss)} ETH (${dividendPrecisionLossPercentage}%)`);
        
        // 分红精度损失应该很小
        expect(dividendPrecisionLossPercentage).to.be.lt(1); // 小于1%
      }
      
      console.log("\n✓ 分红计算精度分析完成");
    });
  });

  describe("2. 极端数值边界测试", function () {
    it("应该处理最小可能的税收收取", async function () {
      console.log("=== 最小税收收取测试 ===");
      
      const pairAddress = await createTradingPair();
      
      // 找到能产生最小非零税收的转账金额
      // 3% 税收，最小税收为1 wei，所以最小转账为 100/3 = 34 wei
      const minTaxAmount = BigInt(34); // 34 wei 应该产生 1 wei 税收
      
      await tokenA.transfer(await user1.getAddress(), ethers.parseEther("1"));
      
      const initialContractBalance = await tokenA.balanceOf(await tokenA.getAddress());
      await tokenA.connect(user1).transfer(pairAddress, minTaxAmount);
      const finalContractBalance = await tokenA.balanceOf(await tokenA.getAddress());
      
      const taxCollected = finalContractBalance - initialContractBalance;
      console.log(`转账金额: ${minTaxAmount} wei`);
      console.log(`收取税收: ${taxCollected} wei`);
      
      // 验证税收收取的下限行为
      if (taxCollected > 0) {
        console.log("✓ 最小税收成功收取");
      } else {
        console.log("✓ 金额过小，税收为零（符合预期）");
      }
    });
    
    it("应该测试接近uint256最大值的安全边界", async function () {
      console.log("=== uint256边界安全测试 ===");
      
      // 测试累积分红率不会溢出
      const maxSafeValue = 2n ** 128n - 1n; // 使用较小的安全值进行测试
      
      // 模拟极大的累积分红率计算
      try {
        const testCalculation = maxSafeValue * BigInt(1e18) / BigInt(1000);
        console.log(`大数值计算结果: ${testCalculation.toString().slice(0, 20)}...`);
        
        // 验证计算不会导致溢出
        expect(testCalculation).to.be.gte(0);
        console.log("✓ 大数值计算安全");
      } catch (error) {
        console.error("大数值计算失败:", error.message);
        throw error;
      }
    });
    
    it("应该测试分红累积的长期稳定性", async function () {
      console.log("=== 分红累积长期稳定性测试 ===");
      
      const pairAddress = await createTradingPair();
      await minerNFT.mint(await user1.getAddress());
      
      // 模拟长期运行：多次小额交易产生分红
      let totalExpectedDividend = 0n;
      const transactionCount = 100;
      const transferAmount = ethers.parseEther("100");
      
      console.log(`执行 ${transactionCount} 次交易...`);
      
      for (let i = 0; i < transactionCount; i++) {
        await tokenA.transfer(await user2.getAddress(), transferAmount);
        await tokenA.connect(user2).transfer(pairAddress, transferAmount);
        
        // 计算预期累积分红
        const tax = transferAmount * BigInt(3) / BigInt(100);
        const dividend = tax * BigInt(30) / BigInt(100);
        totalExpectedDividend += dividend;
        
        // 每10次交易检查一次状态
        if ((i + 1) % 10 === 0) {
          const currentDividend = await tokenA.calculateDividend(await user1.getAddress());
          console.log(`第${i+1}次交易后，累积分红: ${ethers.formatEther(currentDividend)} ETH`);
        }
      }
      
      const finalDividend = await tokenA.calculateDividend(await user1.getAddress());
      console.log(`最终累积分红: ${ethers.formatEther(finalDividend)} ETH`);
      console.log(`理论累积分红: ${ethers.formatEther(totalExpectedDividend)} ETH`);
      
      // 验证长期累积的精度
      const cumulativePrecisionLoss = totalExpectedDividend > finalDividend ?
        totalExpectedDividend - finalDividend : finalDividend - totalExpectedDividend;
      const cumulativePrecisionLossPercentage = Number(cumulativePrecisionLoss * BigInt(10000) / totalExpectedDividend) / 100;
      
      console.log(`累积精度损失: ${cumulativePrecisionLossPercentage}%`);
      expect(cumulativePrecisionLossPercentage).to.be.lt(5); // 小于5%
      
      console.log("✓ 长期累积稳定性良好");
    });
  });

  describe("3. 状态转换边界测试", function () {
    it("应该测试从无NFT到有NFT的状态转换", async function () {
      console.log("=== 无NFT到有NFT状态转换测试 ===");
      
      const pairAddress = await createTradingPair();
      
      // 初始状态：用户无NFT
      let userDividend = await tokenA.calculateDividend(await user1.getAddress());
      expect(userDividend).to.equal(0);
      console.log("初始状态：用户无分红");
      
      // 产生一些分红（用户无法获得）
      await tokenA.transfer(await user2.getAddress(), ethers.parseEther("10000"));
      await tokenA.connect(user2).transfer(pairAddress, ethers.parseEther("5000"));
      
      userDividend = await tokenA.calculateDividend(await user1.getAddress());
      expect(userDividend).to.equal(0);
      console.log("产生分红后：用户仍无分红");
      
      // 用户获得NFT
      await minerNFT.mint(await user1.getAddress());
      console.log("用户获得NFT");
      
      // 再次产生分红
      await tokenA.connect(user2).transfer(pairAddress, ethers.parseEther("5000"));
      
      userDividend = await tokenA.calculateDividend(await user1.getAddress());
      console.log(`获得NFT后的分红: ${ethers.formatEther(userDividend)} ETH`);
      
      expect(userDividend).to.be.gt(0);
      console.log("✓ 状态转换正确：获得NFT后开始累积分红");
    });
    
    it("应该测试NFT转移的分红归属转换", async function () {
      console.log("=== NFT转移分红归属转换测试 ===");
      
      const pairAddress = await createTradingPair();
      
      // 用户A获得NFT
      await minerNFT.mint(await user1.getAddress());
      const tokenId = 1;
      
      // 产生分红
      await tokenA.transfer(await user3.getAddress(), ethers.parseEther("10000"));
      await tokenA.connect(user3).transfer(pairAddress, ethers.parseEther("5000"));
      
      const dividendBeforeTransfer = await tokenA.calculateDividend(await user1.getAddress());
      console.log(`转移前用户A分红: ${ethers.formatEther(dividendBeforeTransfer)} ETH`);
      
      // 用户A领取分红
      if (dividendBeforeTransfer > 0) {
        await tokenA.connect(user1).claimDividend();
        console.log("用户A已领取分红");
      }
      
      // NFT转移给用户B
      await minerNFT.connect(user1).transferFrom(
        await user1.getAddress(),
        await user2.getAddress(),
        tokenId
      );
      console.log("NFT已转移给用户B");
      
      // 再次产生分红
      await tokenA.connect(user3).transfer(pairAddress, ethers.parseEther("5000"));
      
      const dividendAfterTransfer_A = await tokenA.calculateDividend(await user1.getAddress());
      const dividendAfterTransfer_B = await tokenA.calculateDividend(await user2.getAddress());
      
      console.log(`转移后用户A分红: ${ethers.formatEther(dividendAfterTransfer_A)} ETH`);
      console.log(`转移后用户B分红: ${ethers.formatEther(dividendAfterTransfer_B)} ETH`);
      
      expect(dividendAfterTransfer_A).to.equal(0);
      expect(dividendAfterTransfer_B).to.be.gt(0);
      
      console.log("✓ NFT转移后分红归属正确转换");
    });
    
    it("应该测试合约暂停恢复的状态一致性", async function () {
      console.log("=== 合约暂停恢复状态一致性测试 ===");
      
      const pairAddress = await createTradingPair();
      await minerNFT.mint(await user1.getAddress());
      
      // 产生分红
      await tokenA.transfer(await user2.getAddress(), ethers.parseEther("10000"));
      await tokenA.connect(user2).transfer(pairAddress, ethers.parseEther("5000"));
      
      const dividendBeforePause = await tokenA.calculateDividend(await user1.getAddress());
      const contractBalanceBeforePause = await tokenA.balanceOf(await tokenA.getAddress());
      
      console.log(`暂停前分红: ${ethers.formatEther(dividendBeforePause)} ETH`);
      console.log(`暂停前合约余额: ${ethers.formatEther(contractBalanceBeforePause)} ETH`);
      
      // 暂停合约
      await tokenA.pause();
      console.log("合约已暂停");
      
      // 暂停期间检查状态
      const dividendDuringPause = await tokenA.calculateDividend(await user1.getAddress());
      const contractBalanceDuringPause = await tokenA.balanceOf(await tokenA.getAddress());
      
      expect(dividendDuringPause).to.equal(dividendBeforePause);
      expect(contractBalanceDuringPause).to.equal(contractBalanceBeforePause);
      console.log("✓ 暂停期间状态保持不变");
      
      // 恢复合约
      await tokenA.unpause();
      console.log("合约已恢复");
      
      // 恢复后检查状态
      const dividendAfterResume = await tokenA.calculateDividend(await user1.getAddress());
      const contractBalanceAfterResume = await tokenA.balanceOf(await tokenA.getAddress());
      
      expect(dividendAfterResume).to.equal(dividendBeforePause);
      expect(contractBalanceAfterResume).to.equal(contractBalanceBeforePause);
      
      // 恢复后应该可以正常操作
      if (dividendAfterResume > 0) {
        await tokenA.connect(user1).claimDividend();
        console.log("恢复后成功领取分红");
      }
      
      console.log("✓ 合约恢复后状态一致，功能正常");
    });
  });

  describe("4. 并发操作边界测试", function () {
    it("应该测试同时进行税收和分红操作的一致性", async function () {
      console.log("=== 并发税收分红一致性测试 ===");
      
      const pairAddress = await createTradingPair();
      await minerNFT.mint(await user1.getAddress());
      
      // 记录初始状态
      const initialContractBalance = await tokenA.balanceOf(await tokenA.getAddress());
      const initialMarketingBalance = await tokenA.balanceOf(await marketingWallet.getAddress());
      const initialTotalSupply = await tokenA.totalSupply();
      
      // 执行多笔交易（模拟并发）
      const transferAmount = ethers.parseEther("1000");
      const transactionCount = 10;
      
      for (let i = 0; i < transactionCount; i++) {
        await tokenA.transfer(await user2.getAddress(), transferAmount);
        await tokenA.connect(user2).transfer(pairAddress, transferAmount);
      }
      
      // 记录最终状态
      const finalContractBalance = await tokenA.balanceOf(await tokenA.getAddress());
      const finalMarketingBalance = await tokenA.balanceOf(await marketingWallet.getAddress());
      const finalTotalSupply = await tokenA.totalSupply();
      
      // 计算实际变化
      const contractIncrease = finalContractBalance - initialContractBalance;
      const marketingIncrease = finalMarketingBalance - initialMarketingBalance;
      const totalBurned = initialTotalSupply - finalTotalSupply;
      
      // 计算理论值
      const totalTransferred = transferAmount * BigInt(transactionCount);
      const theoreticalTotalTax = totalTransferred * BigInt(3) / BigInt(100);
      const theoreticalMarketing = theoreticalTotalTax * BigInt(20) / BigInt(100);
      const theoreticalBurn = theoreticalTotalTax * BigInt(50) / BigInt(100);
      const theoreticalDividend = theoreticalTotalTax * BigInt(30) / BigInt(100);
      
      console.log(`总转账: ${ethers.formatEther(totalTransferred)} ETH`);
      console.log(`理论总税收: ${ethers.formatEther(theoreticalTotalTax)} ETH`);
      console.log(`实际分配 - 营销: ${ethers.formatEther(marketingIncrease)}, 销毁: ${ethers.formatEther(totalBurned)}, 分红: ${ethers.formatEther(contractIncrease)}`);
      console.log(`理论分配 - 营销: ${ethers.formatEther(theoreticalMarketing)}, 销毁: ${ethers.formatEther(theoreticalBurn)}, 分红: ${ethers.formatEther(theoreticalDividend)}`);
      
      // 验证一致性（允许小的精度误差）
      const marketingPrecision = Number(abs(marketingIncrease - theoreticalMarketing) * BigInt(100) / theoreticalMarketing);
      const burnPrecision = Number(abs(totalBurned - theoreticalBurn) * BigInt(100) / theoreticalBurn);
      const dividendPrecision = Number(abs(contractIncrease - theoreticalDividend) * BigInt(100) / theoreticalDividend);
      
      console.log(`精度偏差 - 营销: ${marketingPrecision}%, 销毁: ${burnPrecision}%, 分红: ${dividendPrecision}%`);
      
      expect(marketingPrecision).to.be.lt(5);
      expect(burnPrecision).to.be.lt(5);
      expect(dividendPrecision).to.be.lt(5);
      
      console.log("✓ 并发操作税收分红一致性良好");
    });
  });

  // 辅助函数
  async function createTradingPair() {
    const liquidityTokenA = ethers.parseEther("50000");
    const liquidityWBNB = ethers.parseEther("50");
    
    await mockWBNB.deposit({ value: liquidityWBNB });
    await tokenA.approve(await mockRouter.getAddress(), liquidityTokenA);
    await mockWBNB.approve(await mockRouter.getAddress(), liquidityWBNB);
    
    await mockRouter.addLiquidity(
      await tokenA.getAddress(),
      await mockWBNB.getAddress(),
      liquidityTokenA,
      liquidityWBNB,
      0, 0,
      await owner.getAddress(),
      Math.floor(Date.now() / 1000) + 3600
    );
    
    const pairAddress = await mockFactory.getPair(await tokenA.getAddress(), await mockWBNB.getAddress());
    await tokenA.setPair(pairAddress, true);
    
    return pairAddress;
  }
  
  function abs(value) {
    return value >= 0n ? value : -value;
  }
});