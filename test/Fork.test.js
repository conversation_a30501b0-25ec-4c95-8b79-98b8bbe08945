const { expect } = require("chai");
const { ethers } = require("hardhat");

describe("Fork环境测试", function () {
  let tokenA, tokenB, minerNFT, miningContract, priceOracle;
  let owner, marketingWallet, user1, user2;
  
  // BSC测试网上的真实合约地址
  const PANCAKE_ROUTER = "******************************************"; // BSC测试网 PancakeSwap Router
  const WBNB = "******************************************"; // BSC测试网 WBNB

  before(async function () {
    // 检查是否在fork环境中运行
    const network = await ethers.provider.getNetwork();
    console.log("Network:", network.name, "Chain ID:", network.chainId);
    
    // 获取当前区块号
    const blockNumber = await ethers.provider.getBlockNumber();
    console.log("Current block number:", blockNumber);
    
    if (blockNumber < 1000000) {
      console.log("警告：可能不在fork环境中运行");
    }
  });

  beforeEach(async function () {
    [owner, marketingWallet, user1, user2] = await ethers.getSigners();
    
    console.log("Owner balance:", ethers.formatEther(await ethers.provider.getBalance(owner.address)), "BNB");

    // 部署价格预言机
    const PriceOracle = await ethers.getContractFactory("PriceOracle");
    priceOracle = await PriceOracle.deploy();
    await priceOracle.waitForDeployment();
    console.log("PriceOracle deployed to:", await priceOracle.getAddress());

    // 部署TokenA，使用真实的PancakeSwap地址
    const TokenA = await ethers.getContractFactory("TokenA");
    tokenA = await TokenA.deploy(
      await marketingWallet.getAddress(),
      PANCAKE_ROUTER,
      WBNB
    );
    await tokenA.waitForDeployment();
    console.log("TokenA deployed to:", await tokenA.getAddress());

    // 部署TokenB
    const TokenB = await ethers.getContractFactory("TokenB");
    tokenB = await TokenB.deploy(
      await marketingWallet.getAddress(),
      PANCAKE_ROUTER,
      WBNB
    );
    await tokenB.waitForDeployment();
    console.log("TokenB deployed to:", await tokenB.getAddress());

    // 部署NFT合约
    const MinerNFT = await ethers.getContractFactory("MinerNFT");
    minerNFT = await MinerNFT.deploy("MinerNFT", "MNFT", await tokenA.getAddress());
    await minerNFT.waitForDeployment();
    console.log("MinerNFT deployed to:", await minerNFT.getAddress());

    // 部署挖矿合约
    const MiningContract = await ethers.getContractFactory("MiningContract");
    miningContract = await MiningContract.deploy(
      await tokenA.getAddress(),
      await tokenB.getAddress(),
      await minerNFT.getAddress(),
      await priceOracle.getAddress()
    );
    await miningContract.waitForDeployment();
    console.log("MiningContract deployed to:", await miningContract.getAddress());

    // 配置合约关系
    await tokenA.setNftContract(await minerNFT.getAddress());
    console.log("NFT contract set for TokenA");
  });

  describe("Fork环境验证", function () {
    it("应该能够访问BSC测试网的PancakeSwap", async function () {
      // 验证PancakeSwap Router存在且可访问
      const routerCode = await ethers.provider.getCode(PANCAKE_ROUTER);
      expect(routerCode).to.not.equal("0x");
      console.log("PancakeSwap Router验证通过");

      // 验证WBNB合约存在
      const wbnbCode = await ethers.provider.getCode(WBNB);
      expect(wbnbCode).to.not.equal("0x");
      console.log("WBNB合约验证通过");
    });

    it("应该能够获取真实的WBNB余额", async function () {
      const wbnbContract = await ethers.getContractAt("IERC20", WBNB);
      const balance = await wbnbContract.balanceOf(owner.address);
      console.log("Owner WBNB balance:", ethers.formatEther(balance));
      // 在fork环境中，余额可能为0，这是正常的
    });

    it("应该能够获取BSC测试网的最新区块信息", async function () {
      const block = await ethers.provider.getBlock("latest");
      console.log("Latest block number:", block.number);
      console.log("Block timestamp:", new Date(block.timestamp * 1000));
      expect(block.number).to.be.greaterThan(1000000);
    });
  });

  describe("合约部署验证", function () {
    it("所有合约应该成功部署", async function () {
      expect(await tokenA.getAddress()).to.not.equal(ethers.ZeroAddress);
      expect(await tokenB.getAddress()).to.not.equal(ethers.ZeroAddress);
      expect(await minerNFT.getAddress()).to.not.equal(ethers.ZeroAddress);
      expect(await miningContract.getAddress()).to.not.equal(ethers.ZeroAddress);
      expect(await priceOracle.getAddress()).to.not.equal(ethers.ZeroAddress);
    });

    it("TokenA应该正确设置初始参数", async function () {
      expect(await tokenA.name()).to.equal("TokenA");
      expect(await tokenA.symbol()).to.equal("TKA");
      expect(await tokenA.totalSupply()).to.equal(ethers.parseEther("250000000"));
      expect(await tokenA.marketingWallet()).to.equal(await marketingWallet.getAddress());
    });

    it("应该能够创建PancakeSwap交易对", async function () {
      // 在fork环境中手动创建交易对
      await tokenA.createAndSetPair();
      
      // 验证交易对是否设置了免税状态
      // 这里我们无法直接验证交易对地址，因为它需要实际的流动性
      console.log("PancakeSwap交易对创建尝试完成");
    });
  });

  describe("Fork环境下的真实交互测试", function () {
    it("应该能够在fork环境中进行代币转账", async function () {
      const transferAmount = ethers.parseEther("1000");
      
      // 检查初始余额
      const initialBalance = await tokenA.balanceOf(owner.address);
      console.log("Owner initial TokenA balance:", ethers.formatEther(initialBalance));
      
      // 转账给user1
      await tokenA.transfer(user1.address, transferAmount);
      
      // 验证转账
      const user1Balance = await tokenA.balanceOf(user1.address);
      expect(user1Balance).to.equal(transferAmount);
      
      const ownerNewBalance = await tokenA.balanceOf(owner.address);
      expect(ownerNewBalance).to.equal(initialBalance - transferAmount);
      
      console.log("代币转账测试通过");
    });

    it("应该能够铸造NFT", async function () {
      // 转移一些TokenA到NFT合约用于释放
      const nftTokenAmount = ethers.parseEther("1000000"); // 100万TokenA
      await tokenA.transfer(await minerNFT.getAddress(), nftTokenAmount);
      
      // 铸造NFT给user1
      await minerNFT.mint(user1.address);
      
      // 验证NFT铸造
      const balance = await minerNFT.balanceOf(user1.address);
      expect(balance).to.equal(1);
      
      const owner = await minerNFT.ownerOf(1);
      expect(owner).to.equal(user1.address);
      
      console.log("NFT铸造测试通过");
    });
  });

  describe("Gas使用量测试", function () {
    it("应该记录各个操作的Gas使用量", async function () {
      // 代币转账Gas测试
      const tx1 = await tokenA.transfer(user1.address, ethers.parseEther("100"));
      const receipt1 = await tx1.wait();
      console.log("TokenA transfer gas used:", receipt1.gasUsed.toString());

      // NFT铸造Gas测试
      const tx2 = await minerNFT.mint(user1.address);
      const receipt2 = await tx2.wait();
      console.log("NFT mint gas used:", receipt2.gasUsed.toString());

      // 设置NFT合约Gas测试
      const tx3 = await tokenA.setTaxExempt(user1.address, true);
      const receipt3 = await tx3.wait();
      console.log("Set tax exempt gas used:", receipt3.gasUsed.toString());
    });
  });

  describe("错误处理测试", function () {
    it("应该正确处理无效的价格预言机查询", async function () {
      // 测试获取不存在代币的价格
      const randomToken = ethers.Wallet.createRandom().address;
      
      try {
        await priceOracle.getTokenPrice(randomToken);
        expect.fail("应该抛出错误");
      } catch (error) {
        expect(error.message).to.include("Pair does not exist");
        console.log("价格预言机错误处理测试通过");
      }
    });
  });
});