const { expect } = require("chai");
const { ethers } = require("hardhat");
const { time } = require("@nomicfoundation/hardhat-network-helpers");

describe("深度调试6期释放", function () {
  let miningContract, tokenA, tokenB, user1;
  
  before(async function () {
    // 从其他测试复制设置...
    [user1] = await ethers.getSigners();
    
    // 简化设置，使用已有的环境
    const ContractFactory = await ethers.getContractFactory("MiningContract");
    // 这里需要实际的合约地址，或者重新部署
  });
  
  it("应该深度调试每期的内部状态", async function () {
    console.log("=== 深度调试测试 ===");
    
    // 直接调用合约的内部函数进行调试
    // 模拟每期释放的计算过程
    
    const startTime = Math.floor(Date.now() / 1000);
    const totalValue = ethers.parseEther("20"); // 20 BNB
    const releaseInterval = 30 * 24 * 3600; // 30天
    
    console.log("模拟释放计算：");
    
    for (let period = 1; period <= 6; period++) {
      const currentTime = startTime + period * releaseInterval;
      const periodsElapsed = Math.floor((currentTime - startTime) / releaseInterval);
      
      // 模拟合约计算
      const shouldHaveReleased = (totalValue * BigInt(periodsElapsed)) / BigInt(6);
      
      console.log(`第${period}期:`);
      console.log(`  经过时间: ${currentTime - startTime} 秒`);
      console.log(`  经过期数: ${periodsElapsed}`);
      console.log(`  应该累积释放: ${ethers.formatEther(shouldHaveReleased)} BNB`);
      
      // 模拟已释放记录的更新
      if (period === 1) {
        const alreadyReleased = BigInt(0);
        const claimable = shouldHaveReleased - alreadyReleased;
        console.log(`  已释放: ${ethers.formatEther(alreadyReleased)} BNB`);
        console.log(`  可领取: ${ethers.formatEther(claimable)} BNB`);
        console.log(`  --> ${claimable > 0 ? '有可领取' : '无可领取'}`);
      } else if (period === 2) {
        // 假设第1期领取了
        const alreadyReleased = (totalValue * BigInt(1)) / BigInt(6);
        const claimable = shouldHaveReleased - alreadyReleased;
        console.log(`  已释放: ${ethers.formatEther(alreadyReleased)} BNB`);
        console.log(`  可领取: ${ethers.formatEther(claimable)} BNB`);
        console.log(`  --> ${claimable > 0 ? '有可领取' : '无可领取'}`);
      } else if (period === 3) {
        // 假设第1、2期都领取了
        const alreadyReleased = (totalValue * BigInt(2)) / BigInt(6);
        const claimable = shouldHaveReleased - alreadyReleased;
        console.log(`  已释放: ${ethers.formatEther(alreadyReleased)} BNB`);
        console.log(`  可领取: ${ethers.formatEther(claimable)} BNB`);
        console.log(`  --> ${claimable > 0 ? '有可领取' : '无可领取'}`);
      }
      console.log();
    }
  });
});