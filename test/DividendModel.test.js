const { expect } = require("chai");
const { ethers } = require("hardhat");

describe("股息模型分红测试", function () {
  let tokenA;
  let owner, marketingWallet, user1, user2, user3, pancakeSwapRouter, wbnb;
  let mockNFT;

  const TOTAL_SUPPLY = ethers.parseEther("250000000"); // 2.5亿

  beforeEach(async function () {
    [owner, marketingWallet, user1, user2, user3, pancakeSwapRouter, wbnb] = await ethers.getSigners();

    // 部署TokenA合约
    const TokenA = await ethers.getContractFactory("TokenA");
    tokenA = await TokenA.deploy(
      await marketingWallet.getAddress(),
      await pancakeSwapRouter.getAddress(),
      await wbnb.getAddress()
    );

    // 部署MinerNFT合约
    const MinerNFT = await ethers.getContractFactory("MinerNFT");
    mockNFT = await MinerNFT.deploy("MinerNFT", "MNFT", await tokenA.getAddress());

    // 设置NFT合约地址
    await tokenA.setNftContract(await mockNFT.getAddress());
    
    // 禁用自动分红以测试手动分红功能
    await tokenA.setAutoDividendConfig(500000, 10, ethers.parseEther("100"), false);
    
    // 设置交易对
    await tokenA.setPair(await user2.getAddress(), true);
    
    // 分发一些代币给用户
    await tokenA.transfer(await user1.getAddress(), ethers.parseEther("10000"));
    await tokenA.transfer(await user3.getAddress(), ethers.parseEther("10000"));
  });

  describe("股息模型分红机制", function () {
    it("应该正确更新累积每股分红", async function () {
      // 给用户铸造NFT
      await mockNFT.mint(await user1.getAddress());
      
      const initialCumulativeDividend = await tokenA.cumulativeDividendPerShare();
      
      // 执行交易产生分红
      await tokenA.connect(user1).transfer(await user2.getAddress(), ethers.parseEther("1000"));
      
      const finalCumulativeDividend = await tokenA.cumulativeDividendPerShare();
      expect(finalCumulativeDividend).to.be.gt(initialCumulativeDividend);
    });

    it("应该正确记录NFT分红信息", async function () {
      // 给用户铸造NFT
      await mockNFT.mint(await user1.getAddress());
      
      // 执行交易产生分红
      await tokenA.connect(user1).transfer(await user2.getAddress(), ethers.parseEther("1000"));
      
      // 领取分红
      await tokenA.connect(user1).claimDividend();
      
      // 获取用户NFT列表并检查分红记录
      const userNFTs = await mockNFT.getUserNFTs(await user1.getAddress());
      const nftInfo = await tokenA.nftDividendInfo(userNFTs[0]);
      expect(nftInfo.lastCumulativeDividend).to.be.gt(0);
      expect(nftInfo.claimedAmount).to.be.gt(0);
    });

    it("多次分红应该累积正确", async function () {
      // 给用户铸造NFT
      await mockNFT.mint(await user1.getAddress());
      
      // 第一次分红
      await tokenA.connect(user1).transfer(await user2.getAddress(), ethers.parseEther("1000"));
      const firstDividend = await tokenA.calculateDividend(await user1.getAddress());
      
      // 第二次分红
      await tokenA.connect(user3).transfer(await user2.getAddress(), ethers.parseEther("1000"));
      const secondDividend = await tokenA.calculateDividend(await user1.getAddress());
      
      // 第二次分红应该大于第一次
      expect(secondDividend).to.be.gt(firstDividend);
    });

    it("NFT转移后分红应该正确计算", async function () {
      // 给用户1铸造NFT
      await mockNFT.mint(await user1.getAddress());
      
      // 产生分红
      await tokenA.connect(user1).transfer(await user2.getAddress(), ethers.parseEther("1000"));
      
      // 用户1领取分红
      await tokenA.connect(user1).claimDividend();
      
      // 获取用户1的NFT列表并转移第一个给用户3
      const user1NFTs = await mockNFT.getUserNFTs(await user1.getAddress());
      await mockNFT.connect(user1).transferFrom(await user1.getAddress(), await user3.getAddress(), user1NFTs[0]);
      
      // 等待一个区块确保转移完成
      await ethers.provider.send("evm_mine", []);
      
      // 验证NFT转移是否成功
      const user1NFTsAfterTransfer = await mockNFT.getUserNFTs(await user1.getAddress());
      const user3NFTsAfterTransfer = await mockNFT.getUserNFTs(await user3.getAddress());
      
      // 再次产生分红
      await tokenA.connect(user1).transfer(await user2.getAddress(), ethers.parseEther("1000"));
      
      // 用户1分红计算
      const user1Dividend = await tokenA.calculateDividend(await user1.getAddress());
      
      // 用户3分红计算
      const user3Dividend = await tokenA.calculateDividend(await user3.getAddress());
      
      // 如果用户1还有NFT，那么它可能还有分红；否则应该没有分红
      if (user1NFTsAfterTransfer.length === 0) {
        expect(user1Dividend).to.equal(0);
      }
      expect(user3Dividend).to.be.gt(0);
    });

    it("多个NFT持有者应该按比例分红", async function () {
      // 给用户1铸造2个NFT
      await mockNFT.mint(await user1.getAddress());
      await mockNFT.mint(await user1.getAddress());
      
      // 给用户3铸造1个NFT
      await mockNFT.mint(await user3.getAddress());
      
      // 产生分红
      await tokenA.connect(user1).transfer(await user2.getAddress(), ethers.parseEther("3000"));
      
      const user1Dividend = await tokenA.calculateDividend(await user1.getAddress());
      const user3Dividend = await tokenA.calculateDividend(await user3.getAddress());
      
      // 用户1持有2个NFT，用户3持有1个NFT
      // 用户1的分红应该是用户3的2倍
      expect(user1Dividend).to.be.approximately(user3Dividend * BigInt(2), user3Dividend / BigInt(10));
    });

    it("应该防止重复领取分红", async function () {
      // 给用户铸造NFT
      await mockNFT.mint(await user1.getAddress());
      
      // 产生分红
      await tokenA.connect(user1).transfer(await user2.getAddress(), ethers.parseEther("1000"));
      
      // 第一次领取分红
      const initialBalance = await tokenA.balanceOf(await user1.getAddress());
      await tokenA.connect(user1).claimDividend();
      const balanceAfterFirstClaim = await tokenA.balanceOf(await user1.getAddress());
      
      const firstClaimAmount = balanceAfterFirstClaim - initialBalance;
      expect(firstClaimAmount).to.be.gt(0);
      
      // 尝试再次领取分红（应该失败）
      await expect(
        tokenA.connect(user1).claimDividend()
      ).to.be.revertedWith("No dividend to claim");
    });

    it("单个NFT分红领取功能", async function () {
      // 给用户铸造多个NFT  
      await mockNFT.mint(await user1.getAddress());
      await mockNFT.mint(await user1.getAddress());
      
      // 产生分红
      await tokenA.connect(user1).transfer(await user2.getAddress(), ethers.parseEther("2000"));
      
      // 获取用户的NFT列表
      const userNFTs = await mockNFT.getUserNFTs(await user1.getAddress());
      
      // 只领取第一个NFT的分红
      const initialBalance = await tokenA.balanceOf(await user1.getAddress());
      await tokenA.connect(user1).claimNFTDividend(userNFTs[0]);
      const balanceAfterClaim = await tokenA.balanceOf(await user1.getAddress());
      
      const claimedAmount = balanceAfterClaim - initialBalance;
      expect(claimedAmount).to.be.gt(0);
      
      // 第一个NFT应该没有可领取分红了
      const nft1Dividend = await tokenA.calculateNFTDividend(userNFTs[0]);
      expect(nft1Dividend).to.equal(0);
      
      // 检查第二个NFT是否还有可领取分红
      if (userNFTs.length > 1) {
        const nft2Dividend = await tokenA.calculateNFTDividend(userNFTs[1]);
        expect(nft2Dividend).to.be.gt(0);
      }
    });
  });
});

