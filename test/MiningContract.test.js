const { expect } = require("chai");
const { ethers } = require("hardhat");

describe("MiningContract 全面测试", function () {
  let tokenA, tokenB, minerNFT, priceOracle, miningContract;
  let owner, user1, user2, user3, user4, marketingWallet, pancakeSwapRouter, wbnb;
  
  const INITIAL_TOKENA_PRICE = ethers.parseEther("0.01"); // 0.01 BNB
  const INITIAL_TOKENB_PRICE = ethers.parseEther("0.02"); // 0.02 BNB
  const REWARD_MULTIPLIER = 2;
  const MONTHLY_RELEASE_RATE = 30; // 30%
  const INVITE_REWARD_RATE = 10; // 10%

  beforeEach(async function () {
    [owner, user1, user2, user3, user4, marketingWallet, pancakeSwapRouter, wbnb] = await ethers.getSigners();

    // 部署TokenA
    const TokenA = await ethers.getContractFactory("TokenA");
    tokenA = await TokenA.deploy(
      await marketingWallet.getAddress(),
      await pancakeSwapRouter.getAddress(),
      await wbnb.getAddress()
    );

    // 部署TokenB
    const TokenB = await ethers.getContractFactory("TokenB");
    tokenB = await TokenB.deploy(
      await marketingWallet.getAddress(),
      await pancakeSwapRouter.getAddress(),
      await wbnb.getAddress()
    );

    // 部署MinerNFT
    const MinerNFT = await ethers.getContractFactory("MinerNFT");
    minerNFT = await MinerNFT.deploy("MinerNFT", "MNFT", await tokenA.getAddress());

    // 部署PriceOracle
    const PriceOracle = await ethers.getContractFactory("PriceOracle");
    priceOracle = await PriceOracle.deploy();

    // 部署MiningContract
    const MiningContract = await ethers.getContractFactory("MiningContract");
    miningContract = await MiningContract.deploy(
      await tokenA.getAddress(),
      await tokenB.getAddress(),
      await minerNFT.getAddress(),
      await priceOracle.getAddress()
    );

    // 设置权限和转入TokenB到挖矿合约
    await tokenA.setTaxExempt(await miningContract.getAddress(), true);
    
    // 将TokenB转入挖矿合约作为奖励池
    const tokenBReserve = ethers.parseEther("10000000"); // 1000万TokenB作为奖励池
    await tokenB.transfer(await miningContract.getAddress(), tokenBReserve);

    // 设置手动价格并延长价格有效期（避免调用PancakeSwap）
    await priceOracle.setManualPrice(await tokenA.getAddress(), INITIAL_TOKENA_PRICE);
    await priceOracle.setManualPrice(await tokenB.getAddress(), INITIAL_TOKENB_PRICE);
    
    // 设置很长的价格有效期，避免价格过期后调用PancakeSwap
    await priceOracle.setPriceParameters(1000, 1, 365 * 24 * 3600); // 1年有效期

    // 分发一些TokenA给用户
    await tokenA.transfer(await user1.getAddress(), ethers.parseEther("100000"));
    await tokenA.transfer(await user2.getAddress(), ethers.parseEther("100000"));
    await tokenA.transfer(await user3.getAddress(), ethers.parseEther("100000"));
    await tokenA.transfer(await user4.getAddress(), ethers.parseEther("100000"));

    // 给用户铸造一些NFT
    await minerNFT.mint(await user1.getAddress());
    await minerNFT.mint(await user2.getAddress());
    await minerNFT.mint(await user3.getAddress());

    // 用户授权TokenA给MiningContract
    await tokenA.connect(user1).approve(await miningContract.getAddress(), ethers.parseEther("100000"));
    await tokenA.connect(user2).approve(await miningContract.getAddress(), ethers.parseEther("100000"));
    await tokenA.connect(user3).approve(await miningContract.getAddress(), ethers.parseEther("100000"));
    await tokenA.connect(user4).approve(await miningContract.getAddress(), ethers.parseEther("100000"));
  });

  describe("邀请关系设置和数据验证", function () {
    it("应该能够正确设置邀请关系", async function () {
      // user2邀请user1
      await miningContract.connect(user1).setInviter(await user2.getAddress());
      
      // 验证邀请关系
      const user1Info = await miningContract.getUserInviteInfo(await user1.getAddress());
      expect(user1Info.inviter).to.equal(await user2.getAddress());
      
      const user2Info = await miningContract.getUserInviteInfo(await user2.getAddress());
      expect(user2Info.totalInvited).to.equal(1);
      expect(user2Info.inviteeList.length).to.equal(1);
      expect(user2Info.inviteeList[0]).to.equal(await user1.getAddress());

      // 验证hasInviter状态
      expect(await miningContract.hasInviter(await user1.getAddress())).to.be.true;
      expect(await miningContract.hasInviter(await user2.getAddress())).to.be.false;
    });

    it("应该防止重复设置邀请人", async function () {
      await miningContract.connect(user1).setInviter(await user2.getAddress());
      
      await expect(
        miningContract.connect(user1).setInviter(await user3.getAddress())
      ).to.be.revertedWith("Inviter already set");
    });

    it("应该防止邀请自己", async function () {
      await expect(
        miningContract.connect(user1).setInviter(await user1.getAddress())
      ).to.be.revertedWith("Cannot invite yourself");
    });

    it("复杂邀请关系网络测试", async function () {
      // 构建邀请关系网络：user1 <- user2 <- user3 <- user4
      await miningContract.connect(user1).setInviter(await user2.getAddress());
      await miningContract.connect(user2).setInviter(await user3.getAddress());
      await miningContract.connect(user3).setInviter(await user4.getAddress());

      // 验证每个级别的邀请数据
      const user2Info = await miningContract.getUserInviteInfo(await user2.getAddress());
      expect(user2Info.totalInvited).to.equal(1);
      expect(user2Info.inviter).to.equal(await user3.getAddress());

      const user3Info = await miningContract.getUserInviteInfo(await user3.getAddress());
      expect(user3Info.totalInvited).to.equal(1);
      expect(user3Info.inviter).to.equal(await user4.getAddress());

      const user4Info = await miningContract.getUserInviteInfo(await user4.getAddress());
      expect(user4Info.totalInvited).to.equal(1);
      expect(user4Info.inviter).to.equal(ethers.ZeroAddress);
    });
  });

  describe("机制一：销毁TokenA获得TokenB的数据计算验证", function () {
    beforeEach(async function () {
      // 设置邀请关系：user1 被 user2 邀请
      await miningContract.connect(user1).setInviter(await user2.getAddress());
    });

    it("应该正确计算销毁TokenA的价值和预期TokenB数量", async function () {
      const burnAmount = ethers.parseEther("1000"); // 1000 TokenA
      
      // 计算预期数据
      const tokenAPrice = await priceOracle.getTokenPrice(await tokenA.getAddress());
      const tokenBPrice = await priceOracle.getTokenPrice(await tokenB.getAddress());
      const expectedBurnedValue = (burnAmount * tokenAPrice) / ethers.parseEther("1");
      const expectedTokenBAmount = (expectedBurnedValue * BigInt(REWARD_MULTIPLIER) * ethers.parseEther("1")) / tokenBPrice;
      
      console.log(`销毁TokenA数量: ${ethers.formatEther(burnAmount)}`);
      console.log(`TokenA价格: ${ethers.formatEther(tokenAPrice)} BNB`);
      console.log(`TokenB价格: ${ethers.formatEther(tokenBPrice)} BNB`);
      console.log(`预期销毁价值: ${ethers.formatEther(expectedBurnedValue)} BNB`);
      console.log(`预期TokenB数量: ${ethers.formatEther(expectedTokenBAmount)}`);

      // 执行销毁
      const tx = await miningContract.connect(user1).burnTokenAForTokenB(burnAmount);
      const receipt = await tx.wait();
      
      // 检查事件
      const event = receipt.logs.find(log => {
        try {
          return miningContract.interface.parseLog(log).name === "TokenABurned";
        } catch {
          return false;
        }
      });
      
      expect(event).to.not.be.undefined;
      const parsedEvent = miningContract.interface.parseLog(event);
      expect(parsedEvent.args.amount).to.equal(burnAmount);
      expect(parsedEvent.args.value).to.equal(expectedBurnedValue);
      expect(parsedEvent.args.expectedTokenB).to.equal(expectedTokenBAmount);

      // 验证挖矿记录
      const [count1] = await miningContract.getUserMiningRecordCount(await user1.getAddress());
      expect(count1).to.equal(1);

      // 验证全局统计
      expect(await miningContract.totalBurnedTokenA()).to.equal(burnAmount);
    });

    it("应该正确处理邀请奖励", async function () {
      const burnAmount = ethers.parseEther("1000");
      
      const user2InfoBefore = await miningContract.getUserInviteInfo(await user2.getAddress());
      const initialAcceleratedAmount = user2InfoBefore.acceleratedReleaseAmount;
      
      await miningContract.connect(user1).burnTokenAForTokenB(burnAmount);
      
      const user2InfoAfter = await miningContract.getUserInviteInfo(await user2.getAddress());
      const finalAcceleratedAmount = user2InfoAfter.acceleratedReleaseAmount;
      
      // 计算预期奖励
      const tokenAPrice = await priceOracle.getTokenPrice(await tokenA.getAddress());
      const burnedValue = (burnAmount * tokenAPrice) / ethers.parseEther("1");
      const expectedReward = (burnedValue * BigInt(INVITE_REWARD_RATE)) / BigInt(100);
      
      expect(finalAcceleratedAmount - initialAcceleratedAmount).to.equal(expectedReward);
      
      console.log(`邀请奖励: ${ethers.formatEther(expectedReward)} BNB value`);
    });

    it("价格变化对计算的影响", async function () {
      const burnAmount = ethers.parseEther("1000");
      
      // 第一次销毁
      await miningContract.connect(user1).burnTokenAForTokenB(burnAmount);
      
      // 改变价格
      const newTokenAPrice = ethers.parseEther("0.02"); // 翻倍
      const newTokenBPrice = ethers.parseEther("0.01"); // 减半
      await priceOracle.setManualPrice(await tokenA.getAddress(), newTokenAPrice);
      await priceOracle.setManualPrice(await tokenB.getAddress(), newTokenBPrice);
      
      // 第二次销毁相同数量
      const tx = await miningContract.connect(user1).burnTokenAForTokenB(burnAmount);
      const receipt = await tx.wait();
      
      const event = receipt.logs.find(log => {
        try {
          return miningContract.interface.parseLog(log).name === "TokenABurned";
        } catch {
          return false;
        }
      });
      
      const parsedEvent = miningContract.interface.parseLog(event);
      
      // 新价格下的预期计算
      const expectedBurnedValue = (burnAmount * newTokenAPrice) / ethers.parseEther("1");
      const expectedTokenBAmount = (expectedBurnedValue * BigInt(REWARD_MULTIPLIER) * ethers.parseEther("1")) / newTokenBPrice;
      
      expect(parsedEvent.args.value).to.equal(expectedBurnedValue);
      expect(parsedEvent.args.expectedTokenB).to.equal(expectedTokenBAmount);
      
      console.log(`价格变化后的TokenB数量: ${ethers.formatEther(expectedTokenBAmount)}`);
    });
  });

  describe("机制二：持有NFT销毁TokenB的数据计算验证", function () {
    beforeEach(async function () {
      // 设置邀请关系
      await miningContract.connect(user1).setInviter(await user2.getAddress());
      
      // 先通过机制一获得一些TokenB
      await miningContract.connect(user1).burnTokenAForTokenB(ethers.parseEther("1000"));
      
      // 时间推进以便领取TokenB
      await ethers.provider.send("evm_increaseTime", [30 * 24 * 60 * 60]); // 30天
      await ethers.provider.send("evm_mine");
      
      // 领取TokenB
      await miningContract.connect(user1).claimTokenBFromMechanism1();
      
      // 授权TokenB给MiningContract
      const tokenBBalance = await tokenB.balanceOf(await user1.getAddress());
      await tokenB.connect(user1).approve(await miningContract.getAddress(), tokenBBalance);
    });

    it("应该正确计算销毁TokenB的价值", async function () {
      const tokenBBalance = await tokenB.balanceOf(await user1.getAddress());
      const burnAmount = tokenBBalance / BigInt(2); // 销毁一半
      
      console.log(`用户TokenB余额: ${ethers.formatEther(tokenBBalance)}`);
      console.log(`销毁TokenB数量: ${ethers.formatEther(burnAmount)}`);
      
      // 计算预期数据
      const tokenBPrice = await priceOracle.getTokenPrice(await tokenB.getAddress());
      const expectedBurnedValue = (burnAmount * tokenBPrice) / ethers.parseEther("1");
      const expectedTotalReleaseValue = expectedBurnedValue * BigInt(REWARD_MULTIPLIER);
      
      console.log(`TokenB价格: ${ethers.formatEther(tokenBPrice)} BNB`);
      console.log(`销毁价值: ${ethers.formatEther(expectedBurnedValue)} BNB`);
      console.log(`总释放价值: ${ethers.formatEther(expectedTotalReleaseValue)} BNB`);
      
      // 执行销毁
      const tx = await miningContract.connect(user1).burnTokenBWithNFT(burnAmount);
      const receipt = await tx.wait();
      
      // 检查事件
      const event = receipt.logs.find(log => {
        try {
          return miningContract.interface.parseLog(log).name === "TokenBBurned";
        } catch {
          return false;
        }
      });
      
      expect(event).to.not.be.undefined;
      const parsedEvent = miningContract.interface.parseLog(event);
      expect(parsedEvent.args.amount).to.equal(burnAmount);
      expect(parsedEvent.args.value).to.equal(expectedBurnedValue);

      // 验证挖矿记录
      const [, count2] = await miningContract.getUserMiningRecordCount(await user1.getAddress());
      expect(count2).to.equal(1);

      // 验证全局统计
      expect(await miningContract.totalBurnedTokenB()).to.equal(burnAmount);
    });

    it("没有NFT的用户不能使用机制二", async function () {
      // user4没有NFT
      await expect(
        miningContract.connect(user4).burnTokenBWithNFT(ethers.parseEther("100"))
      ).to.be.revertedWith("Must hold at least one NFT");
    });

    it("应该正确处理邀请奖励（机制二）", async function () {
      const tokenBBalance = await tokenB.balanceOf(await user1.getAddress());
      const burnAmount = tokenBBalance / BigInt(2);
      
      const user2InfoBefore = await miningContract.getUserInviteInfo(await user2.getAddress());
      const initialAcceleratedAmount = user2InfoBefore.acceleratedReleaseAmount;
      
      await miningContract.connect(user1).burnTokenBWithNFT(burnAmount);
      
      const user2InfoAfter = await miningContract.getUserInviteInfo(await user2.getAddress());
      const finalAcceleratedAmount = user2InfoAfter.acceleratedReleaseAmount;
      
      // 计算预期奖励
      const tokenBPrice = await priceOracle.getTokenPrice(await tokenB.getAddress());
      const burnedValue = (burnAmount * tokenBPrice) / ethers.parseEther("1");
      const expectedReward = (burnedValue * BigInt(INVITE_REWARD_RATE)) / BigInt(100);
      
      const actualReward = finalAcceleratedAmount - initialAcceleratedAmount;
      expect(actualReward).to.equal(expectedReward);
      
      console.log(`机制二邀请奖励: ${ethers.formatEther(expectedReward)} BNB value`);
    });
  });

  describe("TokenB释放机制和时间计算验证", function () {
    beforeEach(async function () {
      // 用户1销毁TokenA
      await miningContract.connect(user1).burnTokenAForTokenB(ethers.parseEther("1000"));
    });

    it("应该正确计算可领取的TokenB数量（时间未到）", async function () {
      // 刚销毁，时间未到30天
      const claimable1 = await miningContract.getUserClaimableAmount1(await user1.getAddress());
      expect(claimable1).to.equal(0);
      
      console.log(`销毁后立即可领取: ${ethers.formatEther(claimable1)} BNB value`);
    });

    it("应该正确计算可领取的TokenB数量（30天后）", async function () {
      // 推进30天
      await ethers.provider.send("evm_increaseTime", [30 * 24 * 60 * 60]);
      await ethers.provider.send("evm_mine");
      
      const claimable1 = await miningContract.getUserClaimableAmount1(await user1.getAddress());
      
      // 计算预期可领取数量：30% of total release value
      const tokenAPrice = await priceOracle.getTokenPrice(await tokenA.getAddress());
      const burnedValue = (ethers.parseEther("1000") * tokenAPrice) / ethers.parseEther("1");
      const totalReleaseValue = burnedValue * BigInt(REWARD_MULTIPLIER);
      const expectedClaimable = (totalReleaseValue * BigInt(MONTHLY_RELEASE_RATE)) / BigInt(100);
      
      expect(claimable1).to.equal(expectedClaimable);
      
      console.log(`30天后可领取: ${ethers.formatEther(claimable1)} BNB value`);
      console.log(`预期可领取: ${ethers.formatEther(expectedClaimable)} BNB value`);
    });

    it("应该正确计算可领取的TokenB数量（60天后）", async function () {
      // 推进60天
      await ethers.provider.send("evm_increaseTime", [60 * 24 * 60 * 60]);
      await ethers.provider.send("evm_mine");
      
      const claimable1 = await miningContract.getUserClaimableAmount1(await user1.getAddress());
      
      // 计算预期可领取数量：60% of total release value
      const tokenAPrice = await priceOracle.getTokenPrice(await tokenA.getAddress());
      const burnedValue = (ethers.parseEther("1000") * tokenAPrice) / ethers.parseEther("1");
      const totalReleaseValue = burnedValue * BigInt(REWARD_MULTIPLIER);
      const expectedClaimable = (totalReleaseValue * BigInt(MONTHLY_RELEASE_RATE) * BigInt(2)) / BigInt(100);
      
      expect(claimable1).to.equal(expectedClaimable);
      
      console.log(`60天后可领取: ${ethers.formatEther(claimable1)} BNB value`);
    });

    it("应该正确处理完全释放（4个月后）", async function () {
      // 推进120天（4个月）
      await ethers.provider.send("evm_increaseTime", [120 * 24 * 60 * 60]);
      await ethers.provider.send("evm_mine");
      
      const claimable1 = await miningContract.getUserClaimableAmount1(await user1.getAddress());
      
      // 计算预期可领取数量：应该是全部（100%）
      const tokenAPrice = await priceOracle.getTokenPrice(await tokenA.getAddress());
      const burnedValue = (ethers.parseEther("1000") * tokenAPrice) / ethers.parseEther("1");
      const totalReleaseValue = burnedValue * BigInt(REWARD_MULTIPLIER);
      
      expect(claimable1).to.equal(totalReleaseValue);
      
      console.log(`120天后可领取: ${ethers.formatEther(claimable1)} BNB value`);
      console.log(`总释放价值: ${ethers.formatEther(totalReleaseValue)} BNB value`);
    });

    it("实际领取TokenB并验证数量", async function () {
      // 推进30天
      await ethers.provider.send("evm_increaseTime", [30 * 24 * 60 * 60]);
      await ethers.provider.send("evm_mine");
      
      const balanceBefore = await tokenB.balanceOf(await user1.getAddress());
      
      await miningContract.connect(user1).claimTokenBFromMechanism1();
      
      const balanceAfter = await tokenB.balanceOf(await user1.getAddress());
      const actualReceived = balanceAfter - balanceBefore;
      
      // 计算预期TokenB数量
      const claimableValue = await miningContract.getUserClaimableAmount1(await user1.getAddress());
      const tokenBPrice = await priceOracle.getTokenPrice(await tokenB.getAddress());
      const expectedTokenBAmount = (claimableValue * ethers.parseEther("1")) / tokenBPrice;
      
      console.log(`实际获得TokenB: ${ethers.formatEther(actualReceived)}`);
      console.log(`预期TokenB数量: ${ethers.formatEther(expectedTokenBAmount)}`);
      
      // 由于有加速释放，实际数量可能大于预期
      expect(actualReceived).to.be.gte(expectedTokenBAmount);
    });
  });

  describe("邀请奖励加速释放机制验证", function () {
    beforeEach(async function () {
      // 设置邀请关系：user1 <- user2 <- user3
      await miningContract.connect(user1).setInviter(await user2.getAddress());
      await miningContract.connect(user2).setInviter(await user3.getAddress());
    });

    it("应该正确累积加速释放额度", async function () {
      // user1销毁TokenA，user2获得邀请奖励
      await miningContract.connect(user1).burnTokenAForTokenB(ethers.parseEther("1000"));
      
      const user2Info1 = await miningContract.getUserInviteInfo(await user2.getAddress());
      const reward1 = user2Info1.acceleratedReleaseAmount;
      
      // user2也销毁TokenA，user3获得邀请奖励
      await miningContract.connect(user2).burnTokenAForTokenB(ethers.parseEther("2000"));
      
      const user3Info = await miningContract.getUserInviteInfo(await user3.getAddress());
      const user2Info2 = await miningContract.getUserInviteInfo(await user2.getAddress());
      const reward2 = user2Info2.acceleratedReleaseAmount;
      
      console.log(`user2第一次邀请奖励: ${ethers.formatEther(reward1)} BNB`);
      console.log(`user2第二次总奖励: ${ethers.formatEther(reward2)} BNB`);
      console.log(`user3邀请奖励: ${ethers.formatEther(user3Info.acceleratedReleaseAmount)} BNB`);
      
      // 验证奖励累积 - user2既是被邀请人又是邀请人，所以奖励可能相同
      expect(reward2).to.be.gte(reward1);
      expect(user3Info.acceleratedReleaseAmount).to.be.gt(0);
    });

    it("加速释放额度应该能够加速TokenB领取", async function () {
      // user1销毁TokenA获得挖矿记录
      await miningContract.connect(user1).burnTokenAForTokenB(ethers.parseEther("1000"));
      
      // user2销毁TokenA，user1作为邀请人获得加速释放额度
      await miningContract.connect(user2).burnTokenAForTokenB(ethers.parseEther("500"));
      
      // 推进时间
      await ethers.provider.send("evm_increaseTime", [30 * 24 * 60 * 60]);
      await ethers.provider.send("evm_mine");
      
      // user1领取TokenB
      const balanceBefore = await tokenB.balanceOf(await user1.getAddress());
      await miningContract.connect(user1).claimTokenBFromMechanism1();
      const balanceAfter = await tokenB.balanceOf(await user1.getAddress());
      
      const actualReceived = balanceAfter - balanceBefore;
      
      // 计算基础释放 + 加速释放
      const tokenAPrice = await priceOracle.getTokenPrice(await tokenA.getAddress());
      const burnedValue = (ethers.parseEther("1000") * tokenAPrice) / ethers.parseEther("1");
      const monthlyRelease = (burnedValue * BigInt(2) * BigInt(30)) / BigInt(100);
      
      // 加速释放额度
      const user1Info = await miningContract.getUserInviteInfo(await user1.getAddress());
      console.log(`user1加速释放额度: ${ethers.formatEther(user1Info.acceleratedReleaseAmount)} BNB`);
      
      console.log(`user1获得TokenB: ${ethers.formatEther(actualReceived)}`);
      console.log(`基础月释放价值: ${ethers.formatEther(monthlyRelease)} BNB`);
      
      expect(actualReceived).to.be.gt(0);
    });

    it("加速释放额度不应超过待释放总额", async function () {
      // user1销毁少量TokenA
      await miningContract.connect(user1).burnTokenAForTokenB(ethers.parseEther("100"));
      
      // user2销毁大量TokenA，给user1产生大量加速释放额度
      await miningContract.connect(user2).burnTokenAForTokenB(ethers.parseEther("10000"));
      
      const user1InfoBefore = await miningContract.getUserInviteInfo(await user1.getAddress());
      const acceleratedBefore = user1InfoBefore.acceleratedReleaseAmount;
      
      // 推进时间
      await ethers.provider.send("evm_increaseTime", [30 * 24 * 60 * 60]);
      await ethers.provider.send("evm_mine");
      
      // user1领取TokenB
      await miningContract.connect(user1).claimTokenBFromMechanism1();
      
      const user1InfoAfter = await miningContract.getUserInviteInfo(await user1.getAddress());
      const acceleratedAfter = user1InfoAfter.acceleratedReleaseAmount;
      const usedAccelerated = acceleratedBefore - acceleratedAfter;
      
      console.log(`加速释放额度使用前: ${ethers.formatEther(acceleratedBefore)} BNB`);
      console.log(`加速释放额度使用后: ${ethers.formatEther(acceleratedAfter)} BNB`);
      console.log(`实际使用额度: ${ethers.formatEther(usedAccelerated)} BNB`);
      
      // 验证使用的加速释放额度不应超过实际可释放额度
      expect(usedAccelerated).to.be.lte(acceleratedBefore);
    });
  });

  describe("价格计算和数值精度验证", function () {
    it("极小数量销毁的精度测试", async function () {
      const smallAmount = ethers.parseEther("0.001"); // 0.001 TokenA
      
      // 授权小额度
      await tokenA.connect(user1).approve(await miningContract.getAddress(), smallAmount);
      
      const tx = await miningContract.connect(user1).burnTokenAForTokenB(smallAmount);
      const receipt = await tx.wait();
      
      const event = receipt.logs.find(log => {
        try {
          return miningContract.interface.parseLog(log).name === "TokenABurned";
        } catch {
          return false;
        }
      });
      
      expect(event).to.not.be.undefined;
      const parsedEvent = miningContract.interface.parseLog(event);
      
      console.log(`小额销毁数量: ${ethers.formatEther(parsedEvent.args.amount)}`);
      console.log(`小额销毁价值: ${ethers.formatEther(parsedEvent.args.value)} BNB`);
      console.log(`预期TokenB: ${ethers.formatEther(parsedEvent.args.expectedTokenB)}`);
      
      expect(parsedEvent.args.value).to.be.gt(0);
      expect(parsedEvent.args.expectedTokenB).to.be.gt(0);
    });

    it("极大数量销毁的精度测试", async function () {
      const largeAmount = ethers.parseEther("50000"); // 5万TokenA
      
      // 授权大额度
      await tokenA.connect(user1).approve(await miningContract.getAddress(), largeAmount);
      
      const tx = await miningContract.connect(user1).burnTokenAForTokenB(largeAmount);
      const receipt = await tx.wait();
      
      const event = receipt.logs.find(log => {
        try {
          return miningContract.interface.parseLog(log).name === "TokenABurned";
        } catch {
          return false;
        }
      });
      
      const parsedEvent = miningContract.interface.parseLog(event);
      
      console.log(`大额销毁数量: ${ethers.formatEther(parsedEvent.args.amount)}`);
      console.log(`大额销毁价值: ${ethers.formatEther(parsedEvent.args.value)} BNB`);
      console.log(`预期TokenB: ${ethers.formatEther(parsedEvent.args.expectedTokenB)}`);
      
      // 验证计算精度
      const expectedValue = (largeAmount * INITIAL_TOKENA_PRICE) / ethers.parseEther("1");
      const expectedTokenB = (expectedValue * BigInt(REWARD_MULTIPLIER) * ethers.parseEther("1")) / INITIAL_TOKENB_PRICE;
      
      expect(parsedEvent.args.value).to.equal(expectedValue);
      expect(parsedEvent.args.expectedTokenB).to.equal(expectedTokenB);
    });

    it("价格变化时的计算一致性", async function () {
      // 记录初始价格下的计算结果
      const amount = ethers.parseEther("1000");
      const tx1 = await miningContract.connect(user1).burnTokenAForTokenB(amount);
      const receipt1 = await tx1.wait();
      const event1 = receipt1.logs.find(log => {
        try {
          return miningContract.interface.parseLog(log).name === "TokenABurned";
        } catch {
          return false;
        }
      });
      const result1 = miningContract.interface.parseLog(event1);
      
      // 改变价格 - TokenA价格增加50%，TokenB价格减少25%
      const newTokenAPrice = (INITIAL_TOKENA_PRICE * BigInt(150)) / BigInt(100);
      const newTokenBPrice = (INITIAL_TOKENB_PRICE * BigInt(75)) / BigInt(100);
      await priceOracle.setManualPrice(await tokenA.getAddress(), newTokenAPrice);
      await priceOracle.setManualPrice(await tokenB.getAddress(), newTokenBPrice);
      
      // 相同数量在新价格下的计算
      const tx2 = await miningContract.connect(user2).burnTokenAForTokenB(amount);
      const receipt2 = await tx2.wait();
      const event2 = receipt2.logs.find(log => {
        try {
          return miningContract.interface.parseLog(log).name === "TokenABurned";
        } catch {
          return false;
        }
      });
      const result2 = miningContract.interface.parseLog(event2);
      
      // 验证价格变化对计算的影响
      console.log(`原始价格 - 价值: ${ethers.formatEther(result1.args.value)} BNB, TokenB: ${ethers.formatEther(result1.args.expectedTokenB)}`);
      console.log(`新价格 - 价值: ${ethers.formatEther(result2.args.value)} BNB, TokenB: ${ethers.formatEther(result2.args.expectedTokenB)}`);
      
      // 新价格下的价值应该是原来的1.5倍
      expect(result2.args.value).to.equal((result1.args.value * BigInt(150)) / BigInt(100));
      
      // 新价格下的TokenB数量应该更多（因为TokenA价格上涨，TokenB价格下跌）
      expect(result2.args.expectedTokenB).to.be.gt(result1.args.expectedTokenB);
    });
  });

  describe("边界条件和异常情况测试", function () {
    it("零金额销毁应该失败", async function () {
      await expect(
        miningContract.connect(user1).burnTokenAForTokenB(0)
      ).to.be.revertedWith("Amount must be greater than zero");
    });

    it("余额不足销毁应该失败", async function () {
      const excessAmount = ethers.parseEther("1000000"); // 超过用户余额
      
      await expect(
        miningContract.connect(user1).burnTokenAForTokenB(excessAmount)
      ).to.be.revertedWith("Insufficient TokenA balance");
    });

    it("授权不足销毁应该失败", async function () {
      const amount = ethers.parseEther("1000");
      
      // 清空授权
      await tokenA.connect(user1).approve(await miningContract.getAddress(), 0);
      
      await expect(
        miningContract.connect(user1).burnTokenAForTokenB(amount)
      ).to.be.revertedWith("Burn amount exceeds allowance");
    });

    it("没有NFT的用户不能使用机制二", async function () {
      // user4没有NFT，应该无法使用机制二
      await expect(
        miningContract.connect(user4).burnTokenBWithNFT(ethers.parseEther("100"))
      ).to.be.revertedWith("Must hold at least one NFT");
    });

    it("没有可领取TokenB时应该失败", async function () {
      // 刚销毁TokenA，时间未到，应该没有可领取的
      await miningContract.connect(user1).burnTokenAForTokenB(ethers.parseEther("1000"));
      
      await expect(
        miningContract.connect(user1).claimTokenBFromMechanism1()
      ).to.be.revertedWith("No tokens to claim");
    });

    it("合约暂停时不能执行操作", async function () {
      await miningContract.pause();
      
      await expect(
        miningContract.connect(user1).burnTokenAForTokenB(ethers.parseEther("1000"))
      ).to.be.revertedWithCustomError(miningContract, "EnforcedPause");
      
      await expect(
        miningContract.connect(user1).claimTokenBFromMechanism1()
      ).to.be.revertedWithCustomError(miningContract, "EnforcedPause");
      
      // 恢复正常
      await miningContract.unpause();
      
      // 现在应该可以正常执行
      await miningContract.connect(user1).burnTokenAForTokenB(ethers.parseEther("1000"));
    });

    it("非owner不能执行管理功能", async function () {
      await expect(
        miningContract.connect(user1).setReleaseInterval(60 * 24 * 60 * 60)
      ).to.be.revertedWithCustomError(miningContract, "OwnableUnauthorizedAccount");
      
      await expect(
        miningContract.connect(user1).pause()
      ).to.be.revertedWithCustomError(miningContract, "OwnableUnauthorizedAccount");
    });

    it("应该正确处理重入攻击保护", async function () {
      // 这个测试验证ReentrancyGuard是否正常工作
      // 在正常情况下，这些操作应该成功
      await miningContract.connect(user1).burnTokenAForTokenB(ethers.parseEther("1000"));
      
      // 推进时间
      await ethers.provider.send("evm_increaseTime", [30 * 24 * 60 * 60]);
      await ethers.provider.send("evm_mine");
      
      // 正常领取应该成功
      await miningContract.connect(user1).claimTokenBFromMechanism1();
    });

    it("验证全局统计数据的准确性", async function () {
      const initialBurnedA = await miningContract.totalBurnedTokenA();
      const initialBurnedB = await miningContract.totalBurnedTokenB();
      const initialReleasedB = await miningContract.totalReleasedTokenB();
      
      // 执行一些操作
      await miningContract.connect(user1).burnTokenAForTokenB(ethers.parseEther("1000"));
      await miningContract.connect(user2).burnTokenAForTokenB(ethers.parseEther("2000"));
      
      // 验证统计数据更新
      const midBurnedA = await miningContract.totalBurnedTokenA();
      expect(midBurnedA - initialBurnedA).to.equal(ethers.parseEther("3000"));
      
      // 推进时间并领取
      await ethers.provider.send("evm_increaseTime", [30 * 24 * 60 * 60]);
      await ethers.provider.send("evm_mine");
      
      await miningContract.connect(user1).claimTokenBFromMechanism1();
      
      const finalReleasedB = await miningContract.totalReleasedTokenB();
      expect(finalReleasedB).to.be.gt(initialReleasedB);
      
      console.log(`总销毁TokenA: ${ethers.formatEther(midBurnedA)}`);
      console.log(`总释放TokenB: ${ethers.formatEther(finalReleasedB)}`);
    });
  });

  describe("多用户并发销毁测试", function () {
    it("大量用户同时销毁TokenA", async function () {
      const users = [user1, user2, user3, user4];
      const amounts = [
        ethers.parseEther("1000"),
        ethers.parseEther("2000"), 
        ethers.parseEther("1500"),
        ethers.parseEther("800")
      ];

      // 给user4也分发一些TokenA
      await tokenA.transfer(await user4.getAddress(), ethers.parseEther("100000"));
      await tokenA.connect(user4).approve(await miningContract.getAddress(), ethers.parseEther("100000"));

      const initialTotalBurned = await miningContract.totalBurnedTokenA();
      
      // 并发销毁
      const burnPromises = users.map((user, index) => 
        miningContract.connect(user).burnTokenAForTokenB(amounts[index])
      );
      
      await Promise.all(burnPromises);
      
      // 验证总销毁量
      const finalTotalBurned = await miningContract.totalBurnedTokenA();
      const expectedTotalBurned = amounts.reduce((sum, amount) => sum + amount, 0n);
      expect(finalTotalBurned - initialTotalBurned).to.equal(expectedTotalBurned);
      
      // 验证每个用户的挖矿记录
      for (let i = 0; i < users.length; i++) {
        const [count1] = await miningContract.getUserMiningRecordCount(await users[i].getAddress());
        expect(count1).to.be.gte(1);
      }
      
      console.log(`并发销毁总量: ${ethers.formatEther(expectedTotalBurned)} TokenA`);
    });

    it("多用户在不同时间点销毁和领取", async function () {
      // 第一批用户销毁
      await miningContract.connect(user1).burnTokenAForTokenB(ethers.parseEther("1000"));
      await miningContract.connect(user2).burnTokenAForTokenB(ethers.parseEther("2000"));
      
      // 推进15天
      await ethers.provider.send("evm_increaseTime", [15 * 24 * 60 * 60]);
      await ethers.provider.send("evm_mine");
      
      // 第二批用户销毁
      await miningContract.connect(user3).burnTokenAForTokenB(ethers.parseEther("1500"));
      
      // 再推进15天 (总共30天)
      await ethers.provider.send("evm_increaseTime", [15 * 24 * 60 * 60]);
      await ethers.provider.send("evm_mine");
      
      // 第一批用户可以领取30天的奖励
      const user1Claimable = await miningContract.getUserClaimableAmount1(await user1.getAddress());
      const user2Claimable = await miningContract.getUserClaimableAmount1(await user2.getAddress());
      
      // 第二批用户只能领取15天的奖励 (因为他们晚15天销毁)
      const user3Claimable = await miningContract.getUserClaimableAmount1(await user3.getAddress());
      
      console.log(`用户1可领取(30天): ${ethers.formatEther(user1Claimable)} BNB`);
      console.log(`用户2可领取(30天): ${ethers.formatEther(user2Claimable)} BNB`);
      console.log(`用户3可领取(15天): ${ethers.formatEther(user3Claimable)} BNB`);
      
      // user3的可领取应该为0 (因为还没到30天)
      expect(user3Claimable).to.equal(0);
      
      // user1和user2应该有可领取的
      expect(user1Claimable).to.be.gt(0);
      expect(user2Claimable).to.be.gt(0);
      expect(user2Claimable).to.be.gt(user1Claimable); // user2销毁更多
    });
  });

  describe("极端价格波动测试", function () {
    it("价格急剧上涨情况下的计算", async function () {
      // 初始销毁
      const amount = ethers.parseEther("1000");
      await miningContract.connect(user1).burnTokenAForTokenB(amount);
      
      // TokenA价格上涨1000% (10倍)
      const extremeTokenAPrice = INITIAL_TOKENA_PRICE * BigInt(10);
      await priceOracle.setManualPrice(await tokenA.getAddress(), extremeTokenAPrice);
      
      // TokenB价格下跌90% (0.1倍)
      const crashedTokenBPrice = INITIAL_TOKENB_PRICE / BigInt(10);
      await priceOracle.setManualPrice(await tokenB.getAddress(), crashedTokenBPrice);
      
      // 在新价格下销毁相同数量
      const tx = await miningContract.connect(user2).burnTokenAForTokenB(amount);
      const receipt = await tx.wait();
      
      const event = receipt.logs.find(log => {
        try {
          return miningContract.interface.parseLog(log).name === "TokenABurned";
        } catch {
          return false;
        }
      });
      
      const parsedEvent = miningContract.interface.parseLog(event);
      
      // 计算预期值
      const expectedValue = (amount * extremeTokenAPrice) / ethers.parseEther("1");
      const expectedTokenB = (expectedValue * BigInt(2) * ethers.parseEther("1")) / crashedTokenBPrice;
      
      console.log(`极端价格下销毁价值: ${ethers.formatEther(expectedValue)} BNB`);
      console.log(`极端价格下预期TokenB: ${ethers.formatEther(expectedTokenB)}`);
      
      expect(parsedEvent.args.value).to.equal(expectedValue);
      expect(parsedEvent.args.expectedTokenB).to.equal(expectedTokenB);
      
      // 验证TokenB数量应该远大于初始价格下的数量
      expect(expectedTokenB).to.be.gt(ethers.parseEther("10000")); // 应该远大于1000
    });

    it("价格急剧下跌情况下的计算", async function () {
      // TokenA价格下跌95% (0.05倍)
      const crashedTokenAPrice = INITIAL_TOKENA_PRICE / BigInt(20);
      await priceOracle.setManualPrice(await tokenA.getAddress(), crashedTokenAPrice);
      
      // TokenB价格上涨2000% (20倍)
      const skyrocketTokenBPrice = INITIAL_TOKENB_PRICE * BigInt(20);
      await priceOracle.setManualPrice(await tokenB.getAddress(), skyrocketTokenBPrice);
      
      const amount = ethers.parseEther("10000"); // 更大数量以确保有意义的价值
      await miningContract.connect(user1).burnTokenAForTokenB(amount);
      
      // 计算应该仍然正确，只是数值较小
      const expectedValue = (amount * crashedTokenAPrice) / ethers.parseEther("1");
      const expectedTokenB = (expectedValue * BigInt(2) * ethers.parseEther("1")) / skyrocketTokenBPrice;
      
      console.log(`下跌价格下销毁价值: ${ethers.formatEther(expectedValue)} BNB`);
      console.log(`下跌价格下预期TokenB: ${ethers.formatEther(expectedTokenB)}`);
      
      expect(expectedValue).to.be.gt(0);
      expect(expectedTokenB).to.be.gt(0);
    });

    it("价格为零时应该失败", async function () {
      // PriceOracle不允许设置0价格，这个测试验证PriceOracle的保护机制
      await expect(
        priceOracle.setManualPrice(await tokenA.getAddress(), 0)
      ).to.be.revertedWith("Invalid price");
      
      await expect(
        priceOracle.setManualPrice(await tokenB.getAddress(), 0)
      ).to.be.revertedWith("Invalid price");
    });
  });

  describe("长期释放周期和多期释放测试", function () {
    beforeEach(async function () {
      // 销毁TokenA创建挖矿记录
      await miningContract.connect(user1).burnTokenAForTokenB(ethers.parseEther("3000"));
    });

    it("测试12个月的完整释放周期", async function () {
      const releaseSchedule = [];
      
      for (let month = 1; month <= 4; month++) { // 只测试4个月，因为4*30%=120%已经完全释放
        // 推进30天
        await ethers.provider.send("evm_increaseTime", [30 * 24 * 60 * 60]);
        await ethers.provider.send("evm_mine");
        
        const claimable = await miningContract.getUserClaimableAmount1(await user1.getAddress());
        releaseSchedule.push({
          month,
          claimable: ethers.formatEther(claimable)
        });
        
        // 如果有可领取的，就领取
        if (claimable > 0) {
          const balanceBefore = await tokenB.balanceOf(await user1.getAddress());
          await miningContract.connect(user1).claimTokenBFromMechanism1();
          const balanceAfter = await tokenB.balanceOf(await user1.getAddress());
          const received = balanceAfter - balanceBefore;
          
          console.log(`第${month}个月领取: ${ethers.formatEther(received)} TokenB`);
        }
      }
      
      console.log("释放时间表:", releaseSchedule);
      
      // 验证第4个月后不应该再有可领取的 (因为30% * 4 > 100%)
      const finalClaimable = await miningContract.getUserClaimableAmount1(await user1.getAddress());
      expect(finalClaimable).to.equal(0);
    });

    it("测试部分释放后继续销毁的情况", async function () {
      // 推进60天，领取2个月的释放
      await ethers.provider.send("evm_increaseTime", [60 * 24 * 60 * 60]);
      await ethers.provider.send("evm_mine");
      
      await miningContract.connect(user1).claimTokenBFromMechanism1();
      
      // 再次销毁TokenA，创建新的挖矿记录
      await miningContract.connect(user1).burnTokenAForTokenB(ethers.parseEther("2000"));
      
      // 推进30天
      await ethers.provider.send("evm_increaseTime", [30 * 24 * 60 * 60]);
      await ethers.provider.send("evm_mine");
      
      const claimable = await miningContract.getUserClaimableAmount1(await user1.getAddress());
      
      // 应该包含第一笔的剩余释放 + 第二笔的首次释放
      console.log(`多笔挖矿记录总可领取: ${ethers.formatEther(claimable)} BNB`);
      expect(claimable).to.be.gt(0);
      
      // 验证用户有2条挖矿记录
      const [count1] = await miningContract.getUserMiningRecordCount(await user1.getAddress());
      expect(count1).to.equal(2);
    });

    it("测试释放间隔调整的影响", async function () {
      // 改变释放间隔为15天
      await miningContract.setReleaseInterval(15 * 24 * 60 * 60);
      
      // 推进15天
      await ethers.provider.send("evm_increaseTime", [15 * 24 * 60 * 60]);
      await ethers.provider.send("evm_mine");
      
      const claimable15Days = await miningContract.getUserClaimableAmount1(await user1.getAddress());
      
      // 再推进15天 (总共30天)
      await ethers.provider.send("evm_increaseTime", [15 * 24 * 60 * 60]);
      await ethers.provider.send("evm_mine");
      
      const claimable30Days = await miningContract.getUserClaimableAmount1(await user1.getAddress());
      
      console.log(`15天间隔可领取: ${ethers.formatEther(claimable15Days)} BNB`);
      console.log(`30天间隔可领取: ${ethers.formatEther(claimable30Days)} BNB`);
      
      // 30天应该是15天的2倍
      expect(claimable30Days).to.equal(claimable15Days * BigInt(2));
    });
  });

  describe("NFT转移对机制二的影响测试", function () {
    beforeEach(async function () {
      // 确保用户有TokenB
      await miningContract.connect(user1).burnTokenAForTokenB(ethers.parseEther("1000"));
      await ethers.provider.send("evm_increaseTime", [30 * 24 * 60 * 60]);
      await ethers.provider.send("evm_mine");
      await miningContract.connect(user1).claimTokenBFromMechanism1();
      
      // 授权TokenB
      const tokenBBalance = await tokenB.balanceOf(await user1.getAddress());
      await tokenB.connect(user1).approve(await miningContract.getAddress(), tokenBBalance);
    });

    it("持有NFT时能使用机制二，转移后不能使用", async function () {
      // 验证user1有NFT，能使用机制二
      const nftBalance1 = await minerNFT.balanceOf(await user1.getAddress());
      expect(nftBalance1).to.be.gt(0);
      
      const tokenBAmount = ethers.parseEther("100");
      await miningContract.connect(user1).burnTokenBWithNFT(tokenBAmount);
      
      // 转移NFT给user4
      const user1NFTs = await minerNFT.getUserNFTs(await user1.getAddress());
      await minerNFT.connect(user1).transferFrom(
        await user1.getAddress(),
        await user4.getAddress(),
        user1NFTs[0]
      );
      
      // 验证user1现在没有NFT，不能使用机制二
      const nftBalanceAfter = await minerNFT.balanceOf(await user1.getAddress());
      if (nftBalanceAfter === 0n) {
        await expect(
          miningContract.connect(user1).burnTokenBWithNFT(tokenBAmount)
        ).to.be.revertedWith("Must hold at least one NFT");
      }
      
      // 验证user4现在有NFT，可以使用机制二
      const user4NFTBalance = await minerNFT.balanceOf(await user4.getAddress());
      expect(user4NFTBalance).to.be.gt(0);
    });

    it("NFT转移不影响已有的机制二挖矿记录", async function () {
      // 使用机制二销毁TokenB
      const tokenBAmount = ethers.parseEther("100");
      await miningContract.connect(user1).burnTokenBWithNFT(tokenBAmount);
      
      // 推进30天
      await ethers.provider.send("evm_increaseTime", [30 * 24 * 60 * 60]);
      await ethers.provider.send("evm_mine");
      
      // 转移NFT给user4
      const user1NFTs = await minerNFT.getUserNFTs(await user1.getAddress());
      if (user1NFTs.length > 0) {
        await minerNFT.connect(user1).transferFrom(
          await user1.getAddress(),
          await user4.getAddress(),
          user1NFTs[0]
        );
      }
      
      // 即使NFT转移了，user1仍然可以领取之前的机制二奖励
      const claimable = await miningContract.getUserClaimableAmount2(await user1.getAddress());
      console.log(`NFT转移后仍可领取: ${ethers.formatEther(claimable)} BNB`);
      
      if (claimable > 0) {
        // 但是领取时需要有NFT，所以这里应该失败
        await expect(
          miningContract.connect(user1).claimTokenBFromMechanism2()
        ).to.be.revertedWith("Must hold at least one NFT");
      }
    });

    it("获得NFT后立即可以使用机制二", async function () {
      // 给user4铸造NFT
      await minerNFT.mint(await user4.getAddress());
      
      // 给user4一些TokenB
      await tokenB.transfer(await user4.getAddress(), ethers.parseEther("1000"));
      await tokenB.connect(user4).approve(await miningContract.getAddress(), ethers.parseEther("1000"));
      
      // user4现在应该能使用机制二
      const tokenBAmount = ethers.parseEther("100");
      await miningContract.connect(user4).burnTokenBWithNFT(tokenBAmount);
      
      // 验证挖矿记录创建
      const [, count2] = await miningContract.getUserMiningRecordCount(await user4.getAddress());
      expect(count2).to.equal(1);
    });
  });

  describe("复杂邀请网络的奖励分配测试", function () {
    let user5, user6, user7, user8;

    beforeEach(async function () {
      // 获取更多用户
      const signers = await ethers.getSigners();
      [, , , , , , user5, user6, user7, user8] = signers;
      
      // 分发TokenA给新用户
      for (const user of [user5, user6, user7, user8]) {
        await tokenA.transfer(await user.getAddress(), ethers.parseEther("100000"));
        await tokenA.connect(user).approve(await miningContract.getAddress(), ethers.parseEther("100000"));
      }
      
      // 构建复杂邀请网络
      // user1 <- user2 <- user3 <- user4
      //   ^        ^
      // user5    user6 <- user7 <- user8
      await miningContract.connect(user1).setInviter(await user2.getAddress());
      await miningContract.connect(user2).setInviter(await user3.getAddress());
      await miningContract.connect(user3).setInviter(await user4.getAddress());
      await miningContract.connect(user5).setInviter(await user1.getAddress());
      await miningContract.connect(user6).setInviter(await user2.getAddress());
      await miningContract.connect(user7).setInviter(await user6.getAddress());
      await miningContract.connect(user8).setInviter(await user7.getAddress());
    });

    it("多级邀请网络的奖励分配", async function () {
      const burnAmounts = {
        user1: ethers.parseEther("1000"),
        user2: ethers.parseEther("1500"),
        user5: ethers.parseEther("800"),
        user6: ethers.parseEther("1200"),
        user7: ethers.parseEther("2000"),
        user8: ethers.parseEther("500")
      };
      
      // 记录销毁前的加速释放额度
      const beforeRewards = {};
      for (const userAddr of [user1, user2, user3, user4, user6, user7]) {
        const info = await miningContract.getUserInviteInfo(await userAddr.getAddress());
        beforeRewards[userAddr.address] = info.acceleratedReleaseAmount;
      }
      
      // 执行销毁操作
      for (const [userKey, amount] of Object.entries(burnAmounts)) {
        const user = eval(userKey);
        await miningContract.connect(user).burnTokenAForTokenB(amount);
      }
      
      // 检查邀请奖励分配
      const afterRewards = {};
      for (const userAddr of [user1, user2, user3, user4, user6, user7]) {
        const info = await miningContract.getUserInviteInfo(await userAddr.getAddress());
        afterRewards[userAddr.address] = info.acceleratedReleaseAmount;
        
        const reward = afterRewards[userAddr.address] - beforeRewards[userAddr.address];
        console.log(`${userAddr.address} 获得邀请奖励: ${ethers.formatEther(reward)} BNB`);
      }
      
      // 验证奖励分配逻辑
      // user2应该获得user1和user6的邀请奖励
      // user3应该获得user2的邀请奖励
      // user4应该获得user3的邀请奖励
      const user2Reward = afterRewards[user2.address] - beforeRewards[user2.address];
      const user3Reward = afterRewards[user3.address] - beforeRewards[user3.address];
      
      expect(user2Reward).to.be.gt(0);
      expect(user3Reward).to.be.gt(0);
    });

    it("邀请奖励的累积和使用", async function () {
      // 多个被邀请人销毁TokenA
      await miningContract.connect(user1).burnTokenAForTokenB(ethers.parseEther("1000"));
      await miningContract.connect(user5).burnTokenAForTokenB(ethers.parseEther("1000"));
      await miningContract.connect(user6).burnTokenAForTokenB(ethers.parseEther("1000"));
      
      // user1和user2都应该获得邀请奖励
      const user1Info = await miningContract.getUserInviteInfo(await user1.getAddress());
      const user2Info = await miningContract.getUserInviteInfo(await user2.getAddress());
      
      console.log(`user1邀请奖励: ${ethers.formatEther(user1Info.acceleratedReleaseAmount)} BNB`);
      console.log(`user2邀请奖励: ${ethers.formatEther(user2Info.acceleratedReleaseAmount)} BNB`);
      
      // user2的奖励应该更多，因为有更多被邀请人
      expect(user2Info.acceleratedReleaseAmount).to.be.gte(user1Info.acceleratedReleaseAmount);
    });
  });

  describe("合约资金池测试", function () {
    it("TokenB资金池不足时的处理", async function () {
      // 清空合约的TokenB余额
      const contractBalance = await tokenB.balanceOf(await miningContract.getAddress());
      await miningContract.emergencyWithdraw(await tokenB.getAddress());
      
      // 用户销毁TokenA
      await miningContract.connect(user1).burnTokenAForTokenB(ethers.parseEther("1000"));
      
      // 推进时间
      await ethers.provider.send("evm_increaseTime", [30 * 24 * 60 * 60]);
      await ethers.provider.send("evm_mine");
      
      // 尝试领取TokenB，应该失败因为合约余额不足
      await expect(
        miningContract.connect(user1).claimTokenBFromMechanism1()
      ).to.be.reverted;
      
      // 重新向合约转入TokenB
      await tokenB.transfer(await miningContract.getAddress(), contractBalance);
      
      // 现在应该能正常领取
      await miningContract.connect(user1).claimTokenBFromMechanism1();
    });

    it("合约余额与理论应付的对比", async function () {
      const users = [user1, user2, user3];
      const amounts = [
        ethers.parseEther("1000"),
        ethers.parseEther("2000"),
        ethers.parseEther("1500")
      ];
      
      // 记录初始合约余额
      const initialBalance = await tokenB.balanceOf(await miningContract.getAddress());
      
      // 多个用户销毁TokenA
      for (let i = 0; i < users.length; i++) {
        await miningContract.connect(users[i]).burnTokenAForTokenB(amounts[i]);
      }
      
      // 推进时间
      await ethers.provider.send("evm_increaseTime", [90 * 24 * 60 * 60]); // 3个月
      await ethers.provider.send("evm_mine");
      
      // 计算理论总可领取量
      let totalClaimable = 0n;
      for (const user of users) {
        const claimable = await miningContract.getUserClaimableAmount1(await user.getAddress());
        totalClaimable += claimable;
      }
      
      // 转换为TokenB数量
      const tokenBPrice = await priceOracle.getTokenPrice(await tokenB.getAddress());
      const totalTokenBNeeded = (totalClaimable * ethers.parseEther("1")) / tokenBPrice;
      
      console.log(`理论需要TokenB: ${ethers.formatEther(totalTokenBNeeded)}`);
      console.log(`合约实际余额: ${ethers.formatEther(initialBalance)}`);
      
      // 验证合约余额足够支付
      expect(initialBalance).to.be.gte(totalTokenBNeeded);
    });
  });

  describe("时间跳跃和连续释放测试", function () {
    beforeEach(async function () {
      await miningContract.connect(user1).burnTokenAForTokenB(ethers.parseEther("3000"));
    });

    it("大幅时间跳跃后的释放计算", async function () {
      // 跳跃10年
      await ethers.provider.send("evm_increaseTime", [10 * 365 * 24 * 60 * 60]);
      await ethers.provider.send("evm_mine");
      
      const claimable = await miningContract.getUserClaimableAmount1(await user1.getAddress());
      
      // 应该等于总释放价值 (因为早就100%释放了)
      // 使用预设的价格值，避免调用可能失败的getTokenPrice
      const totalReleaseValue = (ethers.parseEther("3000") * INITIAL_TOKENA_PRICE * BigInt(2)) / ethers.parseEther("1");
      
      expect(claimable).to.equal(totalReleaseValue);
      
      console.log(`10年后可领取: ${ethers.formatEther(claimable)} BNB`);
      console.log(`理论总释放: ${ethers.formatEther(totalReleaseValue)} BNB`);
    });

    it("时间倒退的处理 (区块链重组模拟)", async function () {
      // 正常推进30天
      await ethers.provider.send("evm_increaseTime", [30 * 24 * 60 * 60]);
      await ethers.provider.send("evm_mine");
      
      const claimable30 = await miningContract.getUserClaimableAmount1(await user1.getAddress());
      
      // 领取奖励
      await miningContract.connect(user1).claimTokenBFromMechanism1();
      
      // 模拟时间不再继续前进的情况
      const claimableAfterClaim = await miningContract.getUserClaimableAmount1(await user1.getAddress());
      
      // 应该没有更多可领取的
      expect(claimableAfterClaim).to.equal(0);
      
      console.log(`30天后领取: ${ethers.formatEther(claimable30)} BNB`);
      console.log(`领取后剩余: ${ethers.formatEther(claimableAfterClaim)} BNB`);
    });

    it("连续小幅时间推进的累积效果", async function () {
      const dailyClaimable = [];
      
      // 每天推进一次，记录30天
      for (let day = 1; day <= 30; day++) {
        await ethers.provider.send("evm_increaseTime", [24 * 60 * 60]); // 1天
        await ethers.provider.send("evm_mine");
        
        const claimable = await miningContract.getUserClaimableAmount1(await user1.getAddress());
        dailyClaimable.push({
          day,
          claimable: ethers.formatEther(claimable)
        });
      }
      
      // 第30天的可领取应该等于30天一次性推进的结果
      const finalClaimable = dailyClaimable[29].claimable;
      
      // 重新设置合约状态进行对比测试
      // 这里我们验证最终结果的合理性
      console.log(`第1天可领取: ${dailyClaimable[0].claimable} BNB`);
      console.log(`第15天可领取: ${dailyClaimable[14].claimable} BNB`);
      console.log(`第30天可领取: ${finalClaimable} BNB`);
      
      // 验证第30天有可领取的奖励
      expect(parseFloat(finalClaimable)).to.be.gt(0);
      
      // 由于释放机制是按30天为单位，所以前29天可能都是0，只有第30天才有奖励
      // 这是正常的设计行为
    });
  });

  describe("数值溢出和精度边界测试", function () {
    it("最大uint256值的处理", async function () {
      // 设置极高的TokenA价格
      const maxPrice = ethers.parseEther("1000000000"); // 10亿 BNB per TokenA
      await priceOracle.setManualPrice(await tokenA.getAddress(), maxPrice);
      
      // 销毁少量TokenA避免溢出
      const smallAmount = ethers.parseEther("0.1");
      
      try {
        await miningContract.connect(user1).burnTokenAForTokenB(smallAmount);
        
        const [count1] = await miningContract.getUserMiningRecordCount(await user1.getAddress());
        expect(count1).to.be.gte(1);
        
        console.log("极高价格处理成功");
      } catch (error) {
        console.log("极高价格处理失败:", error.message);
        // 这是预期的，因为可能会导致溢出
      }
    });

    it("wei级别的最小精度测试", async function () {
      // 设置极低的TokenA价格
      const minPrice = 1; // 1 wei per TokenA
      await priceOracle.setManualPrice(await tokenA.getAddress(), minPrice);
      
      // 销毁用户现有的TokenA数量，避免余额不足
      const userBalance = await tokenA.balanceOf(await user1.getAddress());
      const largeAmount = userBalance; // 使用用户的全部余额
      
      const tx = await miningContract.connect(user1).burnTokenAForTokenB(largeAmount);
      const receipt = await tx.wait();
      
      const event = receipt.logs.find(log => {
        try {
          return miningContract.interface.parseLog(log).name === "TokenABurned";
        } catch {
          return false;
        }
      });
      
      const parsedEvent = miningContract.interface.parseLog(event);
      
      console.log(`最小价格销毁价值: ${parsedEvent.args.value} wei`);
      console.log(`最小价格预期TokenB: ${ethers.formatEther(parsedEvent.args.expectedTokenB)}`);
      
      // 即使价格极低，计算应该仍然正确
      expect(parsedEvent.args.value).to.be.gt(0);
      expect(parsedEvent.args.expectedTokenB).to.be.gt(0);
    });

    it("除法精度损失测试", async function () {
      // 设置导致除法精度损失的价格
      const oddTokenAPrice = ethers.parseEther("0.003333333"); // 1/3000 BNB
      const oddTokenBPrice = ethers.parseEther("0.007777777"); // 一个奇怪的价格
      
      await priceOracle.setManualPrice(await tokenA.getAddress(), oddTokenAPrice);
      await priceOracle.setManualPrice(await tokenB.getAddress(), oddTokenBPrice);
      
      const amount = ethers.parseEther("1357"); // 奇数数量
      
      const tx = await miningContract.connect(user1).burnTokenAForTokenB(amount);
      const receipt = await tx.wait();
      
      const event = receipt.logs.find(log => {
        try {
          return miningContract.interface.parseLog(log).name === "TokenABurned";
        } catch {
          return false;
        }
      });
      
      const parsedEvent = miningContract.interface.parseLog(event);
      
      // 手动计算预期值
      const expectedBurnedValue = (amount * oddTokenAPrice) / ethers.parseEther("1");
      const expectedTokenBAmount = (expectedBurnedValue * BigInt(2) * ethers.parseEther("1")) / oddTokenBPrice;
      
      console.log(`奇数计算 - 实际值: ${parsedEvent.args.value}, 预期值: ${expectedBurnedValue}`);
      console.log(`奇数计算 - 实际TokenB: ${parsedEvent.args.expectedTokenB}, 预期TokenB: ${expectedTokenBAmount}`);
      
      // 验证计算精度 (允许1 wei的误差)
      expect(parsedEvent.args.value).to.be.closeTo(expectedBurnedValue, 1);
      expect(parsedEvent.args.expectedTokenB).to.be.closeTo(expectedTokenBAmount, 1);
    });

    it("百分比计算精度测试", async function () {
      // 测试各种百分比的精度
      const testCases = [
        { value: ethers.parseEther("1"), rate: 10, divisor: 100 }, // 10%
        { value: ethers.parseEther("3"), rate: 33, divisor: 100 }, // 33%
        { value: ethers.parseEther("7"), rate: 30, divisor: 100 }, // 30%
        { value: 333n, rate: 10, divisor: 100 }, // 极小值的10%
      ];
      
      for (const testCase of testCases) {
        const result = (testCase.value * BigInt(testCase.rate)) / BigInt(testCase.divisor);
        const expectedMin = (testCase.value * BigInt(testCase.rate)) / BigInt(testCase.divisor);
        
        console.log(`${testCase.value} * ${testCase.rate}% = ${result}`);
        
        // 验证结果合理性
        expect(result).to.be.lte(testCase.value);
        if (testCase.rate > 0) {
          expect(result).to.be.gt(0);
        }
      }
    });
  });
});