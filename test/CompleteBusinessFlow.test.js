const { expect } = require("chai");
const { ethers } = require("hardhat");

describe("完整DeFi生态系统业务流程测试", function () {
  let tokenA, tokenB, minerNFT, miningContract, priceOracle;
  let mockWBNB, mockFactory, mockRouter;
  let owner, marketingWallet, users;
  
  // 系统常量
  const TOTAL_NFTS = 2100;
  const NFT_TOKEN_ALLOCATION = ethers.parseEther("100000"); // 每个NFT分配100000 TokenA
  const TOTAL_NFT_ALLOCATION = ethers.parseEther("210000000"); // 2.1亿TokenA给NFT
  
  beforeEach(async function () {
    // 获取足够的测试账户
    const signers = await ethers.getSigners();
    [owner, marketingWallet, ...users] = signers.slice(0, 50);
    
    console.log(`=== 初始化完整DeFi生态系统 ===`);
    console.log(`可用测试用户: ${users.length}个`);
    
    // 部署模拟PancakeSwap环境
    const MockWBNB = await ethers.getContractFactory("MockWBNB");
    mockWBNB = await MockWBNB.deploy();
    await mockWBNB.waitForDeployment();
    
    const MockPancakeFactory = await ethers.getContractFactory("MockPancakeFactory");
    mockFactory = await MockPancakeFactory.deploy();
    await mockFactory.waitForDeployment();
    
    const MockPancakeRouter = await ethers.getContractFactory("MockPancakeRouter");
    mockRouter = await MockPancakeRouter.deploy(
      await mockFactory.getAddress(),
      await mockWBNB.getAddress()
    );
    await mockRouter.waitForDeployment();
    
    // 部署核心合约
    console.log("部署核心合约...");
    
    const PriceOracle = await ethers.getContractFactory("PriceOracle");
    priceOracle = await PriceOracle.deploy();
    await priceOracle.waitForDeployment();
    
    const TokenA = await ethers.getContractFactory("TokenA");
    tokenA = await TokenA.deploy(
      await marketingWallet.getAddress(),
      await mockRouter.getAddress(),
      await mockWBNB.getAddress()
    );
    await tokenA.waitForDeployment();
    
    const TokenB = await ethers.getContractFactory("TokenB");
    tokenB = await TokenB.deploy(
      await marketingWallet.getAddress(),
      await mockRouter.getAddress(),
      await mockWBNB.getAddress()
    );
    await tokenB.waitForDeployment();
    
    const MinerNFT = await ethers.getContractFactory("MinerNFT");
    minerNFT = await MinerNFT.deploy("MinerNFT", "MNFT", await tokenA.getAddress());
    await minerNFT.waitForDeployment();
    
    const MiningContract = await ethers.getContractFactory("MiningContract");
    miningContract = await MiningContract.deploy(
      await tokenA.getAddress(),
      await tokenB.getAddress(),
      await minerNFT.getAddress(),
      await priceOracle.getAddress()
    );
    await miningContract.waitForDeployment();
    
    // 配置合约关系
    await tokenA.setNftContract(await minerNFT.getAddress());
    await tokenA.setTaxExempt(await miningContract.getAddress(), true);
    await tokenB.setTaxExempt(await miningContract.getAddress(), true);
    
    // 向NFT合约转入TokenA用于分配
    await tokenA.transfer(await minerNFT.getAddress(), TOTAL_NFT_ALLOCATION);
    
    // 设置价格
    await priceOracle.setManualPrice(await tokenA.getAddress(), ethers.parseEther("0.001"));
    await priceOracle.setManualPrice(await tokenB.getAddress(), ethers.parseEther("0.0001"));
    
    // 调整自动分红配置，降低触发频率
    await tokenA.setAutoDividendConfig(
      300000,                      // gasLimit
      20,                         // maxNFTs per batch
      ethers.parseEther("1"),     // 1 ETH threshold
      true                        // enabled
    );
    
    console.log("=== 核心系统初始化完成 ===");
    console.log(`TokenA总供应量: ${ethers.formatEther(await tokenA.totalSupply())} ETH`);
    console.log(`TokenB总供应量: ${ethers.formatEther(await tokenB.totalSupply())} ETH`);
    console.log(`NFT合约TokenA余额: ${ethers.formatEther(await tokenA.balanceOf(await minerNFT.getAddress()))} ETH`);
  });

  describe("1. 系统初始化和NFT分发", function () {
    it("应该正确初始化全部2100个NFT", async function () {
      console.log("=== 初始化2100个NFT ===");
      
      // 检查初始状态
      const initialNFTSupply = await minerNFT.totalSupply();
      expect(initialNFTSupply).to.equal(0);
      
      console.log("开始批量铸造NFT...");
      
      // 计算需要多少批次
      const batchSize = 50; // 每批50个，避免gas限制
      const totalBatches = Math.ceil(TOTAL_NFTS / batchSize);
      
      for (let batch = 0; batch < totalBatches; batch++) {
        const startIndex = batch * batchSize;
        const endIndex = Math.min(startIndex + batchSize, TOTAL_NFTS);
        const batchCount = endIndex - startIndex;
        
        // 为这批NFT选择接收者
        const batchRecipients = [];
        for (let i = 0; i < batchCount; i++) {
          const userIndex = (startIndex + i) % users.length;
          batchRecipients.push(await users[userIndex].getAddress());
        }
        
        // 批量铸造
        const tx = await minerNFT.batchMint(batchRecipients);
        const receipt = await tx.wait();
        
        console.log(`批次 ${batch + 1}/${totalBatches}: 铸造 ${batchCount} 个NFT, Gas: ${receipt.gasUsed}`);
      }
      
      // 验证最终状态
      const finalNFTSupply = await minerNFT.totalSupply();
      expect(finalNFTSupply).to.equal(TOTAL_NFTS);
      
      console.log(`✓ 成功铸造 ${finalNFTSupply} 个NFT`);
      
      // 验证NFT分配
      const nftContractBalance = await tokenA.balanceOf(await minerNFT.getAddress());
      console.log(`NFT合约TokenA余额: ${ethers.formatEther(nftContractBalance)} ETH`);
      
      // 验证每个NFT的代币配额
      const nftInfo = await minerNFT.nftInfo(1);
      expect(nftInfo.totalTokens).to.equal(NFT_TOKEN_ALLOCATION);
      
      console.log(`✓ 每个NFT配额: ${ethers.formatEther(nftInfo.totalTokens)} TokenA`);
    });
    
    it("应该验证NFT分布和所有权", async function () {
      console.log("=== 验证NFT分布 ===");
      
      // 先铸造所有NFT
      await batchMintAllNFTs();
      
      // 统计每个用户的NFT数量
      const userNFTCounts = new Map();
      
      for (let tokenId = 1; tokenId <= TOTAL_NFTS; tokenId++) {
        const owner = await minerNFT.ownerOf(tokenId);
        const currentCount = userNFTCounts.get(owner) || 0;
        userNFTCounts.set(owner, currentCount + 1);
      }
      
      console.log("NFT分布统计:");
      let totalDistributed = 0;
      for (const [owner, count] of userNFTCounts) {
        console.log(`地址 ${owner.substring(0, 8)}...: ${count} 个NFT`);
        totalDistributed += count;
      }
      
      expect(totalDistributed).to.equal(TOTAL_NFTS);
      console.log(`✓ 总计分布: ${totalDistributed} 个NFT`);
    });
  });

  describe("2. TokenA完整业务流程测试", function () {
    beforeEach(async function () {
      await batchMintAllNFTs();
      await setupTradingPairs();
    });
    
    it("应该测试TokenA的税收和分红完整流程", async function () {
      console.log("=== TokenA完整业务流程测试 ===");
      
      // 1. 初始状态检查
      const initialStats = await getSystemStats();
      console.log("初始系统状态:", initialStats);
      
      // 2. 大额交易测试
      const largeTradeAmount = ethers.parseEther("100000");
      await tokenA.transfer(await users[0].getAddress(), largeTradeAmount);
      
      const pairAddress = await mockFactory.getPair(await tokenA.getAddress(), await mockWBNB.getAddress());
      
      console.log("执行大额交易...");
      const tradeTx = await tokenA.connect(users[0]).transfer(pairAddress, largeTradeAmount);
      const tradeReceipt = await tradeTx.wait();
      
      console.log(`大额交易Gas消耗: ${tradeReceipt.gasUsed}`);
      
      // 分析交易事件
      let taxCollected = null;
      let dividendDistributed = null;
      let autoDividendProcessed = null;
      
      for (const log of tradeReceipt.logs) {
        try {
          const parsed = tokenA.interface.parseLog(log);
          if (parsed.name === "TaxCollected") {
            taxCollected = parsed.args;
          } else if (parsed.name === "DividendDistributed") {
            dividendDistributed = parsed.args;
          } else if (parsed.name === "AutoDividendProcessed") {
            autoDividendProcessed = parsed.args;
          }
        } catch (e) {
          // 忽略其他合约事件
        }
      }
      
      console.log("交易事件分析:");
      if (taxCollected) {
        console.log(`  税收收取: 营销=${ethers.formatEther(taxCollected.marketingAmount)}, 销毁=${ethers.formatEther(taxCollected.burnAmount)}, 分红=${ethers.formatEther(taxCollected.dividendAmount)}`);
      }
      if (dividendDistributed) {
        console.log(`  分红分发: ${ethers.formatEther(dividendDistributed.amount)} ETH`);
      }
      if (autoDividendProcessed) {
        console.log(`  自动分红处理: 从${autoDividendProcessed.startIndex}到${autoDividendProcessed.endIndex}, 总额=${ethers.formatEther(autoDividendProcessed.totalAmount)}`);
      }
      
      // 3. 验证税收计算准确性
      const expectedTax = largeTradeAmount * BigInt(3) / BigInt(100);
      const expectedMarketing = expectedTax * BigInt(20) / BigInt(100);
      const expectedBurn = expectedTax * BigInt(50) / BigInt(100);
      const expectedDividend = expectedTax * BigInt(30) / BigInt(100);
      
      if (taxCollected) {
        expect(taxCollected.marketingAmount).to.equal(expectedMarketing);
        expect(taxCollected.burnAmount).to.equal(expectedBurn);
        expect(taxCollected.dividendAmount).to.equal(expectedDividend);
        
        console.log("✓ 税收计算准确性验证通过");
      }
      
      // 4. 检查分红分发效果
      const finalStats = await getSystemStats();
      console.log("交易后系统状态:", finalStats);
      
      console.log("✓ TokenA完整业务流程测试完成");
    });
    
    it("应该测试多用户并发交易场景", async function () {
      console.log("=== 多用户并发交易测试 ===");
      
      const concurrentUsers = users.slice(0, 20);
      const tradeAmount = ethers.parseEther("10000");
      
      // 给每个用户分配代币
      for (let i = 0; i < concurrentUsers.length; i++) {
        await tokenA.transfer(await concurrentUsers[i].getAddress(), tradeAmount);
      }
      
      const pairAddress = await mockFactory.getPair(await tokenA.getAddress(), await mockWBNB.getAddress());
      
      console.log(`${concurrentUsers.length}个用户并发交易...`);
      
      // 记录初始状态
      const initialMarketingBalance = await tokenA.balanceOf(await marketingWallet.getAddress());
      const initialTotalSupply = await tokenA.totalSupply();
      
      // 并发执行交易
      const tradePromises = concurrentUsers.map(user => 
        tokenA.connect(user).transfer(pairAddress, tradeAmount)
      );
      
      const results = await Promise.allSettled(tradePromises);
      const successfulTrades = results.filter(r => r.status === 'fulfilled').length;
      
      console.log(`成功交易: ${successfulTrades}/${concurrentUsers.length}`);
      
      // 验证总体税收效果
      const finalMarketingBalance = await tokenA.balanceOf(await marketingWallet.getAddress());
      const finalTotalSupply = await tokenA.totalSupply();
      
      const totalMarketingIncrease = finalMarketingBalance - initialMarketingBalance;
      const totalBurned = initialTotalSupply - finalTotalSupply;
      
      const expectedTotalTax = BigInt(successfulTrades) * tradeAmount * BigInt(3) / BigInt(100);
      const expectedTotalMarketing = expectedTotalTax * BigInt(20) / BigInt(100);
      const expectedTotalBurn = expectedTotalTax * BigInt(50) / BigInt(100);
      
      console.log("并发交易结果:");
      console.log(`  预期营销收入: ${ethers.formatEther(expectedTotalMarketing)} ETH`);
      console.log(`  实际营销收入: ${ethers.formatEther(totalMarketingIncrease)} ETH`);
      console.log(`  预期销毁数量: ${ethers.formatEther(expectedTotalBurn)} ETH`);
      console.log(`  实际销毁数量: ${ethers.formatEther(totalBurned)} ETH`);
      
      // 验证精度（允许小范围误差）
      expect(totalMarketingIncrease).to.be.closeTo(expectedTotalMarketing, ethers.parseEther("10"));
      expect(totalBurned).to.be.closeTo(expectedTotalBurn, ethers.parseEther("10"));
      
      console.log("✓ 多用户并发交易测试通过");
    });
  });

  describe("3. TokenB完整业务流程测试", function () {
    beforeEach(async function () {
      await setupTradingPairs();
    });
    
    it("应该测试TokenB的税收机制", async function () {
      console.log("=== TokenB税收机制测试 ===");
      
      const tradeAmount = ethers.parseEther("50000");
      await tokenB.transfer(await users[0].getAddress(), tradeAmount);
      
      const pairAddress = await mockFactory.getPair(await tokenB.getAddress(), await mockWBNB.getAddress());
      
      // 记录初始状态
      const initialMarketingBalance = await tokenB.balanceOf(await marketingWallet.getAddress());
      const initialTotalSupply = await tokenB.totalSupply();
      
      console.log("执行TokenB交易...");
      const tx = await tokenB.connect(users[0]).transfer(pairAddress, tradeAmount);
      const receipt = await tx.wait();
      
      // 记录最终状态
      const finalMarketingBalance = await tokenB.balanceOf(await marketingWallet.getAddress());
      const finalTotalSupply = await tokenB.totalSupply();
      
      // 计算实际变化
      const marketingIncrease = finalMarketingBalance - initialMarketingBalance;
      const burned = initialTotalSupply - finalTotalSupply;
      
      // 计算预期值（TokenB: 50%营销，50%销毁）
      const expectedTax = tradeAmount * BigInt(3) / BigInt(100);
      const expectedMarketing = expectedTax * BigInt(50) / BigInt(100);
      const expectedBurn = expectedTax * BigInt(50) / BigInt(100);
      
      console.log("TokenB交易结果:");
      console.log(`  税收总额: ${ethers.formatEther(expectedTax)} ETH`);
      console.log(`  营销分配: ${ethers.formatEther(marketingIncrease)} ETH (预期: ${ethers.formatEther(expectedMarketing)})`);
      console.log(`  销毁数量: ${ethers.formatEther(burned)} ETH (预期: ${ethers.formatEther(expectedBurn)})`);
      
      // 验证TokenB税收分配
      expect(marketingIncrease).to.equal(expectedMarketing);
      expect(burned).to.equal(expectedBurn);
      
      console.log("✓ TokenB税收机制验证通过");
    });
    
    it("应该测试TokenB的批量转账功能", async function () {
      console.log("=== TokenB批量转账测试 ===");
      
      // 准备批量转账参数
      const batchSize = 10;
      const recipients = users.slice(0, batchSize).map(user => user.getAddress());
      const amounts = new Array(batchSize).fill(ethers.parseEther("1000"));
      
      // 给发送者充足的代币
      const totalAmount = ethers.parseEther("20000");
      await tokenB.transfer(await owner.getAddress(), totalAmount);
      
      console.log(`执行批量转账: ${batchSize}个接收者`);
      
      // 执行批量转账
      const resolvedRecipients = await Promise.all(recipients);
      const tx = await tokenB.connect(owner).batchTransfer(resolvedRecipients, amounts);
      const receipt = await tx.wait();
      
      console.log(`批量转账Gas消耗: ${receipt.gasUsed}`);
      console.log(`平均每笔转账Gas: ${receipt.gasUsed / BigInt(batchSize)}`);
      
      // 验证所有接收者都收到了代币
      for (let i = 0; i < resolvedRecipients.length; i++) {
        const balance = await tokenB.balanceOf(resolvedRecipients[i]);
        expect(balance).to.be.gte(ethers.parseEther("900")); // 考虑可能的税收
      }
      
      console.log("✓ TokenB批量转账测试通过");
    });
  });

  describe("4. MiningContract挖矿机制测试", function () {
    beforeEach(async function () {
      await batchMintAllNFTs();
      await setupTradingPairs();
    });
    
    it("应该测试机制一：销毁TokenA获得TokenB", async function () {
      console.log("=== 机制一：销毁TokenA获得TokenB测试 ===");
      
      const burnAmount = ethers.parseEther("10000");
      
      // 给用户TokenA
      await tokenA.transfer(await users[0].getAddress(), burnAmount);
      
      // 授权挖矿合约销毁TokenA
      await tokenA.connect(users[0]).approve(await miningContract.getAddress(), burnAmount);
      
      console.log("开始销毁TokenA获得TokenB...");
      const burnTx = await miningContract.connect(users[0]).burnTokenAForTokenB(
        burnAmount,
        ethers.ZeroAddress // 无邀请人
      );
      const burnReceipt = await burnTx.wait();
      
      console.log(`销毁交易Gas: ${burnReceipt.gasUsed}`);
      
      // 检查用户释放进度信息
      const releaseProgress = await miningContract.getUserReleaseProgress(await users[0].getAddress());
      console.log("挖矿记录统计:");
      console.log(`  机制一记录数: ${releaseProgress.totalRecords1}`);
      console.log(`  总锁定价值1: ${ethers.formatEther(releaseProgress.totalValueLocked1)} BNB`);
      console.log(`  已释放价值1: ${ethers.formatEther(releaseProgress.totalValueReleased1)} BNB`);
      
      expect(releaseProgress.totalRecords1).to.be.gt(0);
      expect(releaseProgress.totalValueLocked1).to.be.gt(0);
      
      // 检查具体挖矿记录详情
      if (releaseProgress.totalRecords1 > 0) {
        const recordDetail = await miningContract.getUserMiningRecord1Detail(await users[0].getAddress(), 0);
        console.log("第一个挖矿记录详情:");
        console.log(`  销毁TokenA数量: ${ethers.formatEther(recordDetail.burnedTokenAAmount)} ETH`);
        console.log(`  销毁价值(BNB): ${ethers.formatEther(recordDetail.burnedTokenAValue)} BNB`);
        console.log(`  预期TokenB数量: ${ethers.formatEther(recordDetail.expectedTokenBAmount)} ETH`);
        console.log(`  总释放价值: ${ethers.formatEther(recordDetail.totalReleaseValue)} BNB`);
        console.log(`  记录状态: ${recordDetail.active ? '活跃' : '已完成'}`);
        
        expect(recordDetail.burnedTokenAAmount).to.equal(burnAmount);
        expect(recordDetail.active).to.be.true;
      }
      
      // 检查可领取数量
      const claimableAmount = await miningContract.getUserClaimableAmount1(await users[0].getAddress());
      console.log(`  当前可领取: ${ethers.formatEther(claimableAmount)} BNB (可能为0，因为需要等待释放期)`);
      
      console.log("✓ 机制一测试完成");
    });
    
    it("应该测试机制二：持有NFT并销毁TokenB", async function () {
      console.log("=== 机制二：持有NFT并销毁TokenB测试 ===");
      
      const burnAmount = ethers.parseEther("50000");
      
      // 确保用户拥有NFT
      const userNFTs = await minerNFT.getUserNFTs(await users[1].getAddress());
      expect(userNFTs.length).to.be.gt(0);
      console.log(`用户拥有 ${userNFTs.length} 个NFT`);
      
      // 给用户TokenB
      await tokenB.transfer(await users[1].getAddress(), burnAmount);
      
      // 授权挖矿合约销毁TokenB
      await tokenB.connect(users[1]).approve(await miningContract.getAddress(), burnAmount);
      
      console.log("开始销毁TokenB获得奖励...");
      const burnTx = await miningContract.connect(users[1]).burnTokenBWithNFT(
        burnAmount,
        ethers.ZeroAddress // 无邀请人
      );
      const burnReceipt = await burnTx.wait();
      
      console.log(`销毁交易Gas: ${burnReceipt.gasUsed}`);
      
      // 检查用户释放进度信息
      const releaseProgress = await miningContract.getUserReleaseProgress(await users[1].getAddress());
      console.log("挖矿记录统计:");
      console.log(`  机制二记录数: ${releaseProgress.totalRecords2}`);
      console.log(`  总锁定价值2: ${ethers.formatEther(releaseProgress.totalValueLocked2)} BNB`);
      console.log(`  已释放价值2: ${ethers.formatEther(releaseProgress.totalValueReleased2)} BNB`);
      
      expect(releaseProgress.totalRecords2).to.be.gt(0);
      expect(releaseProgress.totalValueLocked2).to.be.gt(0);
      
      // 检查具体挖矿记录详情
      if (releaseProgress.totalRecords2 > 0) {
        const recordDetail = await miningContract.getUserMiningRecord2Detail(await users[1].getAddress(), 0);
        console.log("第一个挖矿记录详情:");
        console.log(`  销毁TokenB数量: ${ethers.formatEther(recordDetail.burnedTokenBAmount)} ETH`);
        console.log(`  销毁价值(BNB): ${ethers.formatEther(recordDetail.burnedTokenBValue)} BNB`);
        console.log(`  总释放价值: ${ethers.formatEther(recordDetail.totalReleaseValue)} BNB`);
        console.log(`  记录状态: ${recordDetail.active ? '活跃' : '已完成'}`);
        
        expect(recordDetail.burnedTokenBAmount).to.equal(burnAmount);
        expect(recordDetail.active).to.be.true;
      }
      
      // 检查可领取数量
      const claimableAmount = await miningContract.getUserClaimableAmount2(await users[1].getAddress());
      console.log(`  当前可领取: ${ethers.formatEther(claimableAmount)} BNB (可能为0，因为需要等待释放期)`);
      
      console.log("✓ 机制二测试完成");
    });
    
    it("应该测试奖励领取和邀请机制", async function () {
      console.log("=== 奖励领取和邀请机制测试 ===");
      
      // 先执行机制一的挖矿
      const burnAmount = ethers.parseEther("5000");
      await tokenA.transfer(await users[2].getAddress(), burnAmount);
      await tokenA.connect(users[2]).approve(await miningContract.getAddress(), burnAmount);
      
      // 设置邀请关系
      await miningContract.connect(users[2]).burnTokenAForTokenB(
        burnAmount,
        await users[0].getAddress() // 设置users[0]为邀请人
      );
      
      console.log("检查邀请关系和奖励...");
      
      // 检查用户信息
      const userInfo = await miningContract.getUserInfo(await users[2].getAddress());
      const inviterInfo = await miningContract.getUserInfo(await users[0].getAddress());
      
      console.log("用户信息:");
      console.log(`  邀请人: ${userInfo.inviter}`);
      console.log(`  邀请人数: ${userInfo.totalInvited}`);
      console.log(`  加速释放额度: ${ethers.formatEther(userInfo.acceleratedReleaseAmount)} ETH`);
      
      console.log("邀请人信息:");
      console.log(`  总邀请人数: ${inviterInfo.totalInvited}`);
      console.log(`  加速释放额度: ${ethers.formatEther(inviterInfo.acceleratedReleaseAmount)} ETH`);
      
      expect(userInfo.inviter).to.equal(await users[0].getAddress());
      expect(inviterInfo.acceleratedReleaseAmount).to.be.gt(0);
      
      // 尝试领取TokenB奖励（可能需要时间推进）
      try {
        console.log("尝试领取TokenB奖励...");
        const claimTx = await miningContract.connect(users[2]).claimTokenBFromMechanism1();
        const claimReceipt = await claimTx.wait();
        console.log(`领取交易Gas: ${claimReceipt.gasUsed}`);
      } catch (error) {
        console.log("领取失败（可能需要等待释放期）:", error.message.substring(0, 100));
      }
      
      console.log("✓ 奖励领取和邀请机制测试完成");
    });
  });

  describe("5. 全系统集成业务流程测试", function () {
    beforeEach(async function () {
      await batchMintAllNFTs();
      await setupTradingPairs();
    });
    
    it("应该测试完整的用户生命周期", async function () {
      console.log("=== 完整用户生命周期测试 ===");
      
      const testUser = users[0];
      const testUserAddress = await testUser.getAddress();
      
      console.log(`\n--- 阶段1: 用户获得NFT ---`);
      // 用户应该已经有NFT（在beforeEach中分配）
      const userNFTs = await minerNFT.getUserNFTs(testUserAddress);
      console.log(`用户NFT数量: ${userNFTs.length}`);
      expect(userNFTs.length).to.be.gt(0);
      
      console.log(`\n--- 阶段2: 用户获得代币 ---`);
      const initialTokenAmount = ethers.parseEther("50000");
      await tokenA.transfer(testUserAddress, initialTokenAmount);
      await tokenB.transfer(testUserAddress, initialTokenAmount);
      
      let userBalanceA = await tokenA.balanceOf(testUserAddress);
      let userBalanceB = await tokenB.balanceOf(testUserAddress);
      console.log(`用户TokenA余额: ${ethers.formatEther(userBalanceA)} ETH`);
      console.log(`用户TokenB余额: ${ethers.formatEther(userBalanceB)} ETH`);
      
      console.log(`\n--- 阶段3: 用户进行交易 ---`);
      const pairAddressA = await mockFactory.getPair(await tokenA.getAddress(), await mockWBNB.getAddress());
      const pairAddressB = await mockFactory.getPair(await tokenB.getAddress(), await mockWBNB.getAddress());
      
      // TokenA交易
      const tradeAmountA = ethers.parseEther("10000");
      await tokenA.connect(testUser).transfer(pairAddressA, tradeAmountA);
      
      // TokenB交易
      const tradeAmountB = ethers.parseEther("20000");
      await tokenB.connect(testUser).transfer(pairAddressB, tradeAmountB);
      
      console.log("执行了TokenA和TokenB交易");
      
      console.log(`\n--- 阶段4: 检查分红 ---`);
      // 等待一小段时间让自动分红处理
      await new Promise(resolve => setTimeout(resolve, 100));
      
      const userDividend = await tokenA.calculateDividend(testUserAddress);
      console.log(`用户可领取分红: ${ethers.formatEther(userDividend)} ETH`);
      
      if (userDividend > 0) {
        console.log("领取分红...");
        await tokenA.connect(testUser).claimDividend();
      }
      
      console.log(`\n--- 阶段5: 参与挖矿 ---`);
      // TokenA质押
      const stakeAmountA = ethers.parseEther("5000");
      await tokenA.connect(testUser).approve(await miningContract.getAddress(), stakeAmountA);
      await miningContract.connect(testUser).stakeTokenA(stakeAmountA, 30 * 24 * 3600);
      
      // TokenB质押
      const stakeAmountB = ethers.parseEther("10000");
      await tokenB.connect(testUser).approve(await miningContract.getAddress(), stakeAmountB);
      await miningContract.connect(testUser).stakeTokenB(stakeAmountB, 60 * 24 * 3600);
      
      console.log("完成TokenA和TokenB质押");
      
      console.log(`\n--- 阶段6: 最终状态检查 ---`);
      userBalanceA = await tokenA.balanceOf(testUserAddress);
      userBalanceB = await tokenB.balanceOf(testUserAddress);
      
      const stakeInfoA = await miningContract.getTokenAStakeInfo(testUserAddress);
      const stakeInfoB = await miningContract.getTokenBStakeInfo(testUserAddress);
      
      console.log("最终用户状态:");
      console.log(`  TokenA余额: ${ethers.formatEther(userBalanceA)} ETH`);
      console.log(`  TokenB余额: ${ethers.formatEther(userBalanceB)} ETH`);
      console.log(`  TokenA质押: ${ethers.formatEther(stakeInfoA.amount)} ETH`);
      console.log(`  TokenB质押: ${ethers.formatEther(stakeInfoB.amount)} ETH`);
      console.log(`  NFT数量: ${userNFTs.length}`);
      
      // 验证用户完成了完整的生命周期
      expect(userBalanceA).to.be.gt(0);
      expect(userBalanceB).to.be.gt(0);
      expect(stakeInfoA.amount).to.equal(stakeAmountA);
      expect(stakeInfoB.amount).to.equal(stakeAmountB);
      
      console.log("✓ 完整用户生命周期测试通过");
    });
    
    it("应该测试系统在高负载下的表现", async function () {
      console.log("=== 高负载系统测试 ===");
      
      const activeUsers = users.slice(0, 30);
      
      console.log(`模拟 ${activeUsers.length} 个用户同时活跃...`);
      
      // 给所有用户分配代币
      for (const user of activeUsers) {
        const userAddress = await user.getAddress();
        await tokenA.transfer(userAddress, ethers.parseEther("20000"));
        await tokenB.transfer(userAddress, ethers.parseEther("40000"));
      }
      
      console.log("所有用户代币分配完成");
      
      // 记录初始系统状态
      const initialStats = await getSystemStats();
      console.log("初始系统状态:", initialStats);
      
      // 模拟各种活动
      const activities = [];
      
      // 1. 交易活动
      const pairAddressA = await mockFactory.getPair(await tokenA.getAddress(), await mockWBNB.getAddress());
      const pairAddressB = await mockFactory.getPair(await tokenB.getAddress(), await mockWBNB.getAddress());
      
      for (let i = 0; i < activeUsers.length; i++) {
        const user = activeUsers[i];
        
        // TokenA交易
        activities.push(
          tokenA.connect(user).transfer(pairAddressA, ethers.parseEther("1000"))
        );
        
        // TokenB交易
        activities.push(
          tokenB.connect(user).transfer(pairAddressB, ethers.parseEther("2000"))
        );
      }
      
      // 2. 质押活动
      for (let i = 0; i < Math.min(10, activeUsers.length); i++) {
        const user = activeUsers[i];
        const userAddress = await user.getAddress();
        
        activities.push(
          tokenA.connect(user).approve(await miningContract.getAddress(), ethers.parseEther("5000"))
            .then(() => miningContract.connect(user).stakeTokenA(ethers.parseEther("5000"), 30 * 24 * 3600))
        );
      }
      
      console.log(`准备执行 ${activities.length} 个并发活动...`);
      
      // 并发执行所有活动
      const startTime = Date.now();
      const results = await Promise.allSettled(activities);
      const endTime = Date.now();
      
      const successCount = results.filter(r => r.status === 'fulfilled').length;
      const failureCount = results.filter(r => r.status === 'rejected').length;
      
      console.log("高负载测试结果:");
      console.log(`  总活动数: ${activities.length}`);
      console.log(`  成功: ${successCount}`);
      console.log(`  失败: ${failureCount}`);
      console.log(`  成功率: ${(successCount / activities.length * 100).toFixed(2)}%`);
      console.log(`  总耗时: ${endTime - startTime}ms`);
      
      // 检查最终系统状态
      const finalStats = await getSystemStats();
      console.log("最终系统状态:", finalStats);
      
      // 验证系统稳定性
      expect(successCount / activities.length).to.be.gte(0.8); // 至少80%成功率
      
      console.log("✓ 高负载系统测试通过");
    });
  });

  describe("6. 经济模型长期稳定性测试", function () {
    beforeEach(async function () {
      await batchMintAllNFTs();
      await setupTradingPairs();
    });
    
    it("应该测试长期运行的经济平衡", async function () {
      console.log("=== 长期经济平衡测试 ===");
      
      const testCycles = 20; // 模拟20个周期的运营
      const activeUsers = users.slice(0, 15);
      
      let totalTaxCollected = 0n;
      let totalDividendDistributed = 0n;
      let totalBurned = 0n;
      
      for (let cycle = 1; cycle <= testCycles; cycle++) {
        console.log(`\n--- 周期 ${cycle}/${testCycles} ---`);
        
        // 每个周期的活动
        for (const user of activeUsers) {
          const userAddress = await user.getAddress();
          
          // 给用户一些代币用于交易
          const cycleAmount = ethers.parseEther("5000");
          await tokenA.transfer(userAddress, cycleAmount);
          
          // 执行交易
          const pairAddress = await mockFactory.getPair(await tokenA.getAddress(), await mockWBNB.getAddress());
          const tradeAmount = ethers.parseEther("2000");
          
          const beforeStats = await getSystemStats();
          await tokenA.connect(user).transfer(pairAddress, tradeAmount);
          const afterStats = await getSystemStats();
          
          // 累计统计
          const cycleTax = afterStats.totalTaxCollected - beforeStats.totalTaxCollected;
          const cycleDividend = afterStats.totalDividendDistributed - beforeStats.totalDividendDistributed;
          const cycleBurn = beforeStats.totalSupply - afterStats.totalSupply;
          
          totalTaxCollected += cycleTax;
          totalDividendDistributed += cycleDividend;
          totalBurned += cycleBurn;
        }
        
        if (cycle % 5 === 0) {
          console.log(`周期 ${cycle} 累计统计:`);
          console.log(`  累计税收: ${ethers.formatEther(totalTaxCollected)} ETH`);
          console.log(`  累计分红: ${ethers.formatEther(totalDividendDistributed)} ETH`);
          console.log(`  累计销毁: ${ethers.formatEther(totalBurned)} ETH`);
        }
      }
      
      // 最终经济平衡检查
      console.log("\n=== 最终经济平衡分析 ===");
      console.log(`总税收: ${ethers.formatEther(totalTaxCollected)} ETH`);
      console.log(`总分红: ${ethers.formatEther(totalDividendDistributed)} ETH`);
      console.log(`总销毁: ${ethers.formatEther(totalBurned)} ETH`);
      
      // 验证经济模型比例
      const expectedDividendRatio = totalTaxCollected * BigInt(30) / BigInt(100);
      const expectedBurnRatio = totalTaxCollected * BigInt(50) / BigInt(100);
      
      console.log(`预期分红比例: ${ethers.formatEther(expectedDividendRatio)} ETH`);
      console.log(`预期销毁比例: ${ethers.formatEther(expectedBurnRatio)} ETH`);
      
      // 允许一定的误差范围（考虑自动分红等因素）
      const dividendDiff = totalDividendDistributed > expectedDividendRatio ? 
        totalDividendDistributed - expectedDividendRatio : expectedDividendRatio - totalDividendDistributed;
      const burnDiff = totalBurned > expectedBurnRatio ? 
        totalBurned - expectedBurnRatio : expectedBurnRatio - totalBurned;
      
      const dividendAccuracy = Number((BigInt(10000) - dividendDiff * BigInt(10000) / expectedDividendRatio)) / 100;
      const burnAccuracy = Number((BigInt(10000) - burnDiff * BigInt(10000) / expectedBurnRatio)) / 100;
      
      console.log(`分红准确度: ${dividendAccuracy.toFixed(2)}%`);
      console.log(`销毁准确度: ${burnAccuracy.toFixed(2)}%`);
      
      expect(dividendAccuracy).to.be.gte(90); // 至少90%准确度
      expect(burnAccuracy).to.be.gte(95); // 至少95%准确度
      
      console.log("✓ 长期经济平衡测试通过");
    });
  });

  // 辅助函数
  async function batchMintAllNFTs() {
    console.log("批量铸造2100个NFT...");
    
    const batchSize = 50;
    const totalBatches = Math.ceil(TOTAL_NFTS / batchSize);
    
    for (let batch = 0; batch < totalBatches; batch++) {
      const startIndex = batch * batchSize;
      const endIndex = Math.min(startIndex + batchSize, TOTAL_NFTS);
      const batchCount = endIndex - startIndex;
      
      const batchRecipients = [];
      for (let i = 0; i < batchCount; i++) {
        const userIndex = (startIndex + i) % users.length;
        batchRecipients.push(await users[userIndex].getAddress());
      }
      
      await minerNFT.batchMint(batchRecipients);
    }
    
    const finalSupply = await minerNFT.totalSupply();
    console.log(`✓ 完成铸造 ${finalSupply} 个NFT`);
  }
  
  async function setupTradingPairs() {
    console.log("设置交易对...");
    
    // TokenA交易对
    const liquidityTokenA = ethers.parseEther("100000");
    const liquidityWBNB_A = ethers.parseEther("100");
    
    await mockWBNB.deposit({ value: liquidityWBNB_A });
    await tokenA.approve(await mockRouter.getAddress(), liquidityTokenA);
    await mockWBNB.approve(await mockRouter.getAddress(), liquidityWBNB_A);
    
    await mockRouter.addLiquidity(
      await tokenA.getAddress(),
      await mockWBNB.getAddress(),
      liquidityTokenA,
      liquidityWBNB_A,
      0, 0,
      await owner.getAddress(),
      Math.floor(Date.now() / 1000) + 3600
    );
    
    const pairAddressA = await mockFactory.getPair(await tokenA.getAddress(), await mockWBNB.getAddress());
    await tokenA.setPair(pairAddressA, true);
    
    // TokenB交易对
    const liquidityTokenB = ethers.parseEther("1000000");
    const liquidityWBNB_B = ethers.parseEther("100");
    
    await mockWBNB.deposit({ value: liquidityWBNB_B });
    await tokenB.approve(await mockRouter.getAddress(), liquidityTokenB);
    await mockWBNB.approve(await mockRouter.getAddress(), liquidityWBNB_B);
    
    await mockRouter.addLiquidity(
      await tokenB.getAddress(),
      await mockWBNB.getAddress(),
      liquidityTokenB,
      liquidityWBNB_B,
      0, 0,
      await owner.getAddress(),
      Math.floor(Date.now() / 1000) + 3600
    );
    
    const pairAddressB = await mockFactory.getPair(await tokenB.getAddress(), await mockWBNB.getAddress());
    await tokenB.setPair(pairAddressB, true);
    
    console.log("✓ 交易对设置完成");
  }
  
  async function getSystemStats() {
    const tokenASupply = await tokenA.totalSupply();
    const tokenBSupply = await tokenB.totalSupply();
    const nftSupply = await minerNFT.totalSupply();
    const marketingBalanceA = await tokenA.balanceOf(await marketingWallet.getAddress());
    const marketingBalanceB = await tokenB.balanceOf(await marketingWallet.getAddress());
    
    // 获取分红相关数据
    const contractBalance = await tokenA.contractBalance();
    const totalDividendDistributed = contractBalance[1];
    
    // 计算总税收收取（近似）
    const initialSupplyA = ethers.parseEther("*********");
    const burnedA = initialSupplyA - tokenASupply;
    const totalTaxCollected = (marketingBalanceA + burnedA + totalDividendDistributed);
    
    return {
      tokenASupply: tokenASupply,
      tokenBSupply: tokenBSupply,
      nftSupply: nftSupply,
      marketingBalanceA: marketingBalanceA,
      marketingBalanceB: marketingBalanceB,
      totalDividendDistributed: totalDividendDistributed,
      totalTaxCollected: totalTaxCollected,
      totalSupply: tokenASupply
    };
  }
});