const { expect } = require("chai");
const { ethers } = require("hardhat");

describe("TokenA分红累积问题专项调试", function () {
  let tokenA, minerNFT, mockWBNB, mockRouter;
  let owner, user1, user2, user3, marketingWallet;
  
  beforeEach(async function () {
    const signers = await ethers.getSigners();
    [owner, user1, user2, user3, marketingWallet] = signers.slice(0, 5);
    
    console.log(`=== TokenA分红累积问题专项调试 ===`);
    
    // 部署Mock合约
    const MockWBNB = await ethers.getContractFactory("MockWBNB");
    mockWBNB = await MockWBNB.deploy();
    
    const MockPancakeFactory = await ethers.getContractFactory("MockPancakeFactory");
    const mockFactory = await MockPancakeFactory.deploy();
    
    const MockPancakeRouter = await ethers.getContractFactory("MockPancakeRouter");
    mockRouter = await MockPancakeRouter.deploy(
      await mockFactory.getAddress(),
      await mockWBNB.getAddress()
    );
    
    // 部署TokenA
    const TokenA = await ethers.getContractFactory("TokenA");
    tokenA = await TokenA.deploy(
      await marketingWallet.getAddress(),
      await mockRouter.getAddress(),
      await mockWBNB.getAddress()
    );
    
    // 部署MinerNFT
    const MinerNFT = await ethers.getContractFactory("MinerNFT");
    minerNFT = await MinerNFT.deploy("MinerNFT", "MNFT", await tokenA.getAddress());
    
    // 设置NFT合约地址
    await tokenA.setNftContract(await minerNFT.getAddress());
    
    console.log(`TokenA合约: ${await tokenA.getAddress()}`);
    console.log(`MinerNFT合约: ${await minerNFT.getAddress()}`);
  });
  
  it("应该详细调试分红累积逻辑", async function () {
    console.log("=== 详细调试分红累积逻辑 ===");
    
    // 给用户铸造NFT
    await minerNFT.batchMintCount(await user1.getAddress(), 1);
    console.log("user1拥有1个NFT");
    
    // 准备交易
    const userTokens = ethers.parseEther("10000");
    await tokenA.transfer(await user1.getAddress(), userTokens);
    await tokenA.transfer(await user3.getAddress(), userTokens);
    await tokenA.setPair(await user3.getAddress(), true);
    
    // 记录详细的初始状态
    console.log("\n=== 交易前详细状态 ===");
    const beforeContractBalance = await tokenA.balanceOf(await tokenA.getAddress());
    const beforeMarketingBalance = await tokenA.balanceOf(await marketingWallet.getAddress());
    const beforeUser1Balance = await tokenA.balanceOf(await user1.getAddress());
    const beforeUser3Balance = await tokenA.balanceOf(await user3.getAddress());
    const beforeTotalSupply = await tokenA.totalSupply();
    const beforeContractInfo = await tokenA.contractBalance();
    
    console.log(`合约余额: ${ethers.formatEther(beforeContractBalance)} TokenA`);
    console.log(`营销余额: ${ethers.formatEther(beforeMarketingBalance)} TokenA`);
    console.log(`user1余额: ${ethers.formatEther(beforeUser1Balance)} TokenA`);
    console.log(`user3余额: ${ethers.formatEther(beforeUser3Balance)} TokenA`);
    console.log(`总供应量: ${ethers.formatEther(beforeTotalSupply)} TokenA`);
    console.log(`累计分红: ${ethers.formatEther(beforeContractInfo.totalDivs)} TokenA`);
    console.log(`每股分红: ${beforeContractInfo.cumulativePerShare}`);
    
    // 检查user3是否正确设置为交易对
    const isUser3Pair = await tokenA.isPair(await user3.getAddress());
    console.log(`\nuser3是否为交易对: ${isUser3Pair}`);
    
    // 检查税收豁免状态
    const isUser3TaxExempt = await tokenA.taxExempt(await user3.getAddress());
    const isUser1TaxExempt = await tokenA.taxExempt(await user1.getAddress());
    console.log(`user3税收豁免: ${isUser3TaxExempt}`);
    console.log(`user1税收豁免: ${isUser1TaxExempt}`);
    
    // 执行交易
    const tradeAmount = ethers.parseEther("1000");
    console.log(`\n=== 执行交易 ===`);
    console.log(`交易类型: user3(交易对) -> user1 (买入交易)`);
    console.log(`交易金额: ${ethers.formatEther(tradeAmount)} TokenA`);
    
    const tx = await tokenA.connect(user3).transfer(await user1.getAddress(), tradeAmount);
    const receipt = await tx.wait();
    
    // 记录交易后状态
    console.log("\n=== 交易后详细状态 ===");
    const afterContractBalance = await tokenA.balanceOf(await tokenA.getAddress());
    const afterMarketingBalance = await tokenA.balanceOf(await marketingWallet.getAddress());
    const afterUser1Balance = await tokenA.balanceOf(await user1.getAddress());
    const afterUser3Balance = await tokenA.balanceOf(await user3.getAddress());
    const afterTotalSupply = await tokenA.totalSupply();
    const afterContractInfo = await tokenA.contractBalance();
    
    console.log(`合约余额: ${ethers.formatEther(afterContractBalance)} TokenA`);
    console.log(`营销余额: ${ethers.formatEther(afterMarketingBalance)} TokenA`);
    console.log(`user1余额: ${ethers.formatEther(afterUser1Balance)} TokenA`);
    console.log(`user3余额: ${ethers.formatEther(afterUser3Balance)} TokenA`);
    console.log(`总供应量: ${ethers.formatEther(afterTotalSupply)} TokenA`);
    console.log(`累计分红: ${ethers.formatEther(afterContractInfo.totalDivs)} TokenA`);
    console.log(`每股分红: ${afterContractInfo.cumulativePerShare}`);
    
    // 计算变化量
    console.log("\n=== 变化量分析 ===");
    const contractBalanceChange = afterContractBalance - beforeContractBalance;
    const marketingBalanceChange = afterMarketingBalance - beforeMarketingBalance;
    const user1BalanceChange = afterUser1Balance - beforeUser1Balance;
    const user3BalanceChange = beforeUser3Balance - afterUser3Balance; // 支付方减少
    const totalSupplyChange = beforeTotalSupply - afterTotalSupply; // 销毁减少供应量
    const totalDivsChange = afterContractInfo.totalDivs - beforeContractInfo.totalDivs;
    
    console.log(`合约余额变化: ${ethers.formatEther(contractBalanceChange)} TokenA`);
    console.log(`营销余额变化: ${ethers.formatEther(marketingBalanceChange)} TokenA`);
    console.log(`user1余额变化: ${ethers.formatEther(user1BalanceChange)} TokenA`);
    console.log(`user3余额变化: ${ethers.formatEther(user3BalanceChange)} TokenA (支付)`);
    console.log(`总供应量减少: ${ethers.formatEther(totalSupplyChange)} TokenA (销毁)`);
    console.log(`累计分红变化: ${ethers.formatEther(totalDivsChange)} TokenA`);
    
    // 计算期望的税收分配
    console.log("\n=== 期望税收分配 ===");
    const totalTax = (tradeAmount * BigInt(3)) / BigInt(100); // 3%
    const expectedMarketing = (totalTax * BigInt(20)) / BigInt(100); // 20%
    const expectedBurn = (totalTax * BigInt(50)) / BigInt(100); // 50%
    const expectedDividend = totalTax - expectedMarketing - expectedBurn; // 30%
    const expectedUserReceive = tradeAmount - totalTax;
    
    console.log(`总税收: ${ethers.formatEther(totalTax)} TokenA`);
    console.log(`期望营销: ${ethers.formatEther(expectedMarketing)} TokenA`);
    console.log(`期望销毁: ${ethers.formatEther(expectedBurn)} TokenA`);
    console.log(`期望分红: ${ethers.formatEther(expectedDividend)} TokenA`);
    console.log(`期望用户收到: ${ethers.formatEther(expectedUserReceive)} TokenA`);
    
    // 分析交易事件
    console.log("\n=== 交易事件分析 ===");
    const logs = receipt.logs;
    for (let i = 0; i < logs.length; i++) {
      try {
        const parsed = tokenA.interface.parseLog(logs[i]);
        if (parsed.name === 'TaxCollected') {
          console.log(`TaxCollected事件:`);
          console.log(`  营销: ${ethers.formatEther(parsed.args[0])} TokenA`);
          console.log(`  销毁: ${ethers.formatEther(parsed.args[1])} TokenA`);
          console.log(`  分红: ${ethers.formatEther(parsed.args[2])} TokenA`);
        } else if (parsed.name === 'DividendDistributed') {
          console.log(`DividendDistributed事件: ${ethers.formatEther(parsed.args[0])} TokenA`);
        } else if (parsed.name === 'Transfer') {
          console.log(`Transfer事件: ${parsed.args[0]} -> ${parsed.args[1]}, ${ethers.formatEther(parsed.args[2])} TokenA`);
        } else {
          console.log(`其他事件: ${parsed.name}`);
        }
      } catch (e) {
        // 忽略无法解析的日志
      }
    }
    
    // 验证结果
    console.log("\n=== 验证结果 ===");
    console.log(`营销分配正确: ${marketingBalanceChange === expectedMarketing ? '✅' : '❌'}`);
    console.log(`销毁数量正确: ${totalSupplyChange === expectedBurn ? '✅' : '❌'}`);
    console.log(`用户收到正确: ${user1BalanceChange === expectedUserReceive ? '✅' : '❌'}`);
    console.log(`分红累积正确: ${contractBalanceChange === expectedDividend ? '✅' : '❌'}`);
    console.log(`分红统计正确: ${totalDivsChange === expectedDividend ? '✅' : '❌'}`);
    
    // 重点分析问题
    console.log("\n=== 问题分析 ===");
    if (contractBalanceChange !== expectedDividend) {
      console.log(`❌ 合约余额变化不正确:`);
      console.log(`  实际: ${ethers.formatEther(contractBalanceChange)} TokenA`);
      console.log(`  期望: ${ethers.formatEther(expectedDividend)} TokenA`);
      console.log(`  差额: ${ethers.formatEther(expectedDividend - contractBalanceChange)} TokenA`);
    }
    
    if (totalDivsChange !== expectedDividend) {
      console.log(`❌ 分红统计变化不正确:`);
      console.log(`  实际: ${ethers.formatEther(totalDivsChange)} TokenA`);
      console.log(`  期望: ${ethers.formatEther(expectedDividend)} TokenA`);
      console.log(`  差额: ${ethers.formatEther(expectedDividend - totalDivsChange)} TokenA`);
    }
    
    // 检查账目平衡
    console.log("\n=== 账目平衡检查 ===");
    const totalAccountedFor = marketingBalanceChange + totalSupplyChange + contractBalanceChange;
    console.log(`总税收: ${ethers.formatEther(totalTax)} TokenA`);
    console.log(`实际分配: ${ethers.formatEther(totalAccountedFor)} TokenA`);
    console.log(`账目平衡: ${totalAccountedFor === totalTax ? '✅' : '❌'}`);
    
    if (totalAccountedFor !== totalTax) {
      console.log(`账目不平衡差额: ${ethers.formatEther(totalTax - totalAccountedFor)} TokenA`);
    }
  });
});