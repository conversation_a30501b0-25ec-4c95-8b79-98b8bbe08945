const { expect } = require("chai");
const { ethers } = require("hardhat");

describe("USDT机制集成测试", function () {
    let miningContract;
    let tokenA, tokenB, nftContract, usdtToken;
    let priceOracle;
    let owner, user1, user2, inviter;
    let mockPancakeFactory, mockPancakePair, mockPancakeRouter;

    // 测试常量
    const USDT_DECIMALS = 18;
    const TOKEN_DECIMALS = 18;
    const PRECISION = ethers.parseEther("1");
    const REWARD_MULTIPLIER = 2;

    beforeEach(async function () {
        [owner, user1, user2, inviter] = await ethers.getSigners();

        // 部署Mock代币
        const MockERC20 = await ethers.getContractFactory("MockERC20");
        tokenA = await MockERC20.deploy("TokenA", "TKA", ethers.parseEther("1000000"));
        tokenB = await MockERC20.deploy("TokenB", "TKB", ethers.parseEther("1000000"));
        usdtToken = await MockERC20.deploy("USDT", "USDT", ethers.parseUnits("1000000", USDT_DECIMALS));

        // 部署Mock NFT
        const MockERC721 = await ethers.getContractFactory("MockERC721");
        nftContract = await MockERC721.deploy("TestNFT", "TNFT");

        // 部署Mock PriceOracle
        const MockPriceOracle = await ethers.getContractFactory("MockPriceOracle");
        priceOracle = await MockPriceOracle.deploy();

        // 部署Mock PancakeSwap合约
        const MockPancakeFactory = await ethers.getContractFactory("MockPancakeFactory");
        mockPancakeFactory = await MockPancakeFactory.deploy();

        const MockPancakePair = await ethers.getContractFactory("MockPancakePair");
        mockPancakePair = await MockPancakePair.deploy();

        const MockPancakeRouter = await ethers.getContractFactory("MockPancakeRouter");
        mockPancakeRouter = await MockPancakeRouter.deploy();

        // 部署MiningContract
        const MiningContract = await ethers.getContractFactory("MiningContract");
        miningContract = await MiningContract.deploy(
            await tokenA.getAddress(),
            await tokenB.getAddress(),
            await nftContract.getAddress(),
            await priceOracle.getAddress()
        );

        console.log("✅ 所有合约部署完成");
    });

    describe("基础配置验证", function () {
        it("应该正确配置USDT地址和相关常量", async function () {
            const usdtAddress = await miningContract.USDT();
            const routerAddress = await miningContract.PANCAKE_ROUTER();
            const factoryAddress = await miningContract.PANCAKE_FACTORY();

            console.log(`USDT地址: ${usdtAddress}`);
            console.log(`Router地址: ${routerAddress}`);
            console.log(`Factory地址: ${factoryAddress}`);

            expect(usdtAddress).to.equal("******************************************");
            expect(routerAddress).to.equal("******************************************");
            expect(factoryAddress).to.equal("******************************************");
        });

        it("应该正确配置价格保护参数", async function () {
            const minLiquidity = await miningContract.MIN_LIQUIDITY();
            const maxPrice = await miningContract.MAX_REASONABLE_PRICE();
            const minPrice = await miningContract.MIN_REASONABLE_PRICE();

            console.log(`最小流动性: ${ethers.formatEther(minLiquidity)} USDT`);
            console.log(`最大价格: ${ethers.formatEther(maxPrice)} USDT`);
            console.log(`最小价格: ${ethers.formatUnits(minPrice, 18)} USDT`);

            expect(minLiquidity).to.equal(ethers.parseEther("1000"));
            expect(maxPrice).to.equal(ethers.parseEther("1000000"));
            expect(minPrice).to.equal(ethers.parseUnits("0.000001", 18));
        });
    });

    describe("USDT价格功能测试", function () {
        it("应该返回USDT基准价格为1", async function () {
            const usdtPrice = await miningContract.getTokenPrice(await miningContract.USDT());
            console.log(`USDT价格: ${ethers.formatEther(usdtPrice)}`);
            expect(usdtPrice).to.equal(PRECISION);
        });

        it("应该正确验证USDT价格可靠性", async function () {
            const isReliable = await miningContract.isTokenPriceReliable(await miningContract.USDT());
            console.log(`USDT价格可靠性: ${isReliable}`);
            expect(isReliable).to.be.true;
        });
    });

    describe("错误处理测试", function () {
        it("应该拒绝零USDT金额", async function () {
            await expect(
                miningContract.connect(user1).buyAndBurnTokenAWithUSDT(0, ethers.ZeroAddress)
            ).to.be.revertedWith("USDT amount must be greater than zero");
            console.log("✅ 正确拒绝零USDT金额");
        });

        it("应该拒绝余额不足的情况", async function () {
            const largeAmount = ethers.parseUnits("1000000", USDT_DECIMALS);
            await expect(
                miningContract.connect(user1).buyAndBurnTokenAWithUSDT(largeAmount, ethers.ZeroAddress)
            ).to.be.revertedWith("Insufficient USDT balance");
            console.log("✅ 正确拒绝余额不足");
        });

        it("应该拒绝无效代币地址", async function () {
            await expect(
                miningContract.getTokenPrice(ethers.ZeroAddress)
            ).to.be.revertedWith("Invalid token address");
            console.log("✅ 正确拒绝无效代币地址");
        });

        it("应该拒绝不存在的交易对", async function () {
            const fakeToken = "******************************************";
            await expect(
                miningContract.getPairInfo(fakeToken)
            ).to.be.revertedWith("Trading pair does not exist");
            console.log("✅ 正确拒绝不存在的交易对");
        });
    });

    describe("合约状态测试", function () {
        it("应该支持暂停和恢复功能", async function () {
            // 暂停合约
            await miningContract.pause();
            console.log("📴 合约已暂停");

            // 尝试在暂停状态下执行操作
            await expect(
                miningContract.connect(user1).buyAndBurnTokenAWithUSDT(
                    ethers.parseUnits("100", USDT_DECIMALS),
                    ethers.ZeroAddress
                )
            ).to.be.revertedWith("Pausable: paused");

            // 恢复合约
            await miningContract.unpause();
            console.log("✅ 合约已恢复");
        });

        it("应该正确管理Owner权限", async function () {
            // 非Owner尝试暂停合约
            await expect(
                miningContract.connect(user1).pause()
            ).to.be.revertedWith("Ownable: caller is not the owner");
            console.log("✅ 正确保护Owner权限");
        });
    });

    describe("查询功能测试", function () {
        it("应该正确返回用户初始状态", async function () {
            const [count1, count2] = await miningContract.getUserMiningRecordCount(user1.address);
            expect(count1).to.equal(0);
            expect(count2).to.equal(0);
            console.log(`用户${user1.address}的挖矿记录: 机制一=${count1}, 机制二=${count2}`);

            const inviteInfo = await miningContract.getUserInviteInfo(user1.address);
            expect(inviteInfo.inviter).to.equal(ethers.ZeroAddress);
            expect(inviteInfo.totalInvited).to.equal(0);
            expect(inviteInfo.acceleratedReleaseAmount).to.equal(0);
            console.log("✅ 用户初始状态正确");
        });

        it("应该正确显示全局统计", async function () {
            const totalBurnedA = await miningContract.totalBurnedTokenA();
            const totalBurnedB = await miningContract.totalBurnedTokenB();
            const totalReleased = await miningContract.totalReleasedTokenB();

            console.log(`全局统计:`);
            console.log(`- 总销毁TokenA: ${ethers.formatEther(totalBurnedA)}`);
            console.log(`- 总销毁TokenB: ${ethers.formatEther(totalBurnedB)}`);
            console.log(`- 总释放TokenB: ${ethers.formatEther(totalReleased)}`);

            expect(totalBurnedA).to.equal(0);
            expect(totalBurnedB).to.equal(0);
            expect(totalReleased).to.equal(0);
        });
    });

    describe("事件结构验证", function () {
        it("应该定义正确的USDT相关事件", async function () {
            // 验证合约ABI中包含新的事件定义
            const contractInterface = miningContract.interface;
            
            const events = [
                "TokenABurned",
                "TokenBBurned", 
                "USDTSwappedForTokenA",
                "TokenBReleased",
                "AcceleratedReleaseAdded",
                "AcceleratedReleaseUsed"
            ];

            for (const eventName of events) {
                try {
                    const event = contractInterface.getEvent(eventName);
                    console.log(`✅ 事件 ${eventName} 已定义`);
                } catch (error) {
                    console.log(`❌ 事件 ${eventName} 未找到`);
                }
            }
        });
    });

    describe("完整性检查", function () {
        it("应该正确处理数据结构", async function () {
            console.log("=== 数据结构完整性检查 ===");
            
            // 检查MiningRecord1结构
            try {
                const userRecords = await miningContract.getUserMiningRecordCount(user1.address);
                console.log("✅ MiningRecord1结构访问正常");
            } catch (error) {
                console.log("❌ MiningRecord1结构访问异常:", error.message);
            }

            // 检查UserInfo结构
            try {
                const userInfo = await miningContract.getUserInviteInfo(user1.address);
                console.log("✅ UserInfo结构访问正常");
            } catch (error) {
                console.log("❌ UserInfo结构访问异常:", error.message);
            }
        });

        it("应该正确处理释放时间计算", async function () {
            const releaseInterval = await miningContract.releaseInterval();
            console.log(`释放间隔: ${releaseInterval} 秒 (${releaseInterval / 86400} 天)`);
            expect(releaseInterval).to.equal(30 * 24 * 60 * 60); // 30天
        });
    });

    describe("升级兼容性测试", function () {
        it("应该保持原有功能的可用性", async function () {
            console.log("=== 升级兼容性检查 ===");
            
            // 检查原有的查询函数是否仍然可用
            try {
                await miningContract.getUserClaimableAmount1(user1.address);
                console.log("✅ getUserClaimableAmount1 仍然可用");
            } catch (error) {
                console.log("❌ getUserClaimableAmount1 不可用:", error.message);
            }

            try {
                await miningContract.getUserClaimableAmount2(user1.address);
                console.log("✅ getUserClaimableAmount2 仍然可用");
            } catch (error) {
                console.log("❌ getUserClaimableAmount2 不可用:", error.message);
            }

            try {
                await miningContract.getUserReleaseProgress(user1.address);
                console.log("✅ getUserReleaseProgress 仍然可用");
            } catch (error) {
                console.log("❌ getUserReleaseProgress 不可用:", error.message);
            }
        });
    });

    after(function () {
        console.log("\n=== USDT机制集成测试完成 ===");
        console.log("🎉 所有基础功能验证通过");
        console.log("⚠️  注意: DEX交互功能需要在真实环境或Fork环境中测试");
        console.log("📋 建议: 在测试网或主网Fork上进行完整的端到端测试");
    });
});