const { expect } = require("chai");
const { ethers } = require("hardhat");

describe("MiningContract DEX价格优化测试", function () {
    let miningContract;
    let tokenA, tokenB, nftContract;
    let mockFactory, mockPair;
    let owner, user1, user2;
    let priceOracle;

    const WBNB = "******************************************";
    const PRECISION = ethers.parseEther("1");

    beforeEach(async function () {
        [owner, user1, user2] = await ethers.getSigners();

        // 部署Mock合约
        const MockERC20 = await ethers.getContractFactory("MockUSDT");
        tokenA = await MockERC20.deploy("TokenA", "TKA");
        tokenB = await MockERC20.deploy("TokenB", "TKB");

        const MockERC721 = await ethers.getContractFactory("MockERC721");
        nftContract = await MockERC721.deploy("MinerNFT", "MNFT");

        // 部署PriceOracle (作为备用)
        const PriceOracle = await ethers.getContractFactory("PriceOracle");
        priceOracle = await PriceOracle.deploy();

        // 部署MiningContract
        const MiningContract = await ethers.getContractFactory("MiningContract");
        miningContract = await MiningContract.deploy(
            await tokenA.getAddress(),
            await tokenB.getAddress(),
            await nftContract.getAddress(),
            await priceOracle.getAddress()
        );

        // 设置初始余额
        await tokenA.mint(user1.address, ethers.parseEther("1000"));
        await tokenB.mint(user1.address, ethers.parseEther("1000"));
        await tokenB.mint(await miningContract.getAddress(), ethers.parseEther("10000"));
    });

    describe("DEX价格获取功能", function () {
        it("应该正确获取WBNB价格", async function () {
            const wbnbPrice = await miningContract.getTokenPrice(WBNB);
            expect(wbnbPrice).to.equal(ethers.parseEther("1"));
        });

        it("应该在交易对不存在时revert", async function () {
            await expect(miningContract.getTokenPrice(await tokenA.getAddress()))
                .to.be.revertedWith("Trading pair does not exist");
        });

        it("应该检查价格合理性范围", async function () {
            // 这里我们需要模拟一个带有异常价格的交易对
            // 由于测试环境限制，这个测试需要在实际DEX环境下进行
            console.log("价格合理性检查需要在实际DEX环境下测试");
        });
    });

    describe("价格可靠性检查", function () {
        it("WBNB价格应该始终可靠", async function () {
            const isReliable = await miningContract.isTokenPriceReliable(WBNB);
            expect(isReliable).to.be.true;
        });

        it("不存在交易对的代币价格不可靠", async function () {
            const isReliable = await miningContract.isTokenPriceReliable(await tokenA.getAddress());
            expect(isReliable).to.be.false;
        });
    });

    describe("交易对信息查询", function () {
        it("应该在交易对不存在时revert", async function () {
            await expect(miningContract.getPairInfo(await tokenA.getAddress()))
                .to.be.revertedWith("Trading pair does not exist");
        });
    });

    describe("基础参数验证", function () {
        it("应该正确设置价格保护常量", async function () {
            const minLiquidity = await miningContract.MIN_LIQUIDITY();
            const maxPrice = await miningContract.MAX_REASONABLE_PRICE();
            const minPrice = await miningContract.MIN_REASONABLE_PRICE();

            expect(minLiquidity).to.equal(ethers.parseEther("10")); // 10 BNB
            expect(maxPrice).to.equal(ethers.parseEther("1000")); // 1000 BNB
            expect(minPrice).to.equal(ethers.parseUnits("1", 12)); // 0.000001 BNB
        });

        it("应该正确设置DEX常量地址", async function () {
            const wbnb = await miningContract.WBNB();
            const factory = await miningContract.PANCAKE_FACTORY();

            expect(wbnb).to.equal("******************************************");
            expect(factory).to.equal("******************************************");
        });
    });

    describe("错误处理", function () {
        it("应该在传入零地址时revert", async function () {
            await expect(miningContract.getTokenPrice(ethers.ZeroAddress))
                .to.be.revertedWith("Invalid token address");
        });

        it("应该在传入零地址到getPairInfo时revert", async function () {
            await expect(miningContract.getPairInfo(ethers.ZeroAddress))
                .to.be.revertedWith("Invalid token address");
        });
    });
});

describe("MiningContract集成测试 (模拟真实环境)", function () {
    // 这些测试需要fork主网或者使用mock的PancakeSwap合约
    it("应该能够处理真实的价格获取流程", async function () {
        console.log("集成测试需要在fork主网环境下进行");
        console.log("或者需要部署完整的PancakeSwap mock合约");
        
        // 建议的测试流程：
        // 1. 使用hardhat fork BSC主网
        // 2. 或者部署MockPancakeFactory和MockPancakePair
        // 3. 设置真实的储备量数据
        // 4. 测试价格计算的准确性
        // 5. 测试各种边界条件
    });
});