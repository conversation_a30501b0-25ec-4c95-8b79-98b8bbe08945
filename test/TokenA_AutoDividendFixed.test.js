const { expect } = require("chai");
const { ethers } = require("hardhat");

describe("TokenA自动分红机制修复验证", function () {
  let tokenA, minerNFT, mockWBNB, mockRouter;
  let owner, user1, user2, user3, marketingWallet;
  
  beforeEach(async function () {
    const signers = await ethers.getSigners();
    [owner, user1, user2, user3, marketingWallet] = signers.slice(0, 5);
    
    console.log(`=== TokenA自动分红机制修复验证 ===`);
    
    // 部署Mock合约
    const MockWBNB = await ethers.getContractFactory("MockWBNB");
    mockWBNB = await MockWBNB.deploy();
    
    const MockPancakeFactory = await ethers.getContractFactory("MockPancakeFactory");
    const mockFactory = await MockPancakeFactory.deploy();
    
    const MockPancakeRouter = await ethers.getContractFactory("MockPancakeRouter");
    mockRouter = await MockPancakeRouter.deploy(
      await mockFactory.getAddress(),
      await mockWBNB.getAddress()
    );
    
    // 部署TokenA
    const TokenA = await ethers.getContractFactory("TokenA");
    tokenA = await TokenA.deploy(
      await marketingWallet.getAddress(),
      await mockRouter.getAddress(),
      await mockWBNB.getAddress()
    );
    
    // 部署MinerNFT
    const MinerNFT = await ethers.getContractFactory("MinerNFT");
    minerNFT = await MinerNFT.deploy("MinerNFT", "MNFT", await tokenA.getAddress());
    
    // 设置NFT合约地址
    await tokenA.setNftContract(await minerNFT.getAddress());
    
    console.log(`TokenA合约: ${await tokenA.getAddress()}`);
    console.log(`MinerNFT合约: ${await minerNFT.getAddress()}`);
  });
  
  it("应该验证分红正确累积到合约余额", async function () {
    console.log("=== 验证分红累积到合约余额 ===");
    
    // 给用户铸造NFT
    await minerNFT.batchMintCount(await user1.getAddress(), 2);
    await minerNFT.batchMintCount(await user2.getAddress(), 1);
    console.log("user1: 2个NFT, user2: 1个NFT");
    
    // 准备交易
    const userTokens = ethers.parseEther("10000");
    await tokenA.transfer(await user1.getAddress(), userTokens);
    await tokenA.transfer(await user3.getAddress(), userTokens);
    await tokenA.setPair(await user3.getAddress(), true);
    
    // 记录初始状态
    const initialContractBalance = await tokenA.balanceOf(await tokenA.getAddress());
    console.log(`初始合约余额: ${ethers.formatEther(initialContractBalance)} TokenA`);
    
    // 执行交易产生分红
    const tradeAmount = ethers.parseEther("1000");
    console.log(`\\n执行交易: ${ethers.formatEther(tradeAmount)} TokenA`);
    
    await tokenA.connect(user3).transfer(await user1.getAddress(), tradeAmount);
    
    // 检查交易后状态
    const afterContractBalance = await tokenA.balanceOf(await tokenA.getAddress());
    const contractBalanceChange = afterContractBalance - initialContractBalance;
    
    console.log(`交易后合约余额: ${ethers.formatEther(afterContractBalance)} TokenA`);
    console.log(`合约余额增加: ${ethers.formatEther(contractBalanceChange)} TokenA`);
    
    // 计算期望分红
    const totalTax = (tradeAmount * BigInt(3)) / BigInt(100); // 3%
    const expectedDividend = (totalTax * BigInt(30)) / BigInt(100); // 30%用于分红
    console.log(`期望分红: ${ethers.formatEther(expectedDividend)} TokenA`);
    
    // 验证分红累积正确
    expect(contractBalanceChange).to.equal(expectedDividend, "分红应该正确累积到合约");
    console.log(`✅ 分红累积验证通过`);
    
    // 检查分红统计
    const contractInfo = await tokenA.contractBalance();
    console.log(`\\n分红统计信息:`);
    console.log(`合约余额: ${ethers.formatEther(contractInfo.balance)} TokenA`);
    console.log(`累计分红: ${ethers.formatEther(contractInfo.totalDivs)} TokenA`);
    console.log(`每股分红: ${contractInfo.cumulativePerShare}`);
    
    expect(contractInfo.totalDivs).to.equal(expectedDividend, "累计分红统计应该正确");
    expect(contractInfo.balance).to.equal(afterContractBalance, "余额统计应该一致");
    console.log(`✅ 分红统计验证通过`);
  });
  
  it("应该验证修复后的自动分红触发机制", async function () {
    console.log("=== 验证修复后的自动分红触发 ===");
    
    // 给用户铸造NFT
    await minerNFT.batchMintCount(await user1.getAddress(), 1);
    await minerNFT.batchMintCount(await user2.getAddress(), 1);
    
    // 设置较低的自动分红门槛
    const lowThreshold = ethers.parseEther("5"); // 5 TokenA
    await tokenA.setAutoDividendConfig(
      500000, // gasLimit
      10,     // maxNFTs
      lowThreshold, // threshold
      true    // enabled
    );
    
    console.log(`设置自动分红门槛: ${ethers.formatEther(lowThreshold)} TokenA`);
    
    // 准备交易
    await tokenA.transfer(await user3.getAddress(), ethers.parseEther("10000"));
    await tokenA.setPair(await user3.getAddress(), true);
    
    // 检查自动分红配置
    const config = await tokenA.getAutoDividendStatus();
    console.log(`\\n自动分红配置:`);
    console.log(`启用状态: ${config.enabled}`);
    console.log(`门槛设置: ${ethers.formatEther(config.threshold)} TokenA`);
    console.log(`Gas限制: ${config.gasLimit}`);
    console.log(`最大NFT数: ${config.maxNFTs}`);
    
    // 执行足够大的交易以触发自动分红
    const bigTradeAmount = ethers.parseEther("1000"); // 产生9 TokenA分红，超过5门槛
    console.log(`\\n执行大额交易: ${ethers.formatEther(bigTradeAmount)} TokenA`);
    
    const beforeUser1Balance = await tokenA.balanceOf(await user1.getAddress());
    const beforeUser2Balance = await tokenA.balanceOf(await user2.getAddress());
    
    // 执行交易 (应该触发自动分红)
    const tx = await tokenA.connect(user3).transfer(await user1.getAddress(), bigTradeAmount);
    const receipt = await tx.wait();
    
    const afterUser1Balance = await tokenA.balanceOf(await user1.getAddress());
    const afterUser2Balance = await tokenA.balanceOf(await user2.getAddress());
    
    console.log(`\\n交易后余额变化:`);
    console.log(`user1变化: ${ethers.formatEther(afterUser1Balance - beforeUser1Balance)} TokenA`);
    console.log(`user2变化: ${ethers.formatEther(afterUser2Balance - beforeUser2Balance)} TokenA`);
    
    // 检查是否有自动分红事件
    const dividendEvents = receipt.logs.filter(log => {
      try {
        const parsed = tokenA.interface.parseLog(log);
        return parsed.name === 'AutoDividendProcessed' || parsed.name === 'DividendClaimed';
      } catch {
        return false;
      }
    });
    
    if (dividendEvents.length > 0) {
      console.log(`✅ 检测到自动分红事件: ${dividendEvents.length}个`);
      for (const event of dividendEvents) {
        const parsed = tokenA.interface.parseLog(event);
        console.log(`事件: ${parsed.name}`);
      }
    } else {
      console.log(`ℹ️  未检测到自动分红事件（可能Gas不足或其他原因）`);
    }
    
    // 检查交易后合约余额
    const finalContractBalance = await tokenA.balanceOf(await tokenA.getAddress());
    console.log(`\\n最终合约余额: ${ethers.formatEther(finalContractBalance)} TokenA`);
    
    // 验证合约余额门槛逻辑
    const expectedDividend = (bigTradeAmount * BigInt(3) * BigInt(30)) / (BigInt(100) * BigInt(100));
    console.log(`期望分红: ${ethers.formatEther(expectedDividend)} TokenA`);
    console.log(`门槛检查: ${finalContractBalance >= lowThreshold ? '✅ 超过门槛' : '❌ 未达门槛'}`);
  });
  
  it("应该验证用户可以正常领取分红", async function () {
    console.log("=== 验证用户分红领取 ===");
    
    // 给用户铸造NFT
    await minerNFT.batchMintCount(await user1.getAddress(), 2);
    await minerNFT.batchMintCount(await user2.getAddress(), 1);
    
    // 产生分红
    await tokenA.transfer(await user3.getAddress(), ethers.parseEther("10000"));
    await tokenA.setPair(await user3.getAddress(), true);
    await tokenA.connect(user3).transfer(await user1.getAddress(), ethers.parseEther("1000"));
    
    console.log("已产生分红");
    
    // 检查合约余额
    const contractBalance = await tokenA.balanceOf(await tokenA.getAddress());
    console.log(`合约分红余额: ${ethers.formatEther(contractBalance)} TokenA`);
    
    // 检查用户可领取分红
    const user1Dividend = await tokenA.calculateDividend(await user1.getAddress());
    const user2Dividend = await tokenA.calculateDividend(await user2.getAddress());
    
    console.log(`\\nuser1可领取: ${ethers.formatEther(user1Dividend)} TokenA`);
    console.log(`user2可领取: ${ethers.formatEther(user2Dividend)} TokenA`);
    
    // 验证分红比例 (user1有2个NFT，user2有1个NFT，应该是2:1)
    if (user1Dividend > 0 && user2Dividend > 0) {
      const ratio = Number(user1Dividend * BigInt(100) / user2Dividend) / 100;
      console.log(`分红比例 user1:user2 = ${ratio.toFixed(2)}:1`);
      expect(ratio).to.be.closeTo(2.0, 0.1, "分红比例应该是2:1");
      console.log(`✅ 分红比例正确`);
      
      // 执行领取
      const beforeUser1Balance = await tokenA.balanceOf(await user1.getAddress());
      await tokenA.connect(user1).claimDividend();
      const afterUser1Balance = await tokenA.balanceOf(await user1.getAddress());
      
      const actualReceived = afterUser1Balance - beforeUser1Balance;
      console.log(`\\nuser1实际领取: ${ethers.formatEther(actualReceived)} TokenA`);
      expect(actualReceived).to.equal(user1Dividend, "应该领取正确数量");
      console.log(`✅ 分红领取成功`);
      
      // 检查领取后合约余额
      const afterContractBalance = await tokenA.balanceOf(await tokenA.getAddress());
      console.log(`领取后合约余额: ${ethers.formatEther(afterContractBalance)} TokenA`);
      
      const expectedContractBalance = contractBalance - user1Dividend;
      expect(afterContractBalance).to.equal(expectedContractBalance, "合约余额应该正确减少");
      console.log(`✅ 合约余额更新正确`);
    } else {
      console.log(`ℹ️  当前无可领取分红`);
    }
  });
});