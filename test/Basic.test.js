const { expect } = require("chai");
const { ethers } = require("hardhat");

describe("基础功能测试", function () {
  let tokenA, tokenB, minerNFT, miningContract, priceOracle;
  let owner, marketingWallet, user1;

  beforeEach(async function () {
    [owner, marketingWallet, user1] = await ethers.getSigners();

    // 部署价格预言机
    const PriceOracle = await ethers.getContractFactory("PriceOracle");
    priceOracle = await PriceOracle.deploy();

    // 部署TokenA - 使用BSC测试网真实地址
    const TokenA = await ethers.getContractFactory("TokenA");
    tokenA = await TokenA.deploy(
      marketingWallet.address,
      "******************************************", // BSC测试网Router
      "******************************************"  // BSC测试网WBNB
    );

    // 部署TokenB - 使用BSC测试网真实地址
    const TokenB = await ethers.getContractFactory("TokenB");
    tokenB = await TokenB.deploy(
      marketingWallet.address,
      "******************************************", // BSC测试网Router
      "******************************************"  // BSC测试网WBNB
    );

    // 部署NFT合约
    const MinerNFT = await ethers.getContractFactory("MinerNFT");
    minerNFT = await MinerNFT.deploy("MinerNFT", "MNFT", await tokenA.getAddress());

    // 部署挖矿合约
    const MiningContract = await ethers.getContractFactory("MiningContract");
    miningContract = await MiningContract.deploy(
      await tokenA.getAddress(),
      await tokenB.getAddress(),
      await minerNFT.getAddress(),
      await priceOracle.getAddress()
    );

    // 基本配置
    await tokenA.setNftContract(await minerNFT.getAddress());
    await tokenB.addMinter(await miningContract.getAddress());
  });

  describe("TokenA", function () {
    it("应该正确设置初始参数", async function () {
      expect(await tokenA.name()).to.equal("TokenA");
      expect(await tokenA.symbol()).to.equal("TKA");
      expect(await tokenA.totalSupply()).to.equal(ethers.parseEther("250000000"));
    });

    it("应该能够转账", async function () {
      const amount = ethers.parseEther("1000");
      await tokenA.transfer(user1.address, amount);
      expect(await tokenA.balanceOf(user1.address)).to.equal(amount);
    });
  });

  describe("TokenB", function () {
    it("应该正确设置初始参数", async function () {
      expect(await tokenB.name()).to.equal("TokenB");
      expect(await tokenB.symbol()).to.equal("TKB");
      expect(await tokenB.totalSupply()).to.equal(ethers.parseEther("2100000000000"));
    });

    it("应该能够铸造代币", async function () {
      const amount = ethers.parseEther("1000");
      await tokenB.mint(user1.address, amount);
      expect(await tokenB.balanceOf(user1.address)).to.equal(amount);
    });
  });

  describe("MinerNFT", function () {
    it("应该能够铸造NFT", async function () {
      await minerNFT.mint(user1.address);
      expect(await minerNFT.balanceOf(user1.address)).to.equal(1);
      expect(await minerNFT.ownerOf(1)).to.equal(user1.address);
    });

    it("NFT应该有正确的代币数量", async function () {
      await minerNFT.mint(user1.address);
      const nftInfo = await minerNFT.nftInfo(1);
      expect(nftInfo.totalTokens).to.equal(ethers.parseEther("100000"));
    });
  });

  describe("PriceOracle", function () {
    it("应该能够设置手动价格", async function () {
      const price = ethers.parseEther("0.001");
      await priceOracle.setManualPrice(await tokenA.getAddress(), price);
      
      const retrievedPrice = await priceOracle.getTokenPrice(await tokenA.getAddress());
      expect(retrievedPrice).to.equal(price);
    });
  });

  describe("MiningContract", function () {
    it("应该正确设置合约地址", async function () {
      expect(await miningContract.tokenAAddress()).to.equal(await tokenA.getAddress());
      expect(await miningContract.tokenBAddress()).to.equal(await tokenB.getAddress());
      expect(await miningContract.nftContractAddress()).to.equal(await minerNFT.getAddress());
    });

    it("应该能够设置邀请关系", async function () {
      await miningContract.connect(user1).setInviter(owner.address);
      
      const inviteInfo = await miningContract.getUserInviteInfo(user1.address);
      expect(inviteInfo.inviter).to.equal(owner.address);
    });
  });
});