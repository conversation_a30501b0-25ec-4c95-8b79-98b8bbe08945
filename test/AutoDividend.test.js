const { expect } = require("chai");
const { ethers } = require("hardhat");

describe("自动分红功能测试", function () {
  let tokenA, minerNFT;
  let owner, marketingWallet, user1, user2, user3, pancakeSwapRouter, wbnb;

  beforeEach(async function () {
    [owner, marketingWallet, user1, user2, user3, pancakeSwapRouter, wbnb] = await ethers.getSigners();

    // 部署TokenA合约
    const TokenA = await ethers.getContractFactory("TokenA");
    tokenA = await TokenA.deploy(
      await marketingWallet.getAddress(),
      await pancakeSwapRouter.getAddress(),
      await wbnb.getAddress()
    );

    // 部署MinerNFT合约
    const MinerNFT = await ethers.getContractFactory("MinerNFT");
    minerNFT = await MinerNFT.deploy("MinerNFT", "MNFT", await tokenA.getAddress());

    // 设置NFT合约地址
    await tokenA.setNftContract(await minerNFT.getAddress());
    
    // 设置交易对
    await tokenA.setPair(await user2.getAddress(), true);
    
    // 分发一些代币给用户
    await tokenA.transfer(await user1.getAddress(), ethers.parseEther("10000"));
    await tokenA.transfer(await user3.getAddress(), ethers.parseEther("10000"));
    
    // 给用户铸造NFT
    await minerNFT.mint(await user1.getAddress());
    await minerNFT.mint(await user3.getAddress());
  });

  describe("转账时自动分红", function () {
    it("交易应该触发自动分红处理", async function () {
      // 获取初始自动分红状态
      const initialStatus = await tokenA.getAutoDividendStatus();
      expect(initialStatus.enabled).to.be.true;
      
      // 执行交易产生分红
      const tx = await tokenA.connect(user1).transfer(await user2.getAddress(), ethers.parseEther("3000"));
      const receipt = await tx.wait();
      
      // 检查是否有自动分红事件
      const autoDividendEvents = receipt.logs.filter(log => {
        try {
          return tokenA.interface.parseLog(log).name === "AutoDividendProcessed";
        } catch {
          return false;
        }
      });
      
      // 如果分红金额足够大，应该触发自动分红
      const dividendEvents = receipt.logs.filter(log => {
        try {
          return tokenA.interface.parseLog(log).name === "DividendDistributed";
        } catch {
          return false;
        }
      });
      
      expect(dividendEvents.length).to.be.gt(0);
    });

    it("应该正确配置自动分红参数", async function () {
      const newGasLimit = 600000;
      const newMaxNFTs = 15;
      const newThreshold = ethers.parseEther("0.002");
      
      await tokenA.setAutoDividendConfig(newGasLimit, newMaxNFTs, newThreshold, true);
      
      const status = await tokenA.getAutoDividendStatus();
      expect(status.gasLimit).to.equal(newGasLimit);
      expect(status.maxNFTs).to.equal(newMaxNFTs);
      expect(status.threshold).to.equal(newThreshold);
      expect(status.enabled).to.be.true;
    });

    it("自动分红应该能够处理多个NFT", async function () {
      // 铸造更多NFT
      await minerNFT.mint(await user1.getAddress());
      await minerNFT.mint(await user1.getAddress());
      await minerNFT.mint(await user3.getAddress());
      
      // 设置较小的处理数量
      await tokenA.setAutoDividendConfig(500000, 2, ethers.parseEther("0.001"), true);
      
      // 执行多笔交易
      await tokenA.connect(user1).transfer(await user2.getAddress(), ethers.parseEther("2000"));
      await tokenA.connect(user3).transfer(await user2.getAddress(), ethers.parseEther("2000"));
      
      // 检查处理索引是否更新
      const status = await tokenA.getAutoDividendStatus();
      expect(status.lastProcessedIndex).to.be.gte(0);
    });

    it("手动触发自动分红应该正常工作", async function () {
      // 先产生一些分红
      await tokenA.connect(user1).transfer(await user2.getAddress(), ethers.parseEther("1000"));
      
      // 手动触发自动分红
      const tx = await tokenA.processAutoDividend();
      const receipt = await tx.wait();
      
      // 检查是否有处理事件
      const autoDividendEvents = receipt.logs.filter(log => {
        try {
          const parsed = tokenA.interface.parseLog(log);
          return parsed.name === "AutoDividendProcessed" || parsed.name === "DividendClaimed";
        } catch {
          return false;
        }
      });
      
      // 应该有一些分红处理活动
      expect(autoDividendEvents.length).to.be.gte(0);
    });

    it("自动分红可以被禁用", async function () {
      // 禁用自动分红
      await tokenA.setAutoDividendConfig(500000, 10, ethers.parseEther("0.001"), false);
      
      const status = await tokenA.getAutoDividendStatus();
      expect(status.enabled).to.be.false;
      
      // 执行交易，应该不会触发自动分红
      await tokenA.connect(user1).transfer(await user2.getAddress(), ethers.parseEther("2000"));
      
      // 检查累积分红是否更新了但没有自动处理
      const cumulativeDividend = await tokenA.cumulativeDividendPerShare();
      expect(cumulativeDividend).to.be.gt(0);
    });
  });

  describe("NFT转移时自动分红领取", function () {
    beforeEach(async function () {
      // 产生一些分红
      await tokenA.connect(user1).transfer(await user2.getAddress(), ethers.parseEther("2000"));
    });

    it("NFT转移应该自动领取原持有者的分红", async function () {
      const tokenId = 1;
      
      // 检查转移前用户1的余额
      const balanceBefore = await tokenA.balanceOf(await user1.getAddress());
      
      // 转移NFT给user2
      await minerNFT.connect(user1).transferFrom(
        await user1.getAddress(), 
        await user2.getAddress(), 
        tokenId
      );
      
      // 检查转移后用户1的余额（应该包含自动领取的分红）
      const balanceAfter = await tokenA.balanceOf(await user1.getAddress());
      
      // 如果有分红，余额应该增加
      if (balanceAfter > balanceBefore) {
        console.log(`自动分红领取：${ethers.formatEther(balanceAfter - balanceBefore)} TKA`);
      }
      
      // 检查NFT现在属于user2
      expect(await minerNFT.ownerOf(tokenId)).to.equal(await user2.getAddress());
    });

    it("用户应该能够手动领取NFT分红", async function () {
      const tokenId = 1;
      
      // 检查可领取的分红
      const claimableDividend = await tokenA.calculateNFTDividend(tokenId);
      
      if (claimableDividend > 0) {
        const balanceBefore = await tokenA.balanceOf(await user1.getAddress());
        
        // 通过NFT合约手动领取分红
        await minerNFT.connect(user1).claimNFTDividend(tokenId);
        
        const balanceAfter = await tokenA.balanceOf(await user1.getAddress());
        expect(balanceAfter - balanceBefore).to.equal(claimableDividend);
      }
    });

    it("用户应该能够批量领取多个NFT的分红", async function () {
      // 给user1更多NFT
      await minerNFT.mint(await user1.getAddress());
      await minerNFT.mint(await user1.getAddress());
      
      // 产生更多分红
      await tokenA.connect(user3).transfer(await user2.getAddress(), ethers.parseEther("1000"));
      
      const tokenIds = [1, 3, 4]; // user1拥有的NFT
      const balanceBefore = await tokenA.balanceOf(await user1.getAddress());
      
      // 批量领取分红
      await minerNFT.connect(user1).batchClaimNFTDividends(tokenIds);
      
      const balanceAfter = await tokenA.balanceOf(await user1.getAddress());
      
      // 应该收到一些分红
      console.log(`批量分红领取：${ethers.formatEther(balanceAfter - balanceBefore)} TKA`);
    });

    it("非NFT持有者不应该能够领取分红", async function () {
      const tokenId = 1; // 属于user1的NFT
      
      await expect(
        minerNFT.connect(user2).claimNFTDividend(tokenId)
      ).to.be.revertedWith("Not the owner of this NFT");
    });
  });

  describe("Gas优化测试", function () {
    it("自动分红应该在Gas限制内运行", async function () {
      // 设置较低的Gas限制
      await tokenA.setAutoDividendConfig(200000, 5, ethers.parseEther("0.001"), true);
      
      // 铸造多个NFT来测试Gas限制
      for (let i = 0; i < 10; i++) {
        await minerNFT.mint(await user3.getAddress());
      }
      
      // 执行交易触发自动分红
      const tx = await tokenA.connect(user1).transfer(await user2.getAddress(), ethers.parseEther("2000"));
      const receipt = await tx.wait();
      
      // 交易应该成功完成，不会因为Gas不足而失败
      expect(receipt.status).to.equal(1);
    });

    it("应该能够循环处理所有NFT", async function () {
      // 铸造一些NFT
      for (let i = 0; i < 15; i++) {
        await minerNFT.mint(await user3.getAddress());
      }
      
      // 设置每次处理5个NFT
      await tokenA.setAutoDividendConfig(500000, 5, ethers.parseEther("0.001"), true);
      
      // 执行多次交易，应该能处理所有NFT
      for (let i = 0; i < 5; i++) {
        await tokenA.connect(user1).transfer(await user2.getAddress(), ethers.parseEther("1000"));
      }
      
      const status = await tokenA.getAutoDividendStatus();
      console.log(`处理进度：${status.lastProcessedIndex}/${status.totalNFTs}`);
    });
  });
});