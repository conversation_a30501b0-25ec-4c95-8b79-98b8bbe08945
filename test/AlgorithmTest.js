const { expect } = require("chai");

describe("线性释放算法纯数学验证", function () {
    
    const TOTAL_RELEASE_DURATION = 180 * 24 * 3600; // 180天的秒数
    
    // 核心算法实现 - 与合约中的算法完全一致
    function calculateReleasableAmount(totalReleaseValue, releasedValue, startTime, currentTime) {
        // 如果记录已完全释放，返回0
        if (releasedValue >= totalReleaseValue) {
            return 0n;
        }
        
        // 计算从开始到现在的总时间
        let totalTimeElapsed = BigInt(currentTime - startTime);
        
        // 如果超过180天，限制在180天
        const maxDuration = BigInt(TOTAL_RELEASE_DURATION);
        if (totalTimeElapsed > maxDuration) {
            totalTimeElapsed = maxDuration;
        }
        
        // 计算到目前为止总共应该释放的价值
        const totalShouldHaveReleased = (totalReleaseValue * totalTimeElapsed) / maxDuration;
        
        // 可领取的金额 = 应该释放的总额 - 已释放的金额
        if (totalShouldHaveReleased > releasedValue) {
            return totalShouldHaveReleased - releasedValue;
        }
        
        return 0n;
    }

    describe("算法正确性验证", function () {
        
        it("✅ 应该在开始时可领取金额为0", function () {
            const startTime = 1000000;
            const currentTime = 1000000; // 相同时间
            const totalValue = 1000n * (10n ** 18n); // 1000 ETH
            const alreadyClaimed = 0n;
            
            const claimable = calculateReleasableAmount(
                totalValue, 
                alreadyClaimed, 
                startTime, 
                currentTime
            );
            
            expect(claimable).to.equal(0n);
        });

        it("✅ 应该在一半时间后可领取一半金额", function () {
            const startTime = 1000000;
            const currentTime = startTime + TOTAL_RELEASE_DURATION / 2; // 90天后
            const totalValue = 1000n * (10n ** 18n); // 1000 ETH
            const alreadyClaimed = 0n;
            
            const claimable = calculateReleasableAmount(
                totalValue, 
                alreadyClaimed, 
                startTime, 
                currentTime
            );
            
            const expectedHalf = totalValue / 2n;
            expect(claimable).to.equal(expectedHalf);
            
            console.log(`    ℹ️  90天后可领取: ${claimable / (10n ** 18n)} ETH`);
        });

        it("✅ 应该在180天后可领取全部金额", function () {
            const startTime = 1000000;
            const currentTime = startTime + TOTAL_RELEASE_DURATION; // 180天后
            const totalValue = 1000n * (10n ** 18n); // 1000 ETH
            const alreadyClaimed = 0n;
            
            const claimable = calculateReleasableAmount(
                totalValue, 
                alreadyClaimed, 
                startTime, 
                currentTime
            );
            
            expect(claimable).to.equal(totalValue);
            
            console.log(`    ℹ️  180天后可领取: ${claimable / (10n ** 18n)} ETH`);
        });

        it("✅ 分批领取应该与一次性领取结果一致", function () {
            const startTime = 1000000;
            const totalValue = 1000n * (10n ** 18n); // 1000 ETH
            
            // 模拟分批领取
            let totalClaimed = 0n;
            
            // 第30天领取
            const time30 = startTime + 30 * 24 * 3600;
            const claimable30 = calculateReleasableAmount(
                totalValue, 
                totalClaimed, 
                startTime, 
                time30
            );
            totalClaimed += claimable30;
            
            // 第60天再次领取
            const time60 = startTime + 60 * 24 * 3600;
            const claimable60 = calculateReleasableAmount(
                totalValue, 
                totalClaimed, 
                startTime, 
                time60
            );
            totalClaimed += claimable60;
            
            // 第90天再次领取
            const time90 = startTime + 90 * 24 * 3600;
            const claimable90 = calculateReleasableAmount(
                totalValue, 
                totalClaimed, 
                startTime, 
                time90
            );
            totalClaimed += claimable90;
            
            // 验证90天时分批领取的总量与一次性计算的结果一致
            const directClaimable90 = calculateReleasableAmount(
                totalValue, 
                0n, 
                startTime, 
                time90
            );
            
            expect(totalClaimed).to.equal(directClaimable90);
            
            console.log(`    ℹ️  分批领取90天总计: ${totalClaimed / (10n ** 18n)} ETH`);
            console.log(`    ℹ️  一次性计算90天: ${directClaimable90 / (10n ** 18n)} ETH`);
        });

        it("✅ 不应该允许领取超过总价值", function () {
            const startTime = 1000000;
            const currentTime = startTime + TOTAL_RELEASE_DURATION * 2; // 360天后
            const totalValue = 1000n * (10n ** 18n); // 1000 ETH
            const alreadyClaimed = 0n;
            
            const claimable = calculateReleasableAmount(
                totalValue, 
                alreadyClaimed, 
                startTime, 
                currentTime
            );
            
            expect(claimable).to.equal(totalValue);
            
            console.log(`    ℹ️  360天后仍然只能领取: ${claimable / (10n ** 18n)} ETH`);
        });

        it("✅ 已经领取完全部金额后，再次查询应该返回0", function () {
            const startTime = 1000000;
            const currentTime = startTime + TOTAL_RELEASE_DURATION; // 180天后
            const totalValue = 1000n * (10n ** 18n); // 1000 ETH
            const alreadyClaimed = totalValue; // 已经领取了全部
            
            const claimable = calculateReleasableAmount(
                totalValue, 
                alreadyClaimed, 
                startTime, 
                currentTime
            );
            
            expect(claimable).to.equal(0n);
        });

        it("✅ 测试每日释放率的一致性", function () {
            const startTime = 1000000;
            const totalValue = 1800n * (10n ** 18n); // 1800 ETH，方便计算
            const oneDayInSeconds = 24 * 3600;
            
            console.log(`    ℹ️  测试每日释放率（总价值1800 ETH）:`);
            
            // 计算每天的释放量
            for (let day = 1; day <= 10; day++) {
                const currentTime = startTime + day * oneDayInSeconds;
                const claimable = calculateReleasableAmount(
                    totalValue, 
                    0n, 
                    startTime, 
                    currentTime
                );
                
                const expectedDaily = (totalValue / 180n) * BigInt(day); // 每天应释放 1800/180 = 10 ETH
                expect(claimable).to.equal(expectedDaily);
                
                console.log(`       第${day}天: 可领取 ${claimable / (10n ** 18n)} ETH`);
            }
        });

        it("✅ 测试精确的数学计算", function () {
            const startTime = 1000000;
            const totalValue = 1000n * (10n ** 18n);
            
            console.log(`    ℹ️  测试关键时间点的精确计算:`);
            
            // 测试各种时间点的精确计算
            const testCases = [
                { days: 1, expectedPercent: 1/180 },
                { days: 30, expectedPercent: 30/180 },
                { days: 90, expectedPercent: 90/180 },
                { days: 180, expectedPercent: 180/180 },
            ];
            
            testCases.forEach(({ days, expectedPercent }) => {
                const currentTime = startTime + days * 24 * 3600;
                const claimable = calculateReleasableAmount(
                    totalValue, 
                    0n, 
                    startTime, 
                    currentTime
                );
                
                const expectedAmount = totalValue * BigInt(Math.floor(expectedPercent * 1000)) / 1000n;
                
                // 允许0.1%的误差（由于整数除法精度）
                const tolerance = totalValue / 1000n;
                const diff = claimable > expectedAmount ? claimable - expectedAmount : expectedAmount - claimable;
                expect(diff).to.be.at.most(tolerance);
                
                console.log(`       第${days}天: 实际 ${claimable / (10n ** 18n)} ETH (${(Number(claimable * 100n / totalValue))}%)`);
            });
        });
    });

    describe("🛡️  安全性和边界条件测试", function () {
        
        it("✅ 处理时间为0的情况", function () {
            const startTime = 1000000;
            const currentTime = startTime; // 相同时间
            const totalValue = 1000n * (10n ** 18n);
            
            const claimable = calculateReleasableAmount(
                totalValue, 
                0n, 
                startTime, 
                currentTime
            );
            
            expect(claimable).to.equal(0n);
        });

        it("✅ 处理很大的时间值", function () {
            const startTime = 1000000;
            const currentTime = startTime + TOTAL_RELEASE_DURATION * 10; // 1800天后
            const totalValue = 1000n * (10n ** 18n);
            
            const claimable = calculateReleasableAmount(
                totalValue, 
                0n, 
                startTime, 
                currentTime
            );
            
            // 即使时间很长，最多也只能领取总价值
            expect(claimable).to.equal(totalValue);
        });

        it("✅ 处理已领取金额大于总价值的异常情况", function () {
            const startTime = 1000000;
            const currentTime = startTime + TOTAL_RELEASE_DURATION;
            const totalValue = 1000n * (10n ** 18n);
            const alreadyClaimed = 1500n * (10n ** 18n); // 异常：超过总价值
            
            const claimable = calculateReleasableAmount(
                totalValue, 
                alreadyClaimed, 
                startTime, 
                currentTime
            );
            
            expect(claimable).to.equal(0n);
        });
    });

    describe("📊 算法对比测试", function () {
        
        it("🆚 新算法vs旧算法：用户体验对比", function () {
            console.log(`\\n    📈 算法对比分析`);
            console.log(`    总锁定价值: 1000 ETH`);
            console.log(`    释放周期: 180天`);
            console.log(`    \\n    新算法（实时线性释放）vs 旧算法（6期固定释放）：`);
            
            const totalValue = 1000n * (10n ** 18n);
            const startTime = 1000000;
            
            // 比较不同时间点的可领取金额
            const timePoints = [1, 7, 15, 30, 45, 60, 90, 120, 150, 180];
            
            timePoints.forEach(days => {
                const currentTime = startTime + days * 24 * 3600;
                
                // 新算法
                const newAlgoClaimable = calculateReleasableAmount(
                    totalValue,
                    0n,
                    startTime,
                    currentTime
                );
                
                // 旧算法（6期制，每30天一期）
                const period = Math.floor(days / 30);
                const oldAlgoClaimable = (totalValue / 6n) * BigInt(period);
                
                const newEth = Number(newAlgoClaimable / (10n ** 18n));
                const oldEth = Number(oldAlgoClaimable / (10n ** 18n));
                const diffEth = newEth - oldEth;
                
                console.log(`    第${days.toString().padStart(3)}天: 新算法 ${newEth.toFixed(1)}ETH | 旧算法 ${oldEth.toFixed(1)}ETH | 差异 ${diffEth >= 0 ? '+' : ''}${diffEth.toFixed(1)}ETH`);
            });
            
            console.log(`\\n    💡 结论: 新算法提供真正的实时产出，用户体验大幅提升！`);
        });
    });
});