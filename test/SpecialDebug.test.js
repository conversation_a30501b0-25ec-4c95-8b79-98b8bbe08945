const { expect } = require("chai");
const { ethers } = require("hardhat");
const { time } = require("@nomicfoundation/hardhat-network-helpers");

describe("专门调试1,3,5期问题", function () {
  let tokenA, tokenB, minerNFT, miningContract, priceOracle;
  let mockWBNB, mockFactory, mockRouter;
  let owner, user1;
  
  const RELEASE_INTERVAL = 30 * 24 * 3600; // 30天
  
  beforeEach(async function () {
    const signers = await ethers.getSigners();
    [owner, user1] = signers.slice(0, 2);
    
    console.log(`=== 专门调试1,3,5期问题 ===`);
    
    // 部署模拟PancakeSwap环境
    const MockWBNB = await ethers.getContractFactory("MockWBNB");
    mockWBNB = await MockWBNB.deploy();
    
    const MockPancakeFactory = await ethers.getContractFactory("MockPancakeFactory");
    mockFactory = await MockPancakeFactory.deploy();
    
    const MockPancakeRouter = await ethers.getContractFactory("MockPancakeRouter");
    mockRouter = await MockPancakeRouter.deploy(
      await mockFactory.getAddress(),
      await mockWBNB.getAddress()
    );
    
    // 部署TestSimplePriceOracle
    const TestSimplePriceOracle = await ethers.getContractFactory("TestSimplePriceOracle");
    priceOracle = await TestSimplePriceOracle.deploy(
      await mockWBNB.getAddress(),
      await mockFactory.getAddress()
    );
    
    const TokenA = await ethers.getContractFactory("TokenA");
    tokenA = await TokenA.deploy(
      await owner.getAddress(),
      await mockRouter.getAddress(),
      await mockWBNB.getAddress()
    );
    
    const TokenB = await ethers.getContractFactory("TokenB");
    tokenB = await TokenB.deploy(
      await owner.getAddress(),
      await mockRouter.getAddress(),
      await mockWBNB.getAddress()
    );
    
    const MinerNFT = await ethers.getContractFactory("MinerNFT");
    minerNFT = await MinerNFT.deploy("MinerNFT", "MNFT", await tokenA.getAddress());
    
    const MiningContract = await ethers.getContractFactory("MiningContract");
    miningContract = await MiningContract.deploy(
      await tokenA.getAddress(),
      await tokenB.getAddress(),
      await minerNFT.getAddress(),
      await priceOracle.getAddress()
    );
    
    // 基本设置
    await tokenA.setTaxExempt(await miningContract.getAddress(), true);
    await tokenB.setTaxExempt(await miningContract.getAddress(), true);
    await tokenB.transfer(await miningContract.getAddress(), ethers.parseEther("10000000"));
    
    // 设置交易对 - 使用和详细测试相同的设置
    const liquidityTokenA = ethers.parseEther("50000");
    const liquidityWBNB = ethers.parseEther("50");
    
    await mockWBNB.deposit({ value: liquidityWBNB });
    await tokenA.approve(await mockRouter.getAddress(), liquidityTokenA);
    await mockWBNB.approve(await mockRouter.getAddress(), liquidityWBNB);
    
    await mockRouter.addLiquidity(
      await tokenA.getAddress(),
      await mockWBNB.getAddress(),
      liquidityTokenA,
      liquidityWBNB,
      0, 0,
      await owner.getAddress(),
      Math.floor(Date.now() / 1000) + 600
    );
    
    const liquidityTokenB = ethers.parseEther("500000");
    const liquidityWBNB2 = ethers.parseEther("50");
    
    await mockWBNB.deposit({ value: liquidityWBNB2 });
    await tokenB.approve(await mockRouter.getAddress(), liquidityTokenB);
    await mockWBNB.approve(await mockRouter.getAddress(), liquidityWBNB2);
    
    await mockRouter.addLiquidity(
      await tokenB.getAddress(),
      await mockWBNB.getAddress(),
      liquidityTokenB,
      liquidityWBNB2,
      0, 0,
      await owner.getAddress(),
      Math.floor(Date.now() / 1000) + 600
    );
    
    // 设置价格
    await priceOracle.configureToken(await tokenA.getAddress());
    await priceOracle.configureToken(await tokenB.getAddress());
    await priceOracle.recordPrice(await tokenA.getAddress());
    await priceOracle.recordPrice(await tokenB.getAddress());
    
    // 给用户TokenA
    await tokenA.transfer(await user1.getAddress(), ethers.parseEther("20000"));
    await tokenA.connect(user1).approve(await miningContract.getAddress(), ethers.parseEther("20000"));
  });
  
  it("应该检查每期释放的具体计算过程", async function () {
    console.log("=== 检查每期释放的具体计算 ===");
    
    // 记录挖矿开始时间
    const startTime = await time.latest();
    console.log(`挖矿开始时间: ${startTime}`);
    
    // 用户挖矿
    const burnAmount = ethers.parseEther("10000");
    await miningContract.connect(user1).burnTokenAForTokenB(burnAmount, ethers.ZeroAddress);
    
    // 获取挖矿记录
    const record = await miningContract.getUserMiningRecord1Detail(await user1.getAddress(), 0);
    console.log(`总释放价值: ${ethers.formatEther(record.totalReleaseValue)} BNB`);
    console.log(`记录中的lastReleaseTime: ${record.lastReleaseTime}`);
    console.log(`记录开始时间和当前时间差: ${Number(record.lastReleaseTime) - startTime} 秒`);
    
    // 测试每一期
    for (let period = 1; period <= 6; period++) {
      console.log(`\n--- 第${period}期详细计算 ---`);
      
      // 推进时间
      const targetTime = startTime + period * RELEASE_INTERVAL;
      await time.increaseTo(targetTime);
      
      const currentTime = await time.latest();
      console.log(`目标时间: ${targetTime}`);
      console.log(`实际时间: ${currentTime}`);
      
      // 重新获取最新的记录状态
      const currentRecord = await miningContract.getUserMiningRecord1Detail(await user1.getAddress(), 0);
      console.log(`当前已释放: ${ethers.formatEther(currentRecord.releasedValue)} BNB`);
      
      // 手动计算预期值
      const timeSinceStart = currentTime - Number(currentRecord.lastReleaseTime);
      const periodsElapsed = Math.floor(timeSinceStart / RELEASE_INTERVAL);
      const totalValue = BigInt(currentRecord.totalReleaseValue);
      const shouldHaveReleased = (totalValue * BigInt(periodsElapsed)) / BigInt(6);
      const alreadyReleased = BigInt(currentRecord.releasedValue);
      const expectedClaimable = shouldHaveReleased > alreadyReleased ? shouldHaveReleased - alreadyReleased : BigInt(0);
      
      console.log(`时间差: ${timeSinceStart} 秒`);
      console.log(`计算期数: ${periodsElapsed}`);
      console.log(`应该累积释放: ${ethers.formatEther(shouldHaveReleased)} BNB`);
      console.log(`已经释放: ${ethers.formatEther(alreadyReleased)} BNB`);
      console.log(`预期可领取: ${ethers.formatEther(expectedClaimable)} BNB`);
      
      // 检查合约实际计算的可领取数量
      const actualClaimable = await miningContract.getUserClaimableAmount1(await user1.getAddress());
      console.log(`合约实际可领取: ${ethers.formatEther(actualClaimable)} BNB`);
      
      console.log(`计算是否匹配: ${expectedClaimable === actualClaimable ? '✅ 匹配' : '❌ 不匹配'}`);
      
      // 如果有可领取的，就领取
      if (actualClaimable > 0) {
        console.log(`✅ 第${period}期有可领取数量，正在领取...`);
        await miningContract.connect(user1).claimTokenBFromMechanism1();
        
        const updatedRecord = await miningContract.getUserMiningRecord1Detail(await user1.getAddress(), 0);
        console.log(`领取后已释放: ${ethers.formatEther(updatedRecord.releasedValue)} BNB`);
      } else {
        console.log(`❌ 第${period}期无可领取数量`);
      }
    }
    
    console.log("\n=== 最终验证 ===");
    const finalRecord = await miningContract.getUserMiningRecord1Detail(await user1.getAddress(), 0);
    console.log(`最终释放: ${ethers.formatEther(finalRecord.releasedValue)} BNB`);
    console.log(`总价值: ${ethers.formatEther(finalRecord.totalReleaseValue)} BNB`);
    console.log(`释放进度: ${Number(finalRecord.releasedValue * BigInt(100) / finalRecord.totalReleaseValue)}%`);
  });
});