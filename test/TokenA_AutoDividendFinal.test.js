const { expect } = require("chai");
const { ethers } = require("hardhat");

describe("TokenA自动分红修复最终验证", function () {
  let tokenA, minerNFT, mockWBNB, mockRouter;
  let owner, user1, user2, user3, marketingWallet;
  
  beforeEach(async function () {
    const signers = await ethers.getSigners();
    [owner, user1, user2, user3, marketingWallet] = signers.slice(0, 5);
    
    console.log(`=== TokenA自动分红修复最终验证 ===`);
    
    // 部署Mock合约
    const MockWBNB = await ethers.getContractFactory("MockWBNB");
    mockWBNB = await MockWBNB.deploy();
    
    const MockPancakeFactory = await ethers.getContractFactory("MockPancakeFactory");
    const mockFactory = await MockPancakeFactory.deploy();
    
    const MockPancakeRouter = await ethers.getContractFactory("MockPancakeRouter");
    mockRouter = await MockPancakeRouter.deploy(
      await mockFactory.getAddress(),
      await mockWBNB.getAddress()
    );
    
    // 部署TokenA
    const TokenA = await ethers.getContractFactory("TokenA");
    tokenA = await TokenA.deploy(
      await marketingWallet.getAddress(),
      await mockRouter.getAddress(),
      await mockWBNB.getAddress()
    );
    
    // 部署MinerNFT
    const MinerNFT = await ethers.getContractFactory("MinerNFT");
    minerNFT = await MinerNFT.deploy("MinerNFT", "MNFT", await tokenA.getAddress());
    
    // 设置NFT合约地址
    await tokenA.setNftContract(await minerNFT.getAddress());
    
    console.log(`TokenA合约: ${await tokenA.getAddress()}`);
    console.log(`MinerNFT合约: ${await minerNFT.getAddress()}`);
  });
  
  it("应该验证修复后的自动分红完整流程", async function () {
    console.log("=== 验证修复后的自动分红完整流程 ===");
    
    // 给用户铸造NFT
    await minerNFT.batchMintCount(await user1.getAddress(), 2);
    await minerNFT.batchMintCount(await user2.getAddress(), 1);
    console.log("user1: 2个NFT, user2: 1个NFT");
    
    // 设置较高的自动分红门槛，防止立即触发
    const highThreshold = ethers.parseEther("100"); // 100 TokenA
    await tokenA.setAutoDividendConfig(
      500000, // gasLimit
      10,     // maxNFTs
      highThreshold, // threshold
      true    // enabled
    );
    
    console.log(`设置高门槛: ${ethers.formatEther(highThreshold)} TokenA`);
    
    // 准备交易
    await tokenA.transfer(await user3.getAddress(), ethers.parseEther("20000"));
    await tokenA.setPair(await user3.getAddress(), true);
    
    console.log("\\n=== 第一阶段：分红累积验证 ===");
    
    // 执行小额交易，不触发自动分红
    const smallTrade = ethers.parseEther("500"); // 产生4.5 TokenA分红，低于100门槛
    await tokenA.connect(user3).transfer(await user1.getAddress(), smallTrade);
    
    const contractBalance1 = await tokenA.balanceOf(await tokenA.getAddress());
    const contractInfo1 = await tokenA.contractBalance();
    
    console.log(`小交易后合约余额: ${ethers.formatEther(contractBalance1)} TokenA`);
    console.log(`累计分红统计: ${ethers.formatEther(contractInfo1.totalDivs)} TokenA`);
    
    // 验证分红正确累积
    const expectedDividend1 = (smallTrade * BigInt(3) * BigInt(30)) / (BigInt(100) * BigInt(100));
    expect(contractBalance1).to.equal(expectedDividend1, "分红应该累积在合约中");
    expect(contractInfo1.totalDivs).to.equal(expectedDividend1, "分红统计应该正确");
    console.log(`✅ 分红累积验证通过: ${ethers.formatEther(expectedDividend1)} TokenA`);
    
    console.log("\\n=== 第二阶段：累积多笔分红 ===");
    
    // 再执行几笔小交易累积分红
    for (let i = 0; i < 3; i++) {
      await tokenA.connect(user3).transfer(await user1.getAddress(), ethers.parseEther("800"));
      console.log(`第${i+2}笔交易完成`);
    }
    
    const contractBalance2 = await tokenA.balanceOf(await tokenA.getAddress());
    const contractInfo2 = await tokenA.contractBalance();
    
    console.log(`累积后合约余额: ${ethers.formatEther(contractBalance2)} TokenA`);
    console.log(`累积分红统计: ${ethers.formatEther(contractInfo2.totalDivs)} TokenA`);
    
    // 验证多笔分红累积
    const totalTrades = smallTrade + ethers.parseEther("800") * BigInt(3);
    const expectedTotalDividend = (totalTrades * BigInt(3) * BigInt(30)) / (BigInt(100) * BigInt(100));
    expect(contractBalance2).to.equal(expectedTotalDividend, "多笔分红应该累积");
    console.log(`✅ 多笔分红累积验证通过: ${ethers.formatEther(expectedTotalDividend)} TokenA`);
    
    console.log("\\n=== 第三阶段：降低门槛触发自动分红 ===");
    
    // 降低门槛到当前累积金额以下
    const currentBalance = await tokenA.balanceOf(await tokenA.getAddress());
    const lowThreshold = currentBalance - ethers.parseEther("1"); // 比当前余额低1 TokenA
    
    await tokenA.setAutoDividendConfig(
      500000, // gasLimit
      10,     // maxNFTs
      lowThreshold, // threshold
      true    // enabled
    );
    
    console.log(`设置低门槛: ${ethers.formatEther(lowThreshold)} TokenA`);
    console.log(`当前合约余额: ${ethers.formatEther(currentBalance)} TokenA`);
    
    // 记录用户分红前余额
    const beforeUser1Balance = await tokenA.balanceOf(await user1.getAddress());
    const beforeUser2Balance = await tokenA.balanceOf(await user2.getAddress());
    
    // 执行一笔小交易触发自动分红
    console.log("\\n执行触发交易...");
    const triggerTx = await tokenA.connect(user3).transfer(await user1.getAddress(), ethers.parseEther("100"));
    const triggerReceipt = await triggerTx.wait();
    
    // 检查自动分红结果
    const afterUser1Balance = await tokenA.balanceOf(await user1.getAddress());
    const afterUser2Balance = await tokenA.balanceOf(await user2.getAddress());
    const afterContractBalance = await tokenA.balanceOf(await tokenA.getAddress());
    
    console.log("\\n=== 自动分红结果 ===");
    console.log(`user1余额变化: ${ethers.formatEther(afterUser1Balance - beforeUser1Balance)} TokenA`);
    console.log(`user2余额变化: ${ethers.formatEther(afterUser2Balance - beforeUser2Balance)} TokenA`);
    console.log(`合约余额变化: ${ethers.formatEther(afterContractBalance - currentBalance)} TokenA`);
    
    // 检查自动分红事件
    const autoEvents = triggerReceipt.logs.filter(log => {
      try {
        const parsed = tokenA.interface.parseLog(log);
        return parsed.name === 'AutoDividendProcessed' || parsed.name === 'DividendClaimed';
      } catch {
        return false;
      }
    });
    
    console.log(`\\n检测到自动分红事件: ${autoEvents.length}个`);
    if (autoEvents.length > 0) {
      console.log("✅ 自动分红成功触发");
      
      // 验证分红比例 (user1有2个NFT，user2有1个NFT，应该是2:1)
      const user1Dividend = afterUser1Balance - beforeUser1Balance - ethers.parseEther("97"); // 减去交易收到的97 TokenA
      const user2Dividend = afterUser2Balance - beforeUser2Balance;
      
      if (user1Dividend > 0 && user2Dividend > 0) {
        const ratio = Number(user1Dividend * BigInt(100) / user2Dividend) / 100;
        console.log(`分红比例 user1:user2 = ${ratio.toFixed(2)}:1`);
        expect(ratio).to.be.closeTo(2.0, 0.1, "分红比例应该是2:1");
        console.log("✅ 分红比例正确");
      }
    } else {
      console.log("ℹ️  未触发自动分红");
    }
    
    console.log("\\n=== 第四阶段：验证手动分红仍可用 ===");
    
    // 产生更多分红
    await tokenA.connect(user3).transfer(await user1.getAddress(), ethers.parseEther("1000"));
    
    // 检查可领取分红
    const user1ManualDividend = await tokenA.calculateDividend(await user1.getAddress());
    const user2ManualDividend = await tokenA.calculateDividend(await user2.getAddress());
    
    console.log(`user1手动可领取: ${ethers.formatEther(user1ManualDividend)} TokenA`);
    console.log(`user2手动可领取: ${ethers.formatEther(user2ManualDividend)} TokenA`);
    
    if (user1ManualDividend > 0) {
      const manualBeforeBalance = await tokenA.balanceOf(await user1.getAddress());
      await tokenA.connect(user1).claimDividend();
      const manualAfterBalance = await tokenA.balanceOf(await user1.getAddress());
      const manualReceived = manualAfterBalance - manualBeforeBalance;
      
      console.log(`手动领取成功: ${ethers.formatEther(manualReceived)} TokenA`);
      expect(manualReceived).to.equal(user1ManualDividend, "手动领取应该正确");
      console.log("✅ 手动分红功能正常");
    }
  });
  
  it("应该验证门槛设置和自动分红控制", async function () {
    console.log("=== 验证门槛设置和自动分红控制 ===");
    
    // 给用户铸造NFT
    await minerNFT.batchMintCount(await user1.getAddress(), 1);
    
    // 准备交易
    await tokenA.transfer(await user3.getAddress(), ethers.parseEther("10000"));
    await tokenA.setPair(await user3.getAddress(), true);
    
    console.log("\\n=== 测试不同门槛设置 ===");
    
    // 测试1：极高门槛，不应触发自动分红
    await tokenA.setAutoDividendConfig(
      500000, // gasLimit
      10,     // maxNFTs  
      ethers.parseEther("10000"), // 10000 TokenA门槛
      true    // enabled
    );
    
    await tokenA.connect(user3).transfer(await user1.getAddress(), ethers.parseEther("1000"));
    const highThresholdBalance = await tokenA.balanceOf(await tokenA.getAddress());
    console.log(`高门槛后合约余额: ${ethers.formatEther(highThresholdBalance)} TokenA`);
    expect(highThresholdBalance).to.be.gt(0, "高门槛时分红应该累积");
    console.log("✅ 高门槛阻止自动分红正常");
    
    // 测试2：关闭自动分红
    await tokenA.setAutoDividendConfig(
      500000, // gasLimit
      10,     // maxNFTs
      ethers.parseEther("1"), // 低门槛
      false   // disabled
    );
    
    const beforeDisabledBalance = await tokenA.balanceOf(await tokenA.getAddress());
    await tokenA.connect(user3).transfer(await user1.getAddress(), ethers.parseEther("1000"));
    const afterDisabledBalance = await tokenA.balanceOf(await tokenA.getAddress());
    
    const expectedNewDividend = (ethers.parseEther("1000") * BigInt(3) * BigInt(30)) / (BigInt(100) * BigInt(100));
    const expectedTotalBalance = beforeDisabledBalance + expectedNewDividend;
    
    console.log(`关闭前合约余额: ${ethers.formatEther(beforeDisabledBalance)} TokenA`);
    console.log(`关闭后合约余额: ${ethers.formatEther(afterDisabledBalance)} TokenA`);
    console.log(`期望累积余额: ${ethers.formatEther(expectedTotalBalance)} TokenA`);
    
    expect(afterDisabledBalance).to.equal(expectedTotalBalance, "关闭自动分红时应该累积");
    console.log("✅ 关闭自动分红功能正常");
    
    console.log("\\n=== 验证配置状态查询 ===");
    const status = await tokenA.getAutoDividendStatus();
    console.log(`启用状态: ${status.enabled}`);
    console.log(`门槛设置: ${ethers.formatEther(status.threshold)} TokenA`);
    console.log(`Gas限制: ${status.gasLimit}`);
    console.log(`最大NFT数: ${status.maxNFTs}`);
    console.log(`总NFT数: ${status.totalNFTs}`);
    
    expect(status.enabled).to.be.false;
    expect(status.threshold).to.equal(ethers.parseEther("1"));
    console.log("✅ 配置状态查询正确");
  });
});