const { expect } = require("chai");
const { ethers } = require("hardhat");

describe("核心业务逻辑验证", function () {
  let tokenA, tokenB, minerNFT, miningContract, priceOracle;
  let mockWBNB, mockFactory, mockRouter;
  let owner, marketingWallet, user1, user2, user3;
  
  beforeEach(async function () {
    [owner, marketingWallet, user1, user2, user3] = await ethers.getSigners();
    
    // 部署模拟PancakeSwap环境
    const MockWBNB = await ethers.getContractFactory("MockWBNB");
    mockWBNB = await MockWBNB.deploy();
    await mockWBNB.waitForDeployment();
    
    const MockPancakeFactory = await ethers.getContractFactory("MockPancakeFactory");
    mockFactory = await MockPancakeFactory.deploy();
    await mockFactory.waitForDeployment();
    
    const MockPancakeRouter = await ethers.getContractFactory("MockPancakeRouter");
    mockRouter = await MockPancakeRouter.deploy(
      await mockFactory.getAddress(),
      await mockWBNB.getAddress()
    );
    await mockRouter.waitForDeployment();
    
    // 部署核心合约
    const PriceOracle = await ethers.getContractFactory("PriceOracle");
    priceOracle = await PriceOracle.deploy();
    await priceOracle.waitForDeployment();
    
    const TokenA = await ethers.getContractFactory("TokenA");
    tokenA = await TokenA.deploy(
      await marketingWallet.getAddress(),
      await mockRouter.getAddress(),
      await mockWBNB.getAddress()
    );
    await tokenA.waitForDeployment();
    
    const TokenB = await ethers.getContractFactory("TokenB");
    tokenB = await TokenB.deploy(
      await marketingWallet.getAddress(),
      await mockRouter.getAddress(),
      await mockWBNB.getAddress()
    );
    await tokenB.waitForDeployment();
    
    const MinerNFT = await ethers.getContractFactory("MinerNFT");
    minerNFT = await MinerNFT.deploy("MinerNFT", "MNFT", await tokenA.getAddress());
    await minerNFT.waitForDeployment();
    
    const MiningContract = await ethers.getContractFactory("MiningContract");
    miningContract = await MiningContract.deploy(
      await tokenA.getAddress(),
      await tokenB.getAddress(),
      await minerNFT.getAddress(),
      await priceOracle.getAddress()
    );
    await miningContract.waitForDeployment();
    
    // 配置合约关系
    await tokenA.setNftContract(await minerNFT.getAddress());
    await tokenA.setTaxExempt(await miningContract.getAddress(), true);
    await tokenB.setTaxExempt(await miningContract.getAddress(), true);
    
    // 向NFT合约转入TokenA
    await tokenA.transfer(await minerNFT.getAddress(), ethers.parseEther("21000000"));
    
    // 设置价格
    await priceOracle.setManualPrice(await tokenA.getAddress(), ethers.parseEther("0.001"));
    await priceOracle.setManualPrice(await tokenB.getAddress(), ethers.parseEther("0.0001"));
    
    console.log("=== 核心测试环境初始化完成 ===");
  });

  describe("1. 代币供应量和分配验证", function () {
    it("应该验证TokenA的经济模型", async function () {
      const totalSupply = await tokenA.totalSupply();
      const ownerBalance = await tokenA.balanceOf(await owner.getAddress());
      const nftBalance = await tokenA.balanceOf(await minerNFT.getAddress());
      
      console.log("TokenA总供应:", ethers.formatEther(totalSupply));
      console.log("Owner余额:", ethers.formatEther(ownerBalance));
      console.log("NFT合约余额:", ethers.formatEther(nftBalance));
      
      expect(totalSupply).to.equal(ethers.parseEther("250000000"));
      expect(nftBalance).to.equal(ethers.parseEther("21000000"));
      
      // 验证总分配正确
      const contractBalance = await tokenA.balanceOf(await tokenA.getAddress());
      console.log("TokenA合约余额:", ethers.formatEther(contractBalance));
    });
    
    it("应该验证TokenB的经济模型", async function () {
      const totalSupply = await tokenB.totalSupply();
      const ownerBalance = await tokenB.balanceOf(await owner.getAddress());
      
      console.log("TokenB总供应:", ethers.formatEther(totalSupply));
      console.log("Owner余额:", ethers.formatEther(ownerBalance));
      
      expect(totalSupply).to.equal(ethers.parseEther("2100000000000"));
      expect(ownerBalance).to.equal(totalSupply);
    });
  });

  describe("2. NFT业务逻辑验证", function () {
    it("应该正确铸造NFT", async function () {
      await minerNFT.mint(await user1.getAddress());
      
      const balance = await minerNFT.balanceOf(await user1.getAddress());
      const owner = await minerNFT.ownerOf(1);
      const totalSupply = await minerNFT.totalSupply();
      
      expect(balance).to.equal(1);
      expect(owner).to.equal(await user1.getAddress());
      expect(totalSupply).to.equal(1);
      
      console.log("✓ NFT铸造成功，总供应量:", totalSupply.toString());
    });
    
    it("应该验证NFT的代币配额", async function () {
      await minerNFT.mint(await user1.getAddress());
      
      const nftInfo = await minerNFT.nftInfo(1);
      console.log("NFT #1 总代币:", ethers.formatEther(nftInfo.totalTokens));
      console.log("NFT #1 已释放:", ethers.formatEther(nftInfo.releasedTokens));
      
      expect(nftInfo.totalTokens).to.equal(ethers.parseEther("100000"));
      expect(nftInfo.releasedTokens).to.equal(0);
    });
    
    it("应该验证批量铸造功能", async function () {
      const recipients = [
        await user1.getAddress(),
        await user2.getAddress(),
        await user3.getAddress()
      ];
      
      await minerNFT.batchMint(recipients);
      
      const totalSupply = await minerNFT.totalSupply();
      expect(totalSupply).to.equal(3);
      
      // 验证每个用户都收到了NFT
      for (let i = 0; i < recipients.length; i++) {
        const balance = await minerNFT.balanceOf(recipients[i]);
        expect(balance).to.equal(1);
      }
      
      console.log("✓ 批量铸造3个NFT成功");
    });
  });

  describe("3. 税收机制验证", function () {
    beforeEach(async function () {
      // 创建流动性池
      const liquidityTokenA = ethers.parseEther("100000");
      const liquidityWBNB = ethers.parseEther("100");
      
      await mockWBNB.deposit({ value: liquidityWBNB });
      await tokenA.approve(await mockRouter.getAddress(), liquidityTokenA);
      await mockWBNB.approve(await mockRouter.getAddress(), liquidityWBNB);
      
      await mockRouter.addLiquidity(
        await tokenA.getAddress(),
        await mockWBNB.getAddress(),
        liquidityTokenA,
        liquidityWBNB,
        0, 0,
        await owner.getAddress(),
        Math.floor(Date.now() / 1000) + 3600
      );
      
      const pairAddress = await mockFactory.getPair(await tokenA.getAddress(), await mockWBNB.getAddress());
      await tokenA.setPair(pairAddress, true);
      
      // 给用户代币
      await tokenA.transfer(await user1.getAddress(), ethers.parseEther("50000"));
    });
    
    it("应该正确收取和分配税收", async function () {
      const pairAddress = await mockFactory.getPair(await tokenA.getAddress(), await mockWBNB.getAddress());
      const tradeAmount = ethers.parseEther("10000");
      
      // 记录初始状态
      const initialContractBalance = await tokenA.balanceOf(await tokenA.getAddress());
      const initialMarketingBalance = await tokenA.balanceOf(await marketingWallet.getAddress());
      const initialTotalSupply = await tokenA.totalSupply();
      
      // 执行交易（卖出到交易对）
      await tokenA.connect(user1).transfer(pairAddress, tradeAmount);
      
      // 检查最终状态
      const finalContractBalance = await tokenA.balanceOf(await tokenA.getAddress());
      const finalMarketingBalance = await tokenA.balanceOf(await marketingWallet.getAddress());
      const finalTotalSupply = await tokenA.totalSupply();
      
      // 计算税收
      const expectedTax = tradeAmount * BigInt(3) / BigInt(100); // 3%
      const expectedMarketing = expectedTax * BigInt(20) / BigInt(100); // 20%
      const expectedBurn = expectedTax * BigInt(50) / BigInt(100); // 50%
      const expectedDividend = expectedTax * BigInt(30) / BigInt(100); // 30%
      
      console.log("交易金额:", ethers.formatEther(tradeAmount));
      console.log("预期税收:", ethers.formatEther(expectedTax));
      console.log("预期营销:", ethers.formatEther(expectedMarketing));
      console.log("预期销毁:", ethers.formatEther(expectedBurn));
      console.log("预期分红:", ethers.formatEther(expectedDividend));
      
      // 验证营销钱包收到代币
      const marketingReceived = finalMarketingBalance - initialMarketingBalance;
      expect(marketingReceived).to.be.closeTo(expectedMarketing, ethers.parseEther("1"));
      
      // 验证代币被销毁
      const burned = initialTotalSupply - finalTotalSupply;
      expect(burned).to.be.closeTo(expectedBurn, ethers.parseEther("1"));
      
      // 验证分红累积
      const totalDividends = await tokenA.totalDividends();
      expect(totalDividends).to.be.closeTo(expectedDividend, ethers.parseEther("1"));
      
      console.log("实际营销收益:", ethers.formatEther(marketingReceived));
      console.log("实际销毁数量:", ethers.formatEther(burned));
      console.log("累积分红数量:", ethers.formatEther(totalDividends));
    });
  });

  describe("4. 分红机制验证", function () {
    beforeEach(async function () {
      // 创建NFT持有者
      await minerNFT.mint(await user1.getAddress());
      await minerNFT.mint(await user2.getAddress());
      
      // 创建流动性和设置交易对
      const liquidityTokenA = ethers.parseEther("100000");
      const liquidityWBNB = ethers.parseEther("100");
      
      await mockWBNB.deposit({ value: liquidityWBNB });
      await tokenA.approve(await mockRouter.getAddress(), liquidityTokenA);
      await mockWBNB.approve(await mockRouter.getAddress(), liquidityWBNB);
      
      await mockRouter.addLiquidity(
        await tokenA.getAddress(),
        await mockWBNB.getAddress(),
        liquidityTokenA,
        liquidityWBNB,
        0, 0,
        await owner.getAddress(),
        Math.floor(Date.now() / 1000) + 3600
      );
      
      const pairAddress = await mockFactory.getPair(await tokenA.getAddress(), await mockWBNB.getAddress());
      await tokenA.setPair(pairAddress, true);
      
      // 给用户代币
      await tokenA.transfer(await user1.getAddress(), ethers.parseEther("50000"));
      
      // 禁用自动分红
      await tokenA.setAutoDividendConfig(500000, 10, ethers.parseEther("100"), false);
    });
    
    it("应该正确计算和分配分红", async function () {
      const pairAddress = await mockFactory.getPair(await tokenA.getAddress(), await mockWBNB.getAddress());
      
      // 执行交易产生分红
      await tokenA.connect(user1).transfer(pairAddress, ethers.parseEther("30000"));
      
      // 检查分红计算
      const user1Dividend = await tokenA.calculateDividend(await user1.getAddress());
      const user2Dividend = await tokenA.calculateDividend(await user2.getAddress());
      
      console.log("User1分红:", ethers.formatEther(user1Dividend));
      console.log("User2分红:", ethers.formatEther(user2Dividend));
      
      // 两个用户持有相同数量NFT，分红应该相等
      expect(user1Dividend).to.equal(user2Dividend);
      expect(user1Dividend).to.be.gt(0);
      
      // 验证分红总额
      const totalSystemDividends = await tokenA.totalDividends();
      const totalUserDividends = user1Dividend + user2Dividend;
      
      console.log("系统总分红:", ethers.formatEther(totalSystemDividends));
      console.log("用户总分红:", ethers.formatEther(totalUserDividends));
      
      expect(totalUserDividends).to.be.closeTo(totalSystemDividends, ethers.parseEther("1"));
    });
    
    it("应该正确执行分红领取", async function () {
      const pairAddress = await mockFactory.getPair(await tokenA.getAddress(), await mockWBNB.getAddress());
      
      // 产生分红
      await tokenA.connect(user1).transfer(pairAddress, ethers.parseEther("30000"));
      
      const initialBalance = await tokenA.balanceOf(await user1.getAddress());
      const claimableDividend = await tokenA.calculateDividend(await user1.getAddress());
      
      if (claimableDividend > 0) {
        // 领取分红
        await tokenA.connect(user1).claimDividend();
        
        const finalBalance = await tokenA.balanceOf(await user1.getAddress());
        const receivedDividend = finalBalance - initialBalance;
        
        console.log("领取分红:", ethers.formatEther(receivedDividend));
        expect(receivedDividend).to.equal(claimableDividend);
        
        // 验证分红清零
        const remainingDividend = await tokenA.calculateDividend(await user1.getAddress());
        expect(remainingDividend).to.equal(0);
      }
    });
  });

  describe("5. 邀请系统验证", function () {
    beforeEach(async function () {
      await tokenA.transfer(await user2.getAddress(), ethers.parseEther("50000"));
    });
    
    it("应该正确设置邀请关系", async function () {
      // 设置邀请关系
      await miningContract.connect(user2).setInviter(await user1.getAddress());
      
      // 验证邀请关系
      const user2Info = await miningContract.getUserInviteInfo(await user2.getAddress());
      const user1Info = await miningContract.getUserInviteInfo(await user1.getAddress());
      
      expect(user2Info.inviter).to.equal(await user1.getAddress());
      expect(user1Info.totalInvited).to.equal(1);
      
      console.log("邀请关系设置成功");
      console.log("User2的邀请人:", user2Info.inviter);
      console.log("User1邀请总数:", user1Info.totalInvited.toString());
    });
    
    it("应该阻止重复设置邀请关系", async function () {
      // 首次设置
      await miningContract.connect(user2).setInviter(await user1.getAddress());
      
      // 尝试重复设置应该失败
      await expect(
        miningContract.connect(user2).setInviter(await user3.getAddress())
      ).to.be.revertedWith("Inviter already set");
      
      console.log("✓ 重复设置邀请关系被正确阻止");
    });
  });

  describe("6. 价格预言机验证", function () {
    it("应该正确设置和获取代币价格", async function () {
      const tokenAPrice = await priceOracle.getTokenPrice(await tokenA.getAddress());
      const tokenBPrice = await priceOracle.getTokenPrice(await tokenB.getAddress());
      
      expect(tokenAPrice).to.equal(ethers.parseEther("0.001"));
      expect(tokenBPrice).to.equal(ethers.parseEther("0.0001"));
      
      console.log("TokenA价格:", ethers.formatEther(tokenAPrice), "BNB");
      console.log("TokenB价格:", ethers.formatEther(tokenBPrice), "BNB");
    });
    
    it("应该正确计算价格比率", async function () {
      const priceRatio = await priceOracle.getTokenPriceRatio(
        await tokenA.getAddress(),
        await tokenB.getAddress()
      );
      
      // 0.001 / 0.0001 = 10
      expect(priceRatio).to.equal(ethers.parseEther("10"));
      
      console.log("TokenA/TokenB价格比率:", ethers.formatEther(priceRatio));
    });
  });

  describe("7. 系统集成验证", function () {
    it("应该完成端到端的业务流程", async function () {
      console.log("=== 端到端业务流程测试 ===");
      
      // 1. 铸造NFT
      await minerNFT.mint(await user1.getAddress());
      console.log("1. ✓ 用户获得NFT");
      
      // 2. 用户获得代币
      await tokenA.transfer(await user1.getAddress(), ethers.parseEther("100000"));
      console.log("2. ✓ 用户获得TokenA");
      
      // 3. 创建交易对
      const liquidityTokenA = ethers.parseEther("50000");
      const liquidityWBNB = ethers.parseEther("50");
      
      await mockWBNB.deposit({ value: liquidityWBNB });
      await tokenA.approve(await mockRouter.getAddress(), liquidityTokenA);
      await mockWBNB.approve(await mockRouter.getAddress(), liquidityWBNB);
      
      await mockRouter.addLiquidity(
        await tokenA.getAddress(),
        await mockWBNB.getAddress(),
        liquidityTokenA,
        liquidityWBNB,
        0, 0,
        await owner.getAddress(),
        Math.floor(Date.now() / 1000) + 3600
      );
      
      const pairAddress = await mockFactory.getPair(await tokenA.getAddress(), await mockWBNB.getAddress());
      await tokenA.setPair(pairAddress, true);
      console.log("3. ✓ 创建交易对和流动性");
      
      // 4. 执行交易产生税收
      await tokenA.setAutoDividendConfig(500000, 10, ethers.parseEther("100"), false);
      await tokenA.connect(user1).transfer(pairAddress, ethers.parseEther("20000"));
      console.log("4. ✓ 交易产生税收和分红");
      
      // 5. 领取分红
      const dividend = await tokenA.calculateDividend(await user1.getAddress());
      if (dividend > 0) {
        await tokenA.connect(user1).claimDividend();
        console.log("5. ✓ 分红领取:", ethers.formatEther(dividend));
      }
      
      // 6. 验证最终状态
      const finalUserBalance = await tokenA.balanceOf(await user1.getAddress());
      const totalDividends = await tokenA.totalDividends();
      
      console.log("6. 最终状态:");
      console.log("   用户TokenA余额:", ethers.formatEther(finalUserBalance));
      console.log("   系统总分红:", ethers.formatEther(totalDividends));
      
      expect(totalDividends).to.be.gt(0);
      console.log("=== 端到端测试完成 ===");
    });
  });
});