const { expect } = require("chai");
const { ethers } = require("hardhat");

describe("本地环境全面测试", function () {
  let tokenA, tokenB, minerNFT, miningContract, priceOracle;
  let owner, marketingWallet, user1, user2, user3;
  
  // 模拟地址（不需要真实PancakeSwap合约）
  const MOCK_ROUTER = "******************************************";
  const MOCK_WBNB = "******************************************";

  beforeEach(async function () {
    [owner, marketingWallet, user1, user2, user3] = await ethers.getSigners();
    
    // 部署价格预言机
    const PriceOracle = await ethers.getContractFactory("PriceOracle");
    priceOracle = await PriceOracle.deploy();
    await priceOracle.waitForDeployment();
    
    // 部署TokenA（在本地环境中不会创建PancakeSwap交易对）
    const TokenA = await ethers.getContractFactory("TokenA");
    tokenA = await TokenA.deploy(
      await marketingWallet.getAddress(),
      MOCK_ROUTER,
      MOCK_WBNB
    );
    await tokenA.waitForDeployment();
    
    // 部署TokenB（在本地环境中不会创建PancakeSwap交易对）
    const TokenB = await ethers.getContractFactory("TokenB");
    tokenB = await TokenB.deploy(
      await marketingWallet.getAddress(),
      MOCK_ROUTER,
      MOCK_WBNB
    );
    await tokenB.waitForDeployment();
    
    // 部署NFT合约
    const MinerNFT = await ethers.getContractFactory("MinerNFT");
    minerNFT = await MinerNFT.deploy("MinerNFT", "MNFT", await tokenA.getAddress());
    await minerNFT.waitForDeployment();
    
    // 部署挖矿合约
    const MiningContract = await ethers.getContractFactory("MiningContract");
    miningContract = await MiningContract.deploy(
      await tokenA.getAddress(),
      await tokenB.getAddress(),
      await minerNFT.getAddress(),
      await priceOracle.getAddress()
    );
    await miningContract.waitForDeployment();
    
    // 配置合约关系
    await tokenA.setNftContract(await minerNFT.getAddress());
    await tokenB.addMinter(await miningContract.getAddress());
    await tokenA.setTaxExempt(await miningContract.getAddress(), true);
    await tokenB.setTaxExempt(await miningContract.getAddress(), true);
    
    // 向NFT合约转入TokenA用于释放
    const nftTokenAmount = ethers.parseEther("21000000"); // 2100万（2100个NFT * 1万）
    await tokenA.transfer(await minerNFT.getAddress(), nftTokenAmount);
    
    // 设置手动价格用于测试
    await priceOracle.setManualPrice(await tokenA.getAddress(), ethers.parseEther("0.001"));
    await priceOracle.setManualPrice(await tokenB.getAddress(), ethers.parseEther("0.0001"));
    
    console.log("=== 合约部署完成 ===");
    console.log("TokenA:", await tokenA.getAddress());
    console.log("TokenB:", await tokenB.getAddress());
    console.log("MinerNFT:", await minerNFT.getAddress());
    console.log("MiningContract:", await miningContract.getAddress());
  });

  describe("合约部署和配置验证", function () {
    it("应该正确部署所有合约", async function () {
      expect(await tokenA.totalSupply()).to.equal(ethers.parseEther("*********"));
      expect(await tokenB.totalSupply()).to.equal(ethers.parseEther("2100000000000"));
      expect(await minerNFT.MAX_SUPPLY()).to.equal(2100);
      expect(await miningContract.tokenAAddress()).to.equal(await tokenA.getAddress());
    });

    it("应该正确设置合约关系", async function () {
      expect(await tokenA.nftContract()).to.equal(await minerNFT.getAddress());
      expect(await tokenB.hasRole(await tokenB.MINTER_ROLE(), await miningContract.getAddress())).to.be.true;
      expect(await tokenA.taxExempt(await miningContract.getAddress())).to.be.true;
    });
  });

  describe("Gas优化效果测试", function () {
    it("应该记录各种操作的Gas消耗", async function () {
      // 1. 基础转账Gas测试
      const tx1 = await tokenA.transfer(await user1.getAddress(), ethers.parseEther("1000"));
      const receipt1 = await tx1.wait();
      console.log("TokenA基础转账 Gas:", receipt1.gasUsed.toString());
      
      // 2. 带税收的转账Gas测试
      await tokenA.setPair(await user2.getAddress(), true);
      const tx2 = await tokenA.connect(user1).transfer(await user2.getAddress(), ethers.parseEther("100"));
      const receipt2 = await tx2.wait();
      console.log("TokenA带税收转账 Gas:", receipt2.gasUsed.toString());
      
      // 3. NFT铸造Gas测试
      const tx3 = await minerNFT.mint(await user1.getAddress());
      const receipt3 = await tx3.wait();
      console.log("NFT铸造 Gas:", receipt3.gasUsed.toString());
      
      // 4. 批量NFT铸造Gas测试
      const addresses = [await user1.getAddress(), await user2.getAddress(), await user3.getAddress()];
      const tx4 = await minerNFT.batchMint(addresses);
      const receipt4 = await tx4.wait();
      console.log("批量NFT铸造 Gas:", receipt4.gasUsed.toString());
      
      // 5. 自动分红配置Gas测试
      const tx5 = await tokenA.setAutoDividendConfig(300000, 20, ethers.parseEther("0.01"), true);
      const receipt5 = await tx5.wait();
      console.log("设置自动分红配置 Gas:", receipt5.gasUsed.toString());
    });
  });

  describe("重入保护机制测试", function () {
    beforeEach(async function () {
      // 给用户铸造NFT
      await minerNFT.mint(await user1.getAddress());
      await minerNFT.mint(await user1.getAddress());
      
      // 设置交易对产生分红
      await tokenA.setPair(await user2.getAddress(), true);
      await tokenA.transfer(await user1.getAddress(), ethers.parseEther("10000"));
      
      // 禁用自动分红以便测试手动分红
      await tokenA.setAutoDividendConfig(500000, 10, ethers.parseEther("100"), false);
    });

    it("应该防止分红重入攻击", async function () {
      // 执行交易产生分红
      await tokenA.connect(user1).transfer(await user2.getAddress(), ethers.parseEther("1000"));
      
      // 检查分红是否产生
      const dividend = await tokenA.calculateDividend(await user1.getAddress());
      console.log("用户可领取分红:", ethers.formatEther(dividend));
      
      if (dividend > 0) {
        // 测试正常分红领取
        const tx = await tokenA.connect(user1).claimDividend();
        const receipt = await tx.wait();
        console.log("分红领取 Gas:", receipt.gasUsed.toString());
        
        // 尝试再次领取应该失败或返回0
        const remainingDividend = await tokenA.calculateDividend(await user1.getAddress());
        expect(remainingDividend).to.equal(0);
      }
    });

    it("应该防止NFT分红重入攻击", async function () {
      // 执行交易产生分红
      await tokenA.connect(user1).transfer(await user2.getAddress(), ethers.parseEther("1000"));
      
      // 测试单个NFT分红领取
      const nftDividend = await tokenA.calculateNFTDividend(1);
      console.log("NFT #1可领取分红:", ethers.formatEther(nftDividend));
      
      if (nftDividend > 0) {
        const tx = await tokenA.connect(user1).claimNFTDividend(1);
        const receipt = await tx.wait();
        console.log("NFT分红领取 Gas:", receipt.gasUsed.toString());
      }
    });
  });

  describe("自动分红机制测试", function () {
    beforeEach(async function () {
      // 给多个用户铸造NFT
      await minerNFT.mint(await user1.getAddress());
      await minerNFT.mint(await user2.getAddress());
      await minerNFT.mint(await user3.getAddress());
      
      // 设置交易对
      await tokenA.setPair(await user2.getAddress(), true);
      await tokenA.transfer(await user1.getAddress(), ethers.parseEther("10000"));
      
      // 启用自动分红
      await tokenA.setAutoDividendConfig(500000, 10, ethers.parseEther("0.01"), true);
    });

    it("应该自动处理分红分配", async function () {
      // 执行大额交易触发自动分红
      const tx = await tokenA.connect(user1).transfer(await user2.getAddress(), ethers.parseEther("3000"));
      const receipt = await tx.wait();
      
      console.log("带自动分红的交易 Gas:", receipt.gasUsed.toString());
      
      // 检查是否有自动分红事件
      const events = receipt.logs.filter(log => {
        try {
          return tokenA.interface.parseLog(log).name === "AutoDividendProcessed";
        } catch (e) {
          return false;
        }
      });
      
      if (events.length > 0) {
        console.log("自动分红已触发");
      }
    });

    it("应该能够手动触发自动分红", async function () {
      // 先产生一些分红
      await tokenA.connect(user1).transfer(await user2.getAddress(), ethers.parseEther("1000"));
      
      // 手动触发自动分红
      const tx = await tokenA.processAutoDividend();
      const receipt = await tx.wait();
      console.log("手动触发自动分红 Gas:", receipt.gasUsed.toString());
    });
  });

  describe("挖矿机制测试", function () {
    beforeEach(async function () {
      // 给用户一些代币
      await tokenA.transfer(await user1.getAddress(), ethers.parseEther("10000"));
      await tokenB.mint(await user1.getAddress(), ethers.parseEther("1000000"));
      
      // 给用户铸造NFT
      await minerNFT.mint(await user1.getAddress());
    });

    it("应该能够销毁TokenA获得TokenB", async function () {
      const burnAmount = ethers.parseEther("1000");
      
      // 授权挖矿合约
      await tokenA.connect(user1).approve(await miningContract.getAddress(), burnAmount);
      
      // 执行销毁
      const tx = await miningContract.connect(user1).burnTokenAForTokenB(burnAmount);
      const receipt = await tx.wait();
      console.log("销毁TokenA挖矿 Gas:", receipt.gasUsed.toString());
      
      // 检查挖矿记录
      const recordCount = await miningContract.getUserMiningRecordCount(await user1.getAddress());
      expect(recordCount.count1).to.equal(1);
    });

    it("应该能够销毁TokenB（需要NFT）", async function () {
      const burnAmount = ethers.parseEther("10000");
      
      // 授权挖矿合约
      await tokenB.connect(user1).approve(await miningContract.getAddress(), burnAmount);
      
      // 执行销毁
      const tx = await miningContract.connect(user1).burnTokenBWithNFT(burnAmount);
      const receipt = await tx.wait();
      console.log("销毁TokenB挖矿 Gas:", receipt.gasUsed.toString());
      
      // 检查挖矿记录
      const recordCount = await miningContract.getUserMiningRecordCount(await user1.getAddress());
      expect(recordCount.count2).to.equal(1);
    });
  });

  describe("边界条件和错误处理", function () {
    it("应该正确处理零地址", async function () {
      await expect(
        tokenA.transfer(ethers.ZeroAddress, ethers.parseEther("100"))
      ).to.be.revertedWith("ERC20: transfer to the zero address");
    });

    it("应该正确处理余额不足", async function () {
      await expect(
        tokenA.connect(user1).transfer(await user2.getAddress(), ethers.parseEther("1000"))
      ).to.be.revertedWith("ERC20: transfer amount exceeds balance");
    });

    it("应该正确处理没有NFT的分红领取", async function () {
      await expect(
        tokenA.connect(user1).claimDividend()
      ).to.be.revertedWith("No NFT to claim dividends");
    });
  });

  describe("性能和存储优化验证", function () {
    it("应该验证存储布局优化", async function () {
      // 测试自动分红配置的读取
      const config = await tokenA.getAutoDividendStatus();
      console.log("自动分红配置:", {
        enabled: config.enabled,
        gasLimit: config.gasLimit.toString(),
        maxNFTs: config.maxNFTs.toString(),
        threshold: ethers.formatEther(config.threshold)
      });
      
      expect(config.enabled).to.be.true;
      expect(config.gasLimit).to.equal(500000);
    });

    it("应该验证循环优化", async function () {
      // 铸造多个NFT测试批量操作
      const addresses = [];
      for (let i = 0; i < 5; i++) {
        addresses.push(await user1.getAddress());
      }
      
      const tx = await minerNFT.batchMint(addresses);
      const receipt = await tx.wait();
      console.log("批量铸造5个NFT Gas:", receipt.gasUsed.toString());
      
      // 预期Gas消耗应该是合理的
      expect(receipt.gasUsed).to.be.lt(500000);
    });
  });
});