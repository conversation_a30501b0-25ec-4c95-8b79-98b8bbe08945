const { expect } = require("chai");
const { ethers } = require("hardhat");

describe("实时线性释放算法测试", function () {
    let miningContract;
    let owner, user1, user2;
    
    // 常量定义
    const TOTAL_RELEASE_DURATION = 180 * 24 * 3600; // 180天的秒数
    const REWARD_MULTIPLIER = 2; // 2倍奖励
    
    beforeEach(async function () {
        [owner, user1, user2] = await ethers.getSigners();
        
        // 注意：这是纯算法测试，不需要部署完整合约
        // 我们将使用纯JavaScript模拟合约算法
    });

    describe("核心算法正确性验证", function () {
        
        it("应该在开始时可领取金额为0", function () {
            const startTime = 1000000;
            const currentTime = 1000000; // 相同时间
            const totalValue = ethers.parseEther("1000");
            const alreadyClaimed = 0n;
            
            const claimable = calculateReleasableAmount(
                totalValue, 
                alreadyClaimed, 
                startTime, 
                currentTime
            );
            
            expect(claimable).to.equal(0n);
        });

        it("应该在一半时间后可领取一半金额", function () {
            const startTime = 1000000;
            const currentTime = startTime + TOTAL_RELEASE_DURATION / 2; // 90天后
            const totalValue = ethers.parseEther("1000");
            const alreadyClaimed = ethers.parseEther("0");
            
            const claimable = calculateReleasableAmount(
                totalValue, 
                alreadyClaimed, 
                startTime, 
                currentTime
            );
            
            const expectedHalf = totalValue / 2n;
            expect(claimable).to.equal(expectedHalf);
        });

        it("应该在180天后可领取全部金额", function () {
            const startTime = 1000000;
            const currentTime = startTime + TOTAL_RELEASE_DURATION; // 180天后
            const totalValue = ethers.parseEther("1000");
            const alreadyClaimed = ethers.parseEther("0");
            
            const claimable = calculateReleasableAmount(
                totalValue, 
                alreadyClaimed, 
                startTime, 
                currentTime
            );
            
            expect(claimable).to.equal(totalValue);
        });

        it("分批领取应该与一次性领取结果一致", function () {
            const startTime = 1000000;
            const totalValue = ethers.parseEther("1000");
            
            // 模拟分批领取
            let totalClaimed = 0n;
            
            // 第30天领取
            const time30 = startTime + 30 * 24 * 3600;
            const claimable30 = calculateReleasableAmount(
                totalValue, 
                totalClaimed, 
                startTime, 
                time30
            );
            totalClaimed = totalClaimed + claimable30;
            
            // 第60天再次领取
            const time60 = startTime + 60 * 24 * 3600;
            const claimable60 = calculateReleasableAmount(
                totalValue, 
                totalClaimed, 
                startTime, 
                time60
            );
            totalClaimed = totalClaimed + claimable60;
            
            // 第90天再次领取
            const time90 = startTime + 90 * 24 * 3600;
            const claimable90 = calculateReleasableAmount(
                totalValue, 
                totalClaimed, 
                startTime, 
                time90
            );
            totalClaimed = totalClaimed + claimable90;
            
            // 验证90天时分批领取的总量与一次性计算的结果一致
            const directClaimable90 = calculateReleasableAmount(
                totalValue, 
                0n, 
                startTime, 
                time90
            );
            
            expect(totalClaimed).to.equal(directClaimable90);
            
            console.log(`分批领取90天总计: ${ethers.formatEther(totalClaimed)} ETH`);
            console.log(`一次性计算90天: ${ethers.formatEther(directClaimable90)} ETH`);
        });

        it("不应该允许领取超过总价值", function () {
            const startTime = 1000000;
            const currentTime = startTime + TOTAL_RELEASE_DURATION * 2; // 360天后
            const totalValue = ethers.parseEther("1000");
            const alreadyClaimed = 0n;
            
            const claimable = calculateReleasableAmount(
                totalValue, 
                alreadyClaimed, 
                startTime, 
                currentTime
            );
            
            expect(claimable).to.equal(totalValue);
        });

        it("已经领取完全部金额后，再次查询应该返回0", function () {
            const startTime = 1000000;
            const currentTime = startTime + TOTAL_RELEASE_DURATION; // 180天后
            const totalValue = ethers.parseEther("1000");
            const alreadyClaimed = totalValue; // 已经领取了全部
            
            const claimable = calculateReleasableAmount(
                totalValue, 
                alreadyClaimed, 
                startTime, 
                currentTime
            );
            
            expect(claimable).to.equal(0n);
        });

        it("测试每日释放率的一致性", function () {
            const startTime = 1000000;
            const totalValue = ethers.parseEther("1800"); // 1800 ETH，方便计算
            const oneDayInSeconds = 24 * 3600;
            
            // 计算每天的释放量
            for (let day = 1; day <= 10; day++) {
                const currentTime = startTime + day * oneDayInSeconds;
                const claimable = calculateReleasableAmount(
                    totalValue, 
                    0n, 
                    startTime, 
                    currentTime
                );
                
                const expectedDaily = (totalValue / 180n) * BigInt(day); // 每天应释放 1800/180 = 10 ETH
                expect(claimable).to.equal(expectedDaily);
                
                console.log(`第${day}天: 可领取 ${ethers.formatEther(claimable)} ETH`);
            }
        });

        it("测试精确的数学计算", function () {
            const startTime = 1000000;
            const totalValue = ethers.parseEther("1000");
            
            // 测试各种时间点的精确计算
            const testCases = [
                { days: 1, expectedPercent: 1/180 },
                { days: 30, expectedPercent: 30/180 },
                { days: 90, expectedPercent: 90/180 },
                { days: 180, expectedPercent: 180/180 },
            ];
            
            testCases.forEach(({ days, expectedPercent }) => {
                const currentTime = startTime + days * 24 * 3600;
                const claimable = calculateReleasableAmount(
                    totalValue, 
                    0n, 
                    startTime, 
                    currentTime
                );
                
                const expectedAmount = (totalValue * BigInt(Math.floor(expectedPercent * 1000))) / 1000n;
                const tolerance = totalValue / 1000n; // 0.1% 容差
                
                expect(claimable).to.be.closeTo(expectedAmount, tolerance);
                
                console.log(`第${days}天: 期望 ${ethers.formatEther(expectedAmount)} ETH, 实际 ${ethers.formatEther(claimable)} ETH`);
            });
        });
    });

    describe("边界条件和安全性测试", function () {
        
        it("处理时间为0的情况", function () {
            const startTime = 1000000;
            const currentTime = startTime; // 相同时间
            const totalValue = ethers.parseEther("1000");
            
            const claimable = calculateReleasableAmount(
                totalValue, 
                0n, 
                startTime, 
                currentTime
            );
            
            expect(claimable).to.equal(0n);
        });

        it("处理很大的时间值", function () {
            const startTime = 1000000;
            const currentTime = startTime + TOTAL_RELEASE_DURATION * 10; // 1800天后
            const totalValue = ethers.parseEther("1000");
            
            const claimable = calculateReleasableAmount(
                totalValue, 
                0n, 
                startTime, 
                currentTime
            );
            
            // 即使时间很长，最多也只能领取总价值
            expect(claimable).to.equal(totalValue);
        });

        it("处理已领取金额大于总价值的异常情况", function () {
            const startTime = 1000000;
            const currentTime = startTime + TOTAL_RELEASE_DURATION;
            const totalValue = ethers.parseEther("1000");
            const alreadyClaimed = ethers.parseEther("1500"); // 异常：超过总价值
            
            const claimable = calculateReleasableAmount(
                totalValue, 
                alreadyClaimed, 
                startTime, 
                currentTime
            );
            
            expect(claimable).to.equal(0);
        });

        it("测试溢出保护", function () {
            // 使用很大的数值测试溢出保护
            const startTime = 1000000;
            const currentTime = startTime + TOTAL_RELEASE_DURATION;
            const totalValue = ethers.parseEther("1000000000"); // 10亿 ETH
            
            const claimable = calculateReleasableAmount(
                totalValue, 
                0n, 
                startTime, 
                currentTime
            );
            
            expect(claimable).to.equal(totalValue);
        });
    });

    describe("性能和Gas效率测试", function () {
        
        it("算法复杂度应该是O(1)", function () {
            const startTime = 1000000;
            const currentTime = startTime + TOTAL_RELEASE_DURATION / 2;
            
            // 测试不同规模的总价值，计算时间应该相近
            const testValues = [
                ethers.parseEther("1"),
                ethers.parseEther("1000"),
                ethers.parseEther("1000000"),
                ethers.parseEther("1000000000")
            ];
            
            testValues.forEach(totalValue => {
                const startTime = Date.now();
                
                const claimable = calculateReleasableAmount(
                    totalValue, 
                    0n, 
                    startTime, 
                    currentTime
                );
                
                const endTime = Date.now();
                const duration = endTime - startTime;
                
                console.log(`总价值 ${ethers.formatEther(totalValue)} ETH: 计算时间 ${duration}ms`);
                
                // 计算时间应该很短
                expect(duration).to.be.lessThan(10);
            });
        });
    });

    // 核心算法实现 - 与合约中的算法完全一致
    function calculateReleasableAmount(totalReleaseValue, releasedValue, startTime, currentTime) {
        // 如果记录已完全释放，返回0
        if (releasedValue >= totalReleaseValue) {
            return 0n;
        }
        
        // 计算从开始到现在的总时间
        let totalTimeElapsed = BigInt(currentTime - startTime);
        
        // 如果超过180天，限制在180天
        const maxDuration = BigInt(TOTAL_RELEASE_DURATION);
        if (totalTimeElapsed > maxDuration) {
            totalTimeElapsed = maxDuration;
        }
        
        // 计算到目前为止总共应该释放的价值
        const totalShouldHaveReleased = (totalReleaseValue * totalTimeElapsed) / maxDuration;
        
        // 可领取的金额 = 应该释放的总额 - 已释放的金额
        if (totalShouldHaveReleased > releasedValue) {
            return totalShouldHaveReleased - releasedValue;
        }
        
        return 0n;
    }
});

describe("算法对比测试", function () {
    
    it("新算法vs旧算法：用户体验对比", function () {
        console.log("\\n=== 算法对比分析 ===");
        
        const totalValue = ethers.parseEther("1000");
        const startTime = 1000000;
        
        console.log("总锁定价值: 1000 ETH");
        console.log("释放周期: 180天");
        console.log("\\n新算法（实时线性释放）vs 旧算法（6期固定释放）：");
        
        // 比较不同时间点的可领取金额
        const timePoints = [1, 7, 15, 30, 45, 60, 90, 120, 150, 180];
        
        timePoints.forEach(days => {
            const currentTime = startTime + days * 24 * 3600;
            
            // 新算法
            const newAlgoClaimable = calculateReleasableAmount(
                totalValue,
                0n,
                startTime,
                currentTime
            );
            
            // 旧算法（6期制，每30天一期）
            const period = Math.floor(days / 30);
            const oldAlgoClaimable = (totalValue / 6n) * BigInt(period);
            
            console.log(`第${days}天:`);
            console.log(`  新算法: ${ethers.formatEther(newAlgoClaimable)} ETH`);
            console.log(`  旧算法: ${ethers.formatEther(oldAlgoClaimable)} ETH`);
            console.log(`  差异: ${ethers.formatEther(newAlgoClaimable - oldAlgoClaimable)} ETH`);
            console.log("");
        });
    });
    
    // 辅助函数
    function calculateReleasableAmount(totalReleaseValue, releasedValue, startTime, currentTime) {
        const TOTAL_RELEASE_DURATION = 180 * 24 * 3600;
        
        if (releasedValue >= totalReleaseValue) {
            return 0n;
        }
        
        let totalTimeElapsed = currentTime - startTime;
        if (totalTimeElapsed > TOTAL_RELEASE_DURATION) {
            totalTimeElapsed = TOTAL_RELEASE_DURATION;
        }
        
        const totalShouldHaveReleased = (totalReleaseValue * BigInt(totalTimeElapsed)) / BigInt(TOTAL_RELEASE_DURATION);
        
        if (totalShouldHaveReleased > releasedValue) {
            return totalShouldHaveReleased - releasedValue;
        }
        
        return 0n;
    }
});