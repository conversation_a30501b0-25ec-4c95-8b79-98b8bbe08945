const { expect } = require("chai");
const { ethers } = require("hardhat");

describe("税收机制调试", function () {
  let tokenA, mockWBNB, mockFactory, mockRouter;
  let owner, marketingWallet, user1;
  
  beforeEach(async function () {
    [owner, marketingWallet, user1] = await ethers.getSigners();
    
    // 部署模拟环境
    const MockWBNB = await ethers.getContractFactory("MockWBNB");
    mockWBNB = await MockWBNB.deploy();
    await mockWBNB.waitForDeployment();
    
    const MockPancakeFactory = await ethers.getContractFactory("MockPancakeFactory");
    mockFactory = await MockPancakeFactory.deploy();
    await mockFactory.waitForDeployment();
    
    const MockPancakeRouter = await ethers.getContractFactory("MockPancakeRouter");
    mockRouter = await MockPancakeRouter.deploy(
      await mockFactory.getAddress(),
      await mockWBNB.getAddress()
    );
    await mockRouter.waitForDeployment();
    
    // 部署TokenA
    const TokenA = await ethers.getContractFactory("TokenA");
    tokenA = await TokenA.deploy(
      await marketingWallet.getAddress(),
      await mockRouter.getAddress(),
      await mockWBNB.getAddress()
    );
    await tokenA.waitForDeployment();
    
    console.log("合约部署完成");
  });

  it("应该调试税收机制", async function () {
    console.log("=== 税收机制调试 ===");
    
    // 1. 检查初始设置
    const taxRate = await tokenA.BUY_TAX_RATE();
    console.log("买入税率:", taxRate.toString(), "%");
    
    const marketingShare = await tokenA.MARKETING_SHARE();
    const burnShare = await tokenA.BURN_SHARE();
    const dividendShare = await tokenA.DIVIDEND_SHARE();
    console.log("分配比例 - 营销:", marketingShare.toString(), "% 销毁:", burnShare.toString(), "% 分红:", dividendShare.toString(), "%");
    
    // 2. 获取自动创建的交易对地址
    let pairAddress = await mockFactory.getPair(await tokenA.getAddress(), await mockWBNB.getAddress());
    console.log("自动创建的交易对地址:", pairAddress);
    
    if (pairAddress === ethers.ZeroAddress) {
      console.log("交易对不存在，手动创建...");
      await tokenA.createAndSetPair();
      pairAddress = await mockFactory.getPair(await tokenA.getAddress(), await mockWBNB.getAddress());
      console.log("手动创建的交易对地址:", pairAddress);
    }
    
    // 3. 检查交易对状态
    const isPair = await tokenA.isPair(pairAddress);
    const isTaxExempt = await tokenA.taxExempt(pairAddress);
    console.log("交易对标记:", isPair);
    console.log("交易对免税:", isTaxExempt);
    
    // 4. 给用户代币
    await tokenA.transfer(await user1.getAddress(), ethers.parseEther("10000"));
    console.log("用户获得代币:", ethers.formatEther(await tokenA.balanceOf(await user1.getAddress())));
    
    // 5. 检查用户是否免税
    const userTaxExempt = await tokenA.taxExempt(await user1.getAddress());
    console.log("用户免税状态:", userTaxExempt);
    
    // 6. 执行转账到交易对
    const transferAmount = ethers.parseEther("1000");
    const initialMarketingBalance = await tokenA.balanceOf(await marketingWallet.getAddress());
    const initialTotalSupply = await tokenA.totalSupply();
    const initialContractBalance = await tokenA.balanceOf(await tokenA.getAddress());
    
    console.log("转账前状态:");
    console.log("  营销钱包余额:", ethers.formatEther(initialMarketingBalance));
    console.log("  总供应量:", ethers.formatEther(initialTotalSupply));
    console.log("  合约余额:", ethers.formatEther(initialContractBalance));
    
    // 执行转账
    const tx = await tokenA.connect(user1).transfer(pairAddress, transferAmount);
    const receipt = await tx.wait();
    console.log("转账交易Gas:", receipt.gasUsed.toString());
    
    // 检查转账后状态
    const finalMarketingBalance = await tokenA.balanceOf(await marketingWallet.getAddress());
    const finalTotalSupply = await tokenA.totalSupply();
    const finalContractBalance = await tokenA.balanceOf(await tokenA.getAddress());
    const finalPairBalance = await tokenA.balanceOf(pairAddress);
    
    console.log("转账后状态:");
    console.log("  营销钱包余额:", ethers.formatEther(finalMarketingBalance));
    console.log("  总供应量:", ethers.formatEther(finalTotalSupply));
    console.log("  合约余额:", ethers.formatEther(finalContractBalance));
    console.log("  交易对余额:", ethers.formatEther(finalPairBalance));
    
    // 计算变化
    const marketingIncrease = finalMarketingBalance - initialMarketingBalance;
    const supplyDecrease = initialTotalSupply - finalTotalSupply;
    const contractIncrease = finalContractBalance - initialContractBalance;
    
    console.log("变化:");
    console.log("  营销增加:", ethers.formatEther(marketingIncrease));
    console.log("  供应减少:", ethers.formatEther(supplyDecrease));
    console.log("  合约增加:", ethers.formatEther(contractIncrease));
    console.log("  交易对收到:", ethers.formatEther(finalPairBalance));
    
    // 检查事件
    const events = receipt.logs;
    console.log("事件数量:", events.length);
    
    for (const log of events) {
      try {
        const parsed = tokenA.interface.parseLog(log);
        console.log("事件:", parsed.name, parsed.args);
      } catch (e) {
        // 忽略无法解析的事件
      }
    }
    
    // 预期税收
    const expectedTax = transferAmount * BigInt(3) / BigInt(100);
    const expectedReceived = transferAmount - expectedTax;
    
    console.log("预期计算:");
    console.log("  预期税收:", ethers.formatEther(expectedTax));
    console.log("  预期到达:", ethers.formatEther(expectedReceived));
    
    // 检查交易对实际收到金额
    if (finalPairBalance < transferAmount) {
      console.log("✓ 税收已收取，交易对收到减少的金额");
    } else {
      console.log("✗ 税收未收取，交易对收到全额");
    }
  });
});