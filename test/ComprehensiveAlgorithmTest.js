const { expect } = require("chai");
const { ethers } = require("hardhat");

describe("🚀 全面的实时线性释放算法测试", function () {
    
    let miningContract;
    let owner, user1, user2;
    let tokenA, tokenB, nftContract, mockUSDT;
    
    // 测试用的不同释放周期
    const TEST_DURATIONS = {
        SHORT: 7 * 24 * 3600,    // 7天
        MEDIUM: 30 * 24 * 3600,  // 30天
        LONG: 180 * 24 * 3600,   // 180天
        EXTENDED: 365 * 24 * 3600 // 365天
    };
    
    beforeEach(async function () {
        [owner, user1, user2] = await ethers.getSigners();
        
        // 为了测试，我们创建一个简化的算法验证函数
        // 实际测试将使用纯数学验证，无需部署完整合约
    });

    describe("📊 核心算法数学验证", function () {
        
        // 核心算法实现 - 与合约算法完全一致
        function calculateReleasableAmount(totalReleaseValue, releasedValue, startTime, currentTime, releaseDuration) {
            if (releasedValue >= totalReleaseValue) {
                return 0n;
            }
            
            let totalTimeElapsed = BigInt(currentTime - startTime);
            
            const maxDuration = BigInt(releaseDuration);
            if (totalTimeElapsed > maxDuration) {
                totalTimeElapsed = maxDuration;
            }
            
            const totalShouldHaveReleased = (totalReleaseValue * totalTimeElapsed) / maxDuration;
            
            if (totalShouldHaveReleased > releasedValue) {
                return totalShouldHaveReleased - releasedValue;
            }
            
            return 0n;
        }

        it("✅ 验证不同释放周期的算法正确性", function () {
            const startTime = 1000000;
            const totalValue = 1000n * (10n ** 18n); // 1000 ETH
            
            Object.entries(TEST_DURATIONS).forEach(([name, duration]) => {
                console.log(`\\n    🔍 测试${name}周期 (${duration / (24 * 3600)}天):`);
                
                // 测试关键时间点
                const testPoints = [0, 0.25, 0.5, 0.75, 1.0, 1.5]; // 0%, 25%, 50%, 75%, 100%, 150%
                
                testPoints.forEach(ratio => {
                    const currentTime = startTime + Math.floor(duration * ratio);
                    const claimable = calculateReleasableAmount(
                        totalValue, 0n, startTime, currentTime, duration
                    );
                    
                    const expectedRatio = Math.min(ratio, 1.0); // 最大100%
                    const expectedAmount = totalValue * BigInt(Math.floor(expectedRatio * 1000)) / 1000n;
                    
                    expect(claimable).to.be.closeTo(expectedAmount, totalValue / 1000n);
                    
                    const percentage = Number(claimable * 100n / totalValue);
                    console.log(`       ${(ratio * 100).toFixed(0).padStart(3)}%时间: 可领取${percentage.toFixed(1)}%`);
                });
            });
        });

        it("✅ 验证分批领取在不同周期下的一致性", function () {
            const startTime = 1000000;
            const totalValue = 1000n * (10n ** 18n);
            
            Object.entries(TEST_DURATIONS).forEach(([name, duration]) => {
                console.log(`\\n    🔄 测试${name}周期分批领取:`);
                
                let totalClaimed = 0n;
                const claimTimes = [0.2, 0.4, 0.6, 0.8, 1.0]; // 20%, 40%, 60%, 80%, 100%
                
                claimTimes.forEach((ratio, index) => {
                    const currentTime = startTime + Math.floor(duration * ratio);
                    const claimable = calculateReleasableAmount(
                        totalValue, totalClaimed, startTime, currentTime, duration
                    );
                    totalClaimed += claimable;
                    
                    console.log(`       第${index + 1}次领取(${(ratio * 100)}%): ${Number(claimable / (10n ** 18n))}ETH, 累计${Number(totalClaimed / (10n ** 18n))}ETH`);
                });
                
                // 验证分批领取总量与一次性计算结果一致
                const finalTime = startTime + duration;
                const directTotal = calculateReleasableAmount(
                    totalValue, 0n, startTime, finalTime, duration
                );
                
                expect(totalClaimed).to.be.closeTo(directTotal, totalValue / 1000n);
                console.log(`       ✓ 分批总计: ${Number(totalClaimed / (10n ** 18n))}ETH, 直接计算: ${Number(directTotal / (10n ** 18n))}ETH`);
            });
        });

        it("✅ 验证极端时间条件下的算法稳定性", function () {
            const totalValue = 1000n * (10n ** 18n);
            const startTime = 1000000;
            const duration = TEST_DURATIONS.MEDIUM;
            
            console.log(`\\n    ⚡ 极端条件测试:`);
            
            // 测试时间为0
            let claimable = calculateReleasableAmount(totalValue, 0n, startTime, startTime, duration);
            expect(claimable).to.equal(0n);
            console.log(`       时间为0: ${Number(claimable)} (应为0) ✓`);
            
            // 测试非常小的时间差
            const tinyTime = startTime + 1; // 1秒后
            claimable = calculateReleasableAmount(totalValue, 0n, startTime, tinyTime, duration);
            const expectedTiny = totalValue / BigInt(duration);
            expect(claimable).to.be.closeTo(expectedTiny, expectedTiny / 10n);
            console.log(`       1秒后: ${Number(claimable / (10n ** 15n))}mETH ✓`);
            
            // 测试超长时间
            const longTime = startTime + duration * 10; // 10倍周期后
            claimable = calculateReleasableAmount(totalValue, 0n, startTime, longTime, duration);
            expect(claimable).to.equal(totalValue);
            console.log(`       10倍周期后: ${Number(claimable / (10n ** 18n))}ETH (应为1000) ✓`);
            
            // 测试已领取超额情况
            const overClaimed = totalValue + (100n * (10n ** 18n)); // 超额100ETH
            claimable = calculateReleasableAmount(totalValue, overClaimed, startTime, longTime, duration);
            expect(claimable).to.equal(0n);
            console.log(`       超额领取后: ${Number(claimable)} (应为0) ✓`);
        });

        it("✅ 验证不同价值规模下的精度保持", function () {
            const startTime = 1000000;
            const duration = TEST_DURATIONS.LONG;
            const halfTime = startTime + duration / 2;
            
            console.log(`\\n    💰 不同价值规模精度测试:`);
            
            const testValues = [
                { amount: 1n * (10n ** 18n), desc: "1 ETH" },
                { amount: 1000n * (10n ** 18n), desc: "1,000 ETH" },
                { amount: 1000000n * (10n ** 18n), desc: "1,000,000 ETH" },
                { amount: 1n * (10n ** 6n), desc: "1 USDT" },
                { amount: 1000000n * (10n ** 6n), desc: "1,000,000 USDT" }
            ];
            
            testValues.forEach(({ amount, desc }) => {
                const claimable = calculateReleasableAmount(amount, 0n, startTime, halfTime, duration);
                const expectedHalf = amount / 2n;
                const precision = amount / 10000n; // 0.01%精度
                
                expect(claimable).to.be.closeTo(expectedHalf, precision);
                
                const actualPercentage = Number(claimable * 100n / amount);
                console.log(`       ${desc}: ${actualPercentage.toFixed(2)}% (期望50%) ✓`);
            });
        });

        it("✅ 验证高频领取操作的数学一致性", function () {
            const startTime = 1000000;
            const totalValue = 1000n * (10n ** 18n);
            const duration = TEST_DURATIONS.SHORT; // 7天，便于高频测试
            
            console.log(`\\n    ⚡ 高频领取测试 (7天周期，每小时领取一次):`);
            
            let totalClaimed = 0n;
            const hoursInDuration = duration / 3600; // 总小时数
            const claimInterval = 3600; // 每小时领取一次
            
            for (let hour = 1; hour <= Math.min(hoursInDuration, 168); hour++) { // 最多测试168小时（7天）
                const currentTime = startTime + hour * claimInterval;
                const claimable = calculateReleasableAmount(
                    totalValue, totalClaimed, startTime, currentTime, duration
                );
                totalClaimed += claimable;
                
                if (hour % 24 === 0) { // 每天报告一次
                    const dayNum = hour / 24;
                    const percentage = Number(totalClaimed * 100n / totalValue);
                    console.log(`       第${dayNum}天结束: 累计领取${percentage.toFixed(2)}% (期望${(dayNum / 7 * 100).toFixed(2)}%)`);
                }
            }
            
            // 验证最终累计与直接计算的一致性
            const finalTime = startTime + duration;
            const directTotal = calculateReleasableAmount(totalValue, 0n, startTime, finalTime, duration);
            const difference = totalClaimed > directTotal ? totalClaimed - directTotal : directTotal - totalClaimed;
            const tolerance = totalValue / 10000n; // 0.01%容差
            
            expect(difference).to.be.at.most(tolerance);
            console.log(`       ✓ 高频领取总计: ${Number(totalClaimed / (10n ** 18n))}ETH`);
            console.log(`       ✓ 直接计算结果: ${Number(directTotal / (10n ** 18n))}ETH`);
            console.log(`       ✓ 差异: ${Number(difference / (10n ** 15n))}mETH (容差内)`);
        });
    });

    describe("🔧 可配置参数测试", function () {
        
        function calculateReleasableAmount(totalReleaseValue, releasedValue, startTime, currentTime, releaseDuration) {
            if (releasedValue >= totalReleaseValue) return 0n;
            
            let totalTimeElapsed = BigInt(currentTime - startTime);
            const maxDuration = BigInt(releaseDuration);
            if (totalTimeElapsed > maxDuration) totalTimeElapsed = maxDuration;
            
            const totalShouldHaveReleased = (totalReleaseValue * totalTimeElapsed) / maxDuration;
            return totalShouldHaveReleased > releasedValue ? totalShouldHaveReleased - releasedValue : 0n;
        }

        it("✅ 验证不同释放周期参数的灵活性", function () {
            const startTime = 1000000;
            const totalValue = 1000n * (10n ** 18n);
            
            console.log(`\\n    ⚙️  可配置周期测试:`);
            
            // 测试各种周期配置
            const customDurations = [
                { days: 1, seconds: 1 * 24 * 3600, desc: "1天" },
                { days: 7, seconds: 7 * 24 * 3600, desc: "1周" },
                { days: 30, seconds: 30 * 24 * 3600, desc: "1月" },
                { days: 90, seconds: 90 * 24 * 3600, desc: "1季度" },
                { days: 180, seconds: 180 * 24 * 3600, desc: "半年" },
                { days: 365, seconds: 365 * 24 * 3600, desc: "1年" }
            ];
            
            customDurations.forEach(({ days, seconds, desc }) => {
                // 测试50%时间点
                const halfTime = startTime + seconds / 2;
                const claimable = calculateReleasableAmount(totalValue, 0n, startTime, halfTime, seconds);
                const percentage = Number(claimable * 100n / totalValue);
                
                expect(percentage).to.be.closeTo(50, 0.1); // 50%±0.1%
                console.log(`       ${desc}周期: 50%时间可领取${percentage.toFixed(1)}% ✓`);
                
                // 测试每日释放率
                const oneDayClaimable = calculateReleasableAmount(totalValue, 0n, startTime, startTime + 24 * 3600, seconds);
                const dailyRate = Number(oneDayClaimable * 100n / totalValue);
                const expectedDailyRate = 100 / days;
                
                expect(dailyRate).to.be.closeTo(expectedDailyRate, Math.max(expectedDailyRate * 0.05, 1)); // 5%误差或至少1%
                console.log(`       ${desc}周期: 每日释放率${dailyRate.toFixed(3)}% (期望${expectedDailyRate.toFixed(3)}%) ✓`);
            });
        });

        it("✅ 验证参数边界值的安全性", function () {
            const startTime = 1000000;
            const totalValue = 1000n * (10n ** 18n);
            
            console.log(`\\n    🛡️  参数边界安全性测试:`);
            
            // 测试最小周期（1天）
            const minDuration = 24 * 3600;
            const maxTime = startTime + minDuration;
            let claimable = calculateReleasableAmount(totalValue, 0n, startTime, maxTime, minDuration);
            expect(claimable).to.equal(totalValue);
            console.log(`       最小周期(1天): 100%时间可领取100% ✓`);
            
            // 测试最大周期（365天）
            const maxDuration = 365 * 24 * 3600;
            const oneDay = startTime + 24 * 3600;
            claimable = calculateReleasableAmount(totalValue, 0n, startTime, oneDay, maxDuration);
            const expectedDaily = totalValue / 365n;
            expect(claimable).to.be.closeTo(expectedDaily, expectedDaily / 100n);
            console.log(`       最大周期(365天): 1天释放率${Number(claimable * 100n / totalValue).toFixed(3)}% ✓`);
            
            // 测试周期变化对现有记录的影响（模拟）
            const oldDuration = 180 * 24 * 3600;
            const newDuration = 90 * 24 * 3600;
            const testTime = startTime + 45 * 24 * 3600; // 45天后
            
            const oldSystemClaimable = calculateReleasableAmount(totalValue, 0n, startTime, testTime, oldDuration);
            const newSystemClaimable = calculateReleasableAmount(totalValue, 0n, startTime, testTime, newDuration);
            
            console.log(`       参数变更影响: 旧系统45天释放${Number(oldSystemClaimable * 100n / totalValue).toFixed(1)}%, 新系统释放${Number(newSystemClaimable * 100n / totalValue).toFixed(1)}%`);
        });
    });

    describe("🚀 性能和复杂度验证", function () {
        
        function calculateReleasableAmount(totalReleaseValue, releasedValue, startTime, currentTime, releaseDuration) {
            if (releasedValue >= totalReleaseValue) return 0n;
            let totalTimeElapsed = BigInt(currentTime - startTime);
            const maxDuration = BigInt(releaseDuration);
            if (totalTimeElapsed > maxDuration) totalTimeElapsed = maxDuration;
            const totalShouldHaveReleased = (totalReleaseValue * totalTimeElapsed) / maxDuration;
            return totalShouldHaveReleased > releasedValue ? totalShouldHaveReleased - releasedValue : 0n;
        }

        it("✅ 验证算法时间复杂度为O(1)", function () {
            const startTime = 1000000;
            const currentTime = startTime + 90 * 24 * 3600; // 90天后
            const duration = 180 * 24 * 3600;
            
            console.log(`\\n    ⚡ 算法性能测试:`);
            
            // 测试不同数量级的数值
            const testSizes = [
                { value: 1n * (10n ** 18n), desc: "1 ETH" },
                { value: 1000n * (10n ** 18n), desc: "1K ETH" },
                { value: 1000000n * (10n ** 18n), desc: "1M ETH" },
                { value: 1000000000n * (10n ** 18n), desc: "1B ETH" }
            ];
            
            testSizes.forEach(({ value, desc }) => {
                const startPerf = process.hrtime.bigint();
                
                // 执行1000次计算
                for (let i = 0; i < 1000; i++) {
                    calculateReleasableAmount(value, 0n, startTime, currentTime, duration);
                }
                
                const endPerf = process.hrtime.bigint();
                const avgTimeNs = Number(endPerf - startPerf) / 1000;
                const avgTimeMicros = avgTimeNs / 1000;
                
                console.log(`       ${desc}: 平均${avgTimeMicros.toFixed(2)}μs/次 (1000次测试)`);
                
                // 验证计算时间应该很短且相对稳定
                expect(avgTimeMicros).to.be.below(100); // 应该小于100微秒
            });
        });

        it("✅ 验证大规模并发计算的稳定性", function () {
            const startTime = 1000000;
            const totalValue = 1000n * (10n ** 18n);
            const duration = 180 * 24 * 3600;
            
            console.log(`\\n    🔄 大规模并发计算测试:`);
            
            // 模拟1000个不同用户在不同时间点的计算
            const users = Array.from({ length: 1000 }, (_, i) => ({
                claimed: totalValue * BigInt(i) / 2000n, // 不同的已领取金额
                currentTime: startTime + (i + 1) * duration / 1000 // 不同的时间点
            }));
            
            const startTime_test = process.hrtime.bigint();
            
            const results = users.map(({ claimed, currentTime }) => 
                calculateReleasableAmount(totalValue, claimed, startTime, currentTime, duration)
            );
            
            const endTime_test = process.hrtime.bigint();
            const totalTime = Number(endTime_test - startTime_test) / 1000000; // 转换为毫秒
            
            // 验证所有结果都是合理的
            results.forEach((result, i) => {
                expect(result).to.be.at.least(0n);
                expect(result).to.be.at.most(totalValue);
            });
            
            console.log(`       1000次并发计算总耗时: ${totalTime.toFixed(2)}ms`);
            console.log(`       平均每次计算: ${(totalTime / 1000).toFixed(3)}ms`);
            console.log(`       ✓ 所有计算结果均在合理范围内`);
            
            // 性能要求：1000次计算应在100ms内完成
            expect(totalTime).to.be.below(100);
        });
    });

    describe("📈 实际业务场景验证", function () {
        
        function calculateReleasableAmount(totalReleaseValue, releasedValue, startTime, currentTime, releaseDuration) {
            if (releasedValue >= totalReleaseValue) return 0n;
            let totalTimeElapsed = BigInt(currentTime - startTime);
            const maxDuration = BigInt(releaseDuration);
            if (totalTimeElapsed > maxDuration) totalTimeElapsed = maxDuration;
            const totalShouldHaveReleased = (totalReleaseValue * totalTimeElapsed) / maxDuration;
            return totalShouldHaveReleased > releasedValue ? totalShouldHaveReleased - releasedValue : 0n;
        }

        it("✅ 模拟真实用户行为模式", function () {
            const startTime = 1000000;
            const totalValue = 10000n * (10n ** 6n); // 10,000 USDT
            const duration = 180 * 24 * 3600; // 180天
            
            console.log(`\\n    👥 真实用户行为模拟:`);
            
            // 模拟不同类型的用户行为
            const userTypes = [
                {
                    name: "保守型用户",
                    claimSchedule: [30, 60, 90, 120, 150, 180], // 每月领取
                    desc: "每月领取一次"
                },
                {
                    name: "积极型用户", 
                    claimSchedule: [7, 14, 21, 28, 35, 42, 49, 56, 63, 70, 77, 84, 91, 98, 105, 112, 119, 126, 133, 140, 147, 154, 161, 168, 175, 180], // 每周领取
                    desc: "每周领取一次"
                },
                {
                    name: "频繁型用户",
                    claimSchedule: Array.from({length: 180}, (_, i) => i + 1), // 每天领取
                    desc: "每天领取一次"
                }
            ];
            
            userTypes.forEach(({ name, claimSchedule, desc }) => {
                console.log(`\\n       🧑 ${name} (${desc}):`);
                
                let claimed = 0n;
                let lastClaimTime = startTime;
                
                claimSchedule.forEach((day, index) => {
                    const claimTime = startTime + day * 24 * 3600;
                    const claimable = calculateReleasableAmount(totalValue, claimed, startTime, claimTime, duration);
                    claimed += claimable;
                    
                    if (index < 5 || index % 10 === 0 || day === 180) { // 只显示前5次和每10次
                        const percentage = Number(claimed * 100n / totalValue);
                        const claimAmount = Number(claimable / (10n ** 6n));
                        console.log(`         第${day}天: 领取${claimAmount.toFixed(1)}USDT, 累计${percentage.toFixed(1)}%`);
                    }
                });
                
                // 验证最终领取接近100%
                const finalPercentage = Number(claimed * 100n / totalValue);
                expect(finalPercentage).to.be.above(99.9);
                console.log(`         ✓ 最终领取: ${finalPercentage.toFixed(2)}%`);
            });
        });

        it("✅ 验证多重奖励叠加场景", function () {
            const startTime = 1000000;
            const duration = 180 * 24 * 3600;
            
            console.log(`\\n    💎 多重奖励叠加场景:`);
            
            // 模拟用户有多个挖矿记录
            const miningRecords = [
                { value: 1000n * (10n ** 6n), startTime: startTime, desc: "第1次挖矿: 1000 USDT" },
                { value: 2000n * (10n ** 6n), startTime: startTime + 30 * 24 * 3600, desc: "第2次挖矿: 2000 USDT (30天后)" },
                { value: 3000n * (10n ** 6n), startTime: startTime + 60 * 24 * 3600, desc: "第3次挖矿: 3000 USDT (60天后)" }
            ];
            
            // 测试不同时间点的总可领取金额
            const testTimes = [90, 120, 150, 180, 240]; // 90, 120, 150, 180, 240天
            
            testTimes.forEach(day => {
                const currentTime = startTime + day * 24 * 3600;
                let totalClaimable = 0n;
                let alreadyClaimed = 0n; // 假设之前没有领取过
                
                console.log(`\\n       📅 第${day}天状态:`);
                
                miningRecords.forEach(({ value, startTime: recordStart, desc }, index) => {
                    if (currentTime >= recordStart) {
                        const claimable = calculateReleasableAmount(value, 0n, recordStart, currentTime, duration);
                        totalClaimable += claimable;
                        
                        const daysActive = (currentTime - recordStart) / (24 * 3600);
                        const percentage = Number(claimable * 100n / value);
                        
                        console.log(`         ${desc}: ${percentage.toFixed(1)}% (活跃${daysActive}天)`);
                    } else {
                        console.log(`         ${desc}: 尚未开始`);
                    }
                });
                
                const totalValue = miningRecords.reduce((sum, record) => sum + record.value, 0n);
                const overallPercentage = Number(totalClaimable * 100n / totalValue);
                console.log(`         💰 总可领取: ${Number(totalClaimable / (10n ** 6n))}USDT (${overallPercentage.toFixed(1)}%)`);
            });
        });
    });
});