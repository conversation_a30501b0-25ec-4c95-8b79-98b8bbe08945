const { expect } = require("chai");
const { ethers } = require("hardhat");

describe("精度保证机制测试", function () {
  let tokenA_Enhanced, precisionTracker, minerNFT;
  let mockWBNB, mockFactory, mockRouter;
  let owner, marketingWallet, user1, user2, user3;
  
  beforeEach(async function () {
    [owner, marketingWallet, user1, user2, user3] = await ethers.getSigners();
    
    // 部署模拟环境
    const MockWBNB = await ethers.getContractFactory("MockWBNB");
    mockWBNB = await MockWBNB.deploy();
    await mockWBNB.waitForDeployment();
    
    const MockPancakeFactory = await ethers.getContractFactory("MockPancakeFactory");
    mockFactory = await MockPancakeFactory.deploy();
    await mockFactory.waitForDeployment();
    
    const MockPancakeRouter = await ethers.getContractFactory("MockPancakeRouter");
    mockRouter = await MockPancakeRouter.deploy(
      await mockFactory.getAddress(),
      await mockWBNB.getAddress()
    );
    await mockRouter.waitForDeployment();
    
    // 部署精度跟踪器
    const TaxPrecisionTracker = await ethers.getContractFactory("TaxPrecisionTracker");
    precisionTracker = await TaxPrecisionTracker.deploy();
    await precisionTracker.waitForDeployment();
    
    // 部署增强版TokenA
    const TokenA_Enhanced = await ethers.getContractFactory("TokenA_Enhanced");
    tokenA_Enhanced = await TokenA_Enhanced.deploy(
      await marketingWallet.getAddress(),
      await mockRouter.getAddress(),
      await mockWBNB.getAddress()
    );
    await tokenA_Enhanced.waitForDeployment();
    
    // 设置精度跟踪器
    await tokenA_Enhanced.setPrecisionTracker(await precisionTracker.getAddress());
    
    // 部署NFT合约
    const MinerNFT = await ethers.getContractFactory("MinerNFT");
    minerNFT = await MinerNFT.deploy("MinerNFT", "MNFT", await tokenA_Enhanced.getAddress());
    await minerNFT.waitForDeployment();
    
    // 配置关系
    await tokenA_Enhanced.setNftContract(await minerNFT.getAddress());
    await tokenA_Enhanced.transfer(await minerNFT.getAddress(), ethers.parseEther("21000000"));
    
    console.log("=== 精度保证测试环境初始化完成 ===");
  });

  describe("1. 精度跟踪机制验证", function () {
    it("应该准确跟踪税收计算精度", async function () {
      console.log("=== 精度跟踪机制验证 ===");
      
      const pairAddress = await createTradingPair();
      
      // 禁用自动分红以简化测试
      await tokenA_Enhanced.setAutoDividendConfig(500000, 10, ethers.parseEther("100"), false);
      
      const transferAmount = ethers.parseEther("10000");
      await tokenA_Enhanced.transfer(await user1.getAddress(), transferAmount);
      
      // 执行转账
      console.log("执行测试转账...");
      await tokenA_Enhanced.connect(user1).transfer(pairAddress, transferAmount);
      
      // 检查精度跟踪数据
      const stats = await precisionTracker.getPrecisionStats(await tokenA_Enhanced.getAddress());
      console.log("精度统计:");
      console.log("  总交易数:", stats[0].toString());
      console.log("  总转账金额:", ethers.formatEther(stats[1]));
      console.log("  累积误差率:", stats[2].toString(), "basis points");
      console.log("  最大单次误差:", ethers.formatEther(stats[3]));
      console.log("  误差事件数:", stats[4].toString());
      
      // 验证精度跟踪
      expect(stats[0]).to.equal(1); // 1笔交易
      expect(stats[1]).to.equal(transferAmount); // 转账金额匹配
      expect(stats[2]).to.be.lte(100); // 误差率小于1%
      
      console.log("✓ 精度跟踪机制正常工作");
    });
    
    it("应该跟踪分配精度", async function () {
      console.log("=== 分配精度跟踪验证 ===");
      
      const pairAddress = await createTradingPair();
      
      // 禁用自动分红
      await tokenA_Enhanced.setAutoDividendConfig(500000, 10, ethers.parseEther("100"), false);
      
      const transferAmount = ethers.parseEther("12345.67891"); // 使用不规则数值
      await tokenA_Enhanced.transfer(await user1.getAddress(), transferAmount);
      
      console.log("执行不规则金额转账:", ethers.formatEther(transferAmount));
      await tokenA_Enhanced.connect(user1).transfer(pairAddress, transferAmount);
      
      // 检查分配精度
      const distStats = await precisionTracker.getDistributionStats(await tokenA_Enhanced.getAddress());
      console.log("分配精度统计:");
      console.log("  营销分配误差率:", distStats[0].toString(), "basis points");
      console.log("  销毁分配误差率:", distStats[1].toString(), "basis points");
      console.log("  分红分配误差率:", distStats[2].toString(), "basis points");
      
      // 验证分配精度
      expect(distStats[0]).to.be.lte(100); // 营销分配误差 < 1%
      expect(distStats[1]).to.be.lte(100); // 销毁分配误差 < 1%
      expect(distStats[2]).to.be.lte(100); // 分红分配误差 < 1%
      
      console.log("✓ 分配精度跟踪正常");
    });
  });

  describe("2. 长期运行精度测试", function () {
    it("应该维持长期运行的精度稳定性", async function () {
      console.log("=== 长期运行精度稳定性测试 ===");
      
      const pairAddress = await createTradingPair();
      
      // 禁用自动分红
      await tokenA_Enhanced.setAutoDividendConfig(500000, 10, ethers.parseEther("100"), false);
      
      console.log("执行100次随机金额转账...");
      
      // 生成随机金额数组
      const transferAmounts = [];
      for (let i = 0; i < 50; i++) { // 减少到50次以节省时间
        const randomAmount = ethers.parseEther((Math.random() * 1000 + 100).toString());
        transferAmounts.push(randomAmount);
      }
      
      // 执行多次转账
      for (let i = 0; i < transferAmounts.length; i++) {
        const amount = transferAmounts[i];
        await tokenA_Enhanced.transfer(await user1.getAddress(), amount);
        await tokenA_Enhanced.connect(user1).transfer(pairAddress, amount);
        
        // 每10次检查一次状态
        if ((i + 1) % 10 === 0) {
          const stats = await precisionTracker.getPrecisionStats(await tokenA_Enhanced.getAddress());
          const errorRate = stats[2]; // 累积误差率
          
          console.log(`第${i+1}次交易后累积误差率: ${errorRate} basis points`);
          
          // 验证误差率保持在合理范围内
          expect(errorRate).to.be.lte(500); // 5%
        }
      }
      
      // 最终精度检查
      const finalStats = await precisionTracker.getPrecisionStats(await tokenA_Enhanced.getAddress());
      const healthScore = await precisionTracker.getPrecisionHealthScore(await tokenA_Enhanced.getAddress());
      const isHealthy = await precisionTracker.isPrecisionHealthy(await tokenA_Enhanced.getAddress());
      
      console.log("\n最终精度状态:");
      console.log("  总交易数:", finalStats[0].toString());
      console.log("  累积误差率:", finalStats[2].toString(), "basis points");
      console.log("  健康度评分:", healthScore.toString());
      console.log("  精度健康:", isHealthy);
      
      // 验证长期精度
      expect(finalStats[2]).to.be.lte(1000); // 累积误差率 < 10%
      expect(healthScore).to.be.gte(70); // 健康度 >= 70分
      expect(isHealthy).to.be.true;
      
      console.log("✓ 长期运行精度稳定性良好");
    });
    
    it("应该测试累积误差校正机制", async function () {
      console.log("=== 累积误差校正机制测试 ===");
      
      const pairAddress = await createTradingPair();
      
      // 启用自动校正
      await tokenA_Enhanced.setPrecisionConfig(
        true,                        // enabled
        ethers.parseEther("0.001"),   // maxSingleErrorWei
        500,                         // maxCumulativeErrorRate (5%)
        ethers.parseEther("0.01"),   // correctionThreshold
        true                         // autoCorrectionEnabled
      );
      
      // 禁用自动分红
      await tokenA_Enhanced.setAutoDividendConfig(500000, 10, ethers.parseEther("100"), false);
      
      console.log("执行多次小额转账以累积舍入误差...");
      
      // 使用容易产生舍入误差的金额
      const problematicAmounts = [
        ethers.parseEther("1.001"),
        ethers.parseEther("3.333"),
        ethers.parseEther("7.777"),
        ethers.parseEther("11.111"),
        ethers.parseEther("33.333")
      ];
      
      for (let i = 0; i < problematicAmounts.length; i++) {
        const amount = problematicAmounts[i];
        await tokenA_Enhanced.transfer(await user1.getAddress(), amount);
        
        // 检查校正前状态
        const beforeStatus = await tokenA_Enhanced.getPrecisionStatus();
        console.log(`\n第${i+1}次转账前累积误差: ${beforeStatus[1].toString()} wei`);
        
        // 执行转账
        const tx = await tokenA_Enhanced.connect(user1).transfer(pairAddress, amount);
        const receipt = await tx.wait();
        
        // 检查是否有校正事件
        for (const log of receipt.logs) {
          try {
            const parsed = tokenA_Enhanced.interface.parseLog(log);
            if (parsed.name === "PrecisionCorrectionApplied") {
              console.log(`精度校正应用: ${parsed.args.distributionType}, 校正值: ${parsed.args.appliedCorrection}`);
            }
          } catch (e) {
            // 忽略其他事件
          }
        }
        
        // 检查校正后状态
        const afterStatus = await tokenA_Enhanced.getPrecisionStatus();
        console.log(`第${i+1}次转账后累积误差: ${afterStatus[1].toString()} wei`);
      }
      
      // 最终精度状态
      const finalStatus = await tokenA_Enhanced.getPrecisionStatus();
      console.log("\n最终精度状态:");
      console.log("  精度跟踪启用:", finalStatus[0]);
      console.log("  累积误差:", finalStatus[1].toString(), "wei");
      console.log("  总校正金额:", ethers.formatEther(finalStatus[2]));
      console.log("  健康度评分:", finalStatus[3].toString());
      console.log("  精度健康:", finalStatus[4]);
      
      // 验证校正机制
      expect(finalStatus[4]).to.be.true; // 应该保持健康
      
      console.log("✓ 累积误差校正机制正常工作");
    });
  });

  describe("3. 极端情况精度测试", function () {
    it("应该处理极小金额的精度", async function () {
      console.log("=== 极小金额精度测试 ===");
      
      const pairAddress = await createTradingPair();
      
      // 禁用自动分红
      await tokenA_Enhanced.setAutoDividendConfig(500000, 10, ethers.parseEther("100"), false);
      
      // 测试极小金额
      const smallAmounts = [
        BigInt(100),        // 100 wei
        BigInt(1000),       // 1000 wei
        BigInt(33333),      // 特殊数值
        BigInt(999999),     // 接近1e6
        ethers.parseEther("0.000001") // 1 szabo
      ];
      
      console.log("测试极小金额的税收精度...");
      
      for (let i = 0; i < smallAmounts.length; i++) {
        const amount = smallAmounts[i];
        
        // 给用户足够的代币
        await tokenA_Enhanced.transfer(await user1.getAddress(), ethers.parseEther("1"));
        
        console.log(`\n测试金额: ${amount} wei (${ethers.formatUnits(amount, "wei")} wei)`);
        
        // 记录转账前状态
        const beforeStats = await precisionTracker.getPrecisionStats(await tokenA_Enhanced.getAddress());
        
        // 执行转账
        try {
          await tokenA_Enhanced.connect(user1).transfer(pairAddress, amount);
          
          // 记录转账后状态
          const afterStats = await precisionTracker.getPrecisionStats(await tokenA_Enhanced.getAddress());
          
          if (afterStats[0] > beforeStats[0]) {
            console.log("  转账成功，精度跟踪更新");
            console.log("  新增误差率:", (afterStats[2] - beforeStats[2]).toString(), "basis points");
          } else {
            console.log("  转账成功，但金额太小无税收");
          }
        } catch (error) {
          console.log("  转账失败:", error.message.substring(0, 50) + "...");
        }
      }
      
      console.log("✓ 极小金额精度测试完成");
    });
    
    it("应该处理大金额的精度稳定性", async function () {
      console.log("=== 大金额精度稳定性测试 ===");
      
      const pairAddress = await createTradingPair();
      
      // 禁用自动分红
      await tokenA_Enhanced.setAutoDividendConfig(500000, 10, ethers.parseEther("100"), false);
      
      // 测试大金额
      const largeAmounts = [
        ethers.parseEther("100000"),    // 10万
        ethers.parseEther("1000000"),   // 100万
        ethers.parseEther("12345678.987654321") // 不规则大数
      ];
      
      console.log("测试大金额的税收精度...");
      
      for (let i = 0; i < largeAmounts.length; i++) {
        const amount = largeAmounts[i];
        
        // 给用户足够的代币
        await tokenA_Enhanced.transfer(await user1.getAddress(), amount);
        
        console.log(`\n测试金额: ${ethers.formatEther(amount)} ETH`);
        
        // 记录转账前状态
        const beforeStats = await precisionTracker.getPrecisionStats(await tokenA_Enhanced.getAddress());
        
        // 执行转账
        await tokenA_Enhanced.connect(user1).transfer(pairAddress, amount);
        
        // 记录转账后状态
        const afterStats = await precisionTracker.getPrecisionStats(await tokenA_Enhanced.getAddress());
        
        // 计算本次交易的精度
        const transactionError = afterStats[2] - beforeStats[2];
        console.log("  本次交易误差率:", transactionError.toString(), "basis points");
        
        // 验证大金额交易的精度
        expect(transactionError).to.be.lte(10); // 单次大金额交易误差 < 0.1%
      }
      
      console.log("✓ 大金额精度稳定性良好");
    });
  });

  describe("4. 精度健康监控测试", function () {
    it("应该检测和报告精度健康状态", async function () {
      console.log("=== 精度健康监控测试 ===");
      
      const pairAddress = await createTradingPair();
      
      // 启用严格的精度配置以触发告警
      await tokenA_Enhanced.setPrecisionConfig(
        true,                      // enabled
        BigInt(1),                 // maxSingleErrorWei - 极严格
        10,                        // maxCumulativeErrorRate - 0.1%
        ethers.parseEther("0.001"), // correctionThreshold
        false                      // 禁用自动校正以观察误差累积
      );
      
      // 禁用自动分红
      await tokenA_Enhanced.setAutoDividendConfig(500000, 10, ethers.parseEther("100"), false);
      
      console.log("执行多次转账以观察健康度变化...");
      
      // 执行多次转账
      for (let i = 0; i < 10; i++) {
        const amount = ethers.parseEther((100 + i * 10).toString());
        await tokenA_Enhanced.transfer(await user1.getAddress(), amount);
        
        const tx = await tokenA_Enhanced.connect(user1).transfer(pairAddress, amount);
        const receipt = await tx.wait();
        
        // 检查是否有健康告警事件
        for (const log of receipt.logs) {
          try {
            const parsed = tokenA_Enhanced.interface.parseLog(log);
            if (parsed.name === "PrecisionHealthAlert") {
              console.log(`健康告警: ${parsed.args.alertType}, 当前值: ${parsed.args.currentValue}, 阈值: ${parsed.args.threshold}`);
            }
          } catch (e) {
            // 忽略其他事件
          }
        }
        
        // 检查当前健康状态
        const status = await tokenA_Enhanced.getPrecisionStatus();
        const isHealthy = await precisionTracker.isPrecisionHealthy(await tokenA_Enhanced.getAddress());
        
        console.log(`第${i+1}次交易后 - 健康度: ${status[3]}, 健康状态: ${isHealthy}`);
      }
      
      // 最终健康状态检查
      const finalStatus = await tokenA_Enhanced.getPrecisionStatus();
      const detailedStats = await tokenA_Enhanced.getDetailedPrecisionStats();
      
      console.log("\n最终健康状态报告:");
      console.log("  精度跟踪启用:", finalStatus[0]);
      console.log("  累积误差:", finalStatus[1].toString(), "wei");
      console.log("  健康度评分:", finalStatus[3].toString());
      console.log("  健康状态:", finalStatus[4]);
      console.log("  总交易数:", detailedStats[0].toString());
      console.log("  累积误差率:", detailedStats[2].toString(), "basis points");
      console.log("  最大单次误差:", ethers.formatEther(detailedStats[3]));
      
      console.log("✓ 精度健康监控机制正常工作");
    });
    
    it("应该支持手动精度校正", async function () {
      console.log("=== 手动精度校正测试 ===");
      
      const pairAddress = await createTradingPair();
      
      // 禁用自动校正
      await tokenA_Enhanced.setPrecisionConfig(
        true,                        // enabled
        ethers.parseEther("0.001"),   // maxSingleErrorWei
        1000,                        // maxCumulativeErrorRate (10%)
        ethers.parseEther("10"),      // 高阈值，防止自动校正
        false                        // 禁用自动校正
      );
      
      // 禁用自动分红
      await tokenA_Enhanced.setAutoDividendConfig(500000, 10, ethers.parseEther("100"), false);
      
      // 执行一些转账以累积误差
      for (let i = 0; i < 5; i++) {
        const amount = ethers.parseEther("1.333"); // 容易产生舍入误差的金额
        await tokenA_Enhanced.transfer(await user1.getAddress(), amount);
        await tokenA_Enhanced.connect(user1).transfer(pairAddress, amount);
      }
      
      // 检查累积误差
      const beforeCorrection = await tokenA_Enhanced.getPrecisionStatus();
      console.log("校正前累积误差:", beforeCorrection[1].toString(), "wei");
      
      if (beforeCorrection[1] !== 0n) {
        console.log("执行手动精度校正...");
        
        // 执行手动校正
        const tx = await tokenA_Enhanced.manualPrecisionCorrection();
        const receipt = await tx.wait();
        
        // 检查校正事件
        for (const log of receipt.logs) {
          try {
            const parsed = tokenA_Enhanced.interface.parseLog(log);
            if (parsed.name === "PrecisionCorrectionApplied") {
              console.log(`手动校正应用: 校正类型=${parsed.args.distributionType}, 校正值=${parsed.args.appliedCorrection}`);
            }
          } catch (e) {
            // 忽略其他事件
          }
        }
        
        // 检查校正后状态
        const afterCorrection = await tokenA_Enhanced.getPrecisionStatus();
        console.log("校正后累积误差:", afterCorrection[1].toString(), "wei");
        console.log("总校正金额:", ethers.formatEther(afterCorrection[2]));
        
        // 验证校正效果
        expect(afterCorrection[1]).to.equal(0); // 累积误差应该被清零
        expect(afterCorrection[2]).to.be.gt(beforeCorrection[2]); // 总校正金额应该增加
        
        console.log("✓ 手动精度校正成功");
      } else {
        console.log("无累积误差需要校正");
      }
    });
  });

  // 辅助函数
  async function createTradingPair() {
    const liquidityTokenA = ethers.parseEther("50000");
    const liquidityWBNB = ethers.parseEther("50");
    
    await mockWBNB.deposit({ value: liquidityWBNB });
    await tokenA_Enhanced.approve(await mockRouter.getAddress(), liquidityTokenA);
    await mockWBNB.approve(await mockRouter.getAddress(), liquidityWBNB);
    
    await mockRouter.addLiquidity(
      await tokenA_Enhanced.getAddress(),
      await mockWBNB.getAddress(),
      liquidityTokenA,
      liquidityWBNB,
      0, 0,
      await owner.getAddress(),
      Math.floor(Date.now() / 1000) + 3600
    );
    
    const pairAddress = await mockFactory.getPair(await tokenA_Enhanced.getAddress(), await mockWBNB.getAddress());
    await tokenA_Enhanced.setPair(pairAddress, true);
    
    return pairAddress;
  }
});