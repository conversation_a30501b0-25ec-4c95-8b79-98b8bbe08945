const { expect } = require("chai");
const { ethers } = require("hardhat");
const { time } = require("@nomicfoundation/hardhat-network-helpers");

describe("TokenA 12期释放机制专项测试", function () {
  let tokenA, minerNFT;
  let owner, user1, user2, user3;
  
  const RELEASE_INTERVAL = 30 * 24 * 3600; // 30天
  const TOTAL_RELEASE_PERIODS = 12; // 12期
  const TOKENS_PER_NFT = ethers.parseEther("100000"); // 每个NFT 10万 TokenA
  
  // 12期释放比例 [3,3,3,5,5,5,10,10,10,10,10,26]
  const RELEASE_PERCENTAGES = [3, 3, 3, 5, 5, 5, 10, 10, 10, 10, 10, 26];
  
  beforeEach(async function () {
    const signers = await ethers.getSigners();
    [owner, user1, user2, user3] = signers.slice(0, 4);
    
    console.log(`=== TokenA 12期释放机制专项测试 ===`);
    
    // 部署TokenA (需要路由和WBNB地址)
    const MockWBNB = await ethers.getContractFactory("MockWBNB");
    const mockWBNB = await MockWBNB.deploy();
    
    const MockPancakeFactory = await ethers.getContractFactory("MockPancakeFactory");
    const mockFactory = await MockPancakeFactory.deploy();
    
    const MockPancakeRouter = await ethers.getContractFactory("MockPancakeRouter");
    const mockRouter = await MockPancakeRouter.deploy(
      await mockFactory.getAddress(),
      await mockWBNB.getAddress()
    );
    
    const TokenA = await ethers.getContractFactory("TokenA");
    tokenA = await TokenA.deploy(
      await owner.getAddress(), // 营销钱包
      await mockRouter.getAddress(),
      await mockWBNB.getAddress()
    );
    
    // 部署MinerNFT
    const MinerNFT = await ethers.getContractFactory("MinerNFT");
    minerNFT = await MinerNFT.deploy("MinerNFT", "MNFT", await tokenA.getAddress());
    
    // 设置TokenA合约中的NFT地址
    await tokenA.setNftContract(await minerNFT.getAddress());
    
    // 给MinerNFT合约转入足够的TokenA用于释放
    const totalTokensNeeded = TOKENS_PER_NFT * BigInt(100); // 为100个NFT准备TokenA
    await tokenA.transfer(await minerNFT.getAddress(), totalTokensNeeded);
    
    console.log(`TokenA合约部署完成: ${await tokenA.getAddress()}`);
    console.log(`MinerNFT合约部署完成: ${await minerNFT.getAddress()}`);
    console.log(`每个NFT内置TokenA: ${ethers.formatEther(TOKENS_PER_NFT)}`);
  });
  
  it("应该测试单个NFT的12期TokenA释放机制", async function () {
    console.log("=== 单个NFT的12期TokenA释放测试 ===");
    
    // 给user1铸造一个NFT
    await minerNFT.batchMintCount(await user1.getAddress(), 1);
    console.log("给user1铸造了1个NFT");
    
    // 获取NFT信息
    const nftInfo = await minerNFT.getNFTReleaseInfo(1);
    console.log(`NFT总TokenA: ${ethers.formatEther(nftInfo.totalTokens)}`);
    console.log(`当前可释放: ${ethers.formatEther(nftInfo.releasableNow)}`);
    
    // 获取释放计划
    const schedule = await minerNFT.getReleaseSchedule();
    console.log(`释放间隔: ${Number(schedule.interval) / (24*3600)} 天`);
    console.log(`总期数: ${schedule.totalPeriods}`);
    console.log(`释放比例: [${schedule.percentages.map(p => Number(p)).join(',')}]`);
    
    // 记录开始时间 (NFT释放开始时间是部署时间+30天)
    const nftReleaseStartTime = await minerNFT.getNFTReleaseStartTime(1);
    console.log(`NFT释放开始时间: ${nftReleaseStartTime}`);
    
    // 推进到释放开始时间
    await time.increaseTo(Number(nftReleaseStartTime));
    
    // 测试每一期的释放
    const releaseResults = [];
    
    for (let period = 1; period <= 12; period++) {
      console.log(`\\n--- 第${period}期TokenA释放测试 ---`);
      
      // 推进到该期的释放时间
      const targetTime = Number(nftReleaseStartTime) + period * RELEASE_INTERVAL;
      await time.increaseTo(targetTime);
      
      const currentTime = await time.latest();
      console.log(`当前时间: ${currentTime}`);
      console.log(`目标时间: ${targetTime}`);
      
      // 检查可释放数量
      const releasableAmount = await minerNFT.getReleasableAmount(1);
      console.log(`第${period}期可释放: ${ethers.formatEther(releasableAmount)} TokenA`);
      
      // 计算期望释放数量
      const expectedPercentage = RELEASE_PERCENTAGES[period - 1];
      const expectedAmount = (TOKENS_PER_NFT * BigInt(expectedPercentage)) / BigInt(100);
      console.log(`期望释放: ${ethers.formatEther(expectedAmount)} TokenA (${expectedPercentage}%)`);
      
      // 验证释放数量是否正确
      const tolerance = ethers.parseEther("1"); // 1 TokenA的容差
      const diff = releasableAmount > expectedAmount ? 
        releasableAmount - expectedAmount : expectedAmount - releasableAmount;
      
      if (diff <= tolerance) {
        console.log(`✅ 第${period}期释放数量正确`);
      } else {
        console.log(`❌ 第${period}期释放数量错误，差异: ${ethers.formatEther(diff)} TokenA`);
      }
      
      releaseResults.push({
        period,
        releasable: releasableAmount,
        expected: expectedAmount,
        percentage: expectedPercentage,
        correct: diff <= tolerance
      });
      
      // 如果有可释放的，执行释放
      if (releasableAmount > 0) {
        const beforeBalance = await tokenA.balanceOf(await user1.getAddress());
        
        await minerNFT.connect(user1).releaseTokens(1);
        
        const afterBalance = await tokenA.balanceOf(await user1.getAddress());
        const actualReceived = afterBalance - beforeBalance;
        
        console.log(`实际领取: ${ethers.formatEther(actualReceived)} TokenA`);
        
        // 更新NFT信息
        const updatedInfo = await minerNFT.getNFTReleaseInfo(1);
        console.log(`累计已释放: ${ethers.formatEther(updatedInfo.releasedTokens)} TokenA`);
        console.log(`剩余可释放: ${ethers.formatEther(updatedInfo.totalTokens - updatedInfo.releasedTokens)} TokenA`);
      } else {
        console.log(`第${period}期无可释放TokenA`);
      }
    }
    
    console.log("\\n=== 12期释放结果汇总 ===");
    let totalReleased = BigInt(0);
    let correctPeriods = 0;
    
    for (const result of releaseResults) {
      totalReleased += result.releasable;
      if (result.correct) correctPeriods++;
      
      console.log(`第${result.period}期: ${ethers.formatEther(result.releasable)} TokenA (${result.percentage}%) ${result.correct ? '✅' : '❌'}`);
    }
    
    console.log(`总释放量: ${ethers.formatEther(totalReleased)} TokenA`);
    console.log(`正确期数: ${correctPeriods}/12`);
    console.log(`释放准确率: ${(correctPeriods/12*100).toFixed(1)}%`);
    
    // 验证最终状态
    const finalInfo = await minerNFT.getNFTReleaseInfo(1);
    console.log(`\\n最终NFT状态:`);
    console.log(`总TokenA: ${ethers.formatEther(finalInfo.totalTokens)}`);
    console.log(`已释放: ${ethers.formatEther(finalInfo.releasedTokens)}`);
    console.log(`释放率: ${Number(finalInfo.releasedTokens * BigInt(100) / finalInfo.totalTokens)}%`);
    
    // 验证100%释放
    expect(finalInfo.releasedTokens).to.equal(finalInfo.totalTokens);
  });
  
  it("应该测试多个NFT的TokenA释放独立性", async function () {
    console.log("=== 多个NFT的TokenA释放独立性测试 ===");
    
    // 给不同用户铸造NFT
    await minerNFT.batchMintCount(await user1.getAddress(), 2); // NFT #1, #2
    await minerNFT.batchMintCount(await user2.getAddress(), 2); // NFT #3, #4
    await minerNFT.batchMintCount(await user3.getAddress(), 1); // NFT #5
    
    console.log("给用户们铸造了NFT");
    console.log(`user1: NFT #1, #2`);
    console.log(`user2: NFT #3, #4`);
    console.log(`user3: NFT #5`);
    
    // 获取释放开始时间
    const releaseStartTime = await minerNFT.getNFTReleaseStartTime(1);
    await time.increaseTo(Number(releaseStartTime));
    
    // 推进到第3期 (3%释放期)
    const period3Time = Number(releaseStartTime) + 3 * RELEASE_INTERVAL;
    await time.increaseTo(period3Time);
    
    console.log("\\n=== 第3期释放测试 ===");
    
    // 检查所有NFT的可释放数量
    const expectedAmount = (TOKENS_PER_NFT * BigInt(3 + 3 + 3)) / BigInt(100); // 前3期累计9%
    
    for (let nftId = 1; nftId <= 5; nftId++) {
      const releasableAmount = await minerNFT.getReleasableAmount(nftId);
      console.log(`NFT #${nftId} 可释放: ${ethers.formatEther(releasableAmount)} TokenA`);
      
      // 验证每个NFT的释放数量都相等
      const tolerance = ethers.parseEther("1");
      const diff = releasableAmount > expectedAmount ? 
        releasableAmount - expectedAmount : expectedAmount - releasableAmount;
      
      expect(diff).to.be.lte(tolerance, `NFT #${nftId} 释放数量应该正确`);
    }
    
    console.log(`✅ 所有NFT在第3期的释放数量都正确`);
    
    // 测试部分释放的独立性
    console.log("\\n=== 测试释放独立性 ===");
    
    // 只释放NFT #1 和 #3
    await minerNFT.connect(user1).releaseTokens(1);
    await minerNFT.connect(user2).releaseTokens(3);
    
    console.log("已释放NFT #1 和 #3");
    
    // 检查其他NFT的可释放数量是否不受影响
    const nft2Releasable = await minerNFT.getReleasableAmount(2);
    const nft4Releasable = await minerNFT.getReleasableAmount(4);
    const nft5Releasable = await minerNFT.getReleasableAmount(5);
    
    console.log(`NFT #2 仍可释放: ${ethers.formatEther(nft2Releasable)} TokenA`);
    console.log(`NFT #4 仍可释放: ${ethers.formatEther(nft4Releasable)} TokenA`);
    console.log(`NFT #5 仍可释放: ${ethers.formatEther(nft5Releasable)} TokenA`);
    
    // 验证未释放的NFT数量不变
    const tolerance = ethers.parseEther("1");
    expect(nft2Releasable).to.be.closeTo(expectedAmount, tolerance);
    expect(nft4Releasable).to.be.closeTo(expectedAmount, tolerance);
    expect(nft5Releasable).to.be.closeTo(expectedAmount, tolerance);
    
    console.log("✅ NFT释放机制相互独立");
  });
  
  it("应该测试TokenA释放的累积计算正确性", async function () {
    console.log("=== TokenA释放累积计算正确性测试 ===");
    
    // 铸造NFT
    await minerNFT.batchMintCount(await user1.getAddress(), 1);
    
    // 推进到释放开始时间
    const releaseStartTime = await minerNFT.getNFTReleaseStartTime(1);
    await time.increaseTo(Number(releaseStartTime));
    
    // 测试跳跃式释放 (跳过几期后再释放)
    console.log("\\n=== 跳跃到第6期测试 ===");
    
    const period6Time = Number(releaseStartTime) + 6 * RELEASE_INTERVAL;
    await time.increaseTo(period6Time);
    
    // 计算前6期的累积释放比例: 3+3+3+5+5+5 = 24%
    const expectedCumulative = RELEASE_PERCENTAGES.slice(0, 6).reduce((sum, p) => sum + p, 0);
    const expectedAmount = (TOKENS_PER_NFT * BigInt(expectedCumulative)) / BigInt(100);
    
    const releasableAmount = await minerNFT.getReleasableAmount(1);
    console.log(`跳跃到第6期可释放: ${ethers.formatEther(releasableAmount)} TokenA`);
    console.log(`期望累积释放: ${ethers.formatEther(expectedAmount)} TokenA (${expectedCumulative}%)`);
    
    // 验证累积计算正确
    const tolerance = ethers.parseEther("10"); // 10 TokenA容差
    const diff = releasableAmount > expectedAmount ? 
      releasableAmount - expectedAmount : expectedAmount - releasableAmount;
    
    expect(diff).to.be.lte(tolerance, "累积释放计算应该正确");
    console.log("✅ 跳跃释放累积计算正确");
    
    // 执行释放
    await minerNFT.connect(user1).releaseTokens(1);
    
    // 检查剩余期数
    console.log("\\n=== 释放后剩余期数测试 ===");
    
    // 推进到第9期
    const period9Time = Number(releaseStartTime) + 9 * RELEASE_INTERVAL;
    await time.increaseTo(period9Time);
    
    // 第7-9期释放比例: 10+10+10 = 30%
    const remainingPercentage = RELEASE_PERCENTAGES.slice(6, 9).reduce((sum, p) => sum + p, 0);
    const expectedRemaining = (TOKENS_PER_NFT * BigInt(remainingPercentage)) / BigInt(100);
    
    const remainingReleasable = await minerNFT.getReleasableAmount(1);
    console.log(`第7-9期可释放: ${ethers.formatEther(remainingReleasable)} TokenA`);
    console.log(`期望释放: ${ethers.formatEther(expectedRemaining)} TokenA (${remainingPercentage}%)`);
    
    const diff2 = remainingReleasable > expectedRemaining ? 
      remainingReleasable - expectedRemaining : expectedRemaining - remainingReleasable;
    
    expect(diff2).to.be.lte(tolerance, "剩余期数计算应该正确");
    console.log("✅ 剩余期数释放计算正确");
  });
  
  it("应该测试TokenA释放时间边界条件", async function () {
    console.log("=== TokenA释放时间边界条件测试 ===");
    
    // 铸造NFT
    await minerNFT.batchMintCount(await user1.getAddress(), 1);
    
    const releaseStartTime = await minerNFT.getNFTReleaseStartTime(1);
    console.log(`释放开始时间: ${releaseStartTime}`);
    
    // 测试释放开始前
    console.log("\\n=== 释放开始前测试 ===");
    const beforeStartTime = Number(releaseStartTime) - 1;
    await time.increaseTo(beforeStartTime);
    
    const beforeStartReleasable = await minerNFT.getReleasableAmount(1);
    console.log(`释放开始前可释放: ${ethers.formatEther(beforeStartReleasable)} TokenA`);
    expect(beforeStartReleasable).to.equal(0, "释放开始前应该无法释放");
    
    // 测试刚好到达释放时间
    console.log("\\n=== 刚好到达第1期测试 ===");
    const exactPeriod1Time = Number(releaseStartTime) + RELEASE_INTERVAL;
    await time.increaseTo(exactPeriod1Time);
    
    const period1Releasable = await minerNFT.getReleasableAmount(1);
    const expectedPeriod1 = (TOKENS_PER_NFT * BigInt(3)) / BigInt(100); // 3%
    
    console.log(`第1期精确时间可释放: ${ethers.formatEther(period1Releasable)} TokenA`);
    console.log(`期望释放: ${ethers.formatEther(expectedPeriod1)} TokenA`);
    
    const tolerance = ethers.parseEther("1");
    const diff = period1Releasable > expectedPeriod1 ? 
      period1Releasable - expectedPeriod1 : expectedPeriod1 - period1Releasable;
    
    expect(diff).to.be.lte(tolerance, "第1期精确时间释放应该正确");
    console.log("✅ 第1期精确时间释放正确");
    
    // 测试最后一期的特殊处理 (26%)
    console.log("\\n=== 第12期(最后一期)测试 ===");
    const period12Time = Number(releaseStartTime) + 12 * RELEASE_INTERVAL;
    await time.increaseTo(period12Time);
    
    // 不执行中间期的释放，直接跳到最后一期
    const period12Releasable = await minerNFT.getReleasableAmount(1);
    
    // 应该释放100% (因为是累积释放)
    const expectedTotal = TOKENS_PER_NFT;
    
    console.log(`第12期累积可释放: ${ethers.formatEther(period12Releasable)} TokenA`);
    console.log(`期望总释放: ${ethers.formatEther(expectedTotal)} TokenA`);
    
    const totalDiff = period12Releasable > expectedTotal ? 
      period12Releasable - expectedTotal : expectedTotal - period12Releasable;
    
    expect(totalDiff).to.be.lte(tolerance, "第12期应该能释放全部TokenA");
    console.log("✅ 第12期累积释放正确");
    
    // 测试超过12期后的行为
    console.log("\\n=== 超过12期后测试 ===");
    const beyondPeriod12Time = Number(releaseStartTime) + 15 * RELEASE_INTERVAL;
    await time.increaseTo(beyondPeriod12Time);
    
    const beyondPeriod12Releasable = await minerNFT.getReleasableAmount(1);
    console.log(`超过12期可释放: ${ethers.formatEther(beyondPeriod12Releasable)} TokenA`);
    
    // 应该不会超过100%
    expect(beyondPeriod12Releasable).to.be.lte(expectedTotal, "超过12期不应该释放更多");
    console.log("✅ 超过12期行为正确");
  });
  
  it("应该测试批量释放功能", async function () {
    console.log("=== 批量释放功能测试 ===");
    
    // 给user1铸造多个NFT
    await minerNFT.batchMintCount(await user1.getAddress(), 3);
    console.log("给user1铸造了3个NFT");
    
    // 推进到释放开始时间
    const releaseStartTime = await minerNFT.getNFTReleaseStartTime(1);
    await time.increaseTo(Number(releaseStartTime));
    
    // 推进到第5期
    const period5Time = Number(releaseStartTime) + 5 * RELEASE_INTERVAL;
    await time.increaseTo(period5Time);
    
    console.log("\\n=== 第5期批量释放测试 ===");
    
    // 计算前5期累积释放: 3+3+3+5+5 = 19%
    const cumulativePercentage = RELEASE_PERCENTAGES.slice(0, 5).reduce((sum, p) => sum + p, 0);
    const expectedPerNFT = (TOKENS_PER_NFT * BigInt(cumulativePercentage)) / BigInt(100);
    const expectedTotal = expectedPerNFT * BigInt(3); // 3个NFT
    
    console.log(`前5期累积比例: ${cumulativePercentage}%`);
    console.log(`每个NFT期望释放: ${ethers.formatEther(expectedPerNFT)} TokenA`);
    console.log(`3个NFT总期望: ${ethers.formatEther(expectedTotal)} TokenA`);
    
    // 检查user1的总可释放数量
    const totalReleasable = await minerNFT.getUserTotalReleasableAmount(await user1.getAddress());
    console.log(`批量可释放总量: ${ethers.formatEther(totalReleasable)} TokenA`);
    
    // 验证批量计算正确
    const tolerance = ethers.parseEther("10");
    const diff = totalReleasable > expectedTotal ? 
      totalReleasable - expectedTotal : expectedTotal - totalReleasable;
    
    expect(diff).to.be.lte(tolerance, "批量可释放计算应该正确");
    console.log("✅ 批量可释放计算正确");
    
    // 执行批量释放
    const beforeBalance = await tokenA.balanceOf(await user1.getAddress());
    
    await minerNFT.connect(user1).batchReleaseTokens();
    
    const afterBalance = await tokenA.balanceOf(await user1.getAddress());
    const actualReceived = afterBalance - beforeBalance;
    
    console.log(`批量释放实际领取: ${ethers.formatEther(actualReceived)} TokenA`);
    
    // 验证批量释放数量正确
    const receivedDiff = actualReceived > expectedTotal ? 
      actualReceived - expectedTotal : expectedTotal - actualReceived;
    
    expect(receivedDiff).to.be.lte(tolerance, "批量释放数量应该正确");
    console.log("✅ 批量释放数量正确");
    
    // 验证所有NFT状态都已更新
    for (let nftId = 1; nftId <= 3; nftId++) {
      const nftInfo = await minerNFT.getNFTReleaseInfo(nftId);
      console.log(`NFT #${nftId} 已释放: ${ethers.formatEther(nftInfo.releasedTokens)} TokenA`);
      
      // 验证每个NFT都释放了正确的数量
      const nftDiff = nftInfo.releasedTokens > expectedPerNFT ? 
        nftInfo.releasedTokens - expectedPerNFT : expectedPerNFT - nftInfo.releasedTokens;
      
      expect(nftDiff).to.be.lte(ethers.parseEther("5"), `NFT #${nftId} 释放数量应该正确`);
    }
    
    console.log("✅ 所有NFT状态正确更新");
  });
});