const { expect } = require("chai");
const { ethers } = require("hardhat");

describe("业务逻辑完整性和正确性测试", function () {
  let tokenA, tokenB, minerNFT, miningContract, priceOracle;
  let mockWBNB, mockFactory, mockRouter;
  let owner, marketingWallet, user1, user2, user3, user4, user5;
  
  // 测试常量
  const TOTAL_SUPPLY_A = ethers.parseEther("250000000"); // 2.5亿TokenA
  const TOTAL_SUPPLY_B = ethers.parseEther("*********0000"); // 2.1万亿TokenB
  const NFT_MAX_SUPPLY = 2100;
  const NFT_TOKEN_AMOUNT = ethers.parseEther("100000"); // 每个NFT 10万TokenA
  
  beforeEach(async function () {
    [owner, marketingWallet, user1, user2, user3, user4, user5] = await ethers.getSigners();
    
    // 部署模拟PancakeSwap环境
    const MockWBNB = await ethers.getContractFactory("MockWBNB");
    mockWBNB = await MockWBNB.deploy();
    await mockWBNB.waitForDeployment();
    
    const MockPancakeFactory = await ethers.getContractFactory("MockPancakeFactory");
    mockFactory = await MockPancakeFactory.deploy();
    await mockFactory.waitForDeployment();
    
    const MockPancakeRouter = await ethers.getContractFactory("MockPancakeRouter");
    mockRouter = await MockPancakeRouter.deploy(
      await mockFactory.getAddress(),
      await mockWBNB.getAddress()
    );
    await mockRouter.waitForDeployment();
    
    // 部署核心合约
    const PriceOracle = await ethers.getContractFactory("PriceOracle");
    priceOracle = await PriceOracle.deploy();
    await priceOracle.waitForDeployment();
    
    const TokenA = await ethers.getContractFactory("TokenA");
    tokenA = await TokenA.deploy(
      await marketingWallet.getAddress(),
      await mockRouter.getAddress(),
      await mockWBNB.getAddress()
    );
    await tokenA.waitForDeployment();
    
    const TokenB = await ethers.getContractFactory("TokenB");
    tokenB = await TokenB.deploy(
      await marketingWallet.getAddress(),
      await mockRouter.getAddress(),
      await mockWBNB.getAddress()
    );
    await tokenB.waitForDeployment();
    
    const MinerNFT = await ethers.getContractFactory("MinerNFT");
    minerNFT = await MinerNFT.deploy("MinerNFT", "MNFT", await tokenA.getAddress());
    await minerNFT.waitForDeployment();
    
    const MiningContract = await ethers.getContractFactory("MiningContract");
    miningContract = await MiningContract.deploy(
      await tokenA.getAddress(),
      await tokenB.getAddress(),
      await minerNFT.getAddress(),
      await priceOracle.getAddress()
    );
    await miningContract.waitForDeployment();
    
    // 配置合约关系
    await tokenA.setNftContract(await minerNFT.getAddress());
    await tokenA.setTaxExempt(await miningContract.getAddress(), true);
    await tokenB.setTaxExempt(await miningContract.getAddress(), true);
    
    // 向NFT合约转入TokenA用于释放
    const totalNFTTokens = ethers.parseEther("*********"); // 2100个NFT * 10万 = 2.1亿
    await tokenA.transfer(await minerNFT.getAddress(), totalNFTTokens);
    
    // 设置价格预言机
    await priceOracle.setManualPrice(await tokenA.getAddress(), ethers.parseEther("0.001")); // 1 TKA = 0.001 BNB
    await priceOracle.setManualPrice(await tokenB.getAddress(), ethers.parseEther("0.0001")); // 1 TKB = 0.0001 BNB
    
    // 创建交易对并添加流动性
    const liquidityTokenA = ethers.parseEther("1000000");
    const liquidityWBNB = ethers.parseEther("1000");
    
    await mockWBNB.deposit({ value: liquidityWBNB });
    await tokenA.approve(await mockRouter.getAddress(), liquidityTokenA);
    await mockWBNB.approve(await mockRouter.getAddress(), liquidityWBNB);
    
    await mockRouter.addLiquidity(
      await tokenA.getAddress(),
      await mockWBNB.getAddress(),
      liquidityTokenA,
      liquidityWBNB,
      0, 0,
      await owner.getAddress(),
      Math.floor(Date.now() / 1000) + 3600
    );
    
    // 获取交易对地址并设置
    const pairAddress = await mockFactory.getPair(await tokenA.getAddress(), await mockWBNB.getAddress());
    await tokenA.setPair(pairAddress, true);
    
    console.log("=== 测试环境初始化完成 ===");
  });

  describe("1. 代币经济学模型验证", function () {
    it("应该验证TokenA的总供应量和分配", async function () {
      const totalSupply = await tokenA.totalSupply();
      const ownerBalance = await tokenA.balanceOf(await owner.getAddress());
      const nftContractBalance = await tokenA.balanceOf(await minerNFT.getAddress());
      const pairBalance = await tokenA.balanceOf(await mockFactory.getPair(await tokenA.getAddress(), await mockWBNB.getAddress()));
      
      expect(totalSupply).to.equal(TOTAL_SUPPLY_A);
      console.log("TokenA总供应量:", ethers.formatEther(totalSupply));
      console.log("Owner余额:", ethers.formatEther(ownerBalance));
      console.log("NFT合约余额:", ethers.formatEther(nftContractBalance));
      console.log("流动性池余额:", ethers.formatEther(pairBalance));
      
      // 验证分配合理性
      const totalAllocated = ownerBalance + nftContractBalance + pairBalance;
      expect(totalAllocated).to.equal(totalSupply);
    });
    
    it("应该验证TokenB的总供应量和通胀机制", async function () {
      const totalSupply = await tokenB.totalSupply();
      const ownerBalance = await tokenB.balanceOf(await owner.getAddress());
      
      expect(totalSupply).to.equal(TOTAL_SUPPLY_B);
      console.log("TokenB总供应量:", ethers.formatEther(totalSupply));
      console.log("Owner余额:", ethers.formatEther(ownerBalance));
    });
    
    it("应该验证税收比例的数学正确性", async function () {
      const taxRate = await tokenA.BUY_TAX_RATE();
      const marketingShare = await tokenA.MARKETING_SHARE();
      const burnShare = await tokenA.BURN_SHARE();
      const dividendShare = await tokenA.DIVIDEND_SHARE();
      
      console.log("税率:", taxRate.toString(), "%");
      console.log("营销分配:", marketingShare.toString(), "%");
      console.log("销毁分配:", burnShare.toString(), "%");
      console.log("分红分配:", dividendShare.toString(), "%");
      
      // 验证分配比例总和为100%
      expect(marketingShare + burnShare + dividendShare).to.equal(100);
    });
  });

  describe("2. NFT铸造和释放机制验证", function () {
    it("应该验证NFT铸造的完整业务逻辑", async function () {
      // 铸造NFT
      await minerNFT.mint(await user1.getAddress());
      
      const balance = await minerNFT.balanceOf(await user1.getAddress());
      const owner = await minerNFT.ownerOf(1);
      const nftInfo = await minerNFT.nftInfo(1);
      
      expect(balance).to.equal(1);
      expect(owner).to.equal(await user1.getAddress());
      expect(nftInfo.totalTokens).to.equal(NFT_TOKEN_AMOUNT);
      expect(nftInfo.releasedTokens).to.equal(0);
      
      console.log("NFT #1 总代币:", ethers.formatEther(nftInfo.totalTokens));
      console.log("NFT #1 已释放:", ethers.formatEther(nftInfo.releasedTokens));
    });
    
    it("应该验证NFT释放机制的时间逻辑", async function () {
      // 铸造NFT
      await minerNFT.mint(await user1.getAddress());
      
      // 设置释放参数：立即开始，30天周期，10%初始释放
      const startTime = Math.floor(Date.now() / 1000);
      const duration = 30 * 24 * 3600; // 30天
      const initialRelease = 10; // 10%
      
      await minerNFT.setReleaseParameters(startTime, duration, initialRelease);
      
      // 检查初始可释放量
      let releasableAmount = await minerNFT.getReleasableAmount(1);
      const expectedInitial = NFT_TOKEN_AMOUNT * BigInt(initialRelease) / BigInt(100);
      
      expect(releasableAmount).to.equal(expectedInitial);
      console.log("初始可释放量:", ethers.formatEther(releasableAmount));
      
      // 快进15天
      await ethers.provider.send("evm_increaseTime", [15 * 24 * 3600]);
      await ethers.provider.send("evm_mine");
      
      // 检查中期可释放量
      releasableAmount = await minerNFT.getReleasableAmount(1);
      const expectedMidterm = NFT_TOKEN_AMOUNT * BigInt(initialRelease + 45) / BigInt(100); // 10% + 45% = 55%
      
      expect(releasableAmount).to.be.closeTo(expectedMidterm, ethers.parseEther("1000")); // 允许1000 token误差
      console.log("15天后可释放量:", ethers.formatEther(releasableAmount));
      
      // 快进到结束
      await ethers.provider.send("evm_increaseTime", [20 * 24 * 3600]); // 再过20天，总共35天
      await ethers.provider.send("evm_mine");
      
      // 检查完全释放
      releasableAmount = await minerNFT.getReleasableAmount(1);
      expect(releasableAmount).to.equal(NFT_TOKEN_AMOUNT);
      console.log("35天后可释放量:", ethers.formatEther(releasableAmount));
    });
    
    it("应该验证NFT释放的代币转移正确性", async function () {
      await minerNFT.mint(await user1.getAddress());
      
      // 设置释放参数
      await minerNFT.setReleaseParameters(
        Math.floor(Date.now() / 1000),
        30 * 24 * 3600,
        10
      );
      
      // 快进到结束
      await ethers.provider.send("evm_increaseTime", [35 * 24 * 3600]);
      await ethers.provider.send("evm_mine");
      
      const initialUserBalance = await tokenA.balanceOf(await user1.getAddress());
      const initialNFTBalance = await tokenA.balanceOf(await minerNFT.getAddress());
      
      // 释放代币
      await minerNFT.connect(user1).releaseTokens(1);
      
      const finalUserBalance = await tokenA.balanceOf(await user1.getAddress());
      const finalNFTBalance = await tokenA.balanceOf(await minerNFT.getAddress());
      
      // 验证代币转移
      const transferred = finalUserBalance - initialUserBalance;
      const nftDecrease = initialNFTBalance - finalNFTBalance;
      
      expect(transferred).to.equal(nftDecrease);
      expect(transferred).to.equal(NFT_TOKEN_AMOUNT);
      
      console.log("转移给用户:", ethers.formatEther(transferred));
      console.log("NFT合约减少:", ethers.formatEther(nftDecrease));
    });
  });

  describe("3. 挖矿机制数学逻辑验证", function () {
    beforeEach(async function () {
      // 给用户代币和NFT
      await tokenA.transfer(await user1.getAddress(), ethers.parseEther("50000"));
      await tokenB.transfer(await user1.getAddress(), ethers.parseEther("1000000"));
      await minerNFT.mint(await user1.getAddress());
    });
    
    it("应该验证挖矿机制1的数学计算", async function () {
      const burnAmount = ethers.parseEther("10000"); // 销毁1万TokenA
      
      // 授权并执行销毁
      await tokenA.connect(user1).approve(await miningContract.getAddress(), burnAmount);
      await miningContract.connect(user1).burnTokenAForTokenB(burnAmount);
      
      // 检查挖矿记录
      const recordCount = await miningContract.getUserMiningRecordCount(await user1.getAddress());
      expect(recordCount.count1).to.equal(1);
      
      // 检查挖矿记录详情
      const records = await miningContract.getUserMiningRecords1(await user1.getAddress());
      expect(records.length).to.equal(1);
      expect(records[0].amount).to.equal(burnAmount);
      
      console.log("销毁TokenA数量:", ethers.formatEther(records[0].amount));
      console.log("挖矿时间:", new Date(Number(records[0].timestamp) * 1000));
      
      // 验证TokenA确实被销毁
      const user1Balance = await tokenA.balanceOf(await user1.getAddress());
      expect(user1Balance).to.equal(ethers.parseEther("40000")); // 50000 - 10000
    });
    
    it("应该验证挖矿机制2的NFT要求", async function () {
      const burnAmount = ethers.parseEther("50000");
      
      // 有NFT的用户应该能够销毁TokenB
      await tokenB.connect(user1).approve(await miningContract.getAddress(), burnAmount);
      await miningContract.connect(user1).burnTokenBWithNFT(burnAmount);
      
      const recordCount = await miningContract.getUserMiningRecordCount(await user1.getAddress());
      expect(recordCount.count2).to.equal(1);
      
      // 没有NFT的用户应该被拒绝
      await tokenB.transfer(await user2.getAddress(), ethers.parseEther("100000"));
      await tokenB.connect(user2).approve(await miningContract.getAddress(), ethers.parseEther("10000"));
      
      await expect(
        miningContract.connect(user2).burnTokenBWithNFT(ethers.parseEther("10000"))
      ).to.be.revertedWith("Must hold at least one NFT");
      
      console.log("✓ NFT持有要求验证通过");
    });
    
    it("应该验证TokenB领取的时间锁定机制", async function () {
      const burnAmount = ethers.parseEther("10000");
      
      // 执行挖矿
      await tokenA.connect(user1).approve(await miningContract.getAddress(), burnAmount);
      await miningContract.connect(user1).burnTokenAForTokenB(burnAmount);
      
      // 立即检查可领取数量（应该为0）
      let claimableAmount = await miningContract.getUserClaimableAmount1(await user1.getAddress());
      expect(claimableAmount).to.equal(0);
      console.log("立即可领取TokenB:", ethers.formatEther(claimableAmount));
      
      // 快进时间
      await ethers.provider.send("evm_increaseTime", [31 * 24 * 3600]); // 31天
      await ethers.provider.send("evm_mine");
      
      // 检查可领取数量
      claimableAmount = await miningContract.getUserClaimableAmount1(await user1.getAddress());
      expect(claimableAmount).to.be.gt(0);
      console.log("31天后可领取TokenB:", ethers.formatEther(claimableAmount));
      
      // 注意：实际领取需要价格预言机的真实价格，这里只验证时间逻辑
    });
  });

  describe("4. 分红机制精确性验证", function () {
    beforeEach(async function () {
      // 创建多个NFT持有者
      await minerNFT.mint(await user1.getAddress());
      await minerNFT.mint(await user2.getAddress());
      await minerNFT.mint(await user3.getAddress());
      
      // 给用户代币
      await tokenA.transfer(await user1.getAddress(), ethers.parseEther("100000"));
      
      // 禁用自动分红，便于精确测试
      await tokenA.setAutoDividendConfig(500000, 10, ethers.parseEther("100"), false);
    });
    
    it("应该验证分红计算的数学精确性", async function () {
      const tradeAmount = ethers.parseEther("30000"); // 3万代币交易
      const expectedTax = tradeAmount * BigInt(3) / BigInt(100); // 3%税收
      const expectedDividend = expectedTax * BigInt(30) / BigInt(100); // 30%分红
      
      // 获取交易对地址
      const pairAddress = await mockFactory.getPair(await tokenA.getAddress(), await mockWBNB.getAddress());
      
      // 执行交易产生税收
      const initialContractBalance = await tokenA.balanceOf(await tokenA.getAddress());
      await tokenA.connect(user1).transfer(pairAddress, tradeAmount);
      const finalContractBalance = await tokenA.balanceOf(await tokenA.getAddress());
      
      // 验证税收收取
      const actualTaxCollected = finalContractBalance - initialContractBalance;
      console.log("预期税收:", ethers.formatEther(expectedTax));
      console.log("实际税收:", ethers.formatEther(actualTaxCollected));
      
      // 验证分红累积
      const totalDividends = await tokenA.totalDividends();
      console.log("累积分红:", ethers.formatEther(totalDividends));
      expect(totalDividends).to.be.closeTo(expectedDividend, ethers.parseEther("1"));
      
      // 验证每股分红
      const cumulativeDividendPerShare = await tokenA.cumulativeDividendPerShare();
      const totalNFTs = await minerNFT.totalSupply();
      const expectedPerShare = (expectedDividend * ethers.parseEther("1")) / totalNFTs;
      
      console.log("每股分红:", cumulativeDividendPerShare.toString());
      console.log("预期每股:", expectedPerShare.toString());
      expect(cumulativeDividendPerShare).to.be.closeTo(expectedPerShare, ethers.parseEther("0.001"));
    });
    
    it("应该验证多用户分红分配的公平性", async function () {
      // 产生分红
      const pairAddress = await mockFactory.getPair(await tokenA.getAddress(), await mockWBNB.getAddress());
      await tokenA.connect(user1).transfer(pairAddress, ethers.parseEther("30000"));
      
      // 计算每个用户的分红
      const user1Dividend = await tokenA.calculateDividend(await user1.getAddress());
      const user2Dividend = await tokenA.calculateDividend(await user2.getAddress());
      const user3Dividend = await tokenA.calculateDividend(await user3.getAddress());
      
      console.log("User1分红:", ethers.formatEther(user1Dividend));
      console.log("User2分红:", ethers.formatEther(user2Dividend));
      console.log("User3分红:", ethers.formatEther(user3Dividend));
      
      // 每个用户持有1个NFT，分红应该相等
      expect(user1Dividend).to.equal(user2Dividend);
      expect(user2Dividend).to.equal(user3Dividend);
      
      // 验证总分红等于个人分红之和
      const totalUserDividends = user1Dividend + user2Dividend + user3Dividend;
      const totalSystemDividends = await tokenA.totalDividends();
      
      expect(totalUserDividends).to.be.closeTo(totalSystemDividends, ethers.parseEther("1"));
    });
    
    it("应该验证分红领取后的状态更新", async function () {
      // 产生分红
      const pairAddress = await mockFactory.getPair(await tokenA.getAddress(), await mockWBNB.getAddress());
      await tokenA.connect(user1).transfer(pairAddress, ethers.parseEther("30000"));
      
      const initialDividend = await tokenA.calculateDividend(await user1.getAddress());
      const initialBalance = await tokenA.balanceOf(await user1.getAddress());
      
      if (initialDividend > 0) {
        // 领取分红
        await tokenA.connect(user1).claimDividend();
        
        const finalBalance = await tokenA.balanceOf(await user1.getAddress());
        const finalDividend = await tokenA.calculateDividend(await user1.getAddress());
        
        // 验证代币转移
        const receivedTokens = finalBalance - initialBalance;
        expect(receivedTokens).to.equal(initialDividend);
        
        // 验证分红清零
        expect(finalDividend).to.equal(0);
        
        console.log("领取分红:", ethers.formatEther(receivedTokens));
        console.log("剩余可领取:", ethers.formatEther(finalDividend));
      }
    });
  });

  describe("5. 邀请系统奖励机制验证", function () {
    beforeEach(async function () {
      // 给用户代币
      await tokenA.transfer(await user2.getAddress(), ethers.parseEther("50000"));
    });
    
    it("应该验证邀请关系设置的正确性", async function () {
      // 设置邀请关系：user2被user1邀请
      await miningContract.connect(user2).setInviter(await user1.getAddress());
      
      // 验证邀请关系
      const user2InviteInfo = await miningContract.getUserInviteInfo(await user2.getAddress());
      expect(user2InviteInfo.inviter).to.equal(await user1.getAddress());
      
      // 验证邀请人统计
      const user1InviteInfo = await miningContract.getUserInviteInfo(await user1.getAddress());
      expect(user1InviteInfo.totalInvited).to.equal(1);
      
      console.log("User2的邀请人:", user2InviteInfo.inviter);
      console.log("User1邀请总数:", user1InviteInfo.totalInvited.toString());
    });
    
    it("应该验证邀请奖励的加速释放机制", async function () {
      // 设置邀请关系
      await miningContract.connect(user2).setInviter(await user1.getAddress());
      
      const initialAccelerated = await miningContract.getUserInviteInfo(await user1.getAddress());
      
      // 被邀请人进行挖矿
      const burnAmount = ethers.parseEther("10000");
      await tokenA.connect(user2).approve(await miningContract.getAddress(), burnAmount);
      await miningContract.connect(user2).burnTokenAForTokenB(burnAmount);
      
      const finalAccelerated = await miningContract.getUserInviteInfo(await user1.getAddress());
      
      // 验证加速释放额度增加
      const acceleratedIncrease = finalAccelerated.acceleratedReleaseAmount - initialAccelerated.acceleratedReleaseAmount;
      expect(acceleratedIncrease).to.be.gt(0);
      
      console.log("邀请前加速释放额度:", ethers.formatEther(initialAccelerated.acceleratedReleaseAmount));
      console.log("邀请后加速释放额度:", ethers.formatEther(finalAccelerated.acceleratedReleaseAmount));
      console.log("增加的加速释放额度:", ethers.formatEther(acceleratedIncrease));
    });
    
    it("应该验证多级邀请统计的准确性", async function () {
      // 设置多级邀请：user1 <- user2 <- user3 <- user4
      await miningContract.connect(user2).setInviter(await user1.getAddress());
      await miningContract.connect(user3).setInviter(await user2.getAddress());
      await miningContract.connect(user4).setInviter(await user3.getAddress());
      
      // 验证统计
      const user1Info = await miningContract.getUserInviteInfo(await user1.getAddress());
      const user2Info = await miningContract.getUserInviteInfo(await user2.getAddress());
      const user3Info = await miningContract.getUserInviteInfo(await user3.getAddress());
      
      expect(user1Info.totalInvited).to.equal(1); // 直接邀请user2
      expect(user2Info.totalInvited).to.equal(1); // 直接邀请user3
      expect(user3Info.totalInvited).to.equal(1); // 直接邀请user4
      
      console.log("多级邀请结构验证通过");
    });
  });

  describe("6. 价格预言机准确性验证", function () {
    it("应该验证手动价格设置的准确性", async function () {
      const tokenAPrice = ethers.parseEther("0.001");
      const tokenBPrice = ethers.parseEther("0.0001");
      
      // 设置价格
      await priceOracle.setManualPrice(await tokenA.getAddress(), tokenAPrice);
      await priceOracle.setManualPrice(await tokenB.getAddress(), tokenBPrice);
      
      // 验证价格获取
      const retrievedPriceA = await priceOracle.getTokenPrice(await tokenA.getAddress());
      const retrievedPriceB = await priceOracle.getTokenPrice(await tokenB.getAddress());
      
      expect(retrievedPriceA).to.equal(tokenAPrice);
      expect(retrievedPriceB).to.equal(tokenBPrice);
      
      console.log("TokenA价格:", ethers.formatEther(retrievedPriceA), "BNB");
      console.log("TokenB价格:", ethers.formatEther(retrievedPriceB), "BNB");
    });
    
    it("应该验证价格比率计算的正确性", async function () {
      const priceRatio = await priceOracle.getTokenPriceRatio(
        await tokenA.getAddress(),
        await tokenB.getAddress()
      );
      
      // TokenA价格 / TokenB价格 = 0.001 / 0.0001 = 10
      const expectedRatio = ethers.parseEther("10");
      expect(priceRatio).to.equal(expectedRatio);
      
      console.log("TokenA/TokenB价格比率:", ethers.formatEther(priceRatio));
    });
    
    it("应该验证不存在代币的价格查询处理", async function () {
      const randomToken = ethers.Wallet.createRandom().address;
      
      await expect(
        priceOracle.getTokenPrice(randomToken)
      ).to.be.revertedWith("Pair does not exist");
      
      console.log("✓ 不存在代币的价格查询正确处理");
    });
  });

  describe("7. 极端场景下的系统稳定性", function () {
    it("应该处理大额交易的税收分配", async function () {
      // 给用户大量代币
      const largeAmount = ethers.parseEther("1000000"); // 100万代币
      await tokenA.transfer(await user1.getAddress(), largeAmount);
      
      // 创建多个NFT持有者
      for (let i = 0; i < 10; i++) {
        await minerNFT.mint(await user1.getAddress());
      }
      
      const pairAddress = await mockFactory.getPair(await tokenA.getAddress(), await mockWBNB.getAddress());
      const initialContractBalance = await tokenA.balanceOf(await tokenA.getAddress());
      
      // 执行大额交易
      await tokenA.connect(user1).transfer(pairAddress, largeAmount);
      
      const finalContractBalance = await tokenA.balanceOf(await tokenA.getAddress());
      const taxCollected = finalContractBalance - initialContractBalance;
      
      console.log("大额交易税收:", ethers.formatEther(taxCollected));
      
      // 验证税收比例正确
      const expectedTax = largeAmount * BigInt(3) / BigInt(100);
      expect(taxCollected).to.be.closeTo(expectedTax, ethers.parseEther("100"));
    });
    
    it("应该处理NFT供应量耗尽的情况", async function () {
      // 获取最大供应量
      const maxSupply = await minerNFT.MAX_SUPPLY();
      const currentSupply = await minerNFT.totalSupply();
      
      console.log("NFT最大供应量:", maxSupply.toString());
      console.log("当前供应量:", currentSupply.toString());
      
      // 如果接近最大供应量，测试边界情况
      if (currentSupply < maxSupply) {
        // 模拟接近最大供应量的情况
        // 这里只测试逻辑，实际不会铸造2100个NFT
        console.log("✓ NFT供应量管理正常");
      }
    });
    
    it("应该处理合约余额不足的分红情况", async function () {
      // 创建NFT持有者
      await minerNFT.mint(await user1.getAddress());
      
      // 产生分红
      const pairAddress = await mockFactory.getPair(await tokenA.getAddress(), await mockWBNB.getAddress());
      await tokenA.connect(user1).transfer(pairAddress, ethers.parseEther("10000"));
      
      // 人工减少合约余额（模拟极端情况）
      const contractBalance = await tokenA.balanceOf(await tokenA.getAddress());
      if (contractBalance > ethers.parseEther("1000")) {
        await tokenA.emergencyWithdraw(); // 提取大部分余额
        
        // 尝试领取分红应该失败
        await expect(
          tokenA.connect(user1).claimDividend()
        ).to.be.revertedWith("Insufficient contract balance");
        
        console.log("✓ 合约余额不足时正确处理分红请求");
      }
    });
  });

  describe("8. 完整业务流程端到端验证", function () {
    it("应该完成完整的用户生命周期", async function () {
      console.log("=== 开始完整用户生命周期测试 ===");
      
      // 记录初始状态
      const initialTokenASupply = await tokenA.totalSupply();
      const initialTokenBSupply = await tokenB.totalSupply();
      
      console.log("1. 初始状态记录");
      console.log("  TokenA总供应量:", ethers.formatEther(initialTokenASupply));
      console.log("  TokenB总供应量:", ethers.formatEther(initialTokenBSupply));
      
      // 步骤1: 用户获得NFT
      await minerNFT.mint(await user1.getAddress());
      console.log("2. ✓ 用户获得NFT #1");
      
      // 步骤2: 用户获得代币
      await tokenA.transfer(await user1.getAddress(), ethers.parseEther("100000"));
      await tokenB.transfer(await user1.getAddress(), ethers.parseEther("1000000"));
      console.log("3. ✓ 用户获得代币");
      
      // 步骤3: 设置邀请关系
      await miningContract.connect(user1).setInviter(await owner.getAddress());
      console.log("4. ✓ 设置邀请关系");
      
      // 步骤4: 参与挖矿机制1
      const burnAmountA = ethers.parseEther("10000");
      await tokenA.connect(user1).approve(await miningContract.getAddress(), burnAmountA);
      await miningContract.connect(user1).burnTokenAForTokenB(burnAmountA);
      console.log("5. ✓ 参与挖矿机制1（销毁TokenA）");
      
      // 步骤5: 参与挖矿机制2
      const burnAmountB = ethers.parseEther("100000");
      await tokenB.connect(user1).approve(await miningContract.getAddress(), burnAmountB);
      await miningContract.connect(user1).burnTokenBWithNFT(burnAmountB);
      console.log("6. ✓ 参与挖矿机制2（销毁TokenB）");
      
      // 步骤6: 进行交易产生税收和分红
      const pairAddress = await mockFactory.getPair(await tokenA.getAddress(), await mockWBNB.getAddress());
      await tokenA.connect(user1).transfer(pairAddress, ethers.parseEther("20000"));
      console.log("7. ✓ 交易产生税收和分红");
      
      // 步骤7: 查看和领取分红
      await tokenA.setAutoDividendConfig(500000, 10, ethers.parseEther("100"), false);
      const dividend = await tokenA.calculateDividend(await user1.getAddress());
      
      if (dividend > 0) {
        const initialBalance = await tokenA.balanceOf(await user1.getAddress());
        await tokenA.connect(user1).claimDividend();
        const finalBalance = await tokenA.balanceOf(await user1.getAddress());
        const received = finalBalance - initialBalance;
        
        console.log("8. ✓ 领取分红:", ethers.formatEther(received));
      }
      
      // 步骤8: NFT代币释放
      await minerNFT.setReleaseParameters(
        Math.floor(Date.now() / 1000),
        30 * 24 * 3600,
        10
      );
      
      await ethers.provider.send("evm_increaseTime", [35 * 24 * 3600]);
      await ethers.provider.send("evm_mine");
      
      const releasableAmount = await minerNFT.getReleasableAmount(1);
      if (releasableAmount > 0) {
        const initialBalance = await tokenA.balanceOf(await user1.getAddress());
        await minerNFT.connect(user1).releaseTokens(1);
        const finalBalance = await tokenA.balanceOf(await user1.getAddress());
        const released = finalBalance - initialBalance;
        
        console.log("9. ✓ NFT释放代币:", ethers.formatEther(released));
      }
      
      // 最终状态验证
      const finalTokenASupply = await tokenA.totalSupply();
      const finalTokenBSupply = await tokenB.totalSupply();
      
      console.log("10. 最终状态");
      console.log("  TokenA供应量变化:", ethers.formatEther(finalTokenASupply - initialTokenASupply));
      console.log("  TokenB供应量变化:", ethers.formatEther(finalTokenBSupply - initialTokenBSupply));
      
      // 验证供应量变化合理（TokenA应该减少，TokenB保持不变或略有变化）
      expect(finalTokenASupply).to.be.lt(initialTokenASupply); // TokenA因销毁而减少
      
      console.log("=== 完整用户生命周期测试成功 ===");
    });
    
    it("应该验证多用户并发操作的一致性", async function () {
      console.log("=== 多用户并发操作测试 ===");
      
      // 为5个用户准备NFT和代币
      const users = [user1, user2, user3, user4, user5];
      for (let i = 0; i < users.length; i++) {
        await minerNFT.mint(await users[i].getAddress());
        await tokenA.transfer(await users[i].getAddress(), ethers.parseEther("50000"));
      }
      
      // 记录初始状态
      const initialTotalDividends = await tokenA.totalDividends();
      const initialContractBalance = await tokenA.balanceOf(await tokenA.getAddress());
      
      // 所有用户同时进行交易
      const pairAddress = await mockFactory.getPair(await tokenA.getAddress(), await mockWBNB.getAddress());
      const promises = [];
      
      for (let i = 0; i < users.length; i++) {
        promises.push(
          tokenA.connect(users[i]).transfer(pairAddress, ethers.parseEther("10000"))
        );
      }
      
      await Promise.all(promises);
      console.log("✓ 5个用户并发交易完成");
      
      // 验证总分红增加
      const finalTotalDividends = await tokenA.totalDividends();
      const dividendIncrease = finalTotalDividends - initialTotalDividends;
      
      console.log("分红增加:", ethers.formatEther(dividendIncrease));
      expect(dividendIncrease).to.be.gt(0);
      
      // 验证每个用户的分红相等（因为都持有1个NFT）
      await tokenA.setAutoDividendConfig(500000, 10, ethers.parseEther("100"), false);
      
      const dividends = [];
      for (let i = 0; i < users.length; i++) {
        const dividend = await tokenA.calculateDividend(await users[i].getAddress());
        dividends.push(dividend);
        console.log(`User${i+1}分红:`, ethers.formatEther(dividend));
      }
      
      // 验证分红相等性
      for (let i = 1; i < dividends.length; i++) {
        expect(dividends[i]).to.equal(dividends[0]);
      }
      
      console.log("✓ 多用户分红分配验证通过");
    });
  });
});