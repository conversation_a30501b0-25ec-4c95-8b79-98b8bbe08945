const { expect } = require("chai");
const { ethers } = require("hardhat");

describe("税收计算精度问题根因分析", function () {
  let tokenA, tokenB;
  let mockWBNB, mockFactory, mockRouter;
  let owner, marketingWallet, user1, user2;
  
  beforeEach(async function () {
    [owner, marketingWallet, user1, user2] = await ethers.getSigners();
    
    // 部署模拟环境
    const MockWBNB = await ethers.getContractFactory("MockWBNB");
    mockWBNB = await MockWBNB.deploy();
    await mockWBNB.waitForDeployment();
    
    const MockPancakeFactory = await ethers.getContractFactory("MockPancakeFactory");
    mockFactory = await MockPancakeFactory.deploy();
    await mockFactory.waitForDeployment();
    
    const MockPancakeRouter = await ethers.getContractFactory("MockPancakeRouter");
    mockRouter = await MockPancakeRouter.deploy(
      await mockFactory.getAddress(),
      await mockWBNB.getAddress()
    );
    await mockRouter.waitForDeployment();
    
    // 部署TokenA和TokenB
    const TokenA = await ethers.getContractFactory("TokenA");
    tokenA = await TokenA.deploy(
      await marketingWallet.getAddress(),
      await mockRouter.getAddress(),
      await mockWBNB.getAddress()
    );
    await tokenA.waitForDeployment();
    
    const TokenB = await ethers.getContractFactory("TokenB");
    tokenB = await TokenB.deploy(
      await marketingWallet.getAddress(),
      await mockRouter.getAddress(),
      await mockWBNB.getAddress()
    );
    await tokenB.waitForDeployment();
  });

  describe("1. 税收计算步骤分析", function () {
    it("应该逐步分析TokenA税收计算过程", async function () {
      console.log("=== TokenA税收计算步骤分析 ===");
      
      const pairAddress = await createTradingPair(tokenA);
      const transferAmount = ethers.parseEther("10000");
      
      // 给用户代币
      await tokenA.transfer(await user1.getAddress(), transferAmount);
      
      console.log("\n--- 理论计算 ---");
      const theoreticalTax = transferAmount * BigInt(3) / BigInt(100);
      const theoreticalMarketing = theoreticalTax * BigInt(20) / BigInt(100);
      const theoreticalBurn = theoreticalTax * BigInt(50) / BigInt(100);
      const theoreticalDividend = theoreticalTax * BigInt(30) / BigInt(100);
      
      console.log("转账金额:", ethers.formatEther(transferAmount));
      console.log("理论税收:", ethers.formatEther(theoreticalTax));
      console.log("理论分配 - 营销:", ethers.formatEther(theoreticalMarketing));
      console.log("理论分配 - 销毁:", ethers.formatEther(theoreticalBurn));
      console.log("理论分配 - 分红:", ethers.formatEther(theoreticalDividend));
      
      // 记录转账前状态
      const beforeMarketing = await tokenA.balanceOf(await marketingWallet.getAddress());
      const beforeContract = await tokenA.balanceOf(await tokenA.getAddress());
      const beforeSupply = await tokenA.totalSupply();
      
      console.log("\n--- 转账前状态 ---");
      console.log("营销钱包余额:", ethers.formatEther(beforeMarketing));
      console.log("合约余额:", ethers.formatEther(beforeContract));
      console.log("总供应量:", ethers.formatEther(beforeSupply));
      
      // 执行转账并分析
      const tx = await tokenA.connect(user1).transfer(pairAddress, transferAmount);
      const receipt = await tx.wait();
      
      // 分析事件
      console.log("\n--- 事件分析 ---");
      for (const log of receipt.logs) {
        try {
          const parsed = tokenA.interface.parseLog(log);
          if (parsed.name === "TaxCollected") {
            console.log("TaxCollected事件:");
            console.log("  营销金额:", ethers.formatEther(parsed.args.marketingAmount));
            console.log("  销毁金额:", ethers.formatEther(parsed.args.burnAmount));
            console.log("  分红金额:", ethers.formatEther(parsed.args.dividendAmount));
            
            const eventTotal = parsed.args.marketingAmount + parsed.args.burnAmount + parsed.args.dividendAmount;
            console.log("  事件总计:", ethers.formatEther(eventTotal));
          }
        } catch (e) {
          // 忽略其他事件
        }
      }
      
      // 记录转账后状态
      const afterMarketing = await tokenA.balanceOf(await marketingWallet.getAddress());
      const afterContract = await tokenA.balanceOf(await tokenA.getAddress());
      const afterSupply = await tokenA.totalSupply();
      
      console.log("\n--- 转账后状态 ---");
      console.log("营销钱包余额:", ethers.formatEther(afterMarketing));
      console.log("合约余额:", ethers.formatEther(afterContract));
      console.log("总供应量:", ethers.formatEther(afterSupply));
      
      // 计算实际变化
      const actualMarketing = afterMarketing - beforeMarketing;
      const actualContract = afterContract - beforeContract;
      const actualBurn = beforeSupply - afterSupply;
      const actualTotal = actualMarketing + actualContract + actualBurn;
      
      console.log("\n--- 实际变化 ---");
      console.log("营销增加:", ethers.formatEther(actualMarketing));
      console.log("合约增加:", ethers.formatEther(actualContract));
      console.log("供应减少:", ethers.formatEther(actualBurn));
      console.log("总计变化:", ethers.formatEther(actualTotal));
      
      // 精度分析
      console.log("\n--- 精度分析 ---");
      const marketingError = abs(actualMarketing - theoreticalMarketing);
      const burnError = abs(actualBurn - theoreticalBurn);
      const dividendError = abs(actualContract - theoreticalDividend);
      const totalError = abs(actualTotal - theoreticalTax);
      
      console.log("营销精度误差:", ethers.formatEther(marketingError));
      console.log("销毁精度误差:", ethers.formatEther(burnError));
      console.log("分红精度误差:", ethers.formatEther(dividendError));
      console.log("总计精度误差:", ethers.formatEther(totalError));
      
      // 百分比误差
      if (theoreticalTax > 0) {
        const totalErrorPercent = Number(totalError * BigInt(10000) / theoreticalTax) / 100;
        console.log("总计精度误差百分比:", totalErrorPercent + "%");
      }
    });
    
    it("应该分析TokenB的税收计算差异", async function () {
      console.log("=== TokenB税收计算分析 ===");
      
      const pairAddress = await createTradingPair(tokenB);
      const transferAmount = ethers.parseEther("10000");
      
      await tokenB.transfer(await user1.getAddress(), transferAmount);
      
      // TokenB的分配比例：50%营销，50%销毁
      const theoreticalTax = transferAmount * BigInt(3) / BigInt(100);
      const theoreticalMarketing = theoreticalTax * BigInt(50) / BigInt(100);
      const theoreticalBurn = theoreticalTax * BigInt(50) / BigInt(100);
      
      console.log("理论税收:", ethers.formatEther(theoreticalTax));
      console.log("理论营销:", ethers.formatEther(theoreticalMarketing));
      console.log("理论销毁:", ethers.formatEther(theoreticalBurn));
      
      const beforeMarketing = await tokenB.balanceOf(await marketingWallet.getAddress());
      const beforeSupply = await tokenB.totalSupply();
      
      await tokenB.connect(user1).transfer(pairAddress, transferAmount);
      
      const afterMarketing = await tokenB.balanceOf(await marketingWallet.getAddress());
      const afterSupply = await tokenB.totalSupply();
      
      const actualMarketing = afterMarketing - beforeMarketing;
      const actualBurn = beforeSupply - afterSupply;
      
      console.log("实际营销:", ethers.formatEther(actualMarketing));
      console.log("实际销毁:", ethers.formatEther(actualBurn));
      
      const marketingError = abs(actualMarketing - theoreticalMarketing);
      const burnError = abs(actualBurn - theoreticalBurn);
      
      console.log("营销误差:", ethers.formatEther(marketingError));
      console.log("销毁误差:", ethers.formatEther(burnError));
    });
  });

  describe("2. 数学运算精度分析", function () {
    it("应该分析除法运算的精度损失", async function () {
      console.log("=== 除法运算精度分析 ===");
      
      const testAmounts = [
        1n,                     // 1 wei
        33n,                    // 接近最小税收
        100n,                   // 100 wei
        1000n,                  // 1000 wei
        ethers.parseEther("1"), // 1 ETH
        ethers.parseEther("100"), // 100 ETH
        ethers.parseEther("12345.67891") // 不规则数值
      ];
      
      for (let amount of testAmounts) {
        console.log(`\n--- 测试金额: ${ethers.formatEther(amount)} ETH ---`);
        
        // 3%税收计算
        const tax = amount * BigInt(3) / BigInt(100);
        const taxRemainder = (amount * BigInt(3)) % BigInt(100);
        
        console.log("税收计算: amount * 3 / 100");
        console.log("  结果:", ethers.formatEther(tax));
        console.log("  余数:", taxRemainder.toString(), "wei");
        
        if (tax > 0) {
          // TokenA的三重分配
          const marketing = tax * BigInt(20) / BigInt(100);
          const burn = tax * BigInt(50) / BigInt(100);
          const dividend = tax * BigInt(30) / BigInt(100);
          
          const marketingRemainder = (tax * BigInt(20)) % BigInt(100);
          const burnRemainder = (tax * BigInt(50)) % BigInt(100);
          const dividendRemainder = (tax * BigInt(30)) % BigInt(100);
          
          console.log("分配计算:");
          console.log("  营销 (20%):", ethers.formatEther(marketing), "余数:", marketingRemainder.toString());
          console.log("  销毁 (50%):", ethers.formatEther(burn), "余数:", burnRemainder.toString());
          console.log("  分红 (30%):", ethers.formatEther(dividend), "余数:", dividendRemainder.toString());
          
          const distributionSum = marketing + burn + dividend;
          const distributionLoss = tax - distributionSum;
          
          console.log("  分配总和:", ethers.formatEther(distributionSum));
          console.log("  分配损失:", ethers.formatEther(distributionLoss), "wei");
          
          if (distributionLoss !== 0n) {
            const lossPercent = Number(distributionLoss * BigInt(10000) / tax) / 100;
            console.log("  损失百分比:", lossPercent + "%");
          }
        }
      }
    });
    
    it("应该分析累积误差的传播", async function () {
      console.log("=== 累积误差传播分析 ===");
      
      let totalTheoreticalTax = 0n;
      let totalActualTax = 0n;
      
      const pairAddress = await createTradingPair(tokenA);
      const transferAmount = ethers.parseEther("1000");
      
      console.log("执行100次转账，观察累积误差...");
      
      for (let i = 0; i < 10; i++) { // 减少到10次以节省时间
        await tokenA.transfer(await user1.getAddress(), transferAmount);
        
        const beforeContract = await tokenA.balanceOf(await tokenA.getAddress());
        const beforeMarketing = await tokenA.balanceOf(await marketingWallet.getAddress());
        const beforeSupply = await tokenA.totalSupply();
        
        await tokenA.connect(user1).transfer(pairAddress, transferAmount);
        
        const afterContract = await tokenA.balanceOf(await tokenA.getAddress());
        const afterMarketing = await tokenA.balanceOf(await marketingWallet.getAddress());
        const afterSupply = await tokenA.totalSupply();
        
        const actualTaxThisRound = (afterMarketing - beforeMarketing) + 
                                  (afterContract - beforeContract) + 
                                  (beforeSupply - afterSupply);
        
        const theoreticalTaxThisRound = transferAmount * BigInt(3) / BigInt(100);
        
        totalActualTax += actualTaxThisRound;
        totalTheoreticalTax += theoreticalTaxThisRound;
        
        if ((i + 1) % 5 === 0) {
          const cumulativeError = abs(totalActualTax - totalTheoreticalTax);
          const cumulativeErrorPercent = Number(cumulativeError * BigInt(10000) / totalTheoreticalTax) / 100;
          
          console.log(`第${i+1}次后累积误差: ${ethers.formatEther(cumulativeError)} ETH (${cumulativeErrorPercent}%)`);
        }
      }
      
      console.log("\n最终累积分析:");
      console.log("理论总税收:", ethers.formatEther(totalTheoreticalTax));
      console.log("实际总税收:", ethers.formatEther(totalActualTax));
      console.log("累积误差:", ethers.formatEther(abs(totalActualTax - totalTheoreticalTax)));
      
      const finalErrorPercent = Number(abs(totalActualTax - totalTheoreticalTax) * BigInt(10000) / totalTheoreticalTax) / 100;
      console.log("最终误差百分比:", finalErrorPercent + "%");
    });
  });

  describe("3. 自动分红对税收统计的影响", function () {
    it("应该分析自动分红如何影响税收统计", async function () {
      console.log("=== 自动分红对税收统计影响分析 ===");
      
      // 部署NFT合约以启用分红
      const MinerNFT = await ethers.getContractFactory("MinerNFT");
      const minerNFT = await MinerNFT.deploy("MinerNFT", "MNFT", await tokenA.getAddress());
      await minerNFT.waitForDeployment();
      
      await tokenA.setNftContract(await minerNFT.getAddress());
      await tokenA.transfer(await minerNFT.getAddress(), ethers.parseEther("1000000"));
      
      // 铸造NFT
      await minerNFT.mint(await user2.getAddress());
      
      const pairAddress = await createTradingPair(tokenA);
      const transferAmount = ethers.parseEther("10000");
      
      console.log("启用自动分红的转账分析:");
      
      // 检查自动分红配置
      const autoDividendStatus = await tokenA.getAutoDividendStatus();
      console.log("自动分红状态:", {
        enabled: autoDividendStatus[0],
        gasLimit: autoDividendStatus[1].toString(),
        maxNFTs: autoDividendStatus[2].toString(),
        threshold: ethers.formatEther(autoDividendStatus[3])
      });
      
      await tokenA.transfer(await user1.getAddress(), transferAmount);
      
      const beforeStates = {
        contract: await tokenA.balanceOf(await tokenA.getAddress()),
        marketing: await tokenA.balanceOf(await marketingWallet.getAddress()),
        supply: await tokenA.totalSupply(),
        user2: await tokenA.balanceOf(await user2.getAddress())
      };
      
      console.log("转账前状态:");
      console.log("  合约余额:", ethers.formatEther(beforeStates.contract));
      console.log("  营销余额:", ethers.formatEther(beforeStates.marketing));
      console.log("  用户2余额:", ethers.formatEther(beforeStates.user2));
      
      // 执行转账
      const tx = await tokenA.connect(user1).transfer(pairAddress, transferAmount);
      const receipt = await tx.wait();
      
      console.log("交易Gas消耗:", receipt.gasUsed.toString());
      
      // 分析事件
      console.log("\n事件分析:");
      for (const log of receipt.logs) {
        try {
          const parsed = tokenA.interface.parseLog(log);
          if (parsed.name === "TaxCollected") {
            console.log("TaxCollected:", {
              marketing: ethers.formatEther(parsed.args.marketingAmount),
              burn: ethers.formatEther(parsed.args.burnAmount),
              dividend: ethers.formatEther(parsed.args.dividendAmount)
            });
          } else if (parsed.name === "DividendDistributed") {
            console.log("DividendDistributed:", ethers.formatEther(parsed.args.amount));
          } else if (parsed.name === "AutoDividendProcessed") {
            console.log("AutoDividendProcessed:", {
              startIndex: parsed.args.startIndex.toString(),
              endIndex: parsed.args.endIndex.toString(),
              totalAmount: ethers.formatEther(parsed.args.totalAmount)
            });
          } else if (parsed.name === "DividendClaimed") {
            console.log("DividendClaimed:", {
              user: parsed.args.user,
              amount: ethers.formatEther(parsed.args.amount)
            });
          }
        } catch (e) {
          // 忽略其他合约事件
        }
      }
      
      const afterStates = {
        contract: await tokenA.balanceOf(await tokenA.getAddress()),
        marketing: await tokenA.balanceOf(await marketingWallet.getAddress()),
        supply: await tokenA.totalSupply(),
        user2: await tokenA.balanceOf(await user2.getAddress())
      };
      
      console.log("\n转账后状态:");
      console.log("  合约余额:", ethers.formatEther(afterStates.contract));
      console.log("  营销余额:", ethers.formatEther(afterStates.marketing));
      console.log("  用户2余额:", ethers.formatEther(afterStates.user2));
      
      // 分析自动分红的影响
      const marketingIncrease = afterStates.marketing - beforeStates.marketing;
      const contractChange = afterStates.contract - beforeStates.contract;
      const supplyDecrease = beforeStates.supply - afterStates.supply;
      const user2Increase = afterStates.user2 - beforeStates.user2;
      
      console.log("\n变化分析:");
      console.log("  营销增加:", ethers.formatEther(marketingIncrease));
      console.log("  合约变化:", ethers.formatEther(contractChange));
      console.log("  供应减少:", ethers.formatEther(supplyDecrease));
      console.log("  用户2增加:", ethers.formatEther(user2Increase));
      
      // 理论vs实际
      const theoreticalTax = transferAmount * BigInt(3) / BigInt(100);
      const theoreticalDividend = theoreticalTax * BigInt(30) / BigInt(100);
      
      console.log("\n理论vs实际:");
      console.log("  理论分红:", ethers.formatEther(theoreticalDividend));
      console.log("  实际用户获得:", ethers.formatEther(user2Increase));
      console.log("  合约剩余:", ethers.formatEther(contractChange));
    });
  });

  // 辅助函数
  async function createTradingPair(token) {
    const liquidityToken = ethers.parseEther("50000");
    const liquidityWBNB = ethers.parseEther("50");
    
    await mockWBNB.deposit({ value: liquidityWBNB });
    await token.approve(await mockRouter.getAddress(), liquidityToken);
    await mockWBNB.approve(await mockRouter.getAddress(), liquidityWBNB);
    
    await mockRouter.addLiquidity(
      await token.getAddress(),
      await mockWBNB.getAddress(),
      liquidityToken,
      liquidityWBNB,
      0, 0,
      await owner.getAddress(),
      Math.floor(Date.now() / 1000) + 3600
    );
    
    const pairAddress = await mockFactory.getPair(await token.getAddress(), await mockWBNB.getAddress());
    await token.setPair(pairAddress, true);
    
    return pairAddress;
  }
  
  function abs(value) {
    return value >= 0n ? value : -value;
  }
});