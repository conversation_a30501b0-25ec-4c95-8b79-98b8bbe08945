const { expect } = require("chai");
const { ethers } = require("hardhat");

describe("TokenA Contract", function () {
  let tokenA;
  let owner, marketingWallet, user1, user2, pancakeSwapRouter, wbnb, nftContract;
  let mockNFT;

  const TOTAL_SUPPLY = ethers.parseEther("*********"); // 2.5亿
  const TAX_RATE = 3; // 3%

  beforeEach(async function () {
    [owner, marketingWallet, user1, user2, pancakeSwapRouter, wbnb] = await ethers.getSigners();

    // 部署TokenA合约 - 使用BSC测试网真实地址
    const TokenA = await ethers.getContractFactory("TokenA");
    tokenA = await TokenA.deploy(
      await marketingWallet.getAddress(),
      "******************************************", // BSC测试网Router
      "******************************************"  // BSC测试网WBNB
    );
    // 部署MinerNFT合约
    const MinerNFT = await ethers.getContractFactory("MinerNFT");
    mockNFT = await MinerNFT.deploy("MinerNFT", "MNFT", await tokenA.getAddress());

    // 设置NFT合约地址
    await tokenA.setNftContract(await mockNFT.getAddress());
  });

  describe("部署", function () {
    it("应该正确设置初始参数", async function () {
      expect(await tokenA.name()).to.equal("TokenA");
      expect(await tokenA.symbol()).to.equal("TKA");
      expect(await tokenA.totalSupply()).to.equal(TOTAL_SUPPLY);
      expect(await tokenA.balanceOf(await owner.getAddress())).to.equal(TOTAL_SUPPLY);
      expect(await tokenA.marketingWallet()).to.equal(await marketingWallet.getAddress());
    });

    it("应该设置正确的税收豁免地址", async function () {
      expect(await tokenA.taxExempt(await owner.getAddress())).to.be.true;
      expect(await tokenA.taxExempt(await tokenA.getAddress())).to.be.true;
      expect(await tokenA.taxExempt(await marketingWallet.getAddress())).to.be.true;
    });
  });

  describe("税收机制", function () {
    beforeEach(async function () {
      // 转一些代币给用户进行测试
      await tokenA.transfer(await user1.getAddress(), ethers.parseEther("1000"));
      
      // 设置一个模拟交易对
      await tokenA.setPair(await user2.getAddress(), true);
    });

    it("税收豁免地址之间转账不应该收税", async function () {
      const amount = ethers.parseEther("100");
      
      await tokenA.connect(owner).transfer(await marketingWallet.getAddress(), amount);
      
      expect(await tokenA.balanceOf(await marketingWallet.getAddress())).to.equal(amount);
    });

    it("向交易对出售时应该收取卖出税", async function () {
      const amount = ethers.parseEther("100");
      const expectedTax = amount * BigInt(TAX_RATE) / BigInt(100);
      const expectedReceived = amount - expectedTax;
      
      const initialBalance = await tokenA.balanceOf(await user2.getAddress());
      
      await tokenA.connect(user1).transfer(await user2.getAddress(), amount);
      
      // 检查接收方实际收到的金额
      const finalBalance = await tokenA.balanceOf(await user2.getAddress());
      expect(finalBalance - initialBalance).to.equal(expectedReceived);
      
      // 检查合约是否收到了税收
      expect(await tokenA.balanceOf(await tokenA.getAddress())).to.be.gt(0);
    });

    it("从交易对购买时应该收取买入税", async function () {
      // 先给交易对一些代币
      await tokenA.transfer(await user2.getAddress(), ethers.parseEther("1000"));
      
      const amount = ethers.parseEther("100");
      const expectedTax = amount * BigInt(TAX_RATE) / BigInt(100);
      const expectedReceived = amount - expectedTax;
      
      const initialBalance = await tokenA.balanceOf(await user1.getAddress());
      
      await tokenA.connect(user2).transfer(await user1.getAddress(), amount);
      
      // 检查接收方实际收到的金额
      const finalBalance = await tokenA.balanceOf(await user1.getAddress());
      expect(finalBalance - initialBalance).to.equal(expectedReceived);
    });
  });

  describe("分红机制", function () {
    beforeEach(async function () {
      // 给用户1铸造一些NFT
      await mockNFT.mint(await user1.getAddress());
      await mockNFT.mint(await user1.getAddress());
      
      // 设置交易对并进行一些交易以产生分红
      await tokenA.setPair(await user2.getAddress(), true);
      await tokenA.transfer(await user1.getAddress(), ethers.parseEther("1000"));
    });

    it("应该正确计算用户分红", async function () {
      // 禁用自动分红来测试手动分红计算
      await tokenA.setAutoDividendConfig(500000, 10, ethers.parseEther("100"), false);
      
      // 执行一笔产生税收的交易
      await tokenA.connect(user1).transfer(await user2.getAddress(), ethers.parseEther("100"));
      
      // 检查是否有分红生成
      const totalDividends = await tokenA.totalDividends();
      expect(totalDividends).to.be.gt(0);
      
      // 计算用户应得分红
      const userDividend = await tokenA.calculateDividend(await user1.getAddress());
      expect(userDividend).to.be.gt(0);
    });

    it("用户应该能够领取分红", async function () {
      // 先禁用自动分红来测试手动分红
      await tokenA.setAutoDividendConfig(500000, 10, ethers.parseEther("100"), false);
      
      // 执行一笔产生税收的交易
      await tokenA.connect(user1).transfer(await user2.getAddress(), ethers.parseEther("100"));
      
      const initialBalance = await tokenA.balanceOf(await user1.getAddress());
      const userDividend = await tokenA.calculateDividend(await user1.getAddress());
      
      if (userDividend > 0) {
        await tokenA.connect(user1).claimDividend();
        
        const finalBalance = await tokenA.balanceOf(await user1.getAddress());
        const actualReceived = finalBalance - initialBalance;
        // 验证用户确实收到了分红（允许合理的精度差异）
        expect(actualReceived).to.be.gt(0);
        console.log(`用户分红：计算 ${ethers.formatEther(userDividend)}, 实际 ${ethers.formatEther(actualReceived)}`);
      } else {
        console.log("没有可领取的分红");
      }
      
      // 重新启用自动分红
      await tokenA.setAutoDividendConfig(500000, 10, ethers.parseEther("0.001"), true);
    });
  });

  describe("管理功能", function () {
    it("只有owner能设置税收豁免地址", async function () {
      await expect(
        tokenA.connect(user1).setTaxExempt(await user1.getAddress(), true)
      ).to.be.revertedWithCustomError(tokenA, "OwnableUnauthorizedAccount");
      
      await tokenA.setTaxExempt(await user1.getAddress(), true);
      expect(await tokenA.taxExempt(await user1.getAddress())).to.be.true;
    });

    it("只有owner能设置交易对地址", async function () {
      await expect(
        tokenA.connect(user1).setPair(await user1.getAddress(), true)
      ).to.be.revertedWithCustomError(tokenA, "OwnableUnauthorizedAccount");
      
      await tokenA.setPair(await user1.getAddress(), true);
      expect(await tokenA.isPair(await user1.getAddress())).to.be.true;
    });

    it("只有owner能暂停/恢复合约", async function () {
      // 给user1一些代币用于测试
      await tokenA.transfer(await user1.getAddress(), ethers.parseEther("1000"));
      
      await expect(
        tokenA.connect(user1).pause()
      ).to.be.revertedWithCustomError(tokenA, "OwnableUnauthorizedAccount");
      
      await tokenA.pause();
      
      await expect(
        tokenA.connect(user1).transfer(await user2.getAddress(), ethers.parseEther("100"))
      ).to.be.revertedWithCustomError(tokenA, "EnforcedPause");
      
      await tokenA.unpause();
      
      // 现在应该可以正常转账
      await tokenA.connect(user1).transfer(await user2.getAddress(), ethers.parseEther("100"));
    });
  });

  describe("紧急功能", function () {
    it("owner应该能够紧急提取合约中的代币", async function () {
      // 先向合约发送一些代币
      await tokenA.transfer(await tokenA.getAddress(), ethers.parseEther("100"));
      
      const contractBalance = await tokenA.balanceOf(await tokenA.getAddress());
      const ownerInitialBalance = await tokenA.balanceOf(await owner.getAddress());
      
      await tokenA.emergencyWithdraw();
      
      const ownerFinalBalance = await tokenA.balanceOf(await owner.getAddress());
      expect(ownerFinalBalance - ownerInitialBalance).to.equal(contractBalance);
      expect(await tokenA.balanceOf(await tokenA.getAddress())).to.equal(0);
    });
  });
});

