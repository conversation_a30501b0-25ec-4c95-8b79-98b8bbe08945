const { expect } = require("chai");
const { ethers } = require("hardhat");

describe("完整系统测试（带模拟PancakeSwap）", function () {
  let tokenA, tokenB, minerNFT, miningContract, priceOracle;
  let mockWBNB, mockFactory, mockRouter;
  let owner, marketingWallet, user1, user2, user3;
  
  beforeEach(async function () {
    [owner, marketingWallet, user1, user2, user3] = await ethers.getSigners();
    
    console.log("=== 开始部署模拟PancakeSwap环境 ===");
    
    // 1. 部署模拟WBNB
    const MockWBNB = await ethers.getContractFactory("MockWBNB");
    mockWBNB = await MockWBNB.deploy();
    await mockWBNB.waitForDeployment();
    console.log("MockWBNB deployed:", await mockWBNB.getAddress());
    
    // 2. 部署模拟Factory
    const MockPancakeFactory = await ethers.getContractFactory("MockPancakeFactory");
    mockFactory = await MockPancakeFactory.deploy();
    await mockFactory.waitForDeployment();
    console.log("MockFactory deployed:", await mockFactory.getAddress());
    
    // 3. 部署模拟Router
    const MockPancakeRouter = await ethers.getContractFactory("MockPancakeRouter");
    mockRouter = await MockPancakeRouter.deploy(
      await mockFactory.getAddress(),
      await mockWBNB.getAddress()
    );
    await mockRouter.waitForDeployment();
    console.log("MockRouter deployed:", await mockRouter.getAddress());
    
    console.log("=== PancakeSwap环境部署完成 ===");
    
    // 4. 部署价格预言机
    const PriceOracle = await ethers.getContractFactory("PriceOracle");
    priceOracle = await PriceOracle.deploy();
    await priceOracle.waitForDeployment();
    console.log("PriceOracle deployed:", await priceOracle.getAddress());
    
    // 5. 部署TokenA（使用模拟的PancakeSwap地址）
    const TokenA = await ethers.getContractFactory("TokenA");
    tokenA = await TokenA.deploy(
      await marketingWallet.getAddress(),
      await mockRouter.getAddress(),
      await mockWBNB.getAddress()
    );
    await tokenA.waitForDeployment();
    console.log("TokenA deployed:", await tokenA.getAddress());
    
    // 6. 部署TokenB（使用模拟的PancakeSwap地址）
    const TokenB = await ethers.getContractFactory("TokenB");
    tokenB = await TokenB.deploy(
      await marketingWallet.getAddress(),
      await mockRouter.getAddress(),
      await mockWBNB.getAddress()
    );
    await tokenB.waitForDeployment();
    console.log("TokenB deployed:", await tokenB.getAddress());
    
    // 7. 部署NFT合约
    const MinerNFT = await ethers.getContractFactory("MinerNFT");
    minerNFT = await MinerNFT.deploy("MinerNFT", "MNFT", await tokenA.getAddress());
    await minerNFT.waitForDeployment();
    console.log("MinerNFT deployed:", await minerNFT.getAddress());
    
    // 8. 部署挖矿合约
    const MiningContract = await ethers.getContractFactory("MiningContract");
    miningContract = await MiningContract.deploy(
      await tokenA.getAddress(),
      await tokenB.getAddress(),
      await minerNFT.getAddress(),
      await priceOracle.getAddress()
    );
    await miningContract.waitForDeployment();
    console.log("MiningContract deployed:", await miningContract.getAddress());
    
    // 9. 配置合约关系
    await tokenA.setNftContract(await minerNFT.getAddress());
    await tokenA.setTaxExempt(await miningContract.getAddress(), true);
    await tokenB.setTaxExempt(await miningContract.getAddress(), true);
    
    // 10. 向NFT合约转入TokenA用于释放
    const nftTokenAmount = ethers.parseEther("21000000"); // 2100万
    await tokenA.transfer(await minerNFT.getAddress(), nftTokenAmount);
    
    // 11. 设置手动价格用于测试
    await priceOracle.setManualPrice(await tokenA.getAddress(), ethers.parseEther("0.001"));
    await priceOracle.setManualPrice(await tokenB.getAddress(), ethers.parseEther("0.0001"));
    
    console.log("=== 系统配置完成 ===");
  });

  describe("PancakeSwap集成测试", function () {
    it("应该能够创建交易对", async function () {
      // 检查TokenA是否自动创建了交易对
      const pairAddress = await mockFactory.getPair(await tokenA.getAddress(), await mockWBNB.getAddress());
      console.log("TokenA-WBNB交易对地址:", pairAddress);
      
      if (pairAddress !== ethers.ZeroAddress) {
        // 验证交易对设置
        expect(await tokenA.isPair(pairAddress)).to.be.true;
        expect(await tokenA.taxExempt(pairAddress)).to.be.true;
        console.log("✓ TokenA交易对创建成功并正确配置");
      } else {
        // 手动创建交易对
        await tokenA.createAndSetPair();
        const newPairAddress = await mockFactory.getPair(await tokenA.getAddress(), await mockWBNB.getAddress());
        expect(newPairAddress).to.not.equal(ethers.ZeroAddress);
        console.log("✓ 手动创建TokenA交易对成功");
      }
    });
    
    it("应该能够添加流动性", async function () {
      // 为WBNB和TokenA添加流动性
      const tokenAAmount = ethers.parseEther("1000000"); // 100万TokenA
      const wbnbAmount = ethers.parseEther("1000"); // 1000 WBNB
      
      // 给WBNB一些余额
      await mockWBNB.deposit({ value: wbnbAmount });
      
      // 授权Router使用代币
      await tokenA.approve(await mockRouter.getAddress(), tokenAAmount);
      await mockWBNB.approve(await mockRouter.getAddress(), wbnbAmount);
      
      // 添加流动性
      const tx = await mockRouter.addLiquidity(
        await tokenA.getAddress(),
        await mockWBNB.getAddress(),
        tokenAAmount,
        wbnbAmount,
        tokenAAmount * BigInt(95) / BigInt(100), // 5%滑点
        wbnbAmount * BigInt(95) / BigInt(100),
        await owner.getAddress(),
        Math.floor(Date.now() / 1000) + 3600 // 1小时后过期
      );
      
      const receipt = await tx.wait();
      console.log("添加流动性 Gas:", receipt.gasUsed.toString());
      
      // 验证流动性添加成功
      const pairAddress = await mockFactory.getPair(await tokenA.getAddress(), await mockWBNB.getAddress());
      const pairBalance = await ethers.getContractAt("MockPancakePair", pairAddress);
      const lpBalance = await pairBalance.balanceOf(await owner.getAddress());
      expect(lpBalance).to.be.gt(0);
      console.log("✓ 流动性添加成功，LP代币余额:", ethers.formatEther(lpBalance));
    });
  });

  describe("税收机制with真实交易对", function () {
    beforeEach(async function () {
      // 创建流动性池
      const tokenAAmount = ethers.parseEther("1000000");
      const wbnbAmount = ethers.parseEther("1000");
      
      await mockWBNB.deposit({ value: wbnbAmount });
      await tokenA.approve(await mockRouter.getAddress(), tokenAAmount);
      await mockWBNB.approve(await mockRouter.getAddress(), wbnbAmount);
      
      await mockRouter.addLiquidity(
        await tokenA.getAddress(),
        await mockWBNB.getAddress(),
        tokenAAmount,
        wbnbAmount,
        0, 0,
        await owner.getAddress(),
        Math.floor(Date.now() / 1000) + 3600
      );
      
      // 给用户一些代币
      await tokenA.transfer(await user1.getAddress(), ethers.parseEther("10000"));
    });
    
    it("应该对真实交易对交易收税", async function () {
      const pairAddress = await mockFactory.getPair(await tokenA.getAddress(), await mockWBNB.getAddress());
      
      // 模拟卖出到交易对（触发卖出税）
      const sellAmount = ethers.parseEther("1000");
      const expectedTax = sellAmount * BigInt(3) / BigInt(100); // 3%税收
      const expectedReceived = sellAmount - expectedTax;
      
      const initialPairBalance = await tokenA.balanceOf(pairAddress);
      const initialContractBalance = await tokenA.balanceOf(await tokenA.getAddress());
      
      // 用户转账到交易对（模拟卖出）
      await tokenA.connect(user1).transfer(pairAddress, sellAmount);
      
      const finalPairBalance = await tokenA.balanceOf(pairAddress);
      const finalContractBalance = await tokenA.balanceOf(await tokenA.getAddress());
      
      // 验证交易对收到的金额（扣除税收）
      expect(finalPairBalance - initialPairBalance).to.equal(expectedReceived);
      
      // 验证合约收到税收
      const taxCollected = finalContractBalance - initialContractBalance;
      expect(taxCollected).to.be.gt(0);
      
      console.log("卖出金额:", ethers.formatEther(sellAmount));
      console.log("预期税收:", ethers.formatEther(expectedTax));
      console.log("实际税收:", ethers.formatEther(taxCollected));
      console.log("✓ 税收机制正常工作");
    });
  });

  describe("Gas优化效果对比", function () {
    beforeEach(async function () {
      // 准备测试环境
      await minerNFT.mint(await user1.getAddress());
      await minerNFT.mint(await user2.getAddress());
      await minerNFT.mint(await user3.getAddress());
      
      const pairAddress = await mockFactory.getPair(await tokenA.getAddress(), await mockWBNB.getAddress());
      await tokenA.setPair(pairAddress, true);
      
      await tokenA.transfer(await user1.getAddress(), ethers.parseEther("10000"));
    });
    
    it("应该记录优化后的Gas使用情况", async function () {
      console.log("=== Gas优化效果测试 ===");
      
      // 1. 基础转账
      const tx1 = await tokenA.transfer(await user1.getAddress(), ethers.parseEther("1000"));
      const receipt1 = await tx1.wait();
      console.log("基础转账 Gas:", receipt1.gasUsed.toString());
      
      // 2. 带税收的转账
      const pairAddress = await mockFactory.getPair(await tokenA.getAddress(), await mockWBNB.getAddress());
      const tx2 = await tokenA.connect(user1).transfer(pairAddress, ethers.parseEther("1000"));
      const receipt2 = await tx2.wait();
      console.log("带税收转账 Gas:", receipt2.gasUsed.toString());
      
      // 3. NFT批量铸造
      const addresses = [await user1.getAddress(), await user2.getAddress(), await user3.getAddress()];
      const tx3 = await minerNFT.batchMint(addresses);
      const receipt3 = await tx3.wait();
      console.log("批量铸造3个NFT Gas:", receipt3.gasUsed.toString());
      
      // 4. 自动分红配置更新（优化的存储写入）
      const tx4 = await tokenA.setAutoDividendConfig(300000, 20, ethers.parseEther("0.01"), true);
      const receipt4 = await tx4.wait();
      console.log("自动分红配置 Gas:", receipt4.gasUsed.toString());
      
      // 5. 分红领取（优化的重入保护）
      // 先产生一些分红
      await tokenA.setAutoDividendConfig(500000, 10, ethers.parseEther("100"), false);
      await tokenA.connect(user1).transfer(pairAddress, ethers.parseEther("3000"));
      
      const dividend = await tokenA.calculateDividend(await user1.getAddress());
      if (dividend > 0) {
        const tx5 = await tokenA.connect(user1).claimDividend();
        const receipt5 = await tx5.wait();
        console.log("分红领取 Gas:", receipt5.gasUsed.toString());
        console.log("分红金额:", ethers.formatEther(dividend));
      }
    });
    
    it("应该验证循环优化效果", async function () {
      // 测试批量操作的Gas效率
      const batchSizes = [1, 3, 5, 10];
      
      for (const size of batchSizes) {
        const addresses = [];
        for (let i = 0; i < size; i++) {
          addresses.push(await user1.getAddress());
        }
        
        const tx = await minerNFT.batchMint(addresses);
        const receipt = await tx.wait();
        const gasPerNFT = receipt.gasUsed / BigInt(size);
        
        console.log(`批量铸造${size}个NFT: 总Gas ${receipt.gasUsed.toString()}, 平均 ${gasPerNFT.toString()}/NFT`);
      }
    });
  });

  describe("重入保护安全测试", function () {
    beforeEach(async function () {
      // 准备分红测试环境
      await minerNFT.mint(await user1.getAddress());
      await minerNFT.mint(await user1.getAddress());
      
      const pairAddress = await mockFactory.getPair(await tokenA.getAddress(), await mockWBNB.getAddress());
      await tokenA.setPair(pairAddress, true);
      await tokenA.transfer(await user1.getAddress(), ethers.parseEther("10000"));
      
      // 禁用自动分红，专注测试手动分红
      await tokenA.setAutoDividendConfig(500000, 10, ethers.parseEther("100"), false);
    });
    
    it("应该防止分红领取重入攻击", async function () {
      // 产生分红
      const pairAddress = await mockFactory.getPair(await tokenA.getAddress(), await mockWBNB.getAddress());
      await tokenA.connect(user1).transfer(pairAddress, ethers.parseEther("3000"));
      
      const dividend = await tokenA.calculateDividend(await user1.getAddress());
      console.log("用户可领取分红:", ethers.formatEther(dividend));
      
      if (dividend > 0) {
        // 第一次领取应该成功
        await tokenA.connect(user1).claimDividend();
        
        // 再次查询应该为0或很少
        const remainingDividend = await tokenA.calculateDividend(await user1.getAddress());
        expect(remainingDividend).to.equal(0);
        
        console.log("✓ 重入保护生效，重复领取被阻止");
      }
    });
    
    it("应该防止NFT分红重入攻击", async function () {
      // 产生分红
      const pairAddress = await mockFactory.getPair(await tokenA.getAddress(), await mockWBNB.getAddress());
      await tokenA.connect(user1).transfer(pairAddress, ethers.parseEther("3000"));
      
      const nftDividend = await tokenA.calculateNFTDividend(1);
      console.log("NFT #1可领取分红:", ethers.formatEther(nftDividend));
      
      if (nftDividend > 0) {
        // 领取特定NFT分红
        await tokenA.connect(user1).claimNFTDividend(1);
        
        // 再次查询应该为0
        const remainingNFTDividend = await tokenA.calculateNFTDividend(1);
        expect(remainingNFTDividend).to.equal(0);
        
        console.log("✓ NFT分红重入保护生效");
      }
    });
  });

  describe("自动分红机制性能测试", function () {
    beforeEach(async function () {
      // 创建多个NFT持有者
      for (let i = 0; i < 10; i++) {
        await minerNFT.mint(await user1.getAddress());
      }
      
      const pairAddress = await mockFactory.getPair(await tokenA.getAddress(), await mockWBNB.getAddress());
      await tokenA.setPair(pairAddress, true);
      await tokenA.transfer(await user1.getAddress(), ethers.parseEther("50000"));
      
      // 启用自动分红
      await tokenA.setAutoDividendConfig(500000, 5, ethers.parseEther("0.01"), true);
    });
    
    it("应该自动处理分红分配", async function () {
      const pairAddress = await mockFactory.getPair(await tokenA.getAddress(), await mockWBNB.getAddress());
      
      // 执行大额交易触发自动分红
      const tx = await tokenA.connect(user1).transfer(pairAddress, ethers.parseEther("10000"));
      const receipt = await tx.wait();
      
      console.log("带自动分红的大额交易 Gas:", receipt.gasUsed.toString());
      
      // 检查是否有自动分红事件
      const events = receipt.logs;
      let autoDividendEvent = null;
      
      for (const log of events) {
        try {
          const parsed = tokenA.interface.parseLog(log);
          if (parsed.name === "AutoDividendProcessed") {
            autoDividendEvent = parsed;
            break;
          }
        } catch (e) {
          // 忽略无法解析的日志
        }
      }
      
      if (autoDividendEvent) {
        console.log("✓ 自动分红已触发");
        console.log("处理区间:", autoDividendEvent.args.startIndex.toString(), "->", autoDividendEvent.args.endIndex.toString());
        console.log("分配金额:", ethers.formatEther(autoDividendEvent.args.totalAmount));
      } else {
        console.log("自动分红未触发（可能阈值未达到）");
      }
    });
    
    it("应该能够手动触发自动分红", async function () {
      // 先产生一些分红
      const pairAddress = await mockFactory.getPair(await tokenA.getAddress(), await mockWBNB.getAddress());
      await tokenA.connect(user1).transfer(pairAddress, ethers.parseEther("5000"));
      
      // 手动触发自动分红
      const tx = await tokenA.processAutoDividend();
      const receipt = await tx.wait();
      console.log("手动触发自动分红 Gas:", receipt.gasUsed.toString());
      
      // 检查自动分红状态
      const status = await tokenA.getAutoDividendStatus();
      console.log("自动分红状态:", {
        enabled: status.enabled,
        gasLimit: status.gasLimit.toString(),
        maxNFTs: status.maxNFTs.toString(),
        totalNFTs: status.totalNFTs.toString()
      });
    });
  });

  describe("完整业务流程测试", function () {
    it("应该完成完整的DeFi生态流程", async function () {
      console.log("=== 开始完整业务流程测试 ===");
      
      // 1. 用户获得NFT
      await minerNFT.mint(await user1.getAddress());
      console.log("✓ 1. 用户获得NFT");
      
      // 2. 用户获得代币
      await tokenA.transfer(await user1.getAddress(), ethers.parseEther("10000"));
      await tokenB.transfer(await user1.getAddress(), ethers.parseEther("1000000"));
      console.log("✓ 2. 用户获得代币");
      
      // 3. 用户参与挖矿（销毁TokenA获得TokenB）
      const burnAmountA = ethers.parseEther("1000");
      await tokenA.connect(user1).approve(await miningContract.getAddress(), burnAmountA);
      await miningContract.connect(user1).burnTokenAForTokenB(burnAmountA);
      console.log("✓ 3. 用户销毁TokenA参与挖矿");
      
      // 4. 用户销毁TokenB（需要NFT）
      const burnAmountB = ethers.parseEther("10000");
      await tokenB.connect(user1).approve(await miningContract.getAddress(), burnAmountB);
      await miningContract.connect(user1).burnTokenBWithNFT(burnAmountB);
      console.log("✓ 4. 用户销毁TokenB（NFT挖矿）");
      
      // 5. 交易产生税收和分红
      const pairAddress = await mockFactory.getPair(await tokenA.getAddress(), await mockWBNB.getAddress());
      await tokenA.connect(user1).transfer(pairAddress, ethers.parseEther("2000"));
      console.log("✓ 5. 交易产生税收和分红");
      
      // 6. 用户领取分红
      await tokenA.setAutoDividendConfig(500000, 10, ethers.parseEther("100"), false);
      const dividend = await tokenA.calculateDividend(await user1.getAddress());
      if (dividend > 0) {
        await tokenA.connect(user1).claimDividend();
        console.log("✓ 6. 用户成功领取分红:", ethers.formatEther(dividend));
      }
      
      // 7. NFT释放代币
      await minerNFT.setReleaseParameters(
        Math.floor(Date.now() / 1000),
        30 * 24 * 3600,
        10
      );
      
      // 快进时间
      await ethers.provider.send("evm_increaseTime", [31 * 24 * 3600]);
      await ethers.provider.send("evm_mine");
      
      const releasableAmount = await minerNFT.getReleasableAmount(1);
      if (releasableAmount > 0) {
        await minerNFT.connect(user1).releaseTokens(1);
        console.log("✓ 7. NFT释放代币:", ethers.formatEther(releasableAmount));
      }
      
      console.log("=== 完整业务流程测试完成 ===");
    });
  });

  describe("边界条件和异常处理", function () {
    it("应该正确处理各种边界情况", async function () {
      // 测试零金额转账
      await expect(
        tokenA.transfer(await user1.getAddress(), 0)
      ).to.be.revertedWith("Transfer amount must be greater than zero");
      
      // 测试余额不足
      await expect(
        tokenA.connect(user1).transfer(await user2.getAddress(), ethers.parseEther("1000"))
      ).to.be.revertedWith("ERC20: transfer amount exceeds balance");
      
      // 测试无NFT分红领取
      await expect(
        tokenA.connect(user2).claimDividend()
      ).to.be.revertedWith("No NFT to claim dividends");
      
      console.log("✓ 所有边界条件测试通过");
    });
  });
});