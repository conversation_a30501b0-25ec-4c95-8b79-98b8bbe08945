const { expect } = require("chai");
const { ethers } = require("hardhat");
const { time } = require("@nomicfoundation/hardhat-network-helpers");

describe("调试6期释放机制", function () {
  let tokenA, tokenB, miningContract, priceOracle;
  let owner, user1;
  
  const RELEASE_INTERVAL = 30 * 24 * 3600; // 30天
  const TOTAL_RELEASE_PERIODS = 6;
  
  beforeEach(async function () {
    [owner, user1] = await ethers.getSigners();
    
    // 部署简化版本进行测试
    const MockWBNB = await ethers.getContractFactory("MockWBNB");
    const mockWBNB = await MockWBNB.deploy();
    
    const MockPancakeFactory = await ethers.getContractFactory("MockPancakeFactory");
    const mockFactory = await MockPancakeFactory.deploy();
    
    const MockPancakeRouter = await ethers.getContractFactory("MockPancakeRouter");
    const mockRouter = await MockPancakeRouter.deploy(
      await mockFactory.getAddress(),
      await mockWBNB.getAddress()
    );
    
    const TestSimplePriceOracle = await ethers.getContractFactory("TestSimplePriceOracle");
    priceOracle = await TestSimplePriceOracle.deploy(
      await mockWBNB.getAddress(),
      await mockFactory.getAddress()
    );
    
    const TokenA = await ethers.getContractFactory("TokenA");
    tokenA = await TokenA.deploy(
      await owner.getAddress(),
      await mockRouter.getAddress(),
      await mockWBNB.getAddress()
    );
    
    const TokenB = await ethers.getContractFactory("TokenB");
    tokenB = await TokenB.deploy(
      await owner.getAddress(),
      await mockRouter.getAddress(),
      await mockWBNB.getAddress()
    );
    
    const MinerNFT = await ethers.getContractFactory("MinerNFT");
    const minerNFT = await MinerNFT.deploy("MinerNFT", "MNFT", await tokenA.getAddress());
    
    const MiningContract = await ethers.getContractFactory("MiningContract");
    miningContract = await MiningContract.deploy(
      await tokenA.getAddress(),
      await tokenB.getAddress(),
      await minerNFT.getAddress(),
      await priceOracle.getAddress()
    );
    
    // 基本设置
    await tokenA.setTaxExempt(await miningContract.getAddress(), true);
    await tokenB.setTaxExempt(await miningContract.getAddress(), true);
    await tokenB.transfer(await miningContract.getAddress(), ethers.parseEther("10000000"));
    
    // 设置交易对 - 使用和详细测试相同的设置
    const liquidityTokenA = ethers.parseEther("50000");
    const liquidityWBNB = ethers.parseEther("50");
    
    await mockWBNB.deposit({ value: liquidityWBNB });
    await tokenA.approve(await mockRouter.getAddress(), liquidityTokenA);
    await mockWBNB.approve(await mockRouter.getAddress(), liquidityWBNB);
    
    await mockRouter.addLiquidity(
      await tokenA.getAddress(),
      await mockWBNB.getAddress(),
      liquidityTokenA,
      liquidityWBNB,
      0, 0,
      await owner.getAddress(),
      Math.floor(Date.now() / 1000) + 600
    );
    
    const liquidityTokenB = ethers.parseEther("500000");
    const liquidityWBNB2 = ethers.parseEther("50");
    
    await mockWBNB.deposit({ value: liquidityWBNB2 });
    await tokenB.approve(await mockRouter.getAddress(), liquidityTokenB);
    await mockWBNB.approve(await mockRouter.getAddress(), liquidityWBNB2);
    
    await mockRouter.addLiquidity(
      await tokenB.getAddress(),
      await mockWBNB.getAddress(),
      liquidityTokenB,
      liquidityWBNB2,
      0, 0,
      await owner.getAddress(),
      Math.floor(Date.now() / 1000) + 600
    );
    
    // 设置价格
    await priceOracle.configureToken(await tokenA.getAddress());
    await priceOracle.configureToken(await tokenB.getAddress());
    await priceOracle.recordPrice(await tokenA.getAddress());
    await priceOracle.recordPrice(await tokenB.getAddress());
    
    // 给用户TokenA
    await tokenA.transfer(await user1.getAddress(), ethers.parseEther("20000"));
    await tokenA.connect(user1).approve(await miningContract.getAddress(), ethers.parseEther("20000"));
  });
  
  it("应该逐期调试释放机制", async function () {
    console.log("=== 调试6期释放机制 ===");
    
    // 用户挖矿 - 使用和详细测试相同的数量
    const burnAmount = ethers.parseEther("10000");
    await miningContract.connect(user1).burnTokenAForTokenB(burnAmount, ethers.ZeroAddress);
    
    // 获取挖矿记录
    const record = await miningContract.getUserMiningRecord1Detail(await user1.getAddress(), 0);
    console.log(`总释放价值: ${ethers.formatEther(record.totalReleaseValue)} BNB`);
    
    const startTime = await time.latest();
    console.log(`开始时间: ${startTime}`);
    
    // 测试每一期
    for (let period = 1; period <= 6; period++) {
      console.log(`\\n--- 第${period}期测试 ---`);
      
      // 推进时间
      await time.increaseTo(startTime + period * RELEASE_INTERVAL);
      const currentTime = await time.latest();
      console.log(`当前时间: ${currentTime}`);
      console.log(`经过时间: ${currentTime - startTime} 秒`);
      console.log(`经过期数: ${Math.floor((currentTime - startTime) / RELEASE_INTERVAL)}`);
      
      // 检查可领取数量
      const claimable = await miningContract.getUserClaimableAmount1(await user1.getAddress());
      console.log(`第${period}期可领取: ${ethers.formatEther(claimable)} BNB`);
      
      // 手动计算应该释放的数量
      const totalValue = BigInt(record.totalReleaseValue);
      const shouldHaveReleased = (totalValue * BigInt(period)) / BigInt(TOTAL_RELEASE_PERIODS);
      const alreadyReleased = BigInt(record.releasedValue);
      const expectedClaimable = shouldHaveReleased - alreadyReleased;
      
      console.log(`应该累积释放: ${ethers.formatEther(shouldHaveReleased)} BNB`);
      console.log(`已经释放: ${ethers.formatEther(alreadyReleased)} BNB`);  
      console.log(`理论可领取: ${ethers.formatEther(expectedClaimable)} BNB`);
      console.log(`实际可领取: ${ethers.formatEther(claimable)} BNB`);
      
      // 如果有可领取的，就领取
      if (claimable > 0) {
        await miningContract.connect(user1).claimTokenBFromMechanism1();
        console.log(`✓ 成功领取`);
        
        // 重新获取记录
        const updatedRecord = await miningContract.getUserMiningRecord1Detail(await user1.getAddress(), 0);
        console.log(`更新后已释放: ${ethers.formatEther(updatedRecord.releasedValue)} BNB`);
      } else {
        console.log(`✗ 无可领取数量`);
      }
    }
    
    console.log("\\n=== 最终状态 ===");
    const finalRecord = await miningContract.getUserMiningRecord1Detail(await user1.getAddress(), 0);
    console.log(`总释放价值: ${ethers.formatEther(finalRecord.totalReleaseValue)} BNB`);
    console.log(`最终已释放: ${ethers.formatEther(finalRecord.releasedValue)} BNB`);
    console.log(`释放进度: ${Number(finalRecord.releasedValue * BigInt(100) / finalRecord.totalReleaseValue)}%`);
  });
});