// SPDX-License-Identifier: MIT
pragma solidity ^0.8.20;

import "forge-std/Test.sol";
import "../contracts/MiningContract.sol";
import "../contracts/TokenA.sol";
import "../contracts/TokenB.sol";
import "../contracts/MinerNFT.sol";
import "../contracts/MockUSDT.sol";

contract BoundaryTest is Test {
    MiningContract public miningContract;
    TokenA public tokenA;
    TokenB public tokenB;
    MinerNFT public nft;
    MockUSDT public usdt;
    
    address public user1 = address(0x1);
    address public user2 = address(0x2);
    address public owner = address(this);
    
    uint256 constant PRECISION = 1e18;
    uint256 constant TOTAL_RELEASE_DURATION = 180 days;

    function setUp() public {
        // 部署代币合约
        usdt = new MockUSDT();
        tokenA = new TokenA(owner, address(0), address(0));
        tokenB = new TokenB(owner, address(0), address(0));
        nft = new MinerNFT(owner, "ipfs://test/");
        
        // 部署挖矿合约
        miningContract = new MiningContract(
            address(tokenA),
            address(tokenB),
            address(nft)
        );
        
        // 给挖矿合约转入足够的TokenB
        tokenB.transfer(address(miningContract), 1000000 * PRECISION);
        
        // 给用户mint一些代币和NFT
        usdt.mint(user1, 1000000 * PRECISION);
        usdt.mint(user2, 1000000 * PRECISION);
        tokenB.mint(user1, 1000000 * PRECISION);
        tokenB.mint(user2, 1000000 * PRECISION);
        nft.mint(user1, 1);
        nft.mint(user2, 1);
        
        // 设置代币价格（模拟DEX）
        // 这里需要根据实际的价格预言机设置
    }

    // ==================== 数值边界测试 ====================
    
    function test_MinimalUSDTMining() public {
        vm.startPrank(user1);
        
        // 批准USDT
        usdt.approve(address(miningContract), 1);
        
        // 尝试用1 wei USDT挖矿
        vm.expectRevert(); // 可能因为价格计算或最小值限制而失败
        miningContract.buyAndBurnTokenAWithUSDT(1, address(0));
        
        vm.stopPrank();
    }
    
    function test_MinimalTokenBBurning() public {
        vm.startPrank(user1);
        
        // 批准TokenB
        tokenB.approve(address(miningContract), 1);
        
        // 尝试销毁1 wei TokenB
        // 这可能会因为精度问题导致burnedValue为0
        vm.expectRevert("Amount must be greater than zero");
        miningContract.burnTokenBWithNFT(0, address(0));
        
        vm.stopPrank();
    }
    
    function test_MaximalValueOverflow() public {
        vm.startPrank(user1);
        
        uint256 maxSafeValue = type(uint256).max / 10; // 避免在测试中真正溢出
        
        // 给用户足够的余额
        usdt.mint(user1, maxSafeValue);
        usdt.approve(address(miningContract), maxSafeValue);
        
        // 测试是否会在计算totalReleaseValue时溢出
        // totalReleaseValue = usdtAmount * REWARD_MULTIPLIER (2)
        // 应该添加溢出保护
        
        vm.stopPrank();
    }

    // ==================== 时间边界测试 ====================
    
    function test_ZeroTimeElapsed() public {
        vm.startPrank(user1);
        
        // 先进行挖矿
        usdt.approve(address(miningContract), 100 * PRECISION);
        miningContract.buyAndBurnTokenAWithUSDT(100 * PRECISION, address(0));
        
        // 立即查询可领取数量（时间未流逝）
        uint256 claimable = miningContract.getUserClaimableAmount1(user1);
        assertEq(claimable, 0, "Should be 0 when no time elapsed");
        
        vm.stopPrank();
    }
    
    function test_ExactlyReleaseDuration() public {
        vm.startPrank(user1);
        
        // 进行挖矿
        usdt.approve(address(miningContract), 100 * PRECISION);
        miningContract.buyAndBurnTokenAWithUSDT(100 * PRECISION, address(0));
        
        // 时间跳转到刚好180天
        vm.warp(block.timestamp + TOTAL_RELEASE_DURATION);
        
        // 应该能够领取全部金额
        uint256 claimable = miningContract.getUserClaimableAmount1(user1);
        uint256 expectedTotal = 100 * PRECISION * 2; // 2倍奖励
        assertEq(claimable, expectedTotal, "Should release full amount at 180 days");
        
        vm.stopPrank();
    }
    
    function test_BeyondReleaseDuration() public {
        vm.startPrank(user1);
        
        // 进行挖矿
        usdt.approve(address(miningContract), 100 * PRECISION);
        miningContract.buyAndBurnTokenAWithUSDT(100 * PRECISION, address(0));
        
        // 时间跳转到超过180天
        vm.warp(block.timestamp + TOTAL_RELEASE_DURATION + 30 days);
        
        // 仍然只能领取全部金额，不应该超额
        uint256 claimable = miningContract.getUserClaimableAmount1(user1);
        uint256 expectedTotal = 100 * PRECISION * 2;
        assertEq(claimable, expectedTotal, "Should not exceed total even after time limit");
        
        vm.stopPrank();
    }

    // ==================== 加速释放边界测试 ====================
    
    function test_AcceleratedReleaseAllRemaining() public {
        vm.startPrank(user1);
        
        // 用户1进行挖矿
        usdt.approve(address(miningContract), 100 * PRECISION);
        miningContract.buyAndBurnTokenAWithUSDT(100 * PRECISION, address(0));
        
        // 用户2进行挖矿并邀请用户1（产生加速释放额度）
        vm.stopPrank();
        vm.startPrank(user2);
        usdt.approve(address(miningContract), 100 * PRECISION);
        miningContract.buyAndBurnTokenAWithUSDT(100 * PRECISION, user1); // user1作为邀请人
        
        vm.stopPrank();
        vm.startPrank(user1);
        
        // 检查user1的加速释放额度
        (, , uint256 acceleratedAmount, ) = miningContract.getUserInviteInfo(user1);
        assertGt(acceleratedAmount, 0, "Should have accelerated release amount");
        
        // 尝试使用所有加速释放额度
        uint256 claimableBefore = miningContract.getUserClaimableAmount1(user1);
        miningContract.claimTokenBFromMechanism1();
        
        // 验证使用后的状态
        (, , uint256 remainingAccelerated, ) = miningContract.getUserInviteInfo(user1);
        // 加速释放额度应该被消耗
        
        vm.stopPrank();
    }
    
    function test_AcceleratedReleaseExceedsRemaining() public {
        vm.startPrank(user1);
        
        // 用户1进行小额挖矿
        usdt.approve(address(miningContract), 10 * PRECISION);
        miningContract.buyAndBurnTokenAWithUSDT(10 * PRECISION, address(0));
        
        // 时间流逝到接近完成
        vm.warp(block.timestamp + TOTAL_RELEASE_DURATION - 1 days);
        
        // 先正常领取大部分
        miningContract.claimTokenBFromMechanism1();
        
        vm.stopPrank();
        vm.startPrank(user2);
        
        // 用户2进行大额挖矿产生大量加速释放额度
        usdt.approve(address(miningContract), 1000 * PRECISION);
        miningContract.buyAndBurnTokenAWithUSDT(1000 * PRECISION, user1);
        
        vm.stopPrank();
        vm.startPrank(user1);
        
        // 尝试使用超过剩余的加速释放额度
        (, , uint256 acceleratedAmount, ) = miningContract.getUserInviteInfo(user1);
        
        // 获取用户1的挖矿记录
        (uint256 count1, ) = miningContract.getUserMiningRecordCount(user1);
        assertGt(count1, 0, "Should have mining records");
        
        // 记录使用前的状态
        uint256 claimableBefore = miningContract.getUserClaimableAmount1(user1);
        
        // 使用加速释放
        if (claimableBefore > 0 || acceleratedAmount > 0) {
            miningContract.claimTokenBFromMechanism1();
        }
        
        // 验证不会超额释放
        vm.stopPrank();
    }

    // ==================== 数学精度测试 ====================
    
    function test_DivisionPrecision() public {
        vm.startPrank(user1);
        
        // 使用不能整除的数值
        uint256 oddAmount = 7 * PRECISION; // 7 USDT
        usdt.approve(address(miningContract), oddAmount);
        miningContract.buyAndBurnTokenAWithUSDT(oddAmount, address(0));
        
        // 时间流逝1/3
        vm.warp(block.timestamp + TOTAL_RELEASE_DURATION / 3);
        
        uint256 claimable = miningContract.getUserClaimableAmount1(user1);
        uint256 expectedApprox = (oddAmount * 2 * PRECISION) / 3; // 约2.333... USDT
        
        // 验证精度损失在合理范围内
        uint256 diff = claimable > expectedApprox ? 
            claimable - expectedApprox : 
            expectedApprox - claimable;
        
        // 精度损失应该小于1e12 (考虑到Solidity的截断)
        assertLt(diff, 1e12, "Precision loss should be minimal");
        
        vm.stopPrank();
    }
    
    function test_AcceleratedTimeCalculation() public {
        vm.startPrank(user1);
        
        // 创建挖矿记录
        usdt.approve(address(miningContract), 100 * PRECISION);
        miningContract.buyAndBurnTokenAWithUSDT(100 * PRECISION, address(0));
        
        vm.stopPrank();
        vm.startPrank(user2);
        
        // 产生加速释放额度
        usdt.approve(address(miningContract), 30 * PRECISION);
        miningContract.buyAndBurnTokenAWithUSDT(30 * PRECISION, user1);
        
        vm.stopPrank();
        vm.startPrank(user1);
        
        // 使用加速释放
        miningContract.claimTokenBFromMechanism1();
        
        // 检查挖矿记录的acceleratedTime
        (,,,,,,,uint256 acceleratedTime,,,) = miningContract.getUserMiningRecord1Detail(user1, 0);
        
        // 计算预期的加速时间
        // acceleratedTime = (acceleratedAmount * totalDuration) / totalReleaseValue
        uint256 acceleratedAmount = 3 * PRECISION; // 30 USDT 的 10% = 3 USDT
        uint256 totalReleaseValue = 200 * PRECISION; // 100 USDT * 2倍
        uint256 expectedAcceleratedTime = (acceleratedAmount * TOTAL_RELEASE_DURATION) / totalReleaseValue;
        
        assertEq(acceleratedTime, expectedAcceleratedTime, "Accelerated time calculation incorrect");
        
        vm.stopPrank();
    }

    // ==================== 时间溢出测试 ====================
    
    function test_TimeOverflowProtection() public {
        vm.startPrank(user1);
        
        // 进行挖矿
        usdt.approve(address(miningContract), 100 * PRECISION);
        miningContract.buyAndBurnTokenAWithUSDT(100 * PRECISION, address(0));
        
        // 设置极端的时间值（接近uint256最大值）
        uint256 extremeTime = type(uint256).max - block.timestamp - 1;
        vm.warp(block.timestamp + extremeTime);
        
        // 验证不会发生溢出
        uint256 claimable = miningContract.getUserClaimableAmount1(user1);
        uint256 maxExpected = 200 * PRECISION; // 100 USDT * 2倍
        assertLe(claimable, maxExpected, "Should not exceed maximum even with time overflow");
        
        vm.stopPrank();
    }

    // ==================== 组合场景测试 ====================
    
    function test_MultipleRecordsAcceleratedDistribution() public {
        vm.startPrank(user1);
        
        // 创建多个挖矿记录
        usdt.approve(address(miningContract), 300 * PRECISION);
        
        // 第一笔挖矿
        miningContract.buyAndBurnTokenAWithUSDT(100 * PRECISION, address(0));
        
        // 时间流逝
        vm.warp(block.timestamp + 30 days);
        
        // 第二笔挖矿
        miningContract.buyAndBurnTokenAWithUSDT(200 * PRECISION, address(0));
        
        vm.stopPrank();
        vm.startPrank(user2);
        
        // 产生大量加速释放额度
        usdt.approve(address(miningContract), 500 * PRECISION);
        miningContract.buyAndBurnTokenAWithUSDT(500 * PRECISION, user1);
        
        vm.stopPrank();
        vm.startPrank(user1);
        
        // 记录使用前状态
        (uint256 count1, ) = miningContract.getUserMiningRecordCount(user1);
        assertEq(count1, 2, "Should have 2 mining records");
        
        // 获取每个记录的详细信息
        (,,,uint256 totalValue1,uint256 released1,,,,,,,) = miningContract.getUserMiningRecord1Detail(user1, 0);
        (,,,uint256 totalValue2,uint256 released2,,,,,,,) = miningContract.getUserMiningRecord1Detail(user1, 1);
        
        uint256 remaining1 = totalValue1 - released1;
        uint256 remaining2 = totalValue2 - released2;
        
        // 使用加速释放
        (, , uint256 acceleratedAmount, ) = miningContract.getUserInviteInfo(user1);
        assertGt(acceleratedAmount, 0, "Should have accelerated amount");
        
        miningContract.claimTokenBFromMechanism1();
        
        // 验证分配逻辑正确
        // 应该优先分配给第一个记录，然后是第二个记录
        
        vm.stopPrank();
    }

    // ==================== 模糊测试辅助函数 ====================
    
    function testFuzz_AcceleratedReleaseInvariant(
        uint256 usdtAmount,
        uint256 timeElapsed,
        uint256 acceleratedRatio
    ) public {
        // 约束输入范围
        vm.assume(usdtAmount >= PRECISION && usdtAmount <= 1000 * PRECISION);
        vm.assume(timeElapsed <= TOTAL_RELEASE_DURATION);
        vm.assume(acceleratedRatio <= 100); // 0-100%
        
        vm.startPrank(user1);
        
        // 进行挖矿
        usdt.mint(user1, usdtAmount);
        usdt.approve(address(miningContract), usdtAmount);
        miningContract.buyAndBurnTokenAWithUSDT(usdtAmount, address(0));
        
        // 时间流逝
        vm.warp(block.timestamp + timeElapsed);
        
        // 记录初始状态
        uint256 claimableBefore = miningContract.getUserClaimableAmount1(user1);
        
        vm.stopPrank();
        vm.startPrank(user2);
        
        // 产生加速释放额度
        uint256 inviteAmount = (usdtAmount * acceleratedRatio) / 100;
        if (inviteAmount > 0) {
            usdt.mint(user2, inviteAmount);
            usdt.approve(address(miningContract), inviteAmount);
            miningContract.buyAndBurnTokenAWithUSDT(inviteAmount, user1);
        }
        
        vm.stopPrank();
        vm.startPrank(user1);
        
        // 使用加速释放
        if (claimableBefore > 0) {
            miningContract.claimTokenBFromMechanism1();
        }
        
        // 验证不变性
        (,,,uint256 totalValue,uint256 releasedValue,,,,,,,) = miningContract.getUserMiningRecord1Detail(user1, 0);
        
        // 不变性1: 已释放不超过总价值
        assertLe(releasedValue, totalValue, "Released should not exceed total");
        
        // 不变性2: 总价值应该是输入的2倍
        assertEq(totalValue, usdtAmount * 2, "Total value should be 2x input");
        
        vm.stopPrank();
    }
}