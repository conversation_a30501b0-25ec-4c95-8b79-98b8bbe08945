// 数学验证脚本 - 验证修复后的挖矿合约计算逻辑
// 使用 JavaScript 进行精确的数学验证

const PRECISION = BigInt(10) ** BigInt(18);
const TOTAL_RELEASE_DURATION = BigInt(180 * 24 * 3600); // 180天，秒为单位
const REWARD_MULTIPLIER = BigInt(2);

// 模拟合约中的计算逻辑
class MiningCalculator {
    constructor() {
        this.testCases = [];
        this.errors = [];
    }

    // 计算应该释放的总价值（原始逻辑）
    calculateTotalShouldHaveReleased(totalReleaseValue, timeElapsed) {
        if (timeElapsed > TOTAL_RELEASE_DURATION) {
            timeElapsed = TOTAL_RELEASE_DURATION;
        }
        return (totalReleaseValue * timeElapsed) / TOTAL_RELEASE_DURATION;
    }

    // 计算加速释放占用的时间
    calculateAcceleratedTime(acceleratedAmount, totalReleaseValue) {
        return (acceleratedAmount * TOTAL_RELEASE_DURATION) / totalReleaseValue;
    }

    // 新的可释放计算逻辑（修复后）
    calculateReleasableAmount(record, currentTime) {
        if (record.releasedValue >= record.totalReleaseValue) {
            return BigInt(0);
        }

        // 计算总时间流逝（包括加速释放时间）
        let totalTimeElapsed = (currentTime - record.startTime) + record.acceleratedTime;
        
        if (totalTimeElapsed > TOTAL_RELEASE_DURATION) {
            totalTimeElapsed = TOTAL_RELEASE_DURATION;
        }

        // 计算总共应该释放的价值
        const totalShouldHaveReleased = this.calculateTotalShouldHaveReleased(
            record.totalReleaseValue, 
            totalTimeElapsed
        );

        // 可领取的金额
        if (totalShouldHaveReleased > record.releasedValue) {
            return totalShouldHaveReleased - record.releasedValue;
        }

        return BigInt(0);
    }

    // 测试用例1: 基本时间释放测试
    testBasicTimeRelease() {
        console.log("=== 测试1: 基本时间释放 ===");
        
        const record = {
            totalReleaseValue: BigInt(100) * PRECISION, // 100 USDT
            releasedValue: BigInt(0),
            acceleratedReleasedValue: BigInt(0),
            acceleratedTime: BigInt(0),
            startTime: BigInt(0),
        };

        const testPoints = [
            { time: BigInt(0), expectedRatio: 0 },
            { time: TOTAL_RELEASE_DURATION / BigInt(4), expectedRatio: 0.25 }, // 25%
            { time: TOTAL_RELEASE_DURATION / BigInt(2), expectedRatio: 0.5 },  // 50%
            { time: (TOTAL_RELEASE_DURATION * BigInt(3)) / BigInt(4), expectedRatio: 0.75 }, // 75%
            { time: TOTAL_RELEASE_DURATION, expectedRatio: 1.0 }, // 100%
            { time: TOTAL_RELEASE_DURATION * BigInt(2), expectedRatio: 1.0 }, // 超时，仍为100%
        ];

        testPoints.forEach((point, index) => {
            const claimable = this.calculateReleasableAmount(record, point.time);
            const expected = BigInt(Math.floor(Number(record.totalReleaseValue) * point.expectedRatio));
            const diff = claimable > expected ? claimable - expected : expected - claimable;
            
            console.log(`  时间点${index + 1}: ${point.expectedRatio * 100}%`);
            console.log(`    计算值: ${this.formatToken(claimable)}`);
            console.log(`    预期值: ${this.formatToken(expected)}`);
            console.log(`    误差: ${this.formatToken(diff)}`);
            
            if (diff > PRECISION / BigInt(1000000)) { // 误差超过0.000001
                this.errors.push(`基本释放测试失败: 时间点${index + 1}`);
            }
        });
    }

    // 测试用例2: 加速释放时间补偿测试
    testAcceleratedTimeCompensation() {
        console.log("\n=== 测试2: 加速释放时间补偿 ===");
        
        const record = {
            totalReleaseValue: BigInt(100) * PRECISION, // 100 USDT
            releasedValue: BigInt(30) * PRECISION,      // 已释放30 USDT
            acceleratedReleasedValue: BigInt(30) * PRECISION, // 30 USDT来自加速释放
            acceleratedTime: BigInt(0), // 将被计算
            startTime: BigInt(0),
        };

        // 计算加速释放占用的时间
        record.acceleratedTime = this.calculateAcceleratedTime(
            record.acceleratedReleasedValue, 
            record.totalReleaseValue
        );

        console.log(`  总释放价值: ${this.formatToken(record.totalReleaseValue)}`);
        console.log(`  已释放价值: ${this.formatToken(record.releasedValue)}`);
        console.log(`  加速释放价值: ${this.formatToken(record.acceleratedReleasedValue)}`);
        console.log(`  加速释放占用时间: ${Number(record.acceleratedTime) / (24 * 3600)} 天`);

        // 测试不同时间点的可释放金额
        const testTimes = [
            BigInt(30 * 24 * 3600),  // 30天
            BigInt(60 * 24 * 3600),  // 60天  
            BigInt(90 * 24 * 3600),  // 90天
            BigInt(120 * 24 * 3600), // 120天
            BigInt(180 * 24 * 3600), // 180天
        ];

        testTimes.forEach((time, index) => {
            const claimable = this.calculateReleasableAmount(record, time);
            const effectiveTime = time + record.acceleratedTime;
            const progressRatio = Number(effectiveTime) / Number(TOTAL_RELEASE_DURATION);
            
            console.log(`  ${Number(time) / (24 * 3600)}天后:`);
            console.log(`    有效时间进度: ${(progressRatio * 100).toFixed(2)}%`);
            console.log(`    可领取金额: ${this.formatToken(claimable)}`);
            
            // 验证不会超额释放
            if (record.releasedValue + claimable > record.totalReleaseValue) {
                this.errors.push(`超额释放: ${Number(time) / (24 * 3600)}天`);
            }
        });
    }

    // 测试用例3: 精度损失测试
    testPrecisionLoss() {
        console.log("\n=== 测试3: 精度损失测试 ===");
        
        // 使用不能整除的数值
        const record = {
            totalReleaseValue: BigInt(7) * PRECISION + BigInt(12345), // 奇数值
            releasedValue: BigInt(0),
            acceleratedReleasedValue: BigInt(0),
            acceleratedTime: BigInt(0),
            startTime: BigInt(0),
        };

        // 测试1/3时间点
        const oneThirdTime = TOTAL_RELEASE_DURATION / BigInt(3);
        const claimable = this.calculateReleasableAmount(record, oneThirdTime);
        
        // 理论值（精确计算）
        const theoreticalValue = (record.totalReleaseValue * oneThirdTime) / TOTAL_RELEASE_DURATION;
        
        const precisionLoss = claimable > theoreticalValue ? 
            claimable - theoreticalValue : 
            theoreticalValue - claimable;
            
        console.log(`  总价值: ${this.formatToken(record.totalReleaseValue)}`);
        console.log(`  1/3时间点计算值: ${this.formatToken(claimable)}`);
        console.log(`  理论精确值: ${this.formatToken(theoreticalValue)}`);
        console.log(`  精度损失: ${this.formatToken(precisionLoss)}`);
        console.log(`  精度损失比例: ${(Number(precisionLoss) / Number(record.totalReleaseValue) * 100).toFixed(10)}%`);
        
        // 精度损失应该在可接受范围内（小于1e-12）
        if (precisionLoss > PRECISION / BigInt(1e12)) {
            this.errors.push("精度损失过大");
        }
    }

    // 测试用例4: 边界条件测试
    testBoundaryConditions() {
        console.log("\n=== 测试4: 边界条件测试 ===");
        
        const testCases = [
            {
                name: "最小值测试",
                record: {
                    totalReleaseValue: BigInt(1),
                    releasedValue: BigInt(0),
                    acceleratedReleasedValue: BigInt(0),
                    acceleratedTime: BigInt(0),
                    startTime: BigInt(0),
                }
            },
            {
                name: "接近最大值测试",
                record: {
                    totalReleaseValue: BigInt(2) ** BigInt(128), // 大值但不溢出
                    releasedValue: BigInt(0),
                    acceleratedReleasedValue: BigInt(0),
                    acceleratedTime: BigInt(0),
                    startTime: BigInt(0),
                }
            },
            {
                name: "全部已释放测试",
                record: {
                    totalReleaseValue: BigInt(100) * PRECISION,
                    releasedValue: BigInt(100) * PRECISION,
                    acceleratedReleasedValue: BigInt(50) * PRECISION,
                    acceleratedTime: TOTAL_RELEASE_DURATION / BigInt(2),
                    startTime: BigInt(0),
                }
            }
        ];

        testCases.forEach(testCase => {
            console.log(`  ${testCase.name}:`);
            try {
                const claimable = this.calculateReleasableAmount(
                    testCase.record, 
                    TOTAL_RELEASE_DURATION
                );
                console.log(`    可领取: ${this.formatToken(claimable)}`);
                
                // 验证不会超额
                if (testCase.record.releasedValue + claimable > testCase.record.totalReleaseValue) {
                    this.errors.push(`${testCase.name}: 超额释放`);
                }
            } catch (error) {
                console.log(`    错误: ${error.message}`);
                this.errors.push(`${testCase.name}: ${error.message}`);
            }
        });
    }

    // 测试用例5: 多次加速释放累积测试
    testMultipleAcceleratedReleases() {
        console.log("\n=== 测试5: 多次加速释放累积测试 ===");
        
        const record = {
            totalReleaseValue: BigInt(100) * PRECISION,
            releasedValue: BigInt(0),
            acceleratedReleasedValue: BigInt(0),
            acceleratedTime: BigInt(0),
            startTime: BigInt(0),
        };

        // 模拟多次加速释放
        const acceleratedReleases = [
            BigInt(10) * PRECISION, // 第1次: 10 USDT
            BigInt(15) * PRECISION, // 第2次: 15 USDT
            BigInt(20) * PRECISION, // 第3次: 20 USDT
        ];

        console.log("  模拟多次加速释放过程:");
        acceleratedReleases.forEach((amount, index) => {
            // 累积加速释放
            record.acceleratedReleasedValue += amount;
            record.releasedValue += amount;
            
            // 累积加速时间
            const additionalTime = this.calculateAcceleratedTime(amount, record.totalReleaseValue);
            record.acceleratedTime += additionalTime;
            
            console.log(`    第${index + 1}次加速释放:`);
            console.log(`      释放金额: ${this.formatToken(amount)}`);
            console.log(`      累积已释放: ${this.formatToken(record.releasedValue)}`);
            console.log(`      累积加速时间: ${Number(record.acceleratedTime) / (24 * 3600)} 天`);
            
            // 检查后续可释放金额
            const futureTime = BigInt(90 * 24 * 3600); // 90天后
            const claimable = this.calculateReleasableAmount(record, futureTime);
            console.log(`      90天后可领取: ${this.formatToken(claimable)}`);
            
            // 验证总和不超过总价值
            if (record.releasedValue + claimable > record.totalReleaseValue) {
                this.errors.push(`多次加速释放第${index + 1}次后超额`);
            }
        });
    }

    // 格式化Token显示
    formatToken(amount) {
        const ether = Number(amount) / Number(PRECISION);
        return `${ether.toFixed(6)} USDT`;
    }

    // 运行所有测试
    runAllTests() {
        console.log("开始数学验证测试...\n");
        
        this.testBasicTimeRelease();
        this.testAcceleratedTimeCompensation();
        this.testPrecisionLoss();
        this.testBoundaryConditions();
        this.testMultipleAcceleratedReleases();
        
        // 输出测试结果
        console.log("\n" + "=".repeat(50));
        console.log("测试结果总结:");
        
        if (this.errors.length === 0) {
            console.log("✅ 所有测试通过！数学逻辑正确。");
        } else {
            console.log("❌ 发现以下问题:");
            this.errors.forEach((error, index) => {
                console.log(`  ${index + 1}. ${error}`);
            });
        }
        
        return this.errors.length === 0;
    }

    // 生成测试报告
    generateReport() {
        const report = {
            timestamp: new Date().toISOString(),
            totalTests: 5,
            errors: this.errors,
            passed: this.errors.length === 0,
            recommendations: []
        };

        if (this.errors.length > 0) {
            report.recommendations.push("建议在部署前修复所有数学计算问题");
            report.recommendations.push("考虑增加更多的边界检查");
            report.recommendations.push("实施额外的溢出保护");
        } else {
            report.recommendations.push("数学逻辑验证通过，可以进行下一步测试");
            report.recommendations.push("建议在测试网络上进行实际合约测试");
        }

        return report;
    }
}

// 运行测试
if (typeof module !== 'undefined' && module.exports) {
    module.exports = MiningCalculator;
} else {
    // 浏览器环境或直接运行
    const calculator = new MiningCalculator();
    const success = calculator.runAllTests();
    const report = calculator.generateReport();
    
    console.log("\n" + "=".repeat(50));
    console.log("测试报告:");
    console.log(JSON.stringify(report, null, 2));
}