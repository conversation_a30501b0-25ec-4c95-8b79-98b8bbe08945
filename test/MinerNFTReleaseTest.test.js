const { expect } = require("chai");
const { ethers } = require("hardhat");
const { time } = require("@nomicfoundation/hardhat-network-helpers");

describe("MinerNFT 6期释放机制专项测试", function () {
  let tokenA, tokenB, minerNFT, miningContract, priceOracle;
  let mockWBNB, mockFactory, mockRouter;
  let owner, user1, user2, user3;
  
  const RELEASE_INTERVAL = 30 * 24 * 3600; // 30天
  const TOTAL_RELEASE_PERIODS = 6;
  
  beforeEach(async function () {
    const signers = await ethers.getSigners();
    [owner, user1, user2, user3] = signers.slice(0, 4);
    
    console.log(`=== MinerNFT 6期释放机制专项测试 ===`);
    
    // 部署模拟PancakeSwap环境
    const MockWBNB = await ethers.getContractFactory("MockWBNB");
    mockWBNB = await MockWBNB.deploy();
    
    const MockPancakeFactory = await ethers.getContractFactory("MockPancakeFactory");
    mockFactory = await MockPancakeFactory.deploy();
    
    const MockPancakeRouter = await ethers.getContractFactory("MockPancakeRouter");
    mockRouter = await MockPancakeRouter.deploy(
      await mockFactory.getAddress(),
      await mockWBNB.getAddress()
    );
    
    // 部署TestSimplePriceOracle
    const TestSimplePriceOracle = await ethers.getContractFactory("TestSimplePriceOracle");
    priceOracle = await TestSimplePriceOracle.deploy(
      await mockWBNB.getAddress(),
      await mockFactory.getAddress()
    );
    
    const TokenA = await ethers.getContractFactory("TokenA");
    tokenA = await TokenA.deploy(
      await owner.getAddress(),
      await mockRouter.getAddress(),
      await mockWBNB.getAddress()
    );
    
    const TokenB = await ethers.getContractFactory("TokenB");
    tokenB = await TokenB.deploy(
      await owner.getAddress(),
      await mockRouter.getAddress(),
      await mockWBNB.getAddress()
    );
    
    const MinerNFT = await ethers.getContractFactory("MinerNFT");
    minerNFT = await MinerNFT.deploy("MinerNFT", "MNFT", await tokenA.getAddress());
    
    const MiningContract = await ethers.getContractFactory("MiningContract");
    miningContract = await MiningContract.deploy(
      await tokenA.getAddress(),
      await tokenB.getAddress(),
      await minerNFT.getAddress(),
      await priceOracle.getAddress()
    );
    
    // 基本设置
    await tokenA.setTaxExempt(await miningContract.getAddress(), true);
    await tokenB.setTaxExempt(await miningContract.getAddress(), true);
    await tokenB.transfer(await miningContract.getAddress(), ethers.parseEther("10000000"));
    
    // 设置交易对 - TokenB/WBNB
    const liquidityTokenB = ethers.parseEther("500000");
    const liquidityWBNB = ethers.parseEther("50");
    
    await mockWBNB.deposit({ value: liquidityWBNB });
    await tokenB.approve(await mockRouter.getAddress(), liquidityTokenB);
    await mockWBNB.approve(await mockRouter.getAddress(), liquidityWBNB);
    
    await mockRouter.addLiquidity(
      await tokenB.getAddress(),
      await mockWBNB.getAddress(),
      liquidityTokenB,
      liquidityWBNB,
      0, 0,
      await owner.getAddress(),
Math.floor(Date.now() / 1000) + 3600
    );
    
    // 设置价格
    await priceOracle.configureToken(await tokenB.getAddress());
    await priceOracle.recordPrice(await tokenB.getAddress());
    
    // 给用户们TokenB
    await tokenB.transfer(await user1.getAddress(), ethers.parseEther("200000"));
    await tokenB.transfer(await user2.getAddress(), ethers.parseEther("200000"));
    await tokenB.transfer(await user3.getAddress(), ethers.parseEther("200000"));
    
    // 批准合约使用TokenB
    await tokenB.connect(user1).approve(await miningContract.getAddress(), ethers.parseEther("200000"));
    await tokenB.connect(user2).approve(await miningContract.getAddress(), ethers.parseEther("200000"));
    await tokenB.connect(user3).approve(await miningContract.getAddress(), ethers.parseEther("200000"));
    
    // 给用户们铸造NFT
    await minerNFT.batchMintCount(await user1.getAddress(), 5); // user1: 5个NFT
    await minerNFT.batchMintCount(await user2.getAddress(), 10); // user2: 10个NFT  
    await minerNFT.batchMintCount(await user3.getAddress(), 20); // user3: 20个NFT
  });
  
  it("应该测试单用户NFT挖矿的完整6期释放周期", async function () {
    console.log("=== 单用户NFT挖矿6期释放测试 ===");
    
    // 检查用户NFT数量
    const user1NFTCount = await minerNFT.balanceOf(await user1.getAddress());
    console.log(`user1 NFT数量: ${user1NFTCount}`);
    
    // 记录开始时间
    const startTime = await time.latest();
    console.log(`测试开始时间: ${startTime}`);
    
    // user1进行NFT挖矿
    const burnAmount = ethers.parseEther("50000"); // 销毁50000 TokenB
    await miningContract.connect(user1).burnTokenBWithNFT(burnAmount, ethers.ZeroAddress);
    
    console.log(`user1销毁TokenB: ${ethers.formatEther(burnAmount)} ETH`);
    
    // 获取挖矿记录
    const record = await miningContract.getUserMiningRecord2Detail(await user1.getAddress(), 0);
    console.log(`销毁TokenB价值: ${ethers.formatEther(record.burnedTokenBValue)} BNB`);
    console.log(`总释放价值: ${ethers.formatEther(record.totalReleaseValue)} BNB (2倍价值)`);
    console.log(`开始时间: ${record.lastReleaseTime}`);
    
    // 测试每一期释放
    for (let period = 1; period <= 6; period++) {
      console.log(`\\n--- MinerNFT第${period}期释放测试 ---`);
      
      // 推进时间
      const targetTime = startTime + period * RELEASE_INTERVAL;
      await time.increaseTo(targetTime);
      
      const currentTime = await time.latest();
      console.log(`推进到时间: ${currentTime}`);
      console.log(`经过时间: ${currentTime - startTime} 秒`);
      console.log(`经过期数: ${Math.floor((currentTime - startTime) / RELEASE_INTERVAL)}`);
      
      // 检查可领取数量
      const claimable = await miningContract.getUserClaimableAmount2(await user1.getAddress());
      console.log(`第${period}期可领取: ${ethers.formatEther(claimable)} BNB价值`);
      
      // 验证是否有可领取数量（每期都应该有）
      if (claimable > 0) {
        console.log(`✅ 第${period}期有可领取数量，正在领取...`);
        
        // 领取前的TokenB余额
        const beforeBalance = await tokenB.balanceOf(await user1.getAddress());
        
        // 执行领取
        await miningContract.connect(user1).claimTokenBFromMechanism2();
        
        // 领取后的TokenB余额
        const afterBalance = await tokenB.balanceOf(await user1.getAddress());
        const receivedTokenB = afterBalance - beforeBalance;
        
        console.log(`实际领取TokenB: ${ethers.formatEther(receivedTokenB)} ETH`);
        
        // 重新获取记录
        const updatedRecord = await miningContract.getUserMiningRecord2Detail(await user1.getAddress(), 0);
        console.log(`已释放价值: ${ethers.formatEther(updatedRecord.releasedValue)} BNB`);
        console.log(`释放进度: ${Number(updatedRecord.releasedValue * BigInt(100) / updatedRecord.totalReleaseValue)}%`);
        console.log(`记录状态: ${updatedRecord.active ? '活跃' : '已完成'}`);
      } else {
        console.log(`❌ 第${period}期无可领取数量 - 这不应该发生！`);
        expect(claimable).to.be.gt(0, `第${period}期应该有可领取数量`);
      }
    }
    
    console.log("\\n=== NFT挖矿最终状态验证 ===");
    const finalRecord = await miningContract.getUserMiningRecord2Detail(await user1.getAddress(), 0);
    console.log(`最终已释放: ${ethers.formatEther(finalRecord.releasedValue)} BNB`);
    console.log(`总释放价值: ${ethers.formatEther(finalRecord.totalReleaseValue)} BNB`);
    console.log(`最终释放率: ${Number(finalRecord.releasedValue * BigInt(100) / finalRecord.totalReleaseValue)}%`);
    console.log(`记录状态: ${finalRecord.active ? '活跃' : '已完成'}`);
    
    // 验证100%释放
    expect(finalRecord.releasedValue).to.equal(finalRecord.totalReleaseValue);
    expect(finalRecord.active).to.be.false;
  });
  
  it("应该测试多用户不同NFT数量的释放一致性", async function () {
    console.log("=== 多用户不同NFT数量释放一致性测试 ===");
    
    // 检查各用户NFT数量
    const user1NFTs = await minerNFT.balanceOf(await user1.getAddress());
    const user2NFTs = await minerNFT.balanceOf(await user2.getAddress()); 
    const user3NFTs = await minerNFT.balanceOf(await user3.getAddress());
    
    console.log(`user1 NFT数量: ${user1NFTs}`);
    console.log(`user2 NFT数量: ${user2NFTs}`);
    console.log(`user3 NFT数量: ${user3NFTs}`);
    
    const startTime = await time.latest();
    
    // 所有用户同时挖矿，销毁相同数量的TokenB
    const burnAmount = ethers.parseEther("100000");
    
    await miningContract.connect(user1).burnTokenBWithNFT(burnAmount, ethers.ZeroAddress);
    await miningContract.connect(user2).burnTokenBWithNFT(burnAmount, ethers.ZeroAddress);
    await miningContract.connect(user3).burnTokenBWithNFT(burnAmount, ethers.ZeroAddress);
    
    console.log(`所有用户都销毁了: ${ethers.formatEther(burnAmount)} TokenB`);
    
    // 获取各用户挖矿记录
    const record1 = await miningContract.getUserMiningRecord2Detail(await user1.getAddress(), 0);
    const record2 = await miningContract.getUserMiningRecord2Detail(await user2.getAddress(), 0);
    const record3 = await miningContract.getUserMiningRecord2Detail(await user3.getAddress(), 0);
    
    console.log(`\\n各用户释放价值:`);
    console.log(`user1总释放价值: ${ethers.formatEther(record1.totalReleaseValue)} BNB`);
    console.log(`user2总释放价值: ${ethers.formatEther(record2.totalReleaseValue)} BNB`);
    console.log(`user3总释放价值: ${ethers.formatEther(record3.totalReleaseValue)} BNB`);
    
    // 验证释放价值相等（与NFT数量无关）
    expect(record1.totalReleaseValue).to.equal(record2.totalReleaseValue);
    expect(record2.totalReleaseValue).to.equal(record3.totalReleaseValue);
    console.log("✅ 验证通过：释放价值与NFT数量无关");
    
    // 测试第3期释放（之前的问题期）
    await time.increaseTo(startTime + 3 * RELEASE_INTERVAL);
    
    const claimable1 = await miningContract.getUserClaimableAmount2(await user1.getAddress());
    const claimable2 = await miningContract.getUserClaimableAmount2(await user2.getAddress());
    const claimable3 = await miningContract.getUserClaimableAmount2(await user3.getAddress());
    
    console.log(`\\n第3期各用户可领取量:`);
    console.log(`user1: ${ethers.formatEther(claimable1)} BNB价值`);
    console.log(`user2: ${ethers.formatEther(claimable2)} BNB价值`);
    console.log(`user3: ${ethers.formatEther(claimable3)} BNB价值`);
    
    // 验证第3期都有可领取数量
    expect(claimable1).to.be.gt(0);
    expect(claimable2).to.be.gt(0);
    expect(claimable3).to.be.gt(0);
    console.log("✅ 验证通过：第3期所有用户都有可领取数量");
    
    // 验证可领取数量相等
    expect(claimable1).to.equal(claimable2);
    expect(claimable2).to.equal(claimable3);
    console.log("✅ 验证通过：第3期可领取数量一致");
  });
  
  it("应该测试NFT持有要求和释放逻辑", async function () {
    console.log("=== NFT持有要求和释放逻辑测试 ===");
    
    // user1进行NFT挖矿
    const burnAmount = ethers.parseEther("50000");
    await miningContract.connect(user1).burnTokenBWithNFT(burnAmount, ethers.ZeroAddress);
    
    const startTime = await time.latest();
    console.log("user1完成NFT挖矿");
    
    // 推进到第2期
    await time.increaseTo(startTime + 2 * RELEASE_INTERVAL);
    
    // 检查user1可领取数量
    const claimableBefore = await miningContract.getUserClaimableAmount2(await user1.getAddress());
    console.log(`第2期可领取: ${ethers.formatEther(claimableBefore)} BNB价值`);
    expect(claimableBefore).to.be.gt(0);
    
    // user1正常领取
    await miningContract.connect(user1).claimTokenBFromMechanism2();
    console.log("✅ user1成功领取第2期奖励");
    
    // 测试：如果用户转移所有NFT，是否还能领取
    console.log("\\n测试：转移NFT后的领取能力");
    
    const user1NFTCount = await minerNFT.balanceOf(await user1.getAddress());
    console.log(`user1当前NFT数量: ${user1NFTCount}`);
    
    // 将user1的所有NFT转移给user2
    for (let i = 0; i < Number(user1NFTCount); i++) {
      const tokenId = await minerNFT.tokenOfOwnerByIndex(await user1.getAddress(), 0);
      await minerNFT.connect(user1).transferFrom(
        await user1.getAddress(),
        await user2.getAddress(),
        tokenId
      );
    }
    
    const user1NFTCountAfter = await minerNFT.balanceOf(await user1.getAddress());
    console.log(`转移后user1 NFT数量: ${user1NFTCountAfter}`);
    
    // 推进到第3期
    await time.increaseTo(startTime + 3 * RELEASE_INTERVAL);
    
    // 尝试领取（应该失败，因为没有NFT）
    try {
      await miningContract.connect(user1).claimTokenBFromMechanism2();
      console.log("❌ 应该失败但成功了");
      expect.fail("没有NFT时不应该能领取");
    } catch (error) {
      console.log("✅ 正确：没有NFT时无法领取");
      expect(error.message).to.include("Must hold at least one NFT");
    }
    
    // 重新给user1一个NFT
    await minerNFT.batchMintCount(await user1.getAddress(), 1);
    console.log("重新给user1铸造1个NFT");
    
    // 现在应该可以领取
    const claimableAfter = await miningContract.getUserClaimableAmount2(await user1.getAddress());
    console.log(`重新持有NFT后可领取: ${ethers.formatEther(claimableAfter)} BNB价值`);
    
    if (claimableAfter > 0) {
      await miningContract.connect(user1).claimTokenBFromMechanism2();
      console.log("✅ 重新持有NFT后成功领取");
    }
  });
  
  it("应该对比机制一和机制二的释放周期一致性", async function () {
    console.log("=== 机制一和机制二释放周期一致性对比 ===");
    
    // 设置TokenA交易对
    const liquidityTokenA = ethers.parseEther("50000");
    const liquidityWBNB = ethers.parseEther("50");
    
    await mockWBNB.deposit({ value: liquidityWBNB });
    await tokenA.approve(await mockRouter.getAddress(), liquidityTokenA);
    await mockWBNB.approve(await mockRouter.getAddress(), liquidityWBNB);
    
    await mockRouter.addLiquidity(
      await tokenA.getAddress(),
      await mockWBNB.getAddress(),
      liquidityTokenA,
      liquidityWBNB,
      0, 0,
      await owner.getAddress(),
Math.floor(Date.now() / 1000) + 3600
    );
    
    await priceOracle.configureToken(await tokenA.getAddress());
    await priceOracle.recordPrice(await tokenA.getAddress());
    
    // 给user1 TokenA
    await tokenA.transfer(await user1.getAddress(), ethers.parseEther("20000"));
    await tokenA.connect(user1).approve(await miningContract.getAddress(), ethers.parseEther("20000"));
    
    const startTime = await time.latest();
    
    // user1同时进行两种机制的挖矿
    // 机制一：销毁TokenA
    const burnTokenA = ethers.parseEther("10000"); // 10 BNB价值 -> 20 BNB释放
    await miningContract.connect(user1).burnTokenAForTokenB(burnTokenA, ethers.ZeroAddress);
    
    // 机制二：销毁TokenB (持有NFT)
    const burnTokenB = ethers.parseEther("100000"); // 10 BNB价值 -> 20 BNB释放
    await miningContract.connect(user1).burnTokenBWithNFT(burnTokenB, ethers.ZeroAddress);
    
    console.log("user1同时进行了机制一和机制二挖矿");
    
    // 获取两个挖矿记录
    const record1 = await miningContract.getUserMiningRecord1Detail(await user1.getAddress(), 0);
    const record2 = await miningContract.getUserMiningRecord2Detail(await user1.getAddress(), 0);
    
    console.log(`机制一总释放价值: ${ethers.formatEther(record1.totalReleaseValue)} BNB`);
    console.log(`机制二总释放价值: ${ethers.formatEther(record2.totalReleaseValue)} BNB`);
    
    // 验证释放价值相等
    expect(record1.totalReleaseValue).to.equal(record2.totalReleaseValue);
    console.log("✅ 两种机制的总释放价值相等");
    
    // 测试关键期数的释放一致性
    const testPeriods = [1, 3, 5, 6]; // 特别测试之前有问题的1,3,5期
    
    for (const period of testPeriods) {
      console.log(`\\n--- 第${period}期一致性测试 ---`);
      
      // 推进时间
      await time.increaseTo(startTime + period * RELEASE_INTERVAL);
      
      // 检查两种机制的可领取数量
      const claimable1 = await miningContract.getUserClaimableAmount1(await user1.getAddress());
      const claimable2 = await miningContract.getUserClaimableAmount2(await user1.getAddress());
      
      console.log(`机制一第${period}期可领取: ${ethers.formatEther(claimable1)} BNB价值`);
      console.log(`机制二第${period}期可领取: ${ethers.formatEther(claimable2)} BNB价值`);
      
      // 验证两种机制都有可领取数量
      expect(claimable1).to.be.gt(0, `机制一第${period}期应该有可领取数量`);
      expect(claimable2).to.be.gt(0, `机制二第${period}期应该有可领取数量`);
      
      // 验证可领取数量相等
      expect(claimable1).to.equal(claimable2, `第${period}期两种机制可领取数量应该相等`);
      
      console.log(`✅ 第${period}期两种机制释放一致`);
      
      // 执行领取
      await miningContract.connect(user1).claimTokenBFromMechanism1();
      await miningContract.connect(user1).claimTokenBFromMechanism2();
      
      console.log(`✅ 第${period}期两种机制都成功领取`);
    }
    
    console.log("\\n✅ 机制一和机制二的释放周期完全一致！");
  });
});