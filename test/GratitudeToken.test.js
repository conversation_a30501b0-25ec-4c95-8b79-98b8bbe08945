const { expect } = require("chai");
const { ethers } = require("hardhat");

describe("GratitudeToken", function () {
    let gratitudeToken;
    let owner;
    let addr1;
    let addr2;
    let addr3;

    const TOTAL_SUPPLY = ethers.parseEther("250000000"); // 2.5亿

    beforeEach(async function () {
        [owner, addr1, addr2, addr3] = await ethers.getSigners();
        
        const GratitudeToken = await ethers.getContractFactory("GratitudeToken");
        gratitudeToken = await GratitudeToken.deploy(owner.address);
        await gratitudeToken.waitForDeployment();
    });

    describe("部署", function () {
        it("应该正确设置代币基本信息", async function () {
            const tokenInfo = await gratitudeToken.getTokenInfo();
            
            expect(tokenInfo.name).to.equal("Gratitude Protocol");
            expect(tokenInfo.symbol).to.equal("GRAT");
            expect(tokenInfo.decimals).to.equal(18);
            expect(tokenInfo.totalSupply).to.equal(TOTAL_SUPPLY);
            expect(tokenInfo.maxSupply).to.equal(TOTAL_SUPPLY);
        });

        it("应该将所有代币铸造给所有者", async function () {
            const ownerBalance = await gratitudeToken.balanceOf(owner.address);
            expect(ownerBalance).to.equal(TOTAL_SUPPLY);
        });

        it("应该设置正确的所有者和铸造者", async function () {
            expect(await gratitudeToken.owner()).to.equal(owner.address);
            expect(await gratitudeToken.isMinter(owner.address)).to.be.true;
        });
    });

    describe("基本转账功能", function () {
        it("应该能够正常转账", async function () {
            const transferAmount = ethers.parseEther("1000");
            
            await gratitudeToken.transfer(addr1.address, transferAmount);
            
            expect(await gratitudeToken.balanceOf(addr1.address)).to.equal(transferAmount);
            expect(await gratitudeToken.balanceOf(owner.address)).to.equal(TOTAL_SUPPLY - transferAmount);
        });

        it("应该能够使用授权转账", async function () {
            const transferAmount = ethers.parseEther("1000");
            
            await gratitudeToken.approve(addr1.address, transferAmount);
            await gratitudeToken.connect(addr1).transferFrom(owner.address, addr2.address, transferAmount);
            
            expect(await gratitudeToken.balanceOf(addr2.address)).to.equal(transferAmount);
        });

        it("余额不足时应该转账失败", async function () {
            const transferAmount = ethers.parseEther("1000");
            
            await expect(
                gratitudeToken.connect(addr1).transfer(addr2.address, transferAmount)
            ).to.be.revertedWithCustomError(gratitudeToken, "ERC20InsufficientBalance");
        });
    });

    describe("铸造者管理", function () {
        it("所有者应该能够添加铸造者", async function () {
            await gratitudeToken.addMinter(addr1.address);
            expect(await gratitudeToken.isMinter(addr1.address)).to.be.true;
        });

        it("所有者应该能够移除铸造者", async function () {
            await gratitudeToken.addMinter(addr1.address);
            await gratitudeToken.removeMinter(addr1.address);
            expect(await gratitudeToken.isMinter(addr1.address)).to.be.false;
        });

        it("非所有者不能添加铸造者", async function () {
            await expect(
                gratitudeToken.connect(addr1).addMinter(addr2.address)
            ).to.be.revertedWithCustomError(gratitudeToken, "OwnableUnauthorizedAccount");
        });

        it("不能移除所有者的铸造者权限", async function () {
            await expect(
                gratitudeToken.removeMinter(owner.address)
            ).to.be.revertedWith("Cannot remove owner as minter");
        });
    });

    describe("代币销毁", function () {
        it("应该能够销毁自己的代币", async function () {
            const burnAmount = ethers.parseEther("1000");
            const initialSupply = await gratitudeToken.totalSupply();
            
            await gratitudeToken.burn(burnAmount);
            
            expect(await gratitudeToken.totalSupply()).to.equal(initialSupply - burnAmount);
            expect(await gratitudeToken.balanceOf(owner.address)).to.equal(TOTAL_SUPPLY - burnAmount);
        });

        it("应该能够销毁授权的代币", async function () {
            const burnAmount = ethers.parseEther("1000");
            
            // 先转一些代币给addr1
            await gratitudeToken.transfer(addr1.address, burnAmount * 2n);
            
            // addr1授权给addr2销毁代币
            await gratitudeToken.connect(addr1).approve(addr2.address, burnAmount);
            
            const initialSupply = await gratitudeToken.totalSupply();
            await gratitudeToken.connect(addr2).burnFrom(addr1.address, burnAmount);
            
            expect(await gratitudeToken.totalSupply()).to.equal(initialSupply - burnAmount);
        });

        it("余额不足时销毁应该失败", async function () {
            const burnAmount = ethers.parseEther("1000");
            
            await expect(
                gratitudeToken.connect(addr1).burn(burnAmount)
            ).to.be.revertedWith("Insufficient balance to burn");
        });
    });

    describe("黑名单功能", function () {
        it("所有者应该能够添加地址到黑名单", async function () {
            await gratitudeToken.addToBlacklist(addr1.address);
            expect(await gratitudeToken.isBlacklisted(addr1.address)).to.be.true;
        });

        it("所有者应该能够从黑名单移除地址", async function () {
            await gratitudeToken.addToBlacklist(addr1.address);
            await gratitudeToken.removeFromBlacklist(addr1.address);
            expect(await gratitudeToken.isBlacklisted(addr1.address)).to.be.false;
        });

        it("黑名单地址不能转账", async function () {
            const transferAmount = ethers.parseEther("1000");
            
            // 先转一些代币给addr1
            await gratitudeToken.transfer(addr1.address, transferAmount);
            
            // 将addr1加入黑名单
            await gratitudeToken.addToBlacklist(addr1.address);
            
            // addr1尝试转账应该失败
            await expect(
                gratitudeToken.connect(addr1).transfer(addr2.address, transferAmount)
            ).to.be.revertedWith("Account is blacklisted");
        });

        it("不能将所有者加入黑名单", async function () {
            await expect(
                gratitudeToken.addToBlacklist(owner.address)
            ).to.be.revertedWith("Cannot blacklist owner");
        });
    });

    describe("批量转账", function () {
        it("应该能够批量转账", async function () {
            const recipients = [addr1.address, addr2.address, addr3.address];
            const amounts = [
                ethers.parseEther("1000"),
                ethers.parseEther("2000"),
                ethers.parseEther("3000")
            ];
            
            await gratitudeToken.batchTransfer(recipients, amounts);
            
            expect(await gratitudeToken.balanceOf(addr1.address)).to.equal(amounts[0]);
            expect(await gratitudeToken.balanceOf(addr2.address)).to.equal(amounts[1]);
            expect(await gratitudeToken.balanceOf(addr3.address)).to.equal(amounts[2]);
        });

        it("数组长度不匹配时应该失败", async function () {
            const recipients = [addr1.address, addr2.address];
            const amounts = [ethers.parseEther("1000")];
            
            await expect(
                gratitudeToken.batchTransfer(recipients, amounts)
            ).to.be.revertedWith("Arrays length mismatch");
        });

        it("余额不足时批量转账应该失败", async function () {
            const recipients = [addr1.address];
            const amounts = [TOTAL_SUPPLY + 1n];
            
            await expect(
                gratitudeToken.batchTransfer(recipients, amounts)
            ).to.be.revertedWith("Insufficient balance");
        });
    });

    describe("暂停功能", function () {
        it("所有者应该能够暂停合约", async function () {
            await gratitudeToken.pause();
            expect(await gratitudeToken.paused()).to.be.true;
        });

        it("暂停时不能转账", async function () {
            await gratitudeToken.pause();
            
            await expect(
                gratitudeToken.transfer(addr1.address, ethers.parseEther("1000"))
            ).to.be.revertedWithCustomError(gratitudeToken, "EnforcedPause");
        });

        it("所有者应该能够恢复合约", async function () {
            await gratitudeToken.pause();
            await gratitudeToken.unpause();
            expect(await gratitudeToken.paused()).to.be.false;
        });
    });

    describe("紧急提取", function () {
        it("所有者应该能够紧急提取合约中的代币", async function () {
            const amount = ethers.parseEther("1000");
            
            // 先向合约转入一些代币
            await gratitudeToken.transfer(await gratitudeToken.getAddress(), amount);
            
            const initialOwnerBalance = await gratitudeToken.balanceOf(owner.address);
            await gratitudeToken.emergencyWithdraw();
            
            expect(await gratitudeToken.balanceOf(owner.address)).to.equal(initialOwnerBalance + amount);
            expect(await gratitudeToken.balanceOf(await gratitudeToken.getAddress())).to.equal(0);
        });
    });
});
