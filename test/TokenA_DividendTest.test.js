const { expect } = require("chai");
const { ethers } = require("hardhat");
const { time } = require("@nomicfoundation/hardhat-network-helpers");

describe("TokenA分红机制专项测试", function () {
  let tokenA, minerNFT, mockWBNB, mockRouter;
  let owner, user1, user2, user3, marketingWallet;
  
  beforeEach(async function () {
    const signers = await ethers.getSigners();
    [owner, user1, user2, user3, marketingWallet] = signers.slice(0, 5);
    
    console.log(`=== TokenA分红机制专项测试 ===`);
    
    // 部署Mock合约
    const MockWBNB = await ethers.getContractFactory("MockWBNB");
    mockWBNB = await MockWBNB.deploy();
    
    const MockPancakeFactory = await ethers.getContractFactory("MockPancakeFactory");
    const mockFactory = await MockPancakeFactory.deploy();
    
    const MockPancakeRouter = await ethers.getContractFactory("MockPancakeRouter");
    mockRouter = await MockPancakeRouter.deploy(
      await mockFactory.getAddress(),
      await mockWBNB.getAddress()
    );
    
    // 部署TokenA
    const TokenA = await ethers.getContractFactory("TokenA");
    tokenA = await TokenA.deploy(
      await marketingWallet.getAddress(), // 营销钱包
      await mockRouter.getAddress(),
      await mockWBNB.getAddress()
    );
    
    // 部署MinerNFT
    const MinerNFT = await ethers.getContractFactory("MinerNFT");
    minerNFT = await MinerNFT.deploy("MinerNFT", "MNFT", await tokenA.getAddress());
    
    // 设置NFT合约地址
    await tokenA.setNftContract(await minerNFT.getAddress());
    
    console.log(`TokenA合约: ${await tokenA.getAddress()}`);
    console.log(`MinerNFT合约: ${await minerNFT.getAddress()}`);
  });
  
  it("应该测试税收分配和分红累积", async function () {
    console.log("=== 税收分配和分红累积测试 ===");
    
    // 给用户们铸造NFT
    await minerNFT.batchMintCount(await user1.getAddress(), 2); // 2个NFT
    await minerNFT.batchMintCount(await user2.getAddress(), 3); // 3个NFT
    
    console.log("user1拥有2个NFT，user2拥有3个NFT");
    
    // 给用户们一些TokenA用于交易
    const userTokens = ethers.parseEther("10000");
    await tokenA.transfer(await user1.getAddress(), userTokens);
    await tokenA.transfer(await user2.getAddress(), userTokens);
    await tokenA.transfer(await user3.getAddress(), userTokens);
    
    // 设置user3为交易对(模拟买卖交易)
    await tokenA.setPair(await user3.getAddress(), true);
    
    console.log("\\n=== 模拟买入交易触发税收 ===");
    
    // 模拟买入交易 (从交易对转出)
    const buyAmount = ethers.parseEther("1000");
    
    const beforeContractBalance = await tokenA.balanceOf(await tokenA.getAddress());
    const beforeMarketingBalance = await tokenA.balanceOf(await marketingWallet.getAddress());
    const beforeTotalSupply = await tokenA.totalSupply();
    
    // user3(交易对) -> user1 (买入交易，征收3%税)
    await tokenA.connect(user3).transfer(await user1.getAddress(), buyAmount);
    
    const afterContractBalance = await tokenA.balanceOf(await tokenA.getAddress());
    const afterMarketingBalance = await tokenA.balanceOf(await marketingWallet.getAddress());
    const afterTotalSupply = await tokenA.totalSupply();
    
    // 计算税收分配
    const totalTax = (buyAmount * BigInt(3)) / BigInt(100); // 3%税收
    const marketingAmount = (totalTax * BigInt(20)) / BigInt(100); // 20%给营销
    const burnAmount = (totalTax * BigInt(50)) / BigInt(100); // 50%销毁
    const dividendAmount = totalTax - marketingAmount - burnAmount; // 30%分红
    
    console.log(`交易金额: ${ethers.formatEther(buyAmount)} TokenA`);
    console.log(`总税收: ${ethers.formatEther(totalTax)} TokenA`);
    console.log(`营销分配: ${ethers.formatEther(marketingAmount)} TokenA`);
    console.log(`销毁数量: ${ethers.formatEther(burnAmount)} TokenA`);
    console.log(`分红数量: ${ethers.formatEther(dividendAmount)} TokenA`);
    
    // 验证营销钱包收到资金
    const marketingReceived = afterMarketingBalance - beforeMarketingBalance;
    expect(marketingReceived).to.equal(marketingAmount, "营销钱包应该收到正确数量");
    console.log(`✅ 营销钱包收到: ${ethers.formatEther(marketingReceived)} TokenA`);
    
    // 验证代币被销毁
    const burnedAmount = beforeTotalSupply - afterTotalSupply;
    expect(burnedAmount).to.equal(burnAmount, "应该销毁正确数量的代币");
    console.log(`✅ 销毁代币: ${ethers.formatEther(burnedAmount)} TokenA`);
    
    // 验证合约累积分红
    const contractReceived = afterContractBalance - beforeContractBalance;
    expect(contractReceived).to.equal(dividendAmount, "合约应该累积分红");
    console.log(`✅ 合约累积分红: ${ethers.formatEther(contractReceived)} TokenA`);
    
    // 检查分红统计
    const contractInfo = await tokenA.contractBalance();
    console.log(`合约当前余额: ${ethers.formatEther(contractInfo.balance)} TokenA`);
    console.log(`累计分红总额: ${ethers.formatEther(contractInfo.totalDivs)} TokenA`);
    console.log(`每股累积分红: ${contractInfo.cumulativePerShare}`);
  });
  
  it("应该测试NFT分红计算和领取", async function () {
    console.log("=== NFT分红计算和领取测试 ===");
    
    // 给用户们铸造NFT
    await minerNFT.batchMintCount(await user1.getAddress(), 2); // NFT #1, #2
    await minerNFT.batchMintCount(await user2.getAddress(), 1); // NFT #3
    
    console.log("user1拥有2个NFT，user2拥有1个NFT");
    
    // 给合约一些TokenA作为分红
    const dividendAmount = ethers.parseEther("900"); // 900 TokenA分红
    await tokenA.transfer(await tokenA.getAddress(), dividendAmount);
    
    // 模拟分红分配 (直接设置累积每股分红)
    const totalNFTs = 3; // 总共3个NFT
    const expectedPerNFT = dividendAmount / BigInt(totalNFTs); // 每个NFT应得300 TokenA
    
    console.log(`总分红: ${ethers.formatEther(dividendAmount)} TokenA`);
    console.log(`总NFT数: ${totalNFTs}`);
    console.log(`每个NFT应得: ${ethers.formatEther(expectedPerNFT)} TokenA`);
    
    // 手动设置累积每股分红 (模拟税收分红过程)
    // 这里我们需要让合约产生实际的分红
    await tokenA.transfer(await user3.getAddress(), ethers.parseEther("10000"));
    await tokenA.setPair(await user3.getAddress(), true);
    
    // 进行多次交易以产生分红
    for (let i = 0; i < 5; i++) {
      await tokenA.connect(user3).transfer(await user1.getAddress(), ethers.parseEther("1000"));
    }
    
    console.log("\\n=== 检查用户可领取分红 ===");
    
    // 检查user1的可领取分红 (2个NFT)
    const user1Dividend = await tokenA.calculateDividend(await user1.getAddress());
    console.log(`user1可领取: ${ethers.formatEther(user1Dividend)} TokenA`);
    
    // 检查user2的可领取分红 (1个NFT)
    const user2Dividend = await tokenA.calculateDividend(await user2.getAddress());
    console.log(`user2可领取: ${ethers.formatEther(user2Dividend)} TokenA`);
    
    // 验证分红比例正确 (user1应该是user2的2倍)
    if (user1Dividend > 0 && user2Dividend > 0) {
      const ratio = Number(user1Dividend * BigInt(100) / user2Dividend) / 100;
      console.log(`分红比例 user1:user2 = ${ratio.toFixed(2)}:1`);
      expect(ratio).to.be.closeTo(2.0, 0.1, "user1应该获得约2倍的分红");
      console.log(`✅ 分红比例正确`);
    }
    
    console.log("\\n=== 执行分红领取 ===");
    
    // user1领取分红
    if (user1Dividend > 0) {
      const beforeBalance = await tokenA.balanceOf(await user1.getAddress());
      await tokenA.connect(user1).claimDividend();
      const afterBalance = await tokenA.balanceOf(await user1.getAddress());
      const actualReceived = afterBalance - beforeBalance;
      
      console.log(`user1实际领取: ${ethers.formatEther(actualReceived)} TokenA`);
      expect(actualReceived).to.equal(user1Dividend, "应该领取正确数量");
      console.log(`✅ user1领取成功`);
    }
    
    // user2领取分红
    if (user2Dividend > 0) {
      const beforeBalance = await tokenA.balanceOf(await user2.getAddress());
      await tokenA.connect(user2).claimDividend();
      const afterBalance = await tokenA.balanceOf(await user2.getAddress());
      const actualReceived = afterBalance - beforeBalance;
      
      console.log(`user2实际领取: ${ethers.formatEther(actualReceived)} TokenA`);
      expect(actualReceived).to.equal(user2Dividend, "应该领取正确数量");
      console.log(`✅ user2领取成功`);
    }
  });
  
  it("应该测试单个NFT分红领取", async function () {
    console.log("=== 单个NFT分红领取测试 ===");
    
    // 给用户铸造NFT
    await minerNFT.batchMintCount(await user1.getAddress(), 1); // NFT #1
    
    // 产生分红
    await tokenA.transfer(await user3.getAddress(), ethers.parseEther("10000"));
    await tokenA.setPair(await user3.getAddress(), true);
    await tokenA.connect(user3).transfer(await user1.getAddress(), ethers.parseEther("1000"));
    
    // 检查NFT可领取分红
    const nftDividend = await tokenA.calculateNFTDividend(1);
    console.log(`NFT #1可领取: ${ethers.formatEther(nftDividend)} TokenA`);
    
    if (nftDividend > 0) {
      // 执行单个NFT分红领取
      const beforeBalance = await tokenA.balanceOf(await user1.getAddress());
      await tokenA.connect(user1).claimNFTDividend(1);
      const afterBalance = await tokenA.balanceOf(await user1.getAddress());
      const actualReceived = afterBalance - beforeBalance;
      
      console.log(`实际领取: ${ethers.formatEther(actualReceived)} TokenA`);
      expect(actualReceived).to.equal(nftDividend, "应该领取正确数量");
      console.log(`✅ 单个NFT分红领取成功`);
    }
  });
  
  it("应该测试分红系统的安全性", async function () {
    console.log("=== 分红系统安全性测试 ===");
    
    // 给用户铸造NFT
    await minerNFT.batchMintCount(await user1.getAddress(), 1);
    
    // 产生分红
    await tokenA.transfer(await user3.getAddress(), ethers.parseEther("10000"));
    await tokenA.setPair(await user3.getAddress(), true);
    await tokenA.connect(user3).transfer(await user1.getAddress(), ethers.parseEther("1000"));
    
    console.log("\\n=== 测试重复领取保护 ===");
    
    // 第一次领取
    const dividend = await tokenA.calculateDividend(await user1.getAddress());
    if (dividend > 0) {
      await tokenA.connect(user1).claimDividend();
      console.log(`✅ 第一次领取成功`);
      
      // 立即尝试第二次领取 (应该无可领取数量)
      const secondDividend = await tokenA.calculateDividend(await user1.getAddress());
      console.log(`第二次可领取: ${ethers.formatEther(secondDividend)} TokenA`);
      expect(secondDividend).to.equal(0, "第二次应该无可领取数量");
      console.log(`✅ 重复领取保护正常`);
    }
    
    console.log("\\n=== 测试无NFT用户领取 ===");
    
    // user2没有NFT，不应该能领取分红
    const user2Dividend = await tokenA.calculateDividend(await user2.getAddress());
    console.log(`无NFT用户可领取: ${ethers.formatEther(user2Dividend)} TokenA`);
    expect(user2Dividend).to.equal(0, "无NFT用户不应该有分红");
    console.log(`✅ 无NFT用户保护正常`);
  });
});