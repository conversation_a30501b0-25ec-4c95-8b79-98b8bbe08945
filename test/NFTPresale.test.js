const { expect } = require("chai");
const { ethers } = require("hardhat");

describe("NFTPresale", function () {
  let nftPresale;
  let usdtToken;
  let owner;
  let buyer1;
  let buyer2;
  let referrer;

  const NFT_PRICE = 1500000; // 1.5 USDT (6 decimals)
  const MAX_SUPPLY = 10000;

  beforeEach(async function () {
    [owner, buyer1, buyer2, referrer] = await ethers.getSigners();

    // 部署模拟USDT代币
    const MockUSDT = await ethers.getContractFactory("MockUSDT");
    usdtToken = await MockUSDT.deploy();
    await usdtToken.waitForDeployment();

    // 部署NFT预售合约
    const NFTPresale = await ethers.getContractFactory("NFTPresale");
    nftPresale = await NFTPresale.deploy(await usdtToken.getAddress());
    await nftPresale.waitForDeployment();

    // 给买家分发USDT
    const usdtAmount = ethers.parseUnits("100", 6); // USDT uses 6 decimals
    await usdtToken.transfer(buyer1.address, usdtAmount);
    await usdtToken.transfer(buyer2.address, usdtAmount);
  });

  describe("部署", function () {
    it("应该设置正确的初始值", async function () {
      const presaleInfo = await nftPresale.getPresaleInfo();
      
      expect(presaleInfo._maxSupply).to.equal(MAX_SUPPLY);
      expect(presaleInfo._nftPrice).to.equal(NFT_PRICE);
      expect(presaleInfo._totalSold).to.equal(0);
      expect(presaleInfo._active).to.be.false;
    });

    it("应该设置正确的owner", async function () {
      expect(await nftPresale.owner()).to.equal(owner.address);
    });
  });

  describe("预售管理", function () {
    it("只有owner可以启动预售", async function () {
      const startTime = Math.floor(Date.now() / 1000) + 100;
      const endTime = startTime + 86400; // 1天后

      await expect(
        nftPresale.connect(buyer1).startPresale(startTime, endTime)
      ).to.be.revertedWithCustomError(nftPresale, "OwnableUnauthorizedAccount");
    });

    it("应该能启动预售", async function () {
      const startTime = Math.floor(Date.now() / 1000) + 100;
      const endTime = startTime + 86400;

      await expect(nftPresale.startPresale(startTime, endTime))
        .to.emit(nftPresale, "PresaleStarted")
        .withArgs(startTime, endTime);

      const presaleInfo = await nftPresale.getPresaleInfo();
      expect(presaleInfo._startTime).to.equal(startTime);
      expect(presaleInfo._endTime).to.equal(endTime);
    });

    it("不能重复启动预售", async function () {
      const startTime = Math.floor(Date.now() / 1000) + 100;
      const endTime = startTime + 86400;

      await nftPresale.startPresale(startTime, endTime);
      
      await expect(
        nftPresale.startPresale(startTime + 1000, endTime + 1000)
      ).to.be.revertedWith("Presale already active");
    });
  });

  describe("购买功能", function () {
    beforeEach(async function () {
      // 获取当前区块时间戳
      const currentBlock = await ethers.provider.getBlock("latest");
      const currentTime = currentBlock.timestamp;
      
      const startTime = currentTime + 10; // 10秒后开始
      const endTime = startTime + 86400; // 1天后结束
      await nftPresale.startPresale(startTime, endTime);
      
      // 等待时间过去，使预售开始
      await ethers.provider.send("evm_increaseTime", [20]);
      await ethers.provider.send("evm_mine");
    });

    it("应该能成功购买NFT", async function () {
      // 授权USDT
      const price = ethers.parseUnits("1.5", 6); // USDT uses 6 decimals
      await usdtToken.connect(buyer1).approve(await nftPresale.getAddress(), price);

      // 购买NFT
      await expect(nftPresale.connect(buyer1).buyNFT(ethers.ZeroAddress))
        .to.emit(nftPresale, "NFTPurchased");

      // 验证状态
      const userInfo = await nftPresale.getUserInfo(buyer1.address);
      expect(userInfo.purchased).to.be.true;

      const presaleInfo = await nftPresale.getPresaleInfo();
      expect(presaleInfo._totalSold).to.equal(1);
      expect(presaleInfo._totalParticipants).to.equal(1);
    });

    it("不能重复购买", async function () {
      // 第一次购买
      const price = ethers.parseUnits("1.5", 6);
      await usdtToken.connect(buyer1).approve(await nftPresale.getAddress(), price);
      await nftPresale.connect(buyer1).buyNFT(ethers.ZeroAddress);

      // 尝试第二次购买
      await usdtToken.connect(buyer1).approve(await nftPresale.getAddress(), price);
      await expect(
        nftPresale.connect(buyer1).buyNFT(ethers.ZeroAddress)
      ).to.be.revertedWith("Already purchased");
    });

    it("应该记录邀请关系", async function () {
      const price = ethers.parseUnits("1.5", 6);
      await usdtToken.connect(buyer1).approve(await nftPresale.getAddress(), price);

      // 使用邀请人购买
      await expect(nftPresale.connect(buyer1).buyNFT(referrer.address))
        .to.emit(nftPresale, "ReferralRecorded")
        .withArgs(referrer.address, buyer1.address);

      // 验证邀请关系
      const userInfo = await nftPresale.getUserInfo(buyer1.address);
      expect(userInfo.referrer).to.equal(referrer.address);

      const referrerInfo = await nftPresale.getUserInfo(referrer.address);
      expect(referrerInfo.refereeCount).to.equal(1);

      const referees = await nftPresale.getReferees(referrer.address);
      expect(referees[0]).to.equal(buyer1.address);
    });
  });

  describe("资金管理", function () {
    beforeEach(async function () {
      const currentBlock = await ethers.provider.getBlock("latest");
      const currentTime = currentBlock.timestamp;
      
      const startTime = currentTime + 10;
      const endTime = startTime + 86400;
      await nftPresale.startPresale(startTime, endTime);
      
      // 等待预售开始
      await ethers.provider.send("evm_increaseTime", [20]);
      await ethers.provider.send("evm_mine");

      // 模拟购买
      const price = ethers.parseUnits("1.5", 6);
      await usdtToken.connect(buyer1).approve(await nftPresale.getAddress(), price);
      await nftPresale.connect(buyer1).buyNFT(ethers.ZeroAddress);
    });

    it("只有owner可以提取资金", async function () {
      await expect(
        nftPresale.connect(buyer1).withdrawFunds(0)
      ).to.be.revertedWithCustomError(nftPresale, "OwnableUnauthorizedAccount");
    });

    it("应该能提取资金", async function () {
      const initialBalance = await usdtToken.balanceOf(owner.address);
      
      await expect(nftPresale.withdrawFunds(0))
        .to.emit(nftPresale, "FundsWithdrawn");

      const finalBalance = await usdtToken.balanceOf(owner.address);
      expect(finalBalance).to.be.gt(initialBalance);
    });
  });

  describe("查询功能", function () {
    it("应该返回正确的预售信息", async function () {
      const presaleInfo = await nftPresale.getPresaleInfo();
      
      expect(presaleInfo._maxSupply).to.equal(MAX_SUPPLY);
      expect(presaleInfo._nftPrice).to.equal(NFT_PRICE);
      expect(presaleInfo._totalSold).to.equal(0);
      expect(presaleInfo._totalParticipants).to.equal(0);
    });

    it("应该正确检查购买资格", async function () {
      // 预售未开始
      let [canPurchase, message] = await nftPresale.canPurchase(buyer1.address);
      expect(canPurchase).to.be.false;
      expect(message).to.equal("Presale not active");

      // 启动预售
      const currentBlock = await ethers.provider.getBlock("latest");
      const currentTime = currentBlock.timestamp;
      
      const startTime = currentTime + 10;
      const endTime = startTime + 86400;
      await nftPresale.startPresale(startTime, endTime);
      
      // 等待预售开始
      await ethers.provider.send("evm_increaseTime", [20]);
      await ethers.provider.send("evm_mine");

      [canPurchase, message] = await nftPresale.canPurchase(buyer1.address);
      expect(canPurchase).to.be.true;
      expect(message).to.equal("");
    });
  });
});