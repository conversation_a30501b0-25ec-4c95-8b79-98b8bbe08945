const { expect } = require("chai");
const { ethers } = require("hardhat");

describe("极端情况完整业务流程测试", function () {
  let tokenA, tokenB, minerNFT, miningContract, priceOracle;
  let mockWBNB, mockFactory, mockRouter;
  let owner, marketingWallet, users;
  
  // 常量定义
  const MAX_UINT256 = 2n ** 256n - 1n;
  const MIN_TRANSFER = 1n; // 1 wei
  const LARGE_AMOUNT = ethers.parseEther("1000000"); // 100万
  
  beforeEach(async function () {
    // 获取更多测试账户用于大规模测试
    const signers = await ethers.getSigners();
    [owner, marketingWallet, ...users] = signers.slice(0, 50); // 使用50个账户
    
    console.log(`=== 初始化测试环境 (${users.length}个用户) ===`);
    
    // 部署模拟PancakeSwap环境
    const MockWBNB = await ethers.getContractFactory("MockWBNB");
    mockWBNB = await MockWBNB.deploy();
    await mockWBNB.waitForDeployment();
    
    const MockPancakeFactory = await ethers.getContractFactory("MockPancakeFactory");
    mockFactory = await MockPancakeFactory.deploy();
    await mockFactory.waitForDeployment();
    
    const MockPancakeRouter = await ethers.getContractFactory("MockPancakeRouter");
    mockRouter = await MockPancakeRouter.deploy(
      await mockFactory.getAddress(),
      await mockWBNB.getAddress()
    );
    await mockRouter.waitForDeployment();
    
    // 部署核心合约
    const PriceOracle = await ethers.getContractFactory("PriceOracle");
    priceOracle = await PriceOracle.deploy();
    await priceOracle.waitForDeployment();
    
    const TokenA = await ethers.getContractFactory("TokenA");
    tokenA = await TokenA.deploy(
      await marketingWallet.getAddress(),
      await mockRouter.getAddress(),
      await mockWBNB.getAddress()
    );
    await tokenA.waitForDeployment();
    
    const TokenB = await ethers.getContractFactory("TokenB");
    tokenB = await TokenB.deploy(
      await marketingWallet.getAddress(),
      await mockRouter.getAddress(),
      await mockWBNB.getAddress()
    );
    await tokenB.waitForDeployment();
    
    const MinerNFT = await ethers.getContractFactory("MinerNFT");
    minerNFT = await MinerNFT.deploy("MinerNFT", "MNFT", await tokenA.getAddress());
    await minerNFT.waitForDeployment();
    
    const MiningContract = await ethers.getContractFactory("MiningContract");
    miningContract = await MiningContract.deploy(
      await tokenA.getAddress(),
      await tokenB.getAddress(),
      await minerNFT.getAddress(),
      await priceOracle.getAddress()
    );
    await miningContract.waitForDeployment();
    
    // 配置合约关系
    await tokenA.setNftContract(await minerNFT.getAddress());
    await tokenA.setTaxExempt(await miningContract.getAddress(), true);
    await tokenB.setTaxExempt(await miningContract.getAddress(), true);
    
    // 向NFT合约转入TokenA用于分红
    await tokenA.transfer(await minerNFT.getAddress(), ethers.parseEther("21000000"));
    
    // 设置价格
    await priceOracle.setManualPrice(await tokenA.getAddress(), ethers.parseEther("0.001"));
    await priceOracle.setManualPrice(await tokenB.getAddress(), ethers.parseEther("0.0001"));
    
    console.log("=== 环境初始化完成 ===");
  });

  describe("1. 零值和最小值边界测试", function () {
    beforeEach(async function () {
      await setupBasicEnvironment();
    });
    
    it("应该处理零值转账尝试", async function () {
      console.log("=== 测试零值转账边界 ===");
      
      // 尝试零值转账应该失败
      await expect(
        tokenA.transfer(await users[0].getAddress(), 0)
      ).to.be.revertedWith("Transfer amount must be greater than zero");
      
      console.log("✓ 零值转账正确拒绝");
    });
    
    it("应该处理最小值转账", async function () {
      console.log("=== 测试最小值转账 ===");
      
      // 给用户足够的代币
      await tokenA.transfer(await users[0].getAddress(), ethers.parseEther("1000"));
      
      // 创建交易对
      const pairAddress = await createTradingPair();
      
      // 记录初始状态
      const initialBalance = await tokenA.balanceOf(await users[0].getAddress());
      const initialContractBalance = await tokenA.balanceOf(await tokenA.getAddress());
      
      // 1 wei转账到交易对
      await tokenA.connect(users[0]).transfer(pairAddress, MIN_TRANSFER);
      
      const finalBalance = await tokenA.balanceOf(await users[0].getAddress());
      const finalContractBalance = await tokenA.balanceOf(await tokenA.getAddress());
      
      // 验证最小值转账处理
      expect(finalBalance).to.equal(initialBalance - MIN_TRANSFER);
      
      // 由于金额太小，税收可能为0
      const taxCollected = finalContractBalance - initialContractBalance;
      console.log("最小转账税收:", ethers.formatUnits(taxCollected, "wei"), "wei");
      
      console.log("✓ 最小值转账处理正确");
    });
    
    it("应该处理零余额账户操作", async function () {
      console.log("=== 测试零余额账户操作 ===");
      
      const zeroUser = users[10];
      const zeroBalance = await tokenA.balanceOf(await zeroUser.getAddress());
      expect(zeroBalance).to.equal(0);
      
      // 零余额用户尝试转账
      await expect(
        tokenA.connect(zeroUser).transfer(await users[0].getAddress(), ethers.parseEther("1"))
      ).to.be.reverted;
      
      // 零余额用户尝试领取分红
      await expect(
        tokenA.connect(zeroUser).claimDividend()
      ).to.be.revertedWith("No NFT to claim dividends");
      
      console.log("✓ 零余额账户操作正确拒绝");
    });
    
    it("应该处理最小分红计算", async function () {
      console.log("=== 测试最小分红计算 ===");
      
      // 给用户NFT
      await minerNFT.mint(await users[0].getAddress());
      
      // 创建最小的税收来产生最小分红
      const pairAddress = await createTradingPair();
      await tokenA.transfer(await users[1].getAddress(), ethers.parseEther("100"));
      
      // 转账产生极小分红 (100 * 3% * 30% = 0.9)
      await tokenA.connect(users[1]).transfer(pairAddress, ethers.parseEther("100"));
      
      const dividend = await tokenA.calculateDividend(await users[0].getAddress());
      console.log("最小分红计算:", ethers.formatEther(dividend));
      
      if (dividend > 0) {
        await tokenA.connect(users[0]).claimDividend();
        console.log("✓ 最小分红领取成功");
      } else {
        console.log("✓ 最小分红为零，符合预期");
      }
    });
  });

  describe("2. 最大值和溢出边界测试", function () {
    beforeEach(async function () {
      await setupBasicEnvironment();
    });
    
    it("应该处理大额代币转账", async function () {
      console.log("=== 测试大额代币转账 ===");
      
      // 给用户大额代币（接近总供应量）
      const largeAmount = ethers.parseEther("100000000"); // 1亿代币
      await tokenA.transfer(await users[0].getAddress(), largeAmount);
      
      const pairAddress = await createTradingPair();
      
      // 大额转账到交易对
      const transferAmount = ethers.parseEther("50000000"); // 5000万
      const initialContractBalance = await tokenA.balanceOf(await tokenA.getAddress());
      
      await tokenA.connect(users[0]).transfer(pairAddress, transferAmount);
      
      const finalContractBalance = await tokenA.balanceOf(await tokenA.getAddress());
      const taxCollected = finalContractBalance - initialContractBalance;
      
      // 验证大额转账的税收计算
      const expectedTax = transferAmount * BigInt(3) / BigInt(100);
      console.log("大额转账税收 - 预期:", ethers.formatEther(expectedTax));
      console.log("大额转账税收 - 实际:", ethers.formatEther(taxCollected));
      
      expect(taxCollected).to.be.closeTo(expectedTax, ethers.parseEther("1000"));
      console.log("✓ 大额转账税收计算正确");
    });
    
    it("应该防止超过余额的转账", async function () {
      console.log("=== 测试超额转账防护 ===");
      
      const userBalance = await tokenA.balanceOf(await users[0].getAddress());
      const excessAmount = userBalance + ethers.parseEther("1");
      
      await expect(
        tokenA.connect(users[0]).transfer(await users[1].getAddress(), excessAmount)
      ).to.be.reverted;
      
      console.log("✓ 超额转账正确拒绝");
    });
    
    it("应该处理大量NFT持有者的分红", async function () {
      console.log("=== 测试大量NFT分红计算 ===");
      
      // 为多个用户铸造NFT
      const nftHolders = users.slice(0, 30); // 30个NFT持有者
      for (let i = 0; i < nftHolders.length; i++) {
        await minerNFT.mint(await nftHolders[i].getAddress());
      }
      
      console.log(`铸造了 ${nftHolders.length} 个NFT`);
      
      // 产生大额分红
      const pairAddress = await createTradingPair();
      await tokenA.transfer(await users[40].getAddress(), ethers.parseEther("1000000"));
      await tokenA.connect(users[40]).transfer(pairAddress, ethers.parseEther("500000"));
      
      // 计算每个NFT的分红
      const totalTax = ethers.parseEther("500000") * BigInt(3) / BigInt(100);
      const totalDividend = totalTax * BigInt(30) / BigInt(100);
      const expectedPerNFT = totalDividend / BigInt(nftHolders.length);
      
      console.log("总分红池:", ethers.formatEther(totalDividend));
      console.log("每NFT预期分红:", ethers.formatEther(expectedPerNFT));
      
      // 验证分红计算
      const firstUserDividend = await tokenA.calculateDividend(await nftHolders[0].getAddress());
      console.log("首个用户实际分红:", ethers.formatEther(firstUserDividend));
      
      expect(firstUserDividend).to.be.closeTo(expectedPerNFT, ethers.parseEther("100"));
      console.log("✓ 大量NFT分红计算正确");
    });
    
    it("应该处理接近uint256最大值的计算", async function () {
      console.log("=== 测试大数值计算边界 ===");
      
      // 测试累积分红率的大数值计算
      // 模拟长期运行后的累积值
      const largeAccumulation = ethers.parseEther("1000000000"); // 10亿
      
      // 验证分红计算不会溢出
      try {
        // 这里我们只验证计算逻辑不会崩溃
        const testValue = largeAccumulation * BigInt(1e18) / BigInt(1000);
        expect(testValue).to.be.gte(0);
        console.log("✓ 大数值计算未溢出");
      } catch (error) {
        console.error("大数值计算失败:", error.message);
        throw error;
      }
    });
  });

  describe("3. 异常状态处理测试", function () {
    beforeEach(async function () {
      await setupBasicEnvironment();
    });
    
    it("应该处理NFT转移后的分红", async function () {
      console.log("=== 测试NFT转移后分红处理 ===");
      
      // 用户A获得NFT并产生分红
      await minerNFT.mint(await users[0].getAddress());
      const tokenId = 1;
      
      const pairAddress = await createTradingPair();
      await tokenA.transfer(await users[2].getAddress(), ethers.parseEther("10000"));
      await tokenA.connect(users[2]).transfer(pairAddress, ethers.parseEther("5000"));
      
      // 检查用户A的分红
      const dividendBeforeTransfer = await tokenA.calculateDividend(await users[0].getAddress());
      console.log("转移前用户A分红:", ethers.formatEther(dividendBeforeTransfer));
      
      // NFT从用户A转移到用户B
      await minerNFT.connect(users[0]).transferFrom(
        await users[0].getAddress(),
        await users[1].getAddress(),
        tokenId
      );
      
      // 检查转移后的分红分配
      const dividendAfterTransfer_A = await tokenA.calculateDividend(await users[0].getAddress());
      const dividendAfterTransfer_B = await tokenA.calculateDividend(await users[1].getAddress());
      
      console.log("转移后用户A分红:", ethers.formatEther(dividendAfterTransfer_A));
      console.log("转移后用户B分红:", ethers.formatEther(dividendAfterTransfer_B));
      
      // 用户A应该没有分红，用户B应该有
      expect(dividendAfterTransfer_A).to.equal(0);
      expect(dividendAfterTransfer_B).to.be.gt(0);
      
      console.log("✓ NFT转移后分红正确重新分配");
    });
    
    it("应该处理分红池耗尽情况", async function () {
      console.log("=== 测试分红池耗尽处理 ===");
      
      // 铸造NFT
      await minerNFT.mint(await users[0].getAddress());
      
      // 产生分红
      const pairAddress = await createTradingPair();
      await tokenA.transfer(await users[1].getAddress(), ethers.parseEther("10000"));
      await tokenA.connect(users[1]).transfer(pairAddress, ethers.parseEther("5000"));
      
      // 正常领取分红
      const dividend = await tokenA.calculateDividend(await users[0].getAddress());
      if (dividend > 0) {
        await tokenA.connect(users[0]).claimDividend();
        console.log("首次分红领取:", ethers.formatEther(dividend));
      }
      
      // 尝试再次领取（应该为0）
      const remainingDividend = await tokenA.calculateDividend(await users[0].getAddress());
      expect(remainingDividend).to.equal(0);
      
      // 尝试领取不存在的分红
      if (remainingDividend === 0n) {
        await expect(
          tokenA.connect(users[0]).claimDividend()
        ).to.be.revertedWith("No dividend to claim");
      }
      
      console.log("✓ 分红池耗尽处理正确");
    });
    
    it("应该处理合约余额不足情况", async function () {
      console.log("=== 测试合约余额不足处理 ===");
      
      // 铸造NFT
      await minerNFT.mint(await users[0].getAddress());
      
      // 产生分红
      const pairAddress = await createTradingPair();
      await tokenA.transfer(await users[1].getAddress(), ethers.parseEther("10000"));
      await tokenA.connect(users[1]).transfer(pairAddress, ethers.parseEther("5000"));
      
      // 人为清空合约余额（模拟异常情况）
      const contractBalance = await tokenA.balanceOf(await tokenA.getAddress());
      if (contractBalance > 0) {
        await tokenA.emergencyWithdraw();
      }
      
      // 尝试领取分红应该失败
      const dividend = await tokenA.calculateDividend(await users[0].getAddress());
      if (dividend > 0) {
        await expect(
          tokenA.connect(users[0]).claimDividend()
        ).to.be.revertedWith("Insufficient contract balance");
        
        console.log("✓ 合约余额不足时正确拒绝分红领取");
      }
    });
    
    it("应该处理无效NFT ID的分红查询", async function () {
      console.log("=== 测试无效NFT分红查询 ===");
      
      // 查询不存在的NFT分红
      const invalidTokenId = 999999;
      const dividend = await tokenA.calculateNFTDividend(invalidTokenId);
      
      expect(dividend).to.equal(0);
      console.log("✓ 无效NFT分红查询返回0");
      
      // 尝试领取不存在NFT的分红
      await expect(
        tokenA.connect(users[0]).claimNFTDividend(invalidTokenId)
      ).to.be.reverted;
      
      console.log("✓ 无效NFT分红领取正确拒绝");
    });
  });

  describe("4. 网络拥堵和Gas限制测试", function () {
    beforeEach(async function () {
      await setupBasicEnvironment();
    });
    
    it("应该在低Gas限制下正常运行", async function () {
      console.log("=== 测试低Gas限制下的操作 ===");
      
      // 设置较低的自动分红Gas限制
      await tokenA.setAutoDividendConfig(
        100000, // 较低的Gas限制
        5,      // 较少的最大NFT数量
        ethers.parseEther("0.01"),
        true
      );
      
      // 铸造多个NFT
      for (let i = 0; i < 10; i++) {
        await minerNFT.mint(await users[i].getAddress());
      }
      
      // 产生分红
      const pairAddress = await createTradingPair();
      await tokenA.transfer(await users[20].getAddress(), ethers.parseEther("50000"));
      
      // 在Gas限制下进行交易
      const tx = await tokenA.connect(users[20]).transfer(pairAddress, ethers.parseEther("20000"));
      const receipt = await tx.wait();
      
      console.log("低Gas限制下交易Gas使用:", receipt.gasUsed.toString());
      
      // 验证自动分红在Gas限制下仍能工作
      const status = await tokenA.getAutoDividendStatus();
      console.log("自动分红状态:", status);
      
      console.log("✓ 低Gas限制下系统正常运行");
    });
    
    it("应该处理批量操作的Gas优化", async function () {
      console.log("=== 测试批量操作Gas优化 ===");
      
      // 准备多个接收者和金额
      const recipients = users.slice(0, 10).map(user => user.getAddress());
      const amounts = new Array(10).fill(ethers.parseEther("100"));
      
      // 给发送者足够的代币
      await tokenA.transfer(await users[30].getAddress(), ethers.parseEther("50000"));
      
      // 批量转账
      const resolvedRecipients = await Promise.all(recipients);
      const tx = await tokenB.connect(users[30]).batchTransfer(resolvedRecipients, amounts);
      const receipt = await tx.wait();
      
      console.log("批量转账Gas使用:", receipt.gasUsed.toString());
      console.log("平均每笔转账Gas:", (receipt.gasUsed / BigInt(10)).toString());
      
      // 验证所有接收者都收到了代币
      for (let i = 0; i < resolvedRecipients.length; i++) {
        const balance = await tokenB.balanceOf(resolvedRecipients[i]);
        expect(balance).to.be.gte(ethers.parseEther("90")); // 考虑可能的税收
      }
      
      console.log("✓ 批量操作Gas优化有效");
    });
    
    it("应该处理自动分红的渐进式处理", async function () {
      console.log("=== 测试自动分红渐进式处理 ===");
      
      // 创建大量NFT持有者
      const holderCount = 20;
      for (let i = 0; i < holderCount; i++) {
        await minerNFT.mint(await users[i].getAddress());
      }
      
      console.log(`创建了 ${holderCount} 个NFT持有者`);
      
      // 设置自动分红参数
      await tokenA.setAutoDividendConfig(
        300000, // 中等Gas限制
        5,      // 每次处理5个NFT
        ethers.parseEther("0.01"),
        true
      );
      
      // 产生分红
      const pairAddress = await createTradingPair();
      await tokenA.transfer(await users[40].getAddress(), ethers.parseEther("100000"));
      
      // 多次小额交易触发自动分红
      for (let i = 0; i < 5; i++) {
        const tx = await tokenA.connect(users[40]).transfer(pairAddress, ethers.parseEther("5000"));
        const receipt = await tx.wait();
        console.log(`第${i+1}次交易Gas:`, receipt.gasUsed.toString());
        
        // 检查自动分红状态
        const status = await tokenA.getAutoDividendStatus();
        console.log(`处理索引: ${status.lastProcessedIndex}/${status.totalNFTs}`);
      }
      
      console.log("✓ 自动分红渐进式处理正常");
    });
  });

  describe("5. 合约暂停恢复流程测试", function () {
    beforeEach(async function () {
      await setupBasicEnvironment();
    });
    
    it("应该在暂停期间阻止所有转账", async function () {
      console.log("=== 测试合约暂停功能 ===");
      
      // 给用户一些代币
      await tokenA.transfer(await users[0].getAddress(), ethers.parseEther("1000"));
      
      // 暂停合约
      await tokenA.pause();
      console.log("合约已暂停");
      
      // 尝试转账应该失败
      await expect(
        tokenA.connect(users[0]).transfer(await users[1].getAddress(), ethers.parseEther("100"))
      ).to.be.revertedWithCustomError(tokenA, "EnforcedPause");
      
      // 尝试领取分红应该失败
      await expect(
        tokenA.connect(users[0]).claimDividend()
      ).to.be.revertedWithCustomError(tokenA, "EnforcedPause");
      
      console.log("✓ 暂停期间所有操作被正确阻止");
      
      // 恢复合约
      await tokenA.unpause();
      console.log("合约已恢复");
      
      // 恢复后应该可以正常转账
      await tokenA.connect(users[0]).transfer(await users[1].getAddress(), ethers.parseEther("100"));
      
      const userBalance = await tokenA.balanceOf(await users[1].getAddress());
      expect(userBalance).to.be.gt(0);
      
      console.log("✓ 恢复后操作正常");
    });
    
    it("应该在暂停期间保持状态一致性", async function () {
      console.log("=== 测试暂停期间状态一致性 ===");
      
      // 设置初始状态
      await minerNFT.mint(await users[0].getAddress());
      const pairAddress = await createTradingPair();
      
      // 产生一些分红
      await tokenA.transfer(await users[1].getAddress(), ethers.parseEther("10000"));
      await tokenA.connect(users[1]).transfer(pairAddress, ethers.parseEther("5000"));
      
      // 记录暂停前状态
      const dividendBeforePause = await tokenA.calculateDividend(await users[0].getAddress());
      const contractBalanceBeforePause = await tokenA.balanceOf(await tokenA.getAddress());
      
      console.log("暂停前分红:", ethers.formatEther(dividendBeforePause));
      
      // 暂停合约
      await tokenA.pause();
      
      // 暂停期间状态应该保持不变
      const dividendDuringPause = await tokenA.calculateDividend(await users[0].getAddress());
      const contractBalanceDuringPause = await tokenA.balanceOf(await tokenA.getAddress());
      
      expect(dividendDuringPause).to.equal(dividendBeforePause);
      expect(contractBalanceDuringPause).to.equal(contractBalanceBeforePause);
      
      // 恢复后状态应该仍然正确
      await tokenA.unpause();
      
      const dividendAfterResume = await tokenA.calculateDividend(await users[0].getAddress());
      expect(dividendAfterResume).to.equal(dividendBeforePause);
      
      // 恢复后应该可以正常领取分红
      if (dividendAfterResume > 0) {
        await tokenA.connect(users[0]).claimDividend();
        console.log("恢复后成功领取分红");
      }
      
      console.log("✓ 暂停期间状态一致性保持良好");
    });
  });

  describe("6. 大规模用户并发测试", function () {
    beforeEach(async function () {
      await setupBasicEnvironment();
    });
    
    it("应该处理大量用户同时交易", async function () {
      console.log("=== 测试大量用户并发交易 ===");
      
      const concurrentUsers = users.slice(0, 20);
      const pairAddress = await createTradingPair();
      
      // 给每个用户分配代币
      for (let i = 0; i < concurrentUsers.length; i++) {
        await tokenA.transfer(await concurrentUsers[i].getAddress(), ethers.parseEther("5000"));
      }
      
      console.log(`为 ${concurrentUsers.length} 个用户分配了代币`);
      
      // 记录初始合约余额
      const initialContractBalance = await tokenA.balanceOf(await tokenA.getAddress());
      
      // 模拟并发交易（在实际区块链上会是顺序执行）
      const transactions = [];
      for (let i = 0; i < concurrentUsers.length; i++) {
        const transferAmount = ethers.parseEther("1000");
        const txPromise = tokenA.connect(concurrentUsers[i]).transfer(pairAddress, transferAmount);
        transactions.push(txPromise);
      }
      
      // 等待所有交易完成
      const results = await Promise.allSettled(transactions);
      const successCount = results.filter(r => r.status === 'fulfilled').length;
      
      console.log(`${successCount}/${concurrentUsers.length} 笔交易成功`);
      
      // 验证税收收取
      const finalContractBalance = await tokenA.balanceOf(await tokenA.getAddress());
      const totalTaxCollected = finalContractBalance - initialContractBalance;
      
      // 预期税收：成功交易数 * 1000 * 3%
      const expectedTax = BigInt(successCount) * ethers.parseEther("1000") * BigInt(3) / BigInt(100);
      console.log("预期总税收:", ethers.formatEther(expectedTax));
      console.log("实际总税收:", ethers.formatEther(totalTaxCollected));
      
      expect(totalTaxCollected).to.be.closeTo(expectedTax, ethers.parseEther("100"));
      console.log("✓ 大量用户并发交易处理正确");
    });
    
    it("应该处理大量用户同时领取分红", async function () {
      console.log("=== 测试大量用户同时领取分红 ===");
      
      const dividendUsers = users.slice(0, 15);
      
      // 为每个用户铸造NFT
      for (let i = 0; i < dividendUsers.length; i++) {
        await minerNFT.mint(await dividendUsers[i].getAddress());
      }
      
      // 产生分红
      const pairAddress = await createTradingPair();
      await tokenA.transfer(await users[30].getAddress(), ethers.parseEther("100000"));
      await tokenA.connect(users[30]).transfer(pairAddress, ethers.parseEther("50000"));
      
      console.log(`为 ${dividendUsers.length} 个用户设置分红环境`);
      
      // 验证每个用户都有分红
      let totalExpectedDividends = 0n;
      for (let i = 0; i < dividendUsers.length; i++) {
        const dividend = await tokenA.calculateDividend(await dividendUsers[i].getAddress());
        totalExpectedDividends += dividend;
        console.log(`用户${i}可领取分红:`, ethers.formatEther(dividend));
      }
      
      console.log("总预期分红:", ethers.formatEther(totalExpectedDividends));
      
      // 同时领取分红
      const claimTransactions = [];
      for (let i = 0; i < dividendUsers.length; i++) {
        const claimPromise = tokenA.connect(dividendUsers[i]).claimDividend();
        claimTransactions.push(claimPromise);
      }
      
      const claimResults = await Promise.allSettled(claimTransactions);
      const successfulClaims = claimResults.filter(r => r.status === 'fulfilled').length;
      
      console.log(`${successfulClaims}/${dividendUsers.length} 个用户成功领取分红`);
      
      // 验证分红后余额
      let totalClaimedDividends = 0n;
      for (let i = 0; i < dividendUsers.length; i++) {
        const userBalance = await tokenA.balanceOf(await dividendUsers[i].getAddress());
        totalClaimedDividends += userBalance;
      }
      
      console.log("实际领取总额:", ethers.formatEther(totalClaimedDividends));
      expect(totalClaimedDividends).to.be.closeTo(totalExpectedDividends, ethers.parseEther("10"));
      
      console.log("✓ 大量用户同时领取分红处理正确");
    });
  });

  describe("7. 经济激励边界测试", function () {
    beforeEach(async function () {
      await setupBasicEnvironment();
    });
    
    it("应该测试税收激励的边界效应", async function () {
      console.log("=== 测试税收激励边界效应 ===");
      
      const pairAddress = await createTradingPair();
      
      // 测试不同规模交易的税收效应
      const testAmounts = [
        ethers.parseEther("1"),      // 极小额
        ethers.parseEther("100"),    // 小额
        ethers.parseEther("10000"),  // 中额
        ethers.parseEther("1000000") // 大额
      ];
      
      for (let i = 0; i < testAmounts.length; i++) {
        const amount = testAmounts[i];
        await tokenA.transfer(await users[i].getAddress(), amount + ethers.parseEther("1000"));
        
        const initialContractBalance = await tokenA.balanceOf(await tokenA.getAddress());
        await tokenA.connect(users[i]).transfer(pairAddress, amount);
        const finalContractBalance = await tokenA.balanceOf(await tokenA.getAddress());
        
        const actualTax = finalContractBalance - initialContractBalance;
        const expectedTax = amount * BigInt(3) / BigInt(100);
        const taxRate = Number(actualTax * BigInt(10000) / amount) / 100;
        
        console.log(`交易额: ${ethers.formatEther(amount)}, 实际税率: ${taxRate}%`);
        
        // 验证税率稳定性
        expect(actualTax).to.be.closeTo(expectedTax, amount / BigInt(1000));
      }
      
      console.log("✓ 税收激励边界效应测试通过");
    });
    
    it("应该测试分红激励的公平性", async function () {
      console.log("=== 测试分红激励公平性 ===");
      
      // 创建不同持有数量的用户
      const singleNFTUser = users[0];
      const multiNFTUser = users[1];
      const noNFTUser = users[2];
      
      // 分配NFT
      await minerNFT.mint(await singleNFTUser.getAddress()); // 1个NFT
      for (let i = 0; i < 5; i++) {
        await minerNFT.mint(await multiNFTUser.getAddress()); // 5个NFT
      }
      // noNFTUser没有NFT
      
      // 产生分红
      const pairAddress = await createTradingPair();
      await tokenA.transfer(await users[10].getAddress(), ethers.parseEther("60000"));
      await tokenA.connect(users[10]).transfer(pairAddress, ethers.parseEther("30000"));
      
      // 计算分红
      const singleUserDividend = await tokenA.calculateDividend(await singleNFTUser.getAddress());
      const multiUserDividend = await tokenA.calculateDividend(await multiNFTUser.getAddress());
      const noNFTUserDividend = await tokenA.calculateDividend(await noNFTUser.getAddress());
      
      console.log("单NFT用户分红:", ethers.formatEther(singleUserDividend));
      console.log("多NFT用户分红:", ethers.formatEther(multiUserDividend));
      console.log("无NFT用户分红:", ethers.formatEther(noNFTUserDividend));
      
      // 验证分红公平性
      expect(noNFTUserDividend).to.equal(0);
      expect(multiUserDividend).to.be.closeTo(singleUserDividend * BigInt(5), ethers.parseEther("1"));
      
      console.log("✓ 分红激励公平性验证通过");
    });
    
    it("应该测试长期持有vs短期交易的激励差异", async function () {
      console.log("=== 测试长期vs短期激励差异 ===");
      
      const longTermUser = users[0];
      const shortTermUser = users[1];
      const pairAddress = await createTradingPair();
      
      // 长期用户：持有NFT，少量交易
      await minerNFT.mint(await longTermUser.getAddress());
      await tokenA.transfer(await longTermUser.getAddress(), ethers.parseEther("10000"));
      
      // 短期用户：无NFT，频繁交易
      await tokenA.transfer(await shortTermUser.getAddress(), ethers.parseEther("50000"));
      
      // 模拟一段时间的交易
      const initialLongTermBalance = await tokenA.balanceOf(await longTermUser.getAddress());
      const initialShortTermBalance = await tokenA.balanceOf(await shortTermUser.getAddress());
      
      // 短期用户进行多次交易（产生税收损失）
      for (let i = 0; i < 5; i++) {
        await tokenA.connect(shortTermUser).transfer(pairAddress, ethers.parseEther("2000"));
      }
      
      // 长期用户进行少量交易
      await tokenA.connect(longTermUser).transfer(pairAddress, ethers.parseEther("1000"));
      
      // 长期用户获得分红
      const longTermDividend = await tokenA.calculateDividend(await longTermUser.getAddress());
      if (longTermDividend > 0) {
        await tokenA.connect(longTermUser).claimDividend();
      }
      
      // 计算最终余额
      const finalLongTermBalance = await tokenA.balanceOf(await longTermUser.getAddress());
      const finalShortTermBalance = await tokenA.balanceOf(await shortTermUser.getAddress());
      
      const longTermResult = finalLongTermBalance - initialLongTermBalance;
      const shortTermResult = finalShortTermBalance - initialShortTermBalance;
      
      console.log("长期用户余额变化:", ethers.formatEther(longTermResult));
      console.log("短期用户余额变化:", ethers.formatEther(shortTermResult));
      
      // 长期用户应该有正收益（分红 > 税收），短期用户应该有负收益
      expect(longTermResult).to.be.gt(shortTermResult);
      console.log("✓ 长期持有激励机制有效");
    });
  });

  // 辅助函数
  async function setupBasicEnvironment() {
    // 基础环境设置
    console.log("设置基础测试环境...");
  }
  
  async function createTradingPair() {
    // 创建流动性
    const liquidityTokenA = ethers.parseEther("50000");
    const liquidityWBNB = ethers.parseEther("50");
    
    await mockWBNB.deposit({ value: liquidityWBNB });
    await tokenA.approve(await mockRouter.getAddress(), liquidityTokenA);
    await mockWBNB.approve(await mockRouter.getAddress(), liquidityWBNB);
    
    await mockRouter.addLiquidity(
      await tokenA.getAddress(),
      await mockWBNB.getAddress(),
      liquidityTokenA,
      liquidityWBNB,
      0, 0,
      await owner.getAddress(),
      Math.floor(Date.now() / 1000) + 3600
    );
    
    const pairAddress = await mockFactory.getPair(await tokenA.getAddress(), await mockWBNB.getAddress());
    await tokenA.setPair(pairAddress, true);
    
    return pairAddress;
  }
});