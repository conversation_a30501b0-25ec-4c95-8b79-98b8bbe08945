const { expect } = require("chai");
const { ethers } = require("hardhat");

describe("分红机制深度调试", function () {
  let tokenA, minerNFT;
  let mockWBNB, mockFactory, mockRouter;
  let owner, marketingWallet, user1, user2;
  
  beforeEach(async function () {
    [owner, marketingWallet, user1, user2] = await ethers.getSigners();
    
    // 部署模拟环境
    const MockWBNB = await ethers.getContractFactory("MockWBNB");
    mockWBNB = await MockWBNB.deploy();
    await mockWBNB.waitForDeployment();
    
    const MockPancakeFactory = await ethers.getContractFactory("MockPancakeFactory");
    mockFactory = await MockPancakeFactory.deploy();
    await mockFactory.waitForDeployment();
    
    const MockPancakeRouter = await ethers.getContractFactory("MockPancakeRouter");
    mockRouter = await MockPancakeRouter.deploy(
      await mockFactory.getAddress(),
      await mockWBNB.getAddress()
    );
    await mockRouter.waitForDeployment();
    
    // 部署TokenA
    const TokenA = await ethers.getContractFactory("TokenA");
    tokenA = await TokenA.deploy(
      await marketingWallet.getAddress(),
      await mockRouter.getAddress(),
      await mockWBNB.getAddress()
    );
    await tokenA.waitForDeployment();
    
    // 部署NFT
    const MinerNFT = await ethers.getContractFactory("MinerNFT");
    minerNFT = await MinerNFT.deploy("MinerNFT", "MNFT", await tokenA.getAddress());
    await minerNFT.waitForDeployment();
    
    // 配置关系
    await tokenA.setNftContract(await minerNFT.getAddress());
    await tokenA.transfer(await minerNFT.getAddress(), ethers.parseEther("21000000"));
    
    console.log("=== 分红调试环境初始化完成 ===");
  });

  it("应该详细调试分红机制的每个步骤", async function () {
    console.log("=== 分红机制详细调试 ===");
    
    // 1. 检查初始状态
    console.log("\n--- 1. 初始状态检查 ---");
    const nftContractAddress = await tokenA.nftContract();
    console.log("NFT合约地址:", nftContractAddress);
    console.log("NFT合约是否设置:", nftContractAddress !== ethers.ZeroAddress);
    
    const initialContractBalance = await tokenA.balanceOf(await tokenA.getAddress());
    console.log("初始合约余额:", ethers.formatEther(initialContractBalance));
    
    const contractBalanceResult = await tokenA.contractBalance();
    const balance = contractBalanceResult[0];
    const totalDivs = contractBalanceResult[1]; 
    const cumulativePerShare = contractBalanceResult[2];
    console.log("合约状态 - 余额:", ethers.formatEther(balance), "总分红:", ethers.formatEther(totalDivs), "累积分红率:", cumulativePerShare.toString());
    
    // 2. 铸造NFT
    console.log("\n--- 2. 铸造NFT ---");
    await minerNFT.mint(await user1.getAddress());
    const nftBalance = await minerNFT.balanceOf(await user1.getAddress());
    console.log("用户NFT数量:", nftBalance.toString());
    
    const totalNftSupply = await minerNFT.totalSupply();
    console.log("NFT总供应量:", totalNftSupply.toString());
    
    // 3. 创建交易对
    console.log("\n--- 3. 创建交易对 ---");
    const liquidityTokenA = ethers.parseEther("50000");
    const liquidityWBNB = ethers.parseEther("50");
    
    await mockWBNB.deposit({ value: liquidityWBNB });
    await tokenA.approve(await mockRouter.getAddress(), liquidityTokenA);
    await mockWBNB.approve(await mockRouter.getAddress(), liquidityWBNB);
    
    await mockRouter.addLiquidity(
      await tokenA.getAddress(),
      await mockWBNB.getAddress(),
      liquidityTokenA,
      liquidityWBNB,
      0, 0,
      await owner.getAddress(),
      Math.floor(Date.now() / 1000) + 3600
    );
    
    const pairAddress = await mockFactory.getPair(await tokenA.getAddress(), await mockWBNB.getAddress());
    await tokenA.setPair(pairAddress, true);
    
    console.log("交易对地址:", pairAddress);
    const isPair = await tokenA.isPair(pairAddress);
    const isTaxExempt = await tokenA.taxExempt(pairAddress);
    console.log("交易对标记:", isPair, "免税状态:", isTaxExempt);
    
    // 4. 执行转账并详细监控税收分配
    console.log("\n--- 4. 转账税收分析 ---");
    const transferAmount = ethers.parseEther("10000");
    await tokenA.transfer(await user2.getAddress(), transferAmount);
    
    // 记录转账前状态
    const beforeMarketingBalance = await tokenA.balanceOf(await marketingWallet.getAddress());
    const beforeContractBalance = await tokenA.balanceOf(await tokenA.getAddress());
    const beforeTotalSupply = await tokenA.totalSupply();
    const beforeDividendState = await tokenA.contractBalance();
    
    console.log("转账前状态:");
    console.log("  营销钱包余额:", ethers.formatEther(beforeMarketingBalance));
    console.log("  合约余额:", ethers.formatEther(beforeContractBalance));
    console.log("  总供应量:", ethers.formatEther(beforeTotalSupply));
    console.log("  分红状态:", ethers.formatEther(beforeDividendState[1]), "累积率:", beforeDividendState[2].toString());
    
    // 执行转账
    console.log("\n执行转账:", ethers.formatEther(transferAmount), "ETH");
    const tx = await tokenA.connect(user2).transfer(pairAddress, transferAmount);
    const receipt = await tx.wait();
    
    // 分析事件
    console.log("\n交易事件分析:");
    for (const log of receipt.logs) {
      try {
        const parsed = tokenA.interface.parseLog(log);
        if (parsed.name === "TaxCollected") {
          console.log("TaxCollected事件:", {
            marketing: ethers.formatEther(parsed.args.marketingAmount),
            burn: ethers.formatEther(parsed.args.burnAmount), 
            dividend: ethers.formatEther(parsed.args.dividendAmount)
          });
        } else if (parsed.name === "DividendDistributed") {
          console.log("DividendDistributed事件:", ethers.formatEther(parsed.args.amount));
        }
      } catch (e) {
        // 忽略其他合约的事件
      }
    }
    
    // 记录转账后状态
    const afterMarketingBalance = await tokenA.balanceOf(await marketingWallet.getAddress());
    const afterContractBalance = await tokenA.balanceOf(await tokenA.getAddress());
    const afterTotalSupply = await tokenA.totalSupply();
    const afterDividendState = await tokenA.contractBalance();
    
    console.log("\n转账后状态:");
    console.log("  营销钱包余额:", ethers.formatEther(afterMarketingBalance));
    console.log("  合约余额:", ethers.formatEther(afterContractBalance));
    console.log("  总供应量:", ethers.formatEther(afterTotalSupply));
    console.log("  分红状态:", ethers.formatEther(afterDividendState[1]), "累积率:", afterDividendState[2].toString());
    
    // 计算变化
    const marketingIncrease = afterMarketingBalance - beforeMarketingBalance;
    const contractIncrease = afterContractBalance - beforeContractBalance;
    const supplyDecrease = beforeTotalSupply - afterTotalSupply;
    const dividendIncrease = afterDividendState[1] - beforeDividendState[1];
    
    console.log("\n变化分析:");
    console.log("  营销增加:", ethers.formatEther(marketingIncrease));
    console.log("  合约增加:", ethers.formatEther(contractIncrease));
    console.log("  供应减少:", ethers.formatEther(supplyDecrease));
    console.log("  分红增加:", ethers.formatEther(dividendIncrease));
    
    // 5. 分红计算分析
    console.log("\n--- 5. 分红计算分析 ---");
    
    // 检查累积分红率
    const cumulativeDividendPerShare = await tokenA.cumulativeDividendPerShare();
    console.log("累积每股分红:", cumulativeDividendPerShare.toString());
    
    // 检查NFT分红信息
    const nftDividendInfo = await tokenA.nftDividendInfo(1);
    console.log("NFT #1 分红信息:", {
      lastCumulative: nftDividendInfo.lastCumulativeDividend.toString(),
      claimed: ethers.formatEther(nftDividendInfo.claimedAmount)
    });
    
    // 计算用户分红
    try {
      const userDividend = await tokenA.calculateDividend(await user1.getAddress());
      console.log("用户可领取分红:", ethers.formatEther(userDividend));
      
      if (userDividend > 0) {
        console.log("尝试领取分红...");
        const claimTx = await tokenA.connect(user1).claimDividend();
        const claimReceipt = await claimTx.wait();
        console.log("分红领取Gas:", claimReceipt.gasUsed.toString());
        
        const finalUserBalance = await tokenA.balanceOf(await user1.getAddress());
        console.log("用户最终余额:", ethers.formatEther(finalUserBalance));
      } else {
        console.log("⚠️ 分红为零，分析原因...");
        
        // 深度分析分红为零的原因
        console.log("\n分红为零原因分析:");
        console.log("1. NFT总数:", await minerNFT.totalSupply());
        console.log("2. 分红池余额:", ethers.formatEther(afterContractBalance));
        console.log("3. 累积分红率:", cumulativeDividendPerShare.toString());
        console.log("4. NFT合约地址匹配:", nftContractAddress === await minerNFT.getAddress());
        
        // 尝试调用getUserNFTs
        try {
          const userNFTs = await minerNFT.getUserNFTs(await user1.getAddress());
          console.log("5. 用户NFT列表:", userNFTs.map(id => id.toString()));
        } catch (e) {
          console.log("5. getUserNFTs调用失败:", e.message);
        }
      }
    } catch (error) {
      console.log("分红计算失败:", error.message);
    }
    
    // 6. 手动计算理论分红
    console.log("\n--- 6. 理论分红计算 ---");
    const theoreticalTax = transferAmount * BigInt(3) / BigInt(100);
    const theoreticalDividend = theoreticalTax * BigInt(30) / BigInt(100);
    const nftCount = await minerNFT.totalSupply();
    const theoreticalPerNFT = nftCount > 0 ? theoreticalDividend / nftCount : 0n;
    
    console.log("理论税收:", ethers.formatEther(theoreticalTax));
    console.log("理论分红:", ethers.formatEther(theoreticalDividend));
    console.log("理论每NFT分红:", ethers.formatEther(theoreticalPerNFT));
    
    console.log("\n=== 分红机制调试完成 ===");
  });
  
  it("应该测试手动分红分发", async function () {
    console.log("=== 手动分红分发测试 ===");
    
    // 设置环境
    await minerNFT.mint(await user1.getAddress());
    const pairAddress = await createTradingPair();
    
    // 禁用自动分红，以便详细观察过程
    await tokenA.setAutoDividendConfig(500000, 10, ethers.parseEther("100"), false);
    console.log("已禁用自动分红");
    
    // 产生税收
    const transferAmount = ethers.parseEther("10000");
    await tokenA.transfer(await user2.getAddress(), transferAmount);
    await tokenA.connect(user2).transfer(pairAddress, transferAmount);
    
    // 检查合约状态
    const contractState = await tokenA.contractBalance();
    console.log("合约状态:", {
      balance: ethers.formatEther(contractState[0]),
      totalDividends: ethers.formatEther(contractState[1]),
      cumulativePerShare: contractState[2].toString()
    });
    
    // 手动触发自动分红处理
    console.log("手动触发分红处理...");
    try {
      const processTx = await tokenA.processAutoDividend();
      const processReceipt = await processTx.wait();
      
      console.log("自动分红处理Gas:", processReceipt.gasUsed.toString());
      
      // 检查事件
      for (const log of processReceipt.logs) {
        try {
          const parsed = tokenA.interface.parseLog(log);
          if (parsed.name === "AutoDividendProcessed") {
            console.log("AutoDividendProcessed事件:", {
              startIndex: parsed.args.startIndex.toString(),
              endIndex: parsed.args.endIndex.toString(),
              totalAmount: ethers.formatEther(parsed.args.totalAmount)
            });
          } else if (parsed.name === "DividendClaimed") {
            console.log("DividendClaimed事件:", {
              user: parsed.args.user,
              amount: ethers.formatEther(parsed.args.amount)
            });
          }
        } catch (e) {
          // 忽略其他事件
        }
      }
      
      // 检查用户余额变化
      const userBalance = await tokenA.balanceOf(await user1.getAddress());
      console.log("用户最终余额:", ethers.formatEther(userBalance));
      
    } catch (error) {
      console.log("手动分红处理失败:", error.message);
    }
  });
  
  // 辅助函数
  async function createTradingPair() {
    const liquidityTokenA = ethers.parseEther("50000");
    const liquidityWBNB = ethers.parseEther("50");
    
    await mockWBNB.deposit({ value: liquidityWBNB });
    await tokenA.approve(await mockRouter.getAddress(), liquidityTokenA);
    await mockWBNB.approve(await mockRouter.getAddress(), liquidityWBNB);
    
    await mockRouter.addLiquidity(
      await tokenA.getAddress(),
      await mockWBNB.getAddress(),
      liquidityTokenA,
      liquidityWBNB,
      0, 0,
      await owner.getAddress(),
      Math.floor(Date.now() / 1000) + 3600
    );
    
    const pairAddress = await mockFactory.getPair(await tokenA.getAddress(), await mockWBNB.getAddress());
    await tokenA.setPair(pairAddress, true);
    
    return pairAddress;
  }
});