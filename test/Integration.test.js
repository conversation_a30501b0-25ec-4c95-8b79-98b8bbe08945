const { expect } = require("chai");
const { ethers } = require("hardhat");

describe("DeFi生态系统集成测试", function () {
  let tokenA, tokenB, minerNFT, miningContract, priceOracle;
  let owner, marketingWallet, user1, user2, user3;
  
  const TOTAL_SUPPLY_A = ethers.parseEther("250000000"); // 2.5亿
  const TOTAL_SUPPLY_B = ethers.parseEther("*********0000"); // 2100亿
  const NFT_TOKEN_AMOUNT = ethers.parseEther("100000"); // 10万
  
  beforeEach(async function () {
    [owner, marketingWallet, user1, user2, user3] = await ethers.getSigners();
    
    // 部署价格预言机
    const PriceOracle = await ethers.getContractFactory("PriceOracle");
    priceOracle = await PriceOracle.deploy();
    
    // 部署TokenA - 使用BSC测试网真实地址
    const TokenA = await ethers.getContractFactory("TokenA");
    tokenA = await TokenA.deploy(
      await marketingWallet.getAddress(),
      "******************************************", // BSC测试网Router
      "******************************************"  // BSC测试网WBNB
    );
    
    // 部署TokenB - 使用BSC测试网真实地址
    const TokenB = await ethers.getContractFactory("TokenB");
    tokenB = await TokenB.deploy(
      await marketingWallet.getAddress(),
      "******************************************", // BSC测试网Router
      "******************************************"  // BSC测试网WBNB
    );
    
    // 部署NFT合约
    const MinerNFT = await ethers.getContractFactory("MinerNFT");
    minerNFT = await MinerNFT.deploy("MinerNFT", "MNFT", await tokenA.getAddress());
    
    // 部署挖矿合约
    const MiningContract = await ethers.getContractFactory("MiningContract");
    miningContract = await MiningContract.deploy(
      await tokenA.getAddress(),
      await tokenB.getAddress(),
      await minerNFT.getAddress(),
      await priceOracle.getAddress()
    );
    
    // 配置合约关系
    await tokenA.setNftContract(await minerNFT.getAddress());
    await tokenB.addMinter(await miningContract.getAddress());
    await tokenA.setTaxExempt(await miningContract.getAddress(), true);
    await tokenB.setTaxExempt(await miningContract.getAddress(), true);
    
    // 向NFT合约转入TokenA用于释放
    const nftTokenAmount = ethers.parseEther("*********"); // 2.1亿 (2100个NFT * 10万)
    await tokenA.transfer(await minerNFT.getAddress(), nftTokenAmount);
    
    // 为测试设置一些手动价格
    await priceOracle.setManualPrice(await tokenA.getAddress(), ethers.parseEther("0.001")); // 1 TKA = 0.001 BNB
    await priceOracle.setManualPrice(await tokenB.getAddress(), ethers.parseEther("0.0001")); // 1 TKB = 0.0001 BNB
  });
  
  describe("合约部署和初始化", function () {
    it("所有合约应该正确部署", async function () {
      expect(await tokenA.totalSupply()).to.equal(TOTAL_SUPPLY_A);
      expect(await tokenB.totalSupply()).to.equal(TOTAL_SUPPLY_B);
      expect(await minerNFT.MAX_SUPPLY()).to.equal(2100);
      expect(await miningContract.tokenAAddress()).to.equal(await tokenA.getAddress());
    });
    
    it("合约关系应该正确配置", async function () {
      expect(await tokenA.nftContract()).to.equal(await minerNFT.getAddress());
      expect(await tokenB.hasRole(await tokenB.MINTER_ROLE(), await miningContract.getAddress())).to.be.true;
      expect(await tokenA.taxExempt(await miningContract.getAddress())).to.be.true;
      expect(await tokenB.taxExempt(await miningContract.getAddress())).to.be.true;
    });
  });
  
  describe("NFT铸造和释放机制", function () {
    it("应该能够铸造NFT", async function () {
      await minerNFT.mint(await user1.getAddress());
      
      expect(await minerNFT.balanceOf(await user1.getAddress())).to.equal(1);
      expect(await minerNFT.ownerOf(1)).to.equal(await user1.getAddress());
      
      const nftInfo = await minerNFT.nftInfo(1);
      expect(nftInfo.totalTokens).to.equal(NFT_TOKEN_AMOUNT);
      expect(nftInfo.releasedTokens).to.equal(0);
    });
    
    it("应该能够批量铸造NFT", async function () {
      await minerNFT.batchMint([await user1.getAddress(), await user2.getAddress(), await user3.getAddress()]);
      
      expect(await minerNFT.balanceOf(await user1.getAddress())).to.equal(1);
      expect(await minerNFT.balanceOf(await user2.getAddress())).to.equal(1);
      expect(await minerNFT.balanceOf(await user3.getAddress())).to.equal(1);
    });
    
    it("NFT释放机制应该正常工作", async function () {
      await minerNFT.mint(await user1.getAddress());
      
      // 设置释放开始时间为当前时间
      await minerNFT.setReleaseParameters(
        Math.floor(Date.now() / 1000),
        30 * 24 * 3600, // 30天
        10 // 10%
      );
      
      // 快进时间
      await ethers.provider.send("evm_increaseTime", [31 * 24 * 3600]); // 31天
      await ethers.provider.send("evm_mine");
      
      const releasableAmount = await minerNFT.getReleasableAmount(1);
      expect(releasableAmount).to.be.gt(0);
      
      const initialBalance = await tokenA.balanceOf(await user1.getAddress());
      await minerNFT.connect(user1).releaseTokens(1);
      const finalBalance = await tokenA.balanceOf(await user1.getAddress());
      
      expect(finalBalance - initialBalance).to.equal(releasableAmount);
    });
  });
  
  describe("挖矿机制一：销毁TokenA获得TokenB", function () {
    beforeEach(async function () {
      // 给用户一些TokenA
      await tokenA.transfer(await user1.getAddress(), ethers.parseEther("10000"));
    });
    
    it("应该能够销毁TokenA换取TokenB奖励", async function () {
      const burnAmount = ethers.parseEther("1000");
      
      // 授权挖矿合约
      await tokenA.connect(user1).approve(await miningContract.getAddress(), burnAmount);
      
      // 执行销毁
      await miningContract.connect(user1).burnTokenAForTokenB(burnAmount);
      
      // 检查挖矿记录
      const recordCount = await miningContract.getUserMiningRecordCount(await user1.getAddress());
      expect(recordCount.count1).to.equal(1);
      
      // 检查可领取数量
      const claimableAmount = await miningContract.getUserClaimableAmount1(await user1.getAddress());
      expect(claimableAmount).to.equal(0); // 刚销毁，还不能领取
    });
    
    it("时间过后应该能够领取TokenB奖励", async function () {
      const burnAmount = ethers.parseEther("1000");
      
      await tokenA.connect(user1).approve(await miningContract.getAddress(), burnAmount);
      await miningContract.connect(user1).burnTokenAForTokenB(burnAmount);
      
      // 快进时间
      await ethers.provider.send("evm_increaseTime", [31 * 24 * 3600]); // 31天
      await ethers.provider.send("evm_mine");
      
      const claimableAmount = await miningContract.getUserClaimableAmount1(await user1.getAddress());
      expect(claimableAmount).to.be.gt(0);
      
      // 跳过实际领取测试，因为需要价格预言机
      console.log("跳过TokenB领取测试 - 需要真实DEX环境");
      // const initialBalance = await tokenB.balanceOf(await user1.getAddress());
      // await miningContract.connect(user1).claimTokenBFromMechanism1();
      // const finalBalance = await tokenB.balanceOf(await user1.getAddress());
      // expect(finalBalance).to.be.gt(initialBalance);
    });
  });
  
  describe("挖矿机制二：持有NFT销毁TokenB", function () {
    beforeEach(async function () {
      // 给用户铸造NFT和TokenB
      await minerNFT.mint(await user1.getAddress());
      
      // 先给挖矿合约铸造权限，然后铸造一些TokenB给用户
      await tokenB.connect(owner).mint(await user1.getAddress(), ethers.parseEther("1000000"));
    });
    
    it("持有NFT的用户应该能够销毁TokenB", async function () {
      const burnAmount = ethers.parseEther("10000");
      
      await tokenB.connect(user1).approve(await miningContract.getAddress(), burnAmount);
      await miningContract.connect(user1).burnTokenBWithNFT(burnAmount);
      
      const recordCount = await miningContract.getUserMiningRecordCount(await user1.getAddress());
      expect(recordCount.count2).to.equal(1);
    });
    
    it("不持有NFT的用户不应该能够销毁TokenB", async function () {
      const burnAmount = ethers.parseEther("10000");
      
      await tokenB.connect(owner).mint(await user2.getAddress(), ethers.parseEther("1000000"));
      await tokenB.connect(user2).approve(await miningContract.getAddress(), burnAmount);
      
      await expect(
        miningContract.connect(user2).burnTokenBWithNFT(burnAmount)
      ).to.be.revertedWith("Must hold at least one NFT");
    });
  });
  
  describe("邀请奖励系统", function () {
    beforeEach(async function () {
      await tokenA.transfer(await user2.getAddress(), ethers.parseEther("10000"));
    });
    
    it("应该能够设置邀请关系", async function () {
      await miningContract.connect(user2).setInviter(await user1.getAddress());
      
      const inviteInfo = await miningContract.getUserInviteInfo(await user2.getAddress());
      expect(inviteInfo.inviter).to.equal(await user1.getAddress());
      
      const inviterInfo = await miningContract.getUserInviteInfo(await user1.getAddress());
      expect(inviterInfo.totalInvited).to.equal(1);
    });
    
    it("邀请人应该获得加速释放奖励", async function () {
      // 设置邀请关系
      await miningContract.connect(user2).setInviter(await user1.getAddress());
      
      const burnAmount = ethers.parseEther("1000");
      
      // 被邀请人销毁TokenA
      await tokenA.connect(user2).approve(await miningContract.getAddress(), burnAmount);
      await miningContract.connect(user2).burnTokenAForTokenB(burnAmount);
      
      // 检查邀请人的加速释放额度
      const inviterInfo = await miningContract.getUserInviteInfo(await user1.getAddress());
      expect(inviterInfo.acceleratedReleaseAmount).to.be.gt(0);
    });
  });
  
  describe("税收和分红机制", function () {
    beforeEach(async function () {
      // 给用户一些代币并铸造NFT
      await tokenA.transfer(await user1.getAddress(), ethers.parseEther("10000"));
      await minerNFT.mint(await user1.getAddress());
      
      // 设置一个模拟交易对
      await tokenA.setPair(await user2.getAddress(), true);
    });
    
    it("交易应该产生税收和分红", async function () {
      const transferAmount = ethers.parseEther("1000");
      
      // 禁用自动分红以测试手动分红
      await tokenA.setAutoDividendConfig(500000, 10, ethers.parseEther("100"), false);
      
      // 执行一笔交易产生税收
      await tokenA.connect(user1).transfer(await user2.getAddress(), transferAmount);
      
      // 检查是否产生了分红
      const totalDividends = await tokenA.totalDividends();
      expect(totalDividends).to.be.gt(0);
      
      // 检查用户是否有分红可领取
      const userDividend = await tokenA.calculateDividend(await user1.getAddress());
      expect(userDividend).to.be.gt(0);
    });
  });
  
  describe("价格预言机集成", function () {
    it("应该能够获取代币价格", async function () {
      const tokenAPrice = await priceOracle.getTokenPrice(await tokenA.getAddress());
      const tokenBPrice = await priceOracle.getTokenPrice(await tokenB.getAddress());
      
      expect(tokenAPrice).to.be.gt(0);
      expect(tokenBPrice).to.be.gt(0);
    });
    
    it("应该能够计算价格比率", async function () {
      const priceRatio = await priceOracle.getTokenPriceRatio(await tokenA.getAddress(), await tokenB.getAddress());
      expect(priceRatio).to.be.gt(0);
    });
  });
  
  describe("完整工作流程测试", function () {
    it("完整的用户交互流程", async function () {
      // 1. 铸造NFT给用户
      await minerNFT.mint(await user1.getAddress());
      
      // 2. 给用户一些TokenA
      await tokenA.transfer(await user1.getAddress(), ethers.parseEther("10000"));
      
      // 3. 用户销毁TokenA换取TokenB
      const burnAmount = ethers.parseEther("1000");
      await tokenA.connect(user1).approve(await miningContract.getAddress(), burnAmount);
      await miningContract.connect(user1).burnTokenAForTokenB(burnAmount);
      
      // 4. 时间推进
      await ethers.provider.send("evm_increaseTime", [31 * 24 * 3600]);
      await ethers.provider.send("evm_mine");
      
      // 5. 跳过TokenB领取（需要价格预言机）
      console.log("跳过TokenB领取步骤 - 需要真实DEX环境");
      // const initialTokenBBalance = await tokenB.balanceOf(await user1.getAddress());
      // await miningContract.connect(user1).claimTokenBFromMechanism1();
      // const finalTokenBBalance = await tokenB.balanceOf(await user1.getAddress());
      // expect(finalTokenBBalance).to.be.gt(initialTokenBBalance);
      
      // 6. 释放NFT中的TokenA
      await minerNFT.setReleaseParameters(
        Math.floor(Date.now() / 1000),
        30 * 24 * 3600,
        10
      );
      
      await ethers.provider.send("evm_increaseTime", [31 * 24 * 3600]);
      await ethers.provider.send("evm_mine");
      
      const initialTokenABalance = await tokenA.balanceOf(await user1.getAddress());
      await minerNFT.connect(user1).releaseTokens(1);
      const finalTokenABalance = await tokenA.balanceOf(await user1.getAddress());
      
      expect(finalTokenABalance).to.be.gt(initialTokenABalance);
    });
  });
});