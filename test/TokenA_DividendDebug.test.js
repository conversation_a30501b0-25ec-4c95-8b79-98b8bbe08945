const { expect } = require("chai");
const { ethers } = require("hardhat");

describe("TokenA分红机制问题调试", function () {
  let tokenA, minerNFT, mockWBNB, mockRouter;
  let owner, user1, user2, user3, marketingWallet;
  
  beforeEach(async function () {
    const signers = await ethers.getSigners();
    [owner, user1, user2, user3, marketingWallet] = signers.slice(0, 5);
    
    console.log(`=== TokenA分红机制问题调试 ===`);
    
    // 部署Mock合约
    const MockWBNB = await ethers.getContractFactory("MockWBNB");
    mockWBNB = await MockWBNB.deploy();
    
    const MockPancakeFactory = await ethers.getContractFactory("MockPancakeFactory");
    const mockFactory = await MockPancakeFactory.deploy();
    
    const MockPancakeRouter = await ethers.getContractFactory("MockPancakeRouter");
    mockRouter = await MockPancakeRouter.deploy(
      await mockFactory.getAddress(),
      await mockWBNB.getAddress()
    );
    
    // 部署TokenA
    const TokenA = await ethers.getContractFactory("TokenA");
    tokenA = await TokenA.deploy(
      await marketingWallet.getAddress(),
      await mockRouter.getAddress(),
      await mockWBNB.getAddress()
    );
    
    // 部署MinerNFT
    const MinerNFT = await ethers.getContractFactory("MinerNFT");
    minerNFT = await MinerNFT.deploy("MinerNFT", "MNFT", await tokenA.getAddress());
    
    // 设置NFT合约地址
    await tokenA.setNftContract(await minerNFT.getAddress());
    
    console.log(`TokenA合约: ${await tokenA.getAddress()}`);
    console.log(`MinerNFT合约: ${await minerNFT.getAddress()}`);
  });
  
  it("应该详细调试分红累积过程", async function () {
    console.log("=== 详细调试分红累积过程 ===");
    
    // 给用户铸造NFT
    await minerNFT.batchMintCount(await user1.getAddress(), 2);
    console.log("user1拥有2个NFT");
    
    // 检查初始状态
    console.log("\\n=== 初始状态 ===");
    const initialContractBalance = await tokenA.balanceOf(await tokenA.getAddress());
    const initialMarketingBalance = await tokenA.balanceOf(await marketingWallet.getAddress());
    const initialTotalSupply = await tokenA.totalSupply();
    const initialContractInfo = await tokenA.contractBalance();
    
    console.log(`合约初始余额: ${ethers.formatEther(initialContractBalance)} TokenA`);
    console.log(`营销钱包初始余额: ${ethers.formatEther(initialMarketingBalance)} TokenA`);
    console.log(`初始总供应量: ${ethers.formatEther(initialTotalSupply)} TokenA`);
    console.log(`初始累计分红: ${ethers.formatEther(initialContractInfo.totalDivs)} TokenA`);
    console.log(`初始每股分红: ${initialContractInfo.cumulativePerShare}`);
    
    // 给用户一些TokenA用于交易
    const userTokens = ethers.parseEther("10000");
    await tokenA.transfer(await user1.getAddress(), userTokens);
    await tokenA.transfer(await user3.getAddress(), userTokens);
    
    // 设置user3为交易对
    await tokenA.setPair(await user3.getAddress(), true);
    console.log("\\n设置user3为交易对");
    
    // 准备执行买入交易
    const buyAmount = ethers.parseEther("1000");
    console.log(`\\n=== 执行买入交易 ===`);
    console.log(`买入数量: ${ethers.formatEther(buyAmount)} TokenA`);
    
    // 记录交易前状态
    const beforeContractBalance = await tokenA.balanceOf(await tokenA.getAddress());
    const beforeMarketingBalance = await tokenA.balanceOf(await marketingWallet.getAddress());
    const beforeTotalSupply = await tokenA.totalSupply();
    const beforeUser1Balance = await tokenA.balanceOf(await user1.getAddress());
    
    console.log(`交易前合约余额: ${ethers.formatEther(beforeContractBalance)} TokenA`);
    console.log(`交易前营销余额: ${ethers.formatEther(beforeMarketingBalance)} TokenA`);
    console.log(`交易前总供应量: ${ethers.formatEther(beforeTotalSupply)} TokenA`);
    console.log(`交易前user1余额: ${ethers.formatEther(beforeUser1Balance)} TokenA`);
    
    // 执行买入交易 (user3 -> user1, 从交易对买入)
    console.log("\\n执行交易: user3(交易对) -> user1");
    const tx = await tokenA.connect(user3).transfer(await user1.getAddress(), buyAmount);
    const receipt = await tx.wait();
    
    // 记录交易后状态
    const afterContractBalance = await tokenA.balanceOf(await tokenA.getAddress());
    const afterMarketingBalance = await tokenA.balanceOf(await marketingWallet.getAddress());
    const afterTotalSupply = await tokenA.totalSupply();
    const afterUser1Balance = await tokenA.balanceOf(await user1.getAddress());
    const afterContractInfo = await tokenA.contractBalance();
    
    console.log(`\\n=== 交易后状态 ===`);
    console.log(`交易后合约余额: ${ethers.formatEther(afterContractBalance)} TokenA`);
    console.log(`交易后营销余额: ${ethers.formatEther(afterMarketingBalance)} TokenA`);
    console.log(`交易后总供应量: ${ethers.formatEther(afterTotalSupply)} TokenA`);
    console.log(`交易后user1余额: ${ethers.formatEther(afterUser1Balance)} TokenA`);
    console.log(`交易后累计分红: ${ethers.formatEther(afterContractInfo.totalDivs)} TokenA`);
    console.log(`交易后每股分红: ${afterContractInfo.cumulativePerShare}`);
    
    // 计算实际变化
    const contractBalanceChange = afterContractBalance - beforeContractBalance;
    const marketingBalanceChange = afterMarketingBalance - beforeMarketingBalance;
    const totalSupplyChange = beforeTotalSupply - afterTotalSupply; // 注意：是减少的
    const user1BalanceChange = afterUser1Balance - beforeUser1Balance;
    
    console.log(`\\n=== 变化分析 ===`);
    console.log(`合约余额变化: ${ethers.formatEther(contractBalanceChange)} TokenA`);
    console.log(`营销余额变化: ${ethers.formatEther(marketingBalanceChange)} TokenA`);
    console.log(`总供应量减少: ${ethers.formatEther(totalSupplyChange)} TokenA (销毁)`);
    console.log(`user1余额变化: ${ethers.formatEther(user1BalanceChange)} TokenA`);
    
    // 计算期望的税收分配
    const totalTax = (buyAmount * BigInt(3)) / BigInt(100); // 3%税收
    const expectedMarketing = (totalTax * BigInt(20)) / BigInt(100); // 20%营销
    const expectedBurn = (totalTax * BigInt(50)) / BigInt(100); // 50%销毁
    const expectedDividend = totalTax - expectedMarketing - expectedBurn; // 30%分红
    const expectedUserReceive = buyAmount - totalTax; // 用户实际收到
    
    console.log(`\\n=== 期望分配 ===`);
    console.log(`总税收: ${ethers.formatEther(totalTax)} TokenA`);
    console.log(`期望营销: ${ethers.formatEther(expectedMarketing)} TokenA`);
    console.log(`期望销毁: ${ethers.formatEther(expectedBurn)} TokenA`);
    console.log(`期望分红: ${ethers.formatEther(expectedDividend)} TokenA`);
    console.log(`期望用户收到: ${ethers.formatEther(expectedUserReceive)} TokenA`);
    
    // 验证和对比
    console.log(`\\n=== 验证结果 ===`);
    
    // 验证营销分配
    const marketingCorrect = marketingBalanceChange === expectedMarketing;
    console.log(`营销分配: ${marketingCorrect ? '✅' : '❌'} (实际: ${ethers.formatEther(marketingBalanceChange)}, 期望: ${ethers.formatEther(expectedMarketing)})`);
    
    // 验证销毁
    const burnCorrect = totalSupplyChange === expectedBurn;
    console.log(`代币销毁: ${burnCorrect ? '✅' : '❌'} (实际: ${ethers.formatEther(totalSupplyChange)}, 期望: ${ethers.formatEther(expectedBurn)})`);
    
    // 验证用户收到
    const userReceiveCorrect = user1BalanceChange === expectedUserReceive;
    console.log(`用户收到: ${userReceiveCorrect ? '✅' : '❌'} (实际: ${ethers.formatEther(user1BalanceChange)}, 期望: ${ethers.formatEther(expectedUserReceive)})`);
    
    // 关键问题：验证分红累积
    const dividendCorrect = contractBalanceChange === expectedDividend;
    console.log(`\\n🔍 关键问题分析:`);
    console.log(`分红累积: ${dividendCorrect ? '✅' : '❌'} (实际: ${ethers.formatEther(contractBalanceChange)}, 期望: ${ethers.formatEther(expectedDividend)})`);
    
    if (!dividendCorrect) {
      console.log(`\\n❌ 问题发现：分红没有正确累积到合约中！`);
      console.log(`差额: ${ethers.formatEther(expectedDividend - contractBalanceChange)} TokenA`);
      
      // 检查交易事件
      console.log(`\\n=== 检查交易事件 ===`);
      const logs = receipt.logs;
      for (let i = 0; i < logs.length; i++) {
        try {
          const parsed = tokenA.interface.parseLog(logs[i]);
          if (parsed.name === 'TaxCollected') {
            console.log(`TaxCollected事件:`);
            console.log(`  营销: ${ethers.formatEther(parsed.args[0])} TokenA`);
            console.log(`  销毁: ${ethers.formatEther(parsed.args[1])} TokenA`);
            console.log(`  分红: ${ethers.formatEther(parsed.args[2])} TokenA`);
          } else if (parsed.name === 'DividendDistributed') {
            console.log(`DividendDistributed事件: ${ethers.formatEther(parsed.args[0])} TokenA`);
          }
        } catch (e) {
          // 忽略无法解析的日志
        }
      }
    } else {
      console.log(`✅ 分红累积正常`);
    }
    
    // 检查总账平衡
    const totalAccountedFor = marketingBalanceChange + totalSupplyChange + contractBalanceChange;
    const totalTaxAccountingCorrect = totalAccountedFor === totalTax;
    console.log(`\\n=== 总账平衡检查 ===`);
    console.log(`总税收: ${ethers.formatEther(totalTax)} TokenA`);
    console.log(`总分配: ${ethers.formatEther(totalAccountedFor)} TokenA`);
    console.log(`账目平衡: ${totalTaxAccountingCorrect ? '✅' : '❌'}`);
    
    if (!totalTaxAccountingCorrect) {
      console.log(`差额: ${ethers.formatEther(totalTax - totalAccountedFor)} TokenA`);
    }
  });
});