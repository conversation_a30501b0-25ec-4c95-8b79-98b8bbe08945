require("@nomicfoundation/hardhat-toolbox");
require("@nomicfoundation/hardhat-verify");
require("dotenv").config();

/** @type import('hardhat/config').HardhatUserConfig */
module.exports = {
  solidity: {
    version: "0.8.20",
    settings: {
      optimizer: {
        enabled: true,
        runs: 200,
      },
    },
  },
  networks: {
    hardhat: {
      chainId: 31337,
      // Fork BSC测试网进行本地测试
      forking: {
        url: "https://bsc-dataseed4.defibit.io/",
        // 使用较旧的区块避免同步问题
        blockNumber: ********,
        enabled: false
      },
      // 增加账户余额用于测试
      accounts: {
        mnemonic: "test test test test test test test test test test test junk",
        count: 20,
        accountsBalance: "10000000000000000000000" // 10000 ETH
      },
      // 配置gas限制
      blockGasLimit: ********,
      gas: ********,
      gasPrice: ***********, // 20 gwei
    },
    // 本地测试网（无fork）
    localhost: {
      url: "http://127.0.0.1:8545",
      chainId: 31337,
      accounts: {
        mnemonic: "test test test test test test test test test test test junk",
        count: 20,
        accountsBalance: "10000000000000000000000"
      }
    },
    // Fork BSC主网进行测试
    hardhat_mainnet_fork: {
      url: "http://127.0.0.1:8545",
      chainId: 31337,
      forking: {
        url: "https://bsc-dataseed1.binance.org/",
        enabled: true
      },
      accounts: {
        mnemonic: "test test test test test test test test test test test junk",
        count: 20,
        accountsBalance: "10000000000000000000000"
      }
    },
    bsc_testnet: {
      url: "https://data-seed-prebsc-1-s1.binance.org:8545/",
      chainId: 97,
      accounts: (process.env.PRIVATE_KEY && process.env.PRIVATE_KEY.length === 64) ? [process.env.PRIVATE_KEY] : [],
      gas: 2100000,
      gasPrice: ***********,
    },
    bsc_mainnet: {
      url: "https://bsc-dataseed1.binance.org/",
      chainId: 56,
      accounts: (process.env.PRIVATE_KEY && process.env.PRIVATE_KEY.length === 64) ? [process.env.PRIVATE_KEY] : [],
      gas: 2100000,
      gasPrice: **********,
    }
  },
  etherscan: {
    apiKey: {
      bsc: process.env.BSCSCAN_API_KEY || "",
      bscTestnet: process.env.BSCSCAN_API_KEY || ""
    }
  },
  sourcify: {
    enabled: true
  },
  gasReporter: {
    enabled: process.env.REPORT_GAS !== undefined,
    currency: "USD",
  },
  paths: {
    sources: "./contracts",
    tests: "./test",
    cache: "./cache",
    artifacts: "./artifacts"
  }
};