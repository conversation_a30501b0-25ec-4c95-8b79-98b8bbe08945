# LaunchApp 访问控制功能

## 功能概述

本功能允许通过环境变量来控制用户对 LaunchApp 功能的访问。当功能被禁用时，用户访问 `/app` 路由会显示一个优雅的"敬请期待"弹窗页面，而不是进入实际的应用功能。关闭弹窗后自动返回首页。

## 配置方法

### 环境变量配置

在 `.env` 文件中设置以下环境变量：

```bash
# LaunchApp 访问控制
# 设置为 'true' 启用 LaunchApp 访问，'false' 显示"敬请期待"消息
REACT_APP_ENABLE_LAUNCH_APP=false
```

### 配置选项

- `REACT_APP_ENABLE_LAUNCH_APP=true`: 用户可以正常访问 LaunchApp 的所有功能
- `REACT_APP_ENABLE_LAUNCH_APP=false`: 用户访问 `/app` 时显示"敬请期待"弹窗页面，无法进入实际应用

## 使用场景

1. **产品预发布**: 在正式发布前，可以设置为 `false` 来提醒用户功能暂未开放
2. **维护模式**: 在系统维护期间临时显示维护提示
3. **分阶段发布**: 可以根据不同环境（开发/测试/生产）来控制功能提示
4. **市场策略**: 在营销活动前展示"敬请期待"来创建期待感
5. **用户体验**: 比完全阻止访问更友好，用户可以看到页面但了解当前状态

## 国际化支持

"敬请期待"弹窗支持以下7种语言：

- 🇨🇳 中文 (zh)
- 🇺🇸 英文 (en) 
- 🇰🇷 韩文 (ko)
- 🇪🇸 西班牙文 (es)
- 🇩🇪 德文 (de)
- 🇫🇷 法文 (fr)
- 🇯🇵 日文 (ja)

语言会根据用户浏览器设置或通过语言切换器自动选择。

## 文件结构

```
dapp/
├── .env                                    # 环境变量配置文件
├── .env.example                           # 环境变量示例文件
├── src/
│   ├── App.jsx                           # 主应用组件
│   ├── pages/
│   │   └── LaunchApp.jsx                 # LaunchApp页面（包含弹窗逻辑）
│   ├── components/
│   │   └── ComingSoonModal.jsx           # "敬请期待"弹窗组件
│   └── i18n/locales/                     # 国际化翻译文件
│       ├── zh.json                       # 中文翻译
│       ├── en.json                       # 英文翻译
│       ├── ko.json                       # 韩文翻译
│       ├── es.json                       # 西班牙文翻译
│       ├── de.json                       # 德文翻译
│       ├── fr.json                       # 法文翻译
│       └── ja.json                       # 日文翻译
```

## 技术实现

### 路由控制逻辑

在 `App.jsx` 中实现了条件路由：

```jsx
function App() {
  const isLaunchAppEnabled = process.env.REACT_APP_ENABLE_LAUNCH_APP === 'true'

  return (
    <div className="App">
      <Routes>
        <Route path="/" element={<HomePage />} />
        <Route path="/app" element={isLaunchAppEnabled ? <LaunchApp /> : <ComingSoonPage />} />
      </Routes>
    </div>
  )
}
```

### ComingSoonModal 组件特性

- **完全适配应用风格**: 使用与应用一致的 violet/purple 和 amber/orange 主题色
- **优雅的设计**: 卡片风格，圆角设计，柔和阴影
- **动态效果**: 脉冲发光动画，涟漪效果，hero级别的入场动画
- **完整的移动端优化**: 
  - 响应式布局适配所有屏幕尺寸
  - 移动端专用字体大小和间距
  - 触摸优化 (`touch-manipulation`)
  - 超小屏幕 (≤375px) 特别优化
- **渐变元素**: 背景渐变，文字渐变，图标渐变
- **联系方式集成**: 内置邮箱联系方式 <EMAIL>
- **用户友好**: 点击背景关闭，自动返回首页
- **完整的国际化支持**: 7种语言全覆盖，包括联系方式文本

## 部署注意事项

1. **生产环境**: 确保在生产环境的 `.env` 文件中正确设置 `REACT_APP_ENABLE_LAUNCH_APP` 值
2. **环境同步**: 建议在 `.env.example` 中也更新相应配置，方便团队成员了解新环境变量
3. **构建验证**: 部署前通过 `npm run build` 验证构建成功
4. **缓存清理**: 修改环境变量后可能需要清理浏览器缓存

## 开发测试

```bash
# 启用 LaunchApp 访问
echo "REACT_APP_ENABLE_LAUNCH_APP=true" > .env
npm run dev

# 禁用 LaunchApp 访问（显示敬请期待页面）
echo "REACT_APP_ENABLE_LAUNCH_APP=false" > .env
npm run dev
```

## 扩展建议

1. **管理后台**: 可以考虑添加管理后台来动态控制功能开关
2. **用户通知**: 在"敬请期待"页面添加邮件订阅功能
3. **倒计时器**: 如果有明确的发布时间，可以添加倒计时功能
4. **社交分享**: 添加社交媒体分享按钮来增加传播度