# Gratitude Protocol Launch App

## 功能特性

### 核心功能
- 🔗 **多钱包支持**: 支持MetaMask、OKX Wallet、TokenPocket、WalletConnect等主流钱包
- 🔄 **智能检测**: 自动检测已安装的钱包并显示可用状态
- 🌐 **网络管理**: 自动检测和切换到BSC网络
- 💰 **资产显示**: 实时显示TokenA、TokenB余额和MinerNFT数量
- ⛏️ **双重挖矿机制**: 
  - 机制一：销毁TokenA获得TokenB（2倍价值奖励）
  - 机制二：持有NFT销毁TokenB（需要NFT资格）
- 🎁 **奖励领取**: 支持30%月释放率的奖励领取
- 🖼️ **NFT管理**: 显示持有的MinerNFT并管理TokenA释放
- 📊 **实时数据**: 动态显示挖矿记录和可领取奖励

### 界面特色
- 🎨 **统一设计风格**: 与官网保持一致的紫色+橙色主题
- 📱 **响应式设计**: 完美适配桌面和移动设备
- ⚡ **流畅交互**: 平滑动画和即时反馈
- 🔄 **实时更新**: 自动刷新用户数据和余额

## 部署说明

### 1. 合约地址配置

在 `app.html` 中找到以下配置并替换为实际部署的合约地址：

```javascript
const CONTRACT_ADDRESSES = {
    TokenA: "0x...", // TokenA合约地址
    TokenB: "0x...", // TokenB合约地址  
    MinerNFT: "0x...", // MinerNFT合约地址
    MiningContract: "0x...", // MiningContract合约地址
    PriceOracle: "0x..." // PriceOracle合约地址
};
```

### 2. 网络配置

默认配置为BSC主网，如需使用测试网，修改以下配置：

```javascript
const BSC_NETWORK = {
    chainId: '0x61', // BSC Testnet: 97 (0x61), BSC Mainnet: 56 (0x38)
    chainName: 'Binance Smart Chain Testnet',
    rpcUrls: ['https://data-seed-prebsc-1-s1.binance.org:8545/'],
    blockExplorerUrls: ['https://testnet.bscscan.com/']
};
```

### 3. 文件部署

1. 将 `app.html` 和 `index.html` 部署到Web服务器
2. 确保用户能通过浏览器访问这些文件
3. 推荐使用HTTPS协议以确保MetaMask正常工作

## 使用指南

### 首次使用
1. **安装钱包**: 确保已安装支持的钱包扩展（MetaMask、OKX Wallet、TokenPocket等）
2. **选择钱包**: 在钱包选择界面点击您想要使用的钱包
3. **添加BSC网络**: 应用会自动提示添加BSC网络
4. **授权连接**: 在钱包中确认连接授权

### 挖矿操作
1. **机制一挖矿**:
   - 输入要销毁的TokenA数量
   - 系统自动计算预期获得的TokenB数量
   - 点击"销毁TokenA挖矿"执行操作

2. **机制二挖矿**:
   - 确保持有至少1个MinerNFT
   - 输入要销毁的TokenB数量
   - 系统计算预期释放价值
   - 点击"销毁TokenB挖矿"执行操作

### NFT管理
1. **查看NFT**: 在"我的MinerNFT"部分查看持有的NFT
2. **释放TokenA**: 点击单个NFT的"释放TokenA"按钮
3. **批量释放**: 使用"一键释放所有TokenA"功能

### 奖励领取
1. **查看可领取**: 在挖矿记录区域查看可领取的奖励
2. **领取奖励**: 点击相应的"领取奖励"按钮
3. **释放机制**: 遵循30%月释放率，每30天可领取30%

## 技术架构

### 前端技术栈
- **HTML5 + CSS3**: 基础页面结构和样式
- **TailwindCSS**: 响应式UI框架
- **Ethers.js v6**: 区块链交互库
- **Vanilla JavaScript**: 原生JS实现所有交互逻辑

### 智能合约交互
- **ERC20标准**: TokenA和TokenB代币交互
- **ERC721标准**: MinerNFT交互
- **自定义合约**: MiningContract挖矿逻辑

### 安全特性
- **输入验证**: 所有用户输入都进行验证
- **网络检测**: 自动检测和切换到正确网络
- **错误处理**: 完善的错误捕获和用户提示
- **授权管理**: 智能代币授权管理

## 注意事项

### 安全提醒
⚠️ **重要**: 
- 确保合约地址正确，错误的地址可能导致资产损失
- 在主网部署前务必在测试网充分测试
- 建议进行安全审计

### 用户体验
- 首次使用需要较多的授权操作
- 建议提供详细的用户指南
- 准备客服支持处理用户问题

### 性能优化
- 合约调用有网络延迟，需要加载提示
- 大量NFT时分页显示以提升性能
- 考虑缓存机制减少重复查询

### 支持的钱包

| 钱包 | 状态 | 平台支持 | 说明 |
|------|------|----------|------|
| 🦊 **MetaMask** | ✅ 完全支持 | 浏览器扩展/移动端 | 最流行的以太坊钱包 |
| 🟡 **OKX Wallet** | ✅ 完全支持 | 浏览器扩展/移动端 | 安全便捷的多链钱包 |
| 💼 **TokenPocket** | ✅ 完全支持 | 浏览器扩展/移动端 | 专业的DeFi钱包 |
| 🔗 **WalletConnect** | 🚧 开发中 | 移动端钱包 | 支持更多移动端钱包 |

#### 连接说明
- **绿色指示器**: 钱包已安装且可用
- **橙色指示器**: 正在连接中
- **红色指示器**: 钱包未安装或不可用
- **选中状态**: 当前连接的钱包会显示高亮边框

#### 切换钱包
1. 点击头部的钱包名称按钮
2. 在钱包选择界面选择新的钱包
3. 确认连接后自动切换到新钱包

## 更新日志

### v1.2.0 (2025-06-26) - 弹窗钱包选择优化
- ✅ **新增弹窗式钱包选择**: 替换内联选择为模态弹窗，提升用户体验
- ✅ **移动端适配优化**: 弹窗完全适配手机端，支持触摸交互
- ✅ **实时钱包状态指示器**: 绿色/红色指示器显示钱包安装状态
- ✅ **智能钱包检测增强**: 更精确的钱包检测逻辑，避免冲突
- ✅ **连接状态可视化**: 连接中、已连接状态的视觉反馈
- ✅ **背景点击关闭**: 支持点击弹窗背景或ESC键关闭
- ✅ **钱包安装引导**: 一键跳转到MetaMask安装页面

### v1.1.0 (2025-01-XX)
- ✅ 新增多钱包支持（MetaMask、OKX、TokenPocket、Trust Wallet）
- ✅ 智能钱包检测和状态显示
- ✅ 钱包切换和断开连接功能
- ✅ 优化连接流程和错误处理
- ✅ 增强用户体验和视觉反馈

### v1.0.0 (2025-01-XX)
- ✅ 完成基础钱包连接功能
- ✅ 实现双重挖矿机制界面
- ✅ 添加NFT管理和TokenA释放功能
- ✅ 集成奖励领取和记录查看
- ✅ 优化用户体验和错误处理

## 支持与反馈

如有问题或建议，请通过以下方式联系：
- GitHub Issues
- 社区Discord
- 官方Telegram

---

**免责声明**: 本应用仅为演示用途，实际使用前请确保进行充分的安全测试和审计。